package com.glodon.qydata.vo.standard.trade;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
　　* @description: 标准统一--内置专业引用列表返回前端数据载体
　　* <AUTHOR>
　　* @date 2021/10/21 17:13
　　*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "内置专业引用列表返回前端数据载体")
public class StandardReferTradeVO implements Serializable {

    private static final long serialVersionUID = 3015389389258824967L;

    @Schema(description = "专业名称")
    private String description;
    @Schema(description = "专业编码")
    private String tradeCode;
    @Schema(description = "专业是否可被引用 true:可以被引用  false: 不可以被引用")
    private Boolean ifReferTrade;
}
