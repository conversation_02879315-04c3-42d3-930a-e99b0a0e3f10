package com.glodon.qydata.service.standard.buildStandard;

import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildCategory;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_category】的数据库操作Service
* @createDate 2022-08-01 10:57:17
*/
public interface ZbStandardsBuildCategoryService {
    /**
     * 根据建造标准id查询列表
     * @param standardId
     * @return
     */
    List<ZbStandardsBuildCategory> selectByStandardId(Long standardId);
    /**
     * 根据建造标准id查询暂存列表
     * @param standardId
     * @return
     */
    List<ZbStandardsBuildCategory> selectSelfByStandardId(Long standardId);

    /**
     * 批量保存暂存表数据
     * @param insertCategoryList
     */
    void batchSaveSelf(List<ZbStandardsBuildCategory> insertCategoryList);

    /**
     * 根据建造标准id删除备注表
     * @param standardId
     */
    void delSelfByStandardId(Long standardId);

    /**
     * 根据建造标准id查询信息
     * @param standIds
     * @return
     */
    List<ZbStandardsBuildCategory> selectSelfByStandardIds(List<Long> standIds);

    /**
     * 根据建造标准批量删除企业数据
     * @param allOriginIds
     */
    void delByStandardIds(List<Long> allOriginIds);

    /**
     * 批量保存数据
     * @param buildCategories
     */
    void batchSave(List<ZbStandardsBuildCategory> buildCategories);

    /**
     * 根据建造标准id批量删除暂存表数据
     * @param allSelfStandardIds
     */
    void delSelfByStandardIds(List<Long> allSelfStandardIds);

    /**
     * 根据建造标准id集合查询列表
     * @param standardIds
     * @return
     */
    List<ZbStandardsBuildCategory> selectByStandardIds(List<Long> standardIds);

}
