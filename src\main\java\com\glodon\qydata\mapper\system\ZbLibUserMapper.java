package com.glodon.qydata.mapper.system;

import com.glodon.qydata.entity.system.ZbLibUser;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * @className: ZbLibUserMapper
 * @description: 用户
 * @author: zhaoyj-g
 * @date: 2020/7/21
 **/
@Repository
public interface ZbLibUserMapper {

    /**
     * 添加
     * @param user
     */
    void insertUser(@Param("user") ZbLibUser user);

    /**
    　　* @description: 批量插入
    　　* @param  用户信息列表
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/11/19 16:59
    　　*/
    void batchInsert(List<ZbLibUser> list);

    /**
     * 更新
     * @param user
     */
    void updateUser(ZbLibUser user);
    /**
     * 根据主键进行更新
     * @param user
     */
    void updateByPrimaryKey(ZbLibUser user);

    /**
     * 根据用户id查询
     * @param globalId
     * @return
     */
    ZbLibUser getByGlobalId(String globalId);

    String getCustomerCodeByEnterpriseId(Long enterpriseId);

    /**
     * 查找某企业下所有主账号
     * @param customerCode
     */
    List<ZbLibUser> getMainAccountUserList(String customerCode);

    /**
     * @Title: getListByGlobalIds
     * @Description: 根据globalIds查询用户信息
     * @throws 
     * @param: 
     * @return: 
     * @auther: zhaohy-c
     * @date: 2021/3/18 20:12
     */
    List<ZbLibUser> getListByGlobalIds(@Param("globalIds") Set<String> globalIds);

    /**
     * @description: 根据企业编码查询所属用户信息
     * @param: * @param customerCode
     * @return:
     * <AUTHOR>
     * @date: 2021/7/12 15:43
     */
    List<ZbLibUser> getListByGlobalId(@Param("customerCode") String customerCode);

    /**
     * 新手指引次数+1
     * @param user
     * <AUTHOR>
     */
    void addGuideNum(ZbLibUser user);

    /**
     * @description: 根据企业编码或者授权平台的企业id查询所属用户信息
     * @param: * @param customerCode
     * @return:
     * <AUTHOR>
     * @date: 2021/7/12 15:43
     */
    List<ZbLibUser> getListByCustomerCode(@Param("customerCode") String customerCode);

    /**
    　　* @description: 查找用户信息中globalId存在多个的用户信息
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/9 15:47
    　　*/
    List<ZbLibUser> selectByCount();

    /**
    　　* @description: 批量删除
    　　* @param
    　　* <AUTHOR>
    　　* @date 2021/12/9 16:23
    　　*/
    void deleteByIdList(List<Long> list);

    /**
     　　* @description: 查找用户企业编码对应表（1对多）sys_zb_customer_dup
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2023/8/16 15:47
     　　*/
    String getEntCustomerDup(@Param("customerCode")String customerCode, @Param("enterpriseId")String enterpriseId);

    String getCustomerCodeByEntIdInEntCustomerDup(@Param("enterpriseId")String enterpriseId);
}
