use `db_cost_data_platform_pro`;

ALTER TABLE `db_cost_data_platform_pro`.`tb_area`
    CHANGE `area_code` `area_code` VARCHAR(20) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '地区编码',
    CHANGE `parent_area_code` `parent_area_code` VARCHAR(20) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '上级地区编码',
    ADD UNIQUE INDEX `area_code` (`area_code`, `displayable`);
ALTER TABLE `db_cost_data_platform_pro`.`tb_area`
    ADD COLUMN `related_area_code` VARCHAR(20) NULL COMMENT '关联的地区编码' AFTER `area_code`;
ALTER TABLE `db_cost_data_platform_pro`.`tb_area`
    DROP INDEX `idx_area_code`;
ALTER TABLE `db_cost_data_platform_pro`.`tb_area`
    DROP INDEX `area_code`;

CREATE TABLE `tb_area_change_record`
(
    `id`           int(11)     NOT NULL AUTO_INCREMENT COMMENT '自增主键',
    `user`         varchar(50) NOT NULL,
    `param`        text        NOT NULL COMMENT '参数',
    `created_time` timestamp   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

