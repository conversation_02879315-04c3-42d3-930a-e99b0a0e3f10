package com.glodon.qydata.mapper.repairdata;

import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 计算口径修复mapper（非业务使用）
 */
public interface TempRepairExpressionMapper {
    Integer selectRepeatName(@Param("customerCode") String customerCode);
    Integer selectRepeatCode(@Param("customerCode") String customerCode);
    Integer selectRepeatRecord(@Param("customerCode") String customerCode);
    List<ProjectFeature> selectFeature(@Param("customerCode") String customerCode, @Param("expressionIds") List<Long> expressionIds);
    List<ProjectFeature> checkFeature(@Param("customerCode") String customerCode);
    void updateExpression(ZbStandardsExpression recordTemp);
    void updateSelfExpression(ZbStandardsExpression recordTemp);
    void setExpressionInvalid(@Param("customerCode") String customerCode, @Param("expressionIds") List<Long> expressionIds);
    void synsSelfExpression(@Param("customerCode") String customerCode, @Param("expressionIds") List<Long> expressionIds);
}
