package com.glodon.qydata.vo.standard.buildStandard;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;

/**
 * @packageName: com.glodon.qydata.vo.standard.buildStandard
 * @className: ImportListVo
 * @author: ya<PERSON><PERSON><PERSON> <EMAIL>
 * @date: 2022/8/22 16:33
 * @description: 建造标准导入列表入参
 */
@Data
public class ImportListVo {
    private Integer pageNum = 1;
    private Integer currentPage = 1;
    private Integer pageSize = 10;
    // 传空查询所有用户
    private Set<String> userIds = new HashSet<>();
    // 查询分类编码数据
    private Set<String> categoryCodes;
    // 关键字搜索
    private String keywords;
    // 测算阶段
    private String stage;
    // 省市区
    private String provinceId;
    private String cityId;
    private String districtId;
    // 产品定位
    private String productPositioning;
    // 创建开始时间 - 结束时间
    private String createStartDate;
    private String createEndDate;
    // 存档开始时间 - 结束时间
    private String archiveStartDate;
    private String archiveEndDate;

    // 排序规则 默认 创建时间 现有需求按归档时间排序
    //archive ： 归档时间
    //create:创建时间
    private String orderBy = "archive";

    public Integer getPageNumTemp() {
        return pageNum;
    }
}
