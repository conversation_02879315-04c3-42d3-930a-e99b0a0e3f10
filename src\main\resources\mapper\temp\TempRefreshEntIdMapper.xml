<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.temp.TempRefreshEntIdMapper">

    <select id="selectAll" resultType="com.glodon.qydata.controller.temp.refreshEnterpriseId.TempSysZbUser">
        select id, global_id as globalId, customer_code as customerCode, qy_code_new as qyCodeNew, qy_flag as qyFlag
        from sys_zb_user
        where 1 = 1
    </select>

    <select id="selectByEntIdIsNull" resultType="com.glodon.qydata.controller.temp.refreshEnterpriseId.TempSysZbUser">
        select id, global_id as globalId, customer_code as customerCode, qy_code_new as qyCodeNew, qy_flag as qyFlag
        from sys_zb_user
        where qy_code_new is null;
    </select>

    <insert id="batchInsertOther">
        insert into sys_zb_customer (
        id, global_id, customer_code, qy_code_new, qy_flag)
        VALUES
        <foreach collection="list" separator="," item="user" index="index">
            (
            #{user.id},
            #{user.globalId},
            #{user.customerCode},
            #{user.qyCodeNew},
            #{user.qyFlag}
            )
        </foreach>
    </insert>

    <select id="selectQYCodeFromTable" resultType="java.lang.String">
        select ${qyCodeName}
        from ${table}
        group by ${qyCodeName}
    </select>

    <update id="updateTableNewQYCode" parameterType="java.lang.String">
        update ${table} a join sys_zb_customer b on a.${qyCodeName} = b.customer_code
        set a.qy_code_old = b.qy_code_new, a.qy_flag = b.qy_flag
        where a.${qyCodeName} = #{qyCodeValue}
    </update>

    <select id="selectVerifyEntId" resultType="com.glodon.qydata.controller.temp.refreshEnterpriseId.TempSysQyCodeIdVerify">
        select id, customer_code as customerCode, qy_code_new as qyCodeNew, qy_flag as qyFlag
        from sys_zb_qy_code_id_verify_1
        where qy_flag = 1;
    </select>

    <insert id="batchInsertOtherVerify">
        insert into sys_zb_qy_code_id_verify_2 (
        id, customer_code, qy_code_new, qy_flag, qy_verify)
        VALUES
        <foreach collection="list" separator="," item="user" index="index">
            (
            #{user.id},
            #{user.customerCode},
            #{user.qyCodeNew},
            #{user.qyFlag},
            #{user.qyVerify}
            )
        </foreach>
    </insert>

</mapper>