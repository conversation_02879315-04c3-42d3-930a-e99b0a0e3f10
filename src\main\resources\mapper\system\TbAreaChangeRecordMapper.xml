<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.system.TbAreaChangeRecordMapper">
    <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.system.TbAreaChangeRecord">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user" property="user" jdbcType="VARCHAR"/>
        <result column="param" property="param" jdbcType="VARCHAR"/>
        <result column="created_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <insert id="insert">
        insert into tb_area_change_record(user, param)
        values (#{record.user}, #{record.param})
    </insert>
</mapper>
