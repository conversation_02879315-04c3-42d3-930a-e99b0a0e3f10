package com.glodon.qydata.service.system.impl;

import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.entity.system.ZbLibUser;
import com.glodon.qydata.mapper.system.ZbLibUserMapper;
import com.glodon.qydata.util.DataDealUtil;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Random;

/**
 * @className: ZbUserService
 * @description: 用户操作
 * @author: zhaoyj-g
 * @date: 2020/7/21
 **/
@Service("zbLibUserService")
@Slf4j
public class ZbLibUserService {
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ZbLibUserMapper userMapper;

    /**
     * 企业编码重复状态
     */
    public static final String ENT_DUP_1 = "1";
    public static final String ENT_DUP_0 = "0";

    public void syncUser(ZbLibUser user) {
        String globalId = user.getGlobalId();
        String isLock = redisUtil.getString(RedisKeyEnum.LOCK_UPDATE_USER,globalId);

        try {
            if(isLock !=null){ //若正在执行，则等一会
                int ms = new Random().nextInt(1000)+3000;
                Thread.sleep(ms);
            }
            //上锁
            redisUtil.setString(RedisKeyEnum.LOCK_UPDATE_USER,"1",globalId);

            ZbLibUser temp = userMapper.getByGlobalId(globalId);
            if (temp != null) {
                userMapper.updateUser(user);
            } else {
                user.setId(SnowflakeIdUtils.getNextId());
                userMapper.insertUser(user);
            }
        } catch (Exception e) {
            log.error("同步用户信息失败 userInfo:{}", JSONObject.toJSONString(user));
            e.printStackTrace();
        }finally {
            //释放锁
            redisUtil.delete(RedisKeyEnum.LOCK_UPDATE_USER,globalId);
        }
    }

    public ZbLibUser getUserByGlobalId(String globalId) {
        return userMapper.getByGlobalId(globalId);
    }

    public String getCustomerCodeByEnterpriseId(String enterpriseId) {
        if (StringUtils.isEmpty(enterpriseId) || !DataDealUtil.isNumeric(enterpriseId)) {
            return null;
        }
        return userMapper.getCustomerCodeByEnterpriseId(Long.parseLong(enterpriseId));
    }

    /**
     * 判断企业id是否在多编码列表
     * @param customerCode
     * @param enterpriseId
     * @return
     * <AUTHOR>
     */
    public boolean isEntCustomerDup(String customerCode, String enterpriseId) {
        if (StringUtils.isBlank(customerCode) || StringUtils.isBlank(enterpriseId)) {
            return true;
        }

        // 放置redis缓存
        String key = customerCode + "_" + enterpriseId;
        String ret = redisUtil.getString(RedisKeyEnum.ENT_CUSTOMER_DUP, key);
        if (ret != null) {
            return ret.equals(ENT_DUP_1);
        }

        try {
            String status = userMapper.getEntCustomerDup(customerCode, enterpriseId);
            redisUtil.setString(RedisKeyEnum.ENT_CUSTOMER_DUP, status != null ? ENT_DUP_1 : ENT_DUP_0, key);
            return status != null;
        } catch (Exception e) {
            log.error("ZbLibUserService.isCustomerDup error,e=", e);
            return false;
        }
    }

    public String getCustomerCodeInSysZbCustomerDup(String enterpriseId) {
        return userMapper.getCustomerCodeByEntIdInEntCustomerDup(enterpriseId);
    }
}
