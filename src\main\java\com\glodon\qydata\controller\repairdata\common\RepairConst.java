package com.glodon.qydata.controller.repairdata.common;

public class RepairConst {
    public static final String success = "修复成功";
    public static final String erro_after_repair = "修复后检查不符合预期";
    public static final String erro_repairing = "修复错误";

    // 数据分组分隔符
    public static final String keyDelimiter = "@";

    // 数据是否无效 2无效
    public static final Integer data_status_invalid_2 = 2;
    // 数据是否无效 1无效
    public static final Integer data_status_invalid = 1;
    // 数据是否无效 0有效
    public static final Integer data_status_valid = 0;
}
