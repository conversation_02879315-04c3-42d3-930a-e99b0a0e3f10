package com.glodon.qydata;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.glodon.qydata.entity.system.TbArea;
import com.glodon.qydata.service.system.IAreaService;
import com.glodon.qydata.util.AMapApiUtil;
import com.glodon.qydata.vo.amap.AMapDistrict;
import com.glodon.qydata.vo.amap.AMapQueryParams;
import com.glodon.qydata.vo.amap.AMapResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 地区相关测试类
 * Created by caidj on 2025/02/25.
 */
@Slf4j
@SpringBootTest
@ExtendWith(SpringExtension.class)
@ActiveProfiles("hw-sprint")
public class TestAreaBak {
    @Autowired
    private IAreaService areaService;

    private static Map<String, String> provinceSkipMap = new HashMap<>();
    private static Map<String, String> citySkipMap = new HashMap<>();
    private static Map<String, String> districtSkipMap = new HashMap<>();
    private static Map<String, String> provinceCoverMap = new HashMap<>();
    private static Map<String, String> cityCoverMap = new HashMap<>();
    private static Map<String, String> districtCoverMap = new HashMap<>();
    static {
        provinceCoverMap.put("台湾", "台湾省");
        provinceSkipMap.put("其他地区", "");

        districtSkipMap.put("河南省-济源市-济源市", ""); //1997年，经国务院批准，济源市不再由焦作市代管，而是由河南省直接管辖，行政级别提升为副地级市。2005年，济源市更是升格为省辖市。
        districtCoverMap.put("河南省-洛阳市-偃师市", "偃师区"); //2021年1月，撤销县级偃师市，设立洛阳市偃师区；同时撤销孟津县、洛阳市吉利区，设立洛阳市孟津区。
        districtCoverMap.put("河南省-洛阳市-孟津县", "孟津区");
        districtCoverMap.put("河南省-洛阳市-吉利区", "孟津区");
        districtCoverMap.put("河南省-三门峡市-陕县", "陕州区"); //2015年，撤销陕县，设立三门峡市陕州区
        districtCoverMap.put("河南省-许昌市-许昌县", "建安区"); //2016年，许昌县被正式撤销，并设立了许昌市建安区
        districtSkipMap.put("河南省-郑州市-经济技术开发区", ""); //行政管理区(又称“功能区”)，不属于行政区划单位
        districtSkipMap.put("河南省-郑州市-高新开发区", "");
        districtSkipMap.put("河南省-郑州市-郑东新区", "");
        districtSkipMap.put("河南省-郑州市-航空港经济综合实验区", "");
        districtSkipMap.put("河南省-郑州市-出口加工区", ""); //特定的经济区域
        districtCoverMap.put("河南省-郑州市-管城区", "管城回族区"); // 管城区和管城回族区在行政区划上是同一个地方，只是名称上有所不同
        districtCoverMap.put("河南省-郑州市-邙山区", "惠济区"); //历史上存在过作为行政区划的邙山区，但现已并入惠济区
        districtCoverMap.put("河南省-周口市-淮阳县", "淮阳区"); //淮阳区是由淮阳县撤销后设立的
        districtCoverMap.put("河南省-新乡市-长垣县", "长垣市"); //长垣市是由长垣县撤县设市而来

        districtCoverMap.put("河南省-开封市-金明区", "龙亭区"); //2014年9月，国务院批复开封市调整部分行政区划，撤销了金明区，其所属行政区域并入龙亭区
        districtCoverMap.put("河南省-开封市-开封县", "祥符区"); //2014年10月，国务院批复同意撤销开封县，设立开封市祥符区，以原开封县的行政区域为祥符区的行政区域
        districtSkipMap.put("河南省-平顶山市-高新技术开发区", ""); //行政管理区
        districtSkipMap.put("广东省-肇庆市-肇庆市", ""); //肇庆市是广东省的一个地级市，下辖鼎湖区、端州区和高要区等市辖区。鼎湖区是从高要县析出而设立的，而端州区则是肇庆市升格为地级市后，原肇庆市改设而来的。
        districtCoverMap.put("广东省-肇庆市-高要市", "高要区"); //高要市原为肇庆市下辖的一个县级市，后经过行政区划调整成为肇庆市高要区

        districtSkipMap.put("广东省-惠州市-大亚湾区", ""); //行政管理区
        districtCoverMap.put("广东省-阳江市-阳东县", "阳东区"); //阳东县被撤销，并设立了阳江市阳东区
        districtCoverMap.put("广东省-茂名市-电白县", "电白区"); //电白县和茂港区被撤销并合并成立了新的电白区
        districtCoverMap.put("广东省-茂名市-茂港区", "电白区");
        districtCoverMap.put("广东省-潮州市-潮安县", "潮安区"); //安县被撤销并设立了潮州市潮安区
        districtCoverMap.put("广东省-揭阳市-揭东县", "揭东区"); //揭东县被撤销并设立了揭阳市揭东区
        districtCoverMap.put("广东省-清远市-清新县", "清新区"); //清新县被撤销并设立了清远市清新区
        districtCoverMap.put("广东省-韶关市-曲江县", "曲江区"); //曲江县进行了撤县设区的调整；库里既有曲江县也有曲江区 todo 观察两条数据是否都更新
        districtCoverMap.put("广东省-云浮市-云安县", "云安区"); //云安县被撤销，并设立了云安区
        districtCoverMap.put("内蒙古自治区-呼伦贝尔市-莫力达瓦", "莫力达瓦达斡尔族自治旗"); //可视为同一单位的不同表述 todo 是否需要改
        districtSkipMap.put("内蒙古自治区-通辽市-经济技术开发区", ""); //行政管理区
        districtSkipMap.put("内蒙古自治区-锡林郭勒盟-乌拉盖管理区", ""); //行政管理区
        districtSkipMap.put("内蒙古自治区-鄂尔多斯市-恩格贝生态示范区", ""); //行政管理区
        districtSkipMap.put("内蒙古自治区-鄂尔多斯市-空港园区", ""); //行政管理区
        districtSkipMap.put("内蒙古自治区-鄂尔多斯市-鄂尔多斯高新技术产业开发区", ""); //行政管理区
        districtSkipMap.put("内蒙古自治区-呼和浩特市-经济技术开发区", ""); //行政管理区
        districtSkipMap.put("内蒙古自治区-呼和浩特市-林格尔新区", ""); //行政管理区
        districtCoverMap.put("黑龙江省-大兴安岭地区-漠河县", "漠河市"); //漠河县已经撤县设市，成为漠河市
        districtCoverMap.put("黑龙江省-哈尔滨市-双城市", "双城区"); //双城市被撤销并设立了双城区
        districtCoverMap.put("黑龙江省-哈尔滨市-动力区", "香坊区"); //哈尔滨市的动力区与香坊区合并，组成了新的香坊区
        districtCoverMap.put("黑龙江省-哈尔滨市-太平区", "道外区"); //哈尔滨市太平区被撤销，其行政区域划归道外区
        districtCoverMap.put("黑龙江省-伊春市-红星区", "丰林县"); //伊春市在2019年进行了部分行政区划的调整，其中撤销了红星区，并将其与原新青区、五营区合并，设立了新的丰林县
        districtCoverMap.put("黑龙江省-齐齐哈尔市-铁峰区", "铁锋区"); //铁峰区”应为“铁锋区”的误写，因原齐齐哈尔铁路局座落于辖区内而得名，寓意“铁路先锋区”
        districtCoverMap.put("黑龙江省-齐齐哈尔市-梅里斯达斡尔区", "梅里斯达斡尔族区"); //正式名称：梅里斯达斡尔族区（有时简称为梅里斯区，但“梅里斯达斡尔区”并非正式名称）
        districtSkipMap.put("新疆维吾尔自治区-石河子市-石河子市", ""); //自治区直辖县级行政单位，层级对应到市级
    }


    @Test
    public void testArea() {
        // 库里的数据
        List<TbArea> dbAreaList = areaService.getAreaTree(null, "ONLY_VALID");
        List<TbArea> dbProvince = dbAreaList.get(0).getChildren();
        // 高德的数据
        List<AMapDistrict> aMapProvince = AMapApiUtil.getProvince();


        List<String> dbProvinceList = dbProvince.stream().map(TbArea::getName).collect(Collectors.toList());
        List<String> aMapProvinceList = aMapProvince.stream().map(AMapDistrict::getName).collect(Collectors.toList());
        compareProvinceLists(dbProvinceList, aMapProvinceList);

        // 库里的和高德的--省的编码是一致的
        Map<String, TbArea> dbProvinceMap = dbProvince.stream().collect(Collectors.toMap(TbArea::getAreaCode, Function.identity()));

        for (AMapDistrict aMapProvinceNode : aMapProvince) {
            String provinceName = aMapProvinceNode.getName();
            String provinceCode = aMapProvinceNode.getAdcode();
            List<AMapDistrict> aMapCity = aMapProvinceNode.getDistricts();
            List<TbArea> dbCity = dbProvinceMap.get(provinceCode).getChildren();

            List<String> dbCityListList = dbCity.stream().map(TbArea::getName).collect(Collectors.toList());
            List<String> aMapCityList = aMapCity.stream().map(AMapDistrict::getName).collect(Collectors.toList());
            compareCityLists(dbCityListList, aMapCityList, provinceName);

            Map<String, TbArea> dbCityMap = dbCity.stream().collect(Collectors.toMap(TbArea::getAreaCode, Function.identity()));
            for (AMapDistrict aMapCityNode : aMapCity) {
                String cityName = aMapCityNode.getName();
                String cityCode = aMapCityNode.getAdcode();
                List<AMapDistrict> aMapDistrict = aMapCityNode.getDistricts().stream().filter(x -> x.getLevel().equals("district")).collect(Collectors.toList());
                List<TbArea> dbDistrict = dbCityMap.get(cityCode) == null ? new ArrayList<>() : dbCityMap.get(cityCode).getChildren();

                List<String> dbDistrictListList = dbDistrict.stream().map(TbArea::getName).collect(Collectors.toList());
                List<String> aMapDistrictList = aMapDistrict.stream().map(AMapDistrict::getName).collect(Collectors.toList());
                compareDistrictLists(dbDistrictListList, aMapDistrictList, provinceName, cityName);
            }
        }

    }

    @Test
    public void testAMap() {
        AMapQueryParams params = new AMapQueryParams();
        params.setSubdistrict(0);
        params.setOffset(9999);

        AMapResponse district = AMapApiUtil.getDistrict(params);
        log.info(JSONUtil.toJsonStr(district));

    }
    public void compareProvinceLists(List<String> dbProvinceList, List<String> aMapProvinceList) {
        // 将两个列表转换为Set以去除重复元素，并便于后续的集合操作
        Set<String> dbProvincesSet = dbProvinceList.stream().filter(province -> !provinceSkipMap.containsKey(province)).map(x -> provinceCoverMap.getOrDefault(x, x)).collect(Collectors.toSet());
        Set<String> aMapProvincesSet = aMapProvinceList.stream().filter(province -> !provinceSkipMap.containsValue(province)).collect(Collectors.toSet());

        // 计算仅存在于dbProvincesSet中的元素
        Set<String> onlyInDb = dbProvincesSet.stream()
                .filter(province -> !aMapProvincesSet.contains(province))
                .collect(Collectors.toSet());

        // 计算仅存在于aMapProvincesSet中的元素
        Set<String> onlyInAMap = aMapProvincesSet.stream()
                .filter(province -> !dbProvincesSet.contains(province))
                .collect(Collectors.toSet());

        // 输出差异
        if (CollUtil.isNotEmpty(onlyInDb)){
            log.info("仅存在于数据库中的省份\t{}", onlyInDb);
        }
        if (CollUtil.isNotEmpty(onlyInAMap)){
            log.info("仅存在于高德API中的省份\t{}", onlyInAMap);
        }
    }

    public void compareCityLists(List<String> dbCityList, List<String> aMapCityList, String provinceName) {
        // 将两个列表转换为Set以去除重复元素，并便于后续的集合操作
        Set<String> dbCitySet = new HashSet<>(dbCityList);
        Set<String> aMapCitySet = new HashSet<>(aMapCityList);

        // 计算仅存在于dbProvincesSet中的元素
        Set<String> onlyInDb = dbCitySet.stream()
                .filter(city -> !aMapCitySet.contains(city))
                .collect(Collectors.toSet());

        // 计算仅存在于aMapProvincesSet中的元素
        Set<String> onlyInAMap = aMapCitySet.stream()
                .filter(city -> !dbCitySet.contains(city))
                .collect(Collectors.toSet());

        // 输出差异
        if (CollUtil.isNotEmpty(onlyInDb)){
            log.info("仅存在于数据库中的市\t{}\t{}", provinceName, onlyInDb);
        }
        if (CollUtil.isNotEmpty(onlyInAMap)){
            log.info("仅存在于高德API中的市\t{}\t{}", provinceName, onlyInAMap);
        }
    }

    public void compareDistrictLists(List<String> dbCityList, List<String> aMapCityList, String provinceName, String cityName) {
        // 将两个列表转换为Set以去除重复元素，并便于后续的集合操作
        Set<String> dbCitySet = dbCityList.stream().filter(districtName -> !districtSkipMap.containsKey(districtNameKey(provinceName, cityName, districtName)))
                .map(x -> districtCoverMap.getOrDefault(districtNameKey(provinceName, cityName, x), x)).collect(Collectors.toSet());
        Set<String> aMapCitySet = aMapCityList.stream().filter(districtName -> !districtSkipMap.containsValue(districtNameKey(provinceName, cityName, districtName))).collect(Collectors.toSet());

        // 计算仅存在于dbProvincesSet中的元素
        Set<String> onlyInDb = dbCitySet.stream()
                .filter(city -> !aMapCitySet.contains(city))
                .collect(Collectors.toSet());

        // 计算仅存在于aMapProvincesSet中的元素
        Set<String> onlyInAMap = aMapCitySet.stream()
                .filter(city -> !dbCitySet.contains(city))
                .collect(Collectors.toSet());

        // 输出差异
        if (CollUtil.isNotEmpty(onlyInDb)){
            log.info("仅存在于数据库中的区\t{}-{}\t{}", provinceName, cityName, onlyInDb);
        }
        if (CollUtil.isNotEmpty(onlyInAMap)){
            log.info("仅存在于高德API中的区\t{}-{}\t{}", provinceName, cityName, onlyInAMap);
        }
    }

    private String districtNameKey(String provinceName, String cityName, String districtName) {
        return StrUtil.format("{}-{}-{}", provinceName, cityName, districtName);
    }
}


