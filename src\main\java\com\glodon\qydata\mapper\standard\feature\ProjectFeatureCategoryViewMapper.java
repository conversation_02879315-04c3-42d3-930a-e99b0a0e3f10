package com.glodon.qydata.mapper.standard.feature;

import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 
 * <AUTHOR>
 * @date 2021/11/5 8:42
 */
@Repository
public interface ProjectFeatureCategoryViewMapper {

    /**
     *  批量插入
     * @param list
     * @return int
     * <AUTHOR>
     * @date 2021/11/5 9:05 
     */
    int saveBatch(@Param("list") List<ProjectFeatureCategoryView> list);

    /**
    　　* @description: 批量删除专业下所有工程特征的分类视图
    　　* @param
    　　* @return
    　　* <AUTHOR>
    　　* @date 2022/1/7 16:06
    　　*/
    void deleteByTradeId(@Param("tradeId") Long tradeId);
    /**
     *  批量删除
     * @param tradeIds
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 9:05
     */
    void batchDeleteByTradeId(List<Long> tradeIds);

    /**
     *  根据分类和专业查询
     * @param tradeId
     * @param categoryCode
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeatureCategoryView>
     * <AUTHOR>
     * @date 2021/11/5 9:05 
     */
    List<ProjectFeatureCategoryView> selectByCategoryAndTrade(Long tradeId, String categoryCode, Integer type);

    /**
     *  根据企业和工程分类查询特征
     * @param categoryCode
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeatureCategoryView>
     * <AUTHOR>
     * @date 2021/11/5 9:06 
     */
    List<ProjectFeatureCategoryView> selectByCategoryAndCustomerCode(@Param("categoryCode")String categoryCode,
                                                                     @Param("customerCode")String customerCode,
                                                                     @Param("type") Integer type);
    /**
     * 根据企业编码删除计算口径
     * @param customerCode
     * @return void
     * <AUTHOR>
     * @date 2021/11/9 10:42
     */
    void deleteByCustomerCode(String customerCode, Integer type);

    /**
     *  根据企业和工程分类和工程专业查询特征
     * @param categoryCode
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeatureCategoryView>
     * <AUTHOR>
     * @date 2021/11/5 9:06
     */
    List<ProjectFeatureCategoryView> selectByCategoryAndCustomerCodeAndTradeId(@Param("categoryCode")String categoryCode,
                                                                     @Param("customerCode")String customerCode,
                                                                     @Param("tradeId")Long tradeId, @Param("type") Integer type);

    List<String> selectAllCustomerCode();

    /**
     * 根据Id批量删除
     * @param ids
     */
    void batchDeleteByIds(@Param("ids")List<Long> ids);
    List<ProjectFeatureCategoryView> selectByFeatureIds(String customerCode, @Param("ids")List<Long> ids);

    void batchUpdateOrd(@Param("list") List<ProjectFeatureCategoryView> list);

    void updateOrd(ProjectFeatureCategoryView categoryView);

    List<ProjectFeatureCategoryView> selectByCategoryAndFeatureIds(String customerCode, @Param("categoryCode")String categoryCode, @Param("ids")List<Long> ids);
    Integer selectMaxOrdByCondition(@Param("categoryCode")String categoryCode, @Param("customerCode")String customerCode, @Param("type") Integer type);

}