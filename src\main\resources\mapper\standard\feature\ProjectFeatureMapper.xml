<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper" >
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.feature.ProjectFeature" >
      <id column="id" property="id" jdbcType="BIGINT" />
      <result column="expression_id" property="expressionId" jdbcType="BIGINT" />
      <result column="feature_level" property="featureLevel" jdbcType="VARCHAR" />
      <result column="is_sync" property="isSync" jdbcType="BIT" />
      <result column="type" property="type" jdbcType="INTEGER"/>
      <result column="project_type" property="projectType" jdbcType="VARCHAR" />
      <result column="is_using" property="isUsing" jdbcType="BIT" />
      <result column="is_default" property="isDefault" jdbcType="BIT" />
      <result column="is_required" property="isRequired" jdbcType="BIT" />
      <result column="remark" property="remark" jdbcType="VARCHAR" />
      <result column="is_from_system" property="isFromSystem" jdbcType="BIT" />
      <result column="zb_project_feature_id" property="zbProjectFeatureId" jdbcType="BIGINT" />
      <result column="customer_code" property="customerCode" jdbcType="VARCHAR" />
      <result column="is_deleted" property="isDeleted" jdbcType="BIT" />
      <result column="create_global_id" property="createGlobalId" jdbcType="BIGINT" />
      <result column="update_global_id" property="updateGlobalId" jdbcType="BIGINT" />
      <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
      <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
      <result column="product_pro_type" property="productProType" jdbcType="INTEGER"/>
      <result column="is_expression" property="isExpression" jdbcType="INTEGER"/>
      <result column="ord_trade" property="ordTrade" jdbcType="INTEGER"/>
      <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
      <result column="is_search_condition" property="isSearchCondition" jdbcType="INTEGER"/>
      <result column="trade_name" property="tradeName" jdbcType="VARCHAR" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
      <result column="option" jdbcType="VARCHAR" property="option" />
      <result column="unit" jdbcType="VARCHAR" property="unit" />
      <result column="expression_code" jdbcType="VARCHAR" property="expressionCode" />
      <result column="is_expression_default" jdbcType="INTEGER" property="isExpressionDefault" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, expression_id, feature_level, is_sync, `type`, project_type, is_using, is_default, is_deleted,
    is_required, remark, zb_project_feature_id, is_from_system, customer_code, create_global_id, update_global_id,
    create_time, update_time, product_pro_type, is_expression, ord_trade, trade_id, is_search_condition, trade_name,
    `name`, type_code, `option`, unit, expression_code, is_expression_default, invalid
  </sql>
  <sql id="No_Blob_Column_List" >
    id, expression_id, feature_level, is_sync, `type`, is_using, is_default, is_deleted,
    is_required, remark, zb_project_feature_id, is_from_system, customer_code, create_global_id, update_global_id,
    create_time, update_time, product_pro_type, is_expression, ord_trade, trade_id, is_search_condition, trade_name,
    `name`, type_code, `option`, unit, expression_code, is_expression_default
  </sql>

  <insert id="batchInsert" parameterType="com.glodon.qydata.entity.standard.feature.ProjectFeature">
    insert into zb_project_feature_standards ( id, expression_id, feature_level, is_sync,type, project_type, is_using, is_default,
    is_required, remark, zb_project_feature_id, is_from_system, customer_code, is_deleted,create_global_id, update_global_id,
    create_time, update_time, product_pro_type, is_expression, ord_trade, trade_id, is_search_condition, trade_name,
    `name`, type_code, `option`, unit, expression_code, is_expression_default, qy_code_old, qy_flag)
    values
    <foreach collection="list" item="item" index="index" separator="," >
    (
      #{item.id,jdbcType=BIGINT},  #{item.expressionId,jdbcType=BIGINT},
      #{item.featureLevel,jdbcType=VARCHAR},#{item.isSync,jdbcType=BIT},#{item.type,jdbcType=INTEGER}, #{item.projectType,jdbcType=VARCHAR},  #{item.isUsing,jdbcType=BIT},
      #{item.isDefault,jdbcType=BIT}, #{item.isRequired,jdbcType=BIT}, #{item.remark,jdbcType=VARCHAR},#{item.zbProjectFeatureId,jdbcType=BIGINT},
      #{item.isFromSystem,jdbcType=BIT},#{item.customerCode,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=BIT}, #{item.createGlobalId,jdbcType=BIGINT},
      #{item.updateGlobalId,jdbcType=BIGINT},now(), #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.productProType,jdbcType=INTEGER}, #{item.isExpression,jdbcType=INTEGER}, #{item.ordTrade,jdbcType=INTEGER},
      #{item.tradeId,jdbcType=BIGINT}, #{item.isSearchCondition,jdbcType=INTEGER}, #{item.tradeName,jdbcType=VARCHAR},
      #{item.name,jdbcType=VARCHAR}, #{item.typeCode,jdbcType=VARCHAR}, #{item.option,jdbcType=VARCHAR},
      #{item.unit,jdbcType=VARCHAR}, #{item.expressionCode,jdbcType=VARCHAR}, #{item.isExpressionDefault,jdbcType=INTEGER},
      #{item.qyCodeOld,jdbcType=VARCHAR}, #{item.qyFlag,jdbcType=INTEGER}
      )
    </foreach>

  </insert>

  <!--根据工程特征主键ID查询工程特征信息-->
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards
    where is_deleted = 0
    and id = #{id,jdbcType=BIGINT}
  </select>

  <!--根据企业编码和源工程特征ID及工程分类查询工程特征信息-->
  <select id="selectByTradeIdAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards
    where is_deleted = 0
    and trade_id = #{tradeId,jdbcType=BIGINT}
    and type = #{type,jdbcType=INTEGER}
    --  此条件实现当专业为有效数据时，查有效工程特征，当专业为无效数据时，查所有的工程特征 (invalid值为1为重复数据，2位找不到有效专业数据)
    and invalid != 1
    order by ord_trade
  </select>

  <!--根据专业id查询工程特征信息，若专业id为空，则查询该企业下所有专业锁对应的工程特征信息-->
  <select id="selectTradeProjectFeature" resultType="com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO">
      SELECT
          zpfs.id,
          zpfs.expression_id as expressionId,
          zpfs.`name`,
          zpfs.type_code as typeCode,
          zpfs.`option`,
          zpfs.`unit`,
          zpfs.feature_level as featureLevel,
          zpfs.is_sync as isSync,
          <if test="isNeedCategoryInfo == 1">
              zpfs.project_type as projectType,
          </if>
          zpfs.is_using as isUsing,
          zpfs.is_default as isDefault,
          zpfs.is_deleted as isDeleted,
          zpfs.is_required as isRequired,
          zpfs.remark as remark,
          zpfs.zb_project_feature_id as zbProjectFeatureId,
          zpfs.is_from_system as isFromSystem,
          zpfs.customer_code as customerCode,
          zpfs.create_global_id as createGlobalId,
          zpfs.update_global_id as updateGlobalId,
          zpfs.create_time as createTime,
          zpfs.update_time as updateTime,
          zpfs.product_pro_type as productProType,
          zpfs.is_expression as isExpression,
          zpfs.ord_trade as ordTrade,
          zpfs.trade_id as tradeId,
          zst.trade_code as tradeCode,
          zst.description as tradeName,
          zst.enabled as tradeEnabled,
          zst.ord as tradeOrder
      FROM zb_project_feature_standards zpfs
      LEFT JOIN zb_standards_trade zst ON zst.id = zpfs.trade_id
      WHERE zpfs.customer_code = #{customerCode}
      --  此条件实现当专业为有效数据时，查有效工程特征，当专业为无效数据时，查所有的工程特征 (invalid值为1为重复数据，2位找不到有效专业数据)
      and zst.invalid != 1 and  zpfs.invalid  != 1
      AND zpfs.type = #{type}
      AND zpfs.is_deleted = 0
      <if test="tradeIds != null and tradeIds.size() > 0">
          AND zpfs.trade_id in
          <foreach collection="tradeIds" item="item" index="index" separator="," open="(" close=")">
            #{item}
          </foreach>
      </if>
  </select>

 <update id="deleteByTradeId" parameterType="java.lang.Long">
      update zb_project_feature_standards  set is_deleted = 1 where trade_id = #{tradeId,jdbcType=BIGINT}
 </update>

  <!--根据企业编码和源工程特征ID查询工程特征信息-->
  <select id="selectByTradeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards
    where is_deleted = 0
    and trade_id = #{tradeId,jdbcType=BIGINT}
    and invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
    order by ord_trade
  </select>

  <select id="selectByFeatureIdList"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
    <if test="isShowNotUsing != 1" >
      AND is_using = 1
    </if>
    and id in
    <foreach collection="featureIds" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_standards
    where customer_code = #{customerCode} and invalid = 0
    <if test="type != null" >
      and `type` = #{type,jdbcType=INTEGER}
    </if>
      and is_deleted = 0
  </select>

  <delete id="deleteByCustomerCode">
    delete from zb_project_feature_standards
    where customer_code = #{customerCode,jdbcType=VARCHAR}
    <if test="type != null" >
      and `type` = #{type,jdbcType=INTEGER}
    </if>
  </delete>

  <select id="selectInit" resultType="java.lang.Integer">
    select count(1)
    from zb_project_feature_standards
    where customer_code = #{customerCode} and create_global_id = '-100' and invalid = 0
    <if test="type != null" >
        and `type` = #{type,jdbcType=INTEGER}
    </if>
  </select>

  <insert id="insertBatch" parameterType="list">
    insert into zb_project_feature_standards (id,expression_id,feature_level,is_sync,type,project_type,is_using,is_default,is_required,remark,is_from_system,zb_project_feature_id,
    customer_code,is_deleted,create_global_id,update_global_id,create_time,update_time,product_pro_type,is_expression,ord_trade,trade_id,trade_name,is_search_condition,
    `name`, type_code, `option`, unit, expression_code, is_expression_default, qy_code_old, qy_flag) values
    <foreach collection="list" item="item" index="index" separator="," >
      (
      #{item.id}, #{item.expressionId}, #{item.featureLevel}, #{item.isSync},#{item.type}, #{item.projectType},
      #{item.isUsing}, #{item.isDefault}, #{item.isRequired}, #{item.remark}, #{item.isFromSystem},
      #{item.zbProjectFeatureId}, #{item.customerCode}, #{item.isDeleted}, #{item.createGlobalId}, #{item.updateGlobalId},
      #{item.createTime}, #{item.updateTime}, #{item.productProType}, #{item.isExpression}, #{item.ordTrade},
      #{item.tradeId}, #{item.tradeName}, #{item.isSearchCondition},
      #{item.name}, #{item.typeCode}, #{item.option}, #{item.unit}, #{item.expressionCode}, #{item.isExpressionDefault},
      #{item.qyCodeOld}, #{item.qyFlag}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    expression_id = VALUES(expression_id),
    feature_level = VALUES(feature_level),
    is_sync = VALUES(is_sync),
    type = VALUES(type),
    project_type = VALUES(project_type),
    is_using = VALUES(is_using),
    is_default = VALUES(is_default),
    is_required = VALUES(is_required),
    remark = VALUES(remark),
    is_from_system = VALUES(is_from_system),
    zb_project_feature_id = VALUES(zb_project_feature_id),
    customer_code = VALUES(customer_code),
    is_deleted = VALUES(is_deleted),
    create_global_id = VALUES(create_global_id),
    update_global_id = VALUES(update_global_id),
    create_time = VALUES(create_time),
    update_time = VALUES(update_time),
    product_pro_type = VALUES(product_pro_type),
    is_expression = VALUES(is_expression),
    ord_trade = VALUES(ord_trade),
    trade_id = VALUES(trade_id),
    trade_name = VALUES(trade_name),
    is_search_condition = VALUES(is_search_condition),
    `name` = VALUES(`name`),
    type_code = VALUES(type_code),
    `option` = VALUES(`option`),
    unit = VALUES(unit),
    expression_code = VALUES(expression_code),
    is_expression_default = VALUES(is_expression_default),
    qy_code_old = VALUES(qy_code_old),
    qy_flag = VALUES(qy_flag)
  </insert>

  <update id="updateBatch" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_project_feature_standards
      <set>
          feature_level = #{item.featureLevel,jdbcType=VARCHAR},
          is_sync = #{item.isSync,jdbcType=BIT},
          type = #{item.type,jdbcType=INTEGER},
          project_type = #{item.projectType,jdbcType=VARCHAR},
          is_using = #{item.isUsing,jdbcType=BIT},
          is_default = #{item.isDefault,jdbcType=BIT},
          is_required = #{item.isRequired,jdbcType=BIT},
          remark = #{item.remark,jdbcType=VARCHAR},
          zb_project_feature_id = #{item.zbProjectFeatureId,jdbcType=BIGINT},
          is_from_system = #{item.isFromSystem,jdbcType=BIT},
          customer_code = #{item.customerCode,jdbcType=VARCHAR},
          is_deleted = #{item.isDeleted,jdbcType=BIT},
          create_global_id = #{item.createGlobalId,jdbcType=BIGINT},
          update_global_id = #{item.updateGlobalId,jdbcType=BIGINT},
          create_time = #{item.createTime,jdbcType=TIMESTAMP},
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
          product_pro_type = #{item.productProType,jdbcType=INTEGER},
          is_expression = #{item.isExpression,jdbcType=INTEGER},
          ord_trade = #{item.ordTrade,jdbcType=INTEGER},
          trade_id = #{item.tradeId,jdbcType=INTEGER},
          trade_name = #{item.tradeName,jdbcType=VARCHAR},
          is_search_condition = #{item.isSearchCondition,jdbcType=INTEGER},
          `name` = #{item.name,jdbcType=VARCHAR},
          type_code = #{item.typeCode,jdbcType=VARCHAR},
          `option` = #{item.option,jdbcType=VARCHAR},
          unit = #{item.unit,jdbcType=VARCHAR},
          expression_code = #{item.expressionCode,jdbcType=VARCHAR},
          is_expression_default = #{item.isExpressionDefault,jdbcType=INTEGER}
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectByCustomeCode" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from zb_project_feature_standards
      where customer_code = #{customerCode} and invalid = 0
      <if test="isShowDelete == null or isShowDelete == 0" >
          and is_deleted = 0
      </if>
      <if test="type != null" >
          and `type` = #{type,jdbcType=INTEGER}
      </if>
    </select>

    <select id="selectNoBlobByCustomerCode" resultMap="BaseResultMap">
        select
        <include refid="No_Blob_Column_List" />
        from zb_project_feature_standards
        where customer_code = #{customerCode} and invalid = 0
        <if test="isShowDelete == null or isShowDelete == 0" >
            and is_deleted = 0
        </if>
        <if test="type != null" >
            and `type` = #{type,jdbcType=INTEGER}
        </if>
    </select>

    <select id="selectUserTypesAndOrderByCustomerCode" parameterType="java.lang.String" resultType="map">
        select `type`+0 as `type`,max(ord_trade) as orderTrade from zb_project_feature_standards where customer_code = #{customerCode} and invalid = 0 group by `type` order by `type`;
    </select>

    <select id="selectAllCustomerCode" resultType="java.lang.String">
        select distinct customer_code from zb_project_feature_standards where is_deleted = 0 and invalid = 0;
    </select>

    <select id="selectAllCustomerCodes" resultType="java.lang.String">
        select distinct customer_code from zb_project_feature_standards
        where customer_code not in ('-100', 'null', 'test', '-100(old)', '-1', 'standardList_TC_0.data.gldUserId', '测试基础库初始化重复-可删除-企业1', '测试基础库初始化重复-可删除-企业2', 'defaultCustomerCode');
    </select>

    <update id="batchUpdateSelective" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update zb_project_feature_standards
            <set >
                <if test="item.expressionId != null" >
                    expression_id = #{item.expressionId,jdbcType=BIGINT},
                </if>
                <if test="item.featureLevel != null" >
                    feature_level = #{item.featureLevel,jdbcType=VARCHAR},
                </if>
                <if test="item.isSync != null" >
                    is_sync = #{item.isSync,jdbcType=BIT},
                </if>
                <if test="item.type != null" >
                    `type` = #{item.type,jdbcType=INTEGER},
                </if>
                <if test="item.projectType != null" >
                    project_type = #{item.projectType,jdbcType=VARCHAR},
                </if>
                <if test="item.isUsing != null" >
                    is_using = #{item.isUsing,jdbcType=BIT},
                </if>
                <if test="item.isDefault != null" >
                    is_default = #{item.isDefault,jdbcType=BIT},
                </if>
                <if test="item.isRequired != null" >
                    is_required = #{item.isRequired,jdbcType=BIT},
                </if>
                <if test="item.remark != null" >
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
                <if test="item.isFromSystem != null" >
                    is_from_system = #{item.isFromSystem,jdbcType=BIT},
                </if>
                <if test="item.zbProjectFeatureId != null" >
                    zb_project_feature_id = #{item.zbProjectFeatureId,jdbcType=BIGINT},
                </if>
                <if test="item.customerCode != null" >
                    customer_code = #{item.customerCode,jdbcType=VARCHAR},
                </if>
                <if test="item.isDeleted != null" >
                    is_deleted = #{item.isDeleted,jdbcType=BIT},
                </if>
                <if test="item.createGlobalId != null" >
                    create_global_id = #{item.createGlobalId,jdbcType=BIGINT},
                </if>
                <if test="item.updateGlobalId != null" >
                    update_global_id = #{item.updateGlobalId,jdbcType=BIGINT},
                </if>
                <if test="item.createTime != null" >
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateTime != null" >
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.productProType != null" >
                    product_pro_type = #{item.productProType,jdbcType=VARCHAR},
                </if>
                <if test="item.isExpression != null" >
                    is_expression = #{item.isExpression,jdbcType=INTEGER},
                </if>
                <if test="item.ordTrade != null" >
                    ord_trade = #{item.ordTrade,jdbcType=INTEGER},
                </if>
                <if test="item.tradeId != null" >
                    trade_id = #{item.tradeId,jdbcType=BIGINT},
                </if>
                <if test="item.isSearchCondition != null" >
                    is_search_condition = #{item.isSearchCondition,jdbcType=INTEGER},
                </if>
                <if test="item.tradeName != null" >
                    trade_name = #{item.tradeName,jdbcType=VARCHAR},
                </if>
                <if test="item.name != null" >
                    `name` = #{item.name,jdbcType=VARCHAR},
                </if>
                <if test="item.typeCode != null" >
                    type_code = #{item.typeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.option != null" >
                    `option` = #{item.option,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null" >
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.expressionCode != null" >
                    expression_code = #{item.expressionCode,jdbcType=VARCHAR},
                </if>
                <if test="item.isExpressionDefault != null" >
                    is_expression_default = #{item.isExpressionDefault,jdbcType=INTEGER},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>

    <select id="selectExpression" resultType="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
        select
        `name`, `unit`, update_time
        from zb_project_feature_standards
        where customer_code = #{customerCode}
        and invalid = 0
        and `type` = #{type,jdbcType=INTEGER}
        and type_code = "number"
        and is_expression = 1
        and is_deleted = 0
    </select>

    <select id="selectRepeat" resultMap="BaseResultMap">
        SELECT customer_code,`type`,trade_id,`name`,type_code,COUNT(1) AS ord_trade
        FROM zb_project_feature_standards
        where customer_code = #{customerCode} and invalid = 0
        GROUP BY customer_code,`type`,trade_id,`name`,type_code HAVING ord_trade > 1
    </select>


    <select id="selectDistinctCustomerCode" resultType="java.lang.String">
        SELECT distinct customer_code
        FROM zb_project_feature_standards WHERE customer_code != '-100'
    </select>

    <!--根据专业id查询工程特征信息，若专业id为空，则查询该企业下所有专业锁对应的工程特征信息-->
    <select id="selectTradeFeatureByCondition" resultType="com.glodon.qydata.entity.standard.feature.ProjectFeature">
        SELECT
        zpfs.id,
        zpfs.expression_id as expressionId,
        zpfs.`name`,
        zpfs.type_code as typeCode,
        zpfs.`option`,
        zpfs.`unit`,
        zpfs.feature_level as featureLevel,
        zpfs.is_sync as isSync,
        zpfs.project_type as projectType,
        zpfs.is_using as isUsing,
        zpfs.is_default as isDefault,
        zpfs.is_deleted as isDeleted,
        zpfs.is_required as isRequired,
        zpfs.remark as remark,
        zpfs.zb_project_feature_id as zbProjectFeatureId,
        zpfs.is_from_system as isFromSystem,
        zpfs.customer_code as customerCode,
        zpfs.create_global_id as createGlobalId,
        zpfs.update_global_id as updateGlobalId,
        zpfs.create_time as createTime,
        zpfs.update_time as updateTime,
        zpfs.product_pro_type as productProType,
        zpfs.is_expression as isExpression,
        zpfs.ord_trade as ordTrade,
        zpfs.trade_id as tradeId,
        zst.trade_code as tradeCode,
        zst.description as tradeName,
        zst.enabled as tradeEnabled,
        zst.ord as tradeOrder
        FROM zb_project_feature_standards zpfs
        LEFT JOIN zb_standards_trade zst ON zst.id = zpfs.trade_id
        WHERE zpfs.customer_code = #{customerCode}
        --  此条件实现当专业为有效数据时，查有效工程特征，当专业为无效数据时，查所有的工程特征 (invalid值为1为重复数据，2位找不到有效专业数据)
        and zst.invalid != 1 and  zpfs.invalid  != 1
        AND zpfs.type = #{type}
        AND zpfs.is_deleted = 0
        <if test="tradeIds != null and tradeIds.size() > 0">
            AND zpfs.trade_id in
            <foreach collection="tradeIds" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="names != null and names.size() > 0">
            AND zpfs.`name` in
            <foreach collection="names" item="item" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
    </select>

  <select id="selectTradeFeatureMaxOrd" resultType="java.lang.Integer">
      select max(ord_trade) from zb_project_feature_standards
      where customer_code = #{customerCode}
      and type = #{type}
      and trade_id = #{tradeId}
    </select>
</mapper>