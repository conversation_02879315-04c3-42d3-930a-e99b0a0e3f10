package com.glodon.qydata.controller.standard.trade;


import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.dto.StandardTradeDto;
import com.glodon.qydata.dto.TradeDownOrUpDTO;
import com.glodon.qydata.dto.TradeEnableDTO;
import com.glodon.qydata.entity.standard.trade.TradeTagEnum;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.standard.trade.IStandardsTradeService;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.trade.StandardReferTradeVO;
import com.glodon.qydata.vo.standard.trade.StandardTradeVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
　　* @description: 标准打通--工程专业控制层
　　* <AUTHOR>
　　* @date 2021/10/21 19:27
　　*/
@RestController
@RequestMapping("/basicInfo/standards")
@Slf4j
@Tag(name = "工程专业相关接口类", description = "工程专业相关接口类")
public class StandardTradeController extends BaseController {
    @Autowired
    private IStandardsTradeService tradeService;
    /**
     　　* @description: 根据企业编码查看待选专业
     　　* @param
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "根据企业编码查看待选专业")
    @GetMapping("/getTradeReferList")
    public ResponseVo<StandardReferTradeVO> getTradeReferList() throws BusinessException{
        String customerCode = getCustomerCode();
        List<StandardReferTradeVO> returnList = tradeService.getReferListByCustomerCode(customerCode);
        return new ResponseVo(ResponseCode.SUCCESS,returnList);
    }

    /**
     　　* @description: 根据企业编码查看企业下专业列表
     　　* @param
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "根据企业编码查看企业下专业列表")
    @GetMapping("/getTradeList")
    public ResponseVo<StandardTradeVO> getTradeList(@RequestParam(value = "needTag", required = false, defaultValue = "false") Boolean needTag) throws BusinessException{
        String customerCode = getCustomerCode();
        List<StandardTradeVO> returnList = tradeService.getTradeVoList(customerCode);

        // 调整为带标签的结构
        returnList = tradeService.convertToTradeTree(customerCode, returnList, needTag);

        return new ResponseVo(ResponseCode.SUCCESS, returnList);
    }

    /**
     　　* @description: 新增专业信息
     　　* @param StandardTradeBo 新增专业数据载体
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "新增专业信息")
    @Permission
    @PostMapping("/insertTrade")
    public ResponseVo<ZbStandardsTrade> insertTrade(@RequestBody @Validated StandardTradeDto standardTradeDto) throws BusinessException{
        Long globalId = getGlobalIdLong();
        String customerCode = getCustomerCode();
        ZbStandardsTrade zbStandardsTrade = tradeService.insertTrade(standardTradeDto, customerCode, globalId);
        return new ResponseVo(ResponseCode.SUCCESS,zbStandardsTrade);
    }

    /**
     　　* @description: 更新专业信息
     　　* @param StandardTradeBo 新增专业数据载体
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "更新专业信息")
    @Permission
    @PutMapping("/updateTrade")
    public ResponseVo<ZbStandardsTrade> updateTrade(@RequestBody @Validated StandardTradeDto standardTradeDto) throws BusinessException{

        Long globalId = getGlobalIdLong();
        String customerCode = getCustomerCode();
        ZbStandardsTrade zbStandardsTrade = tradeService.updateTrade(standardTradeDto, customerCode, globalId);
        return new ResponseVo(ResponseCode.SUCCESS,zbStandardsTrade);
    }

    @Operation(summary = "获取专业分类标签")
    @Permission
    @GetMapping("/getTradeTagList")
    public ResponseVo<TradeTagEnum> getTradeTagList() {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        List<TradeTagEnum> tradeTagEnums = tradeService.getTradeOptions(customerCode);
        return new ResponseVo(ResponseCode.SUCCESS, tradeTagEnums);
    }

    @Operation(summary = "保存专业分类标签字典")
    @Permission
    @PostMapping("/saveTradeTags")
    public ResponseVo<String> modifyCategoryTagEnum(@RequestBody List<TradeTagEnum> tradeTagEnums) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        tradeService.saveTradeTag(customerCode, tradeTagEnums);
        return ResponseVo.success("编辑专业分类标签成功");
    }

    /**
     　　* @description: 新增修改专业-名称判重
     　　* @param id:修改接口的名称判重，id会存在，排除此id专业的名称再进行判重
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 20:36
     　　*/
    @Operation(summary = "新增修改专业-名称判重")
    @GetMapping("/checkName")
    public ResponseVo checkedName (@RequestParam("name") String name,@RequestParam( name = "id", required = false) Long id) {
        String customerCode = getCustomerCode();
        return ResponseVo.success(tradeService.checkedName(customerCode, name, id));
    }

    /**
     　　* @description: 删除指定专业
     　　* @param id:待删除专业id
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 20:36
     　　*/
    @Operation(summary = "删除指定专业")
    @Permission
    @DeleteMapping("/delete/{id}")
    public ResponseVo checkedName (@PathVariable("id") Long id) throws BusinessException {
        Long globalId = getGlobalIdLong();
        String customerCode = getCustomerCode();
        tradeService.deleteTrade(customerCode, id, globalId);
        return ResponseVo.success();
    }

    @Operation(summary = "上移下移指定专业")
    @Permission
    @PostMapping("/tradeObject/downOrUp")
    public ResponseVo downOrUp(@Valid @RequestBody TradeDownOrUpDTO tradeDownOrUpDTO) throws BusinessException {
        String customerCode = getCustomerCode();
        String globalId = getGlobalId();
        tradeService.downOrUpTrade(customerCode, globalId, tradeDownOrUpDTO);
        return ResponseVo.success();
    }

    @Operation(summary = "批量更新启用状态")
    @Permission
    @PostMapping("/tradeObject/modify")
    public ResponseVo changeEnable(@Valid @RequestBody List<TradeEnableDTO> tradeEnableDTOS) throws BusinessException {
        String customerCode = getCustomerCode();
        String globalId = getGlobalId();
        tradeService.changeEnable(customerCode, globalId, tradeEnableDTOS);
        return ResponseVo.success();
    }
}
