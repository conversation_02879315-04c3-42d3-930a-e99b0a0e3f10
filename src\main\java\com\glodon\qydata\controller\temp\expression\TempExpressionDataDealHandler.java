package com.glodon.qydata.controller.temp.expression;

import com.glodon.qydata.common.constant.CategoryTypeConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ExpressionTypeEnum;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.controller.temp.TempConstant;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionSelfMapper;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 刷计算口径数据  todo 线上执行后，可直接删除掉
 * <AUTHOR>
 * @date 2022/7/22 16:28
 */
@Service
@Slf4j
public class TempExpressionDataDealHandler {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private TempExpressionDealService tempExpressionDealService;
    @Autowired
    private TempExpressionDealSelfService tempExpressionDealSelfService;
    @Autowired
    private ZbStandardsExpressionSelfMapper standardsExpressionSelfMapper;

    final ThreadPoolExecutor dealExecutor = new ThreadPoolExecutor(5, 10, 3000, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());

    /**
     * @description: 处理原先的内置数据，执行前把所有数据都改成不是内置计算口径 UPDATE zb_standards_expression_copy SET expression_is_from_system=0 WHERE qy_code != "systemData_0722"
     * @param
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/7/24 15:08
     */
    public String systemExpressionDataDeal() {
        String lockValueString = redisUtil.generateLockValue();

        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.TEMP_EXPRESSION_DATA_DEAL, TempConstant.TYPE_SYSTEM)) {
            return "不要重复调用...";
        }

        try {
            // 产品提供的内置数据
            Map<String, TempExpression> excelData = tempExpressionDealService.getExcelData();

            if (excelData == null){
                return "产品提供的内置数据为空...";
            }

            // 查询老的数字类型的内置数据
            List<TempExpression> oldSystemData = tempExpressionDealService.getNumberExpression(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE);

            if (oldSystemData == null){
                return "老的内置数据为空...";
            }

            this.allDataDeal(excelData, oldSystemData, Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE);
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.TEMP_EXPRESSION_DATA_DEAL, TempConstant.TYPE_SYSTEM);
        }

        log.info("内置数据处理完成...");
        return "内置数据处理完成...";
    }

    /**
     * @param targetData 系统内置的数据
     * @param sourceData 企业的数据
     * @param customerCode
     */
    private void allDataDeal(Map<String, TempExpression> targetData, List<TempExpression> sourceData, String customerCode){
        // 需要更新的数据
        List<TempExpression> updateList = new ArrayList<>();
        // 需要新增的数据
        List<TempExpression> insertList = new ArrayList<>();

        // 按类型分组
        Map<Integer, List<TempExpression>> sourceTypeGroup = sourceData.stream().collect(Collectors.groupingBy(TempExpression::getType));

        for (Map.Entry<Integer, List<TempExpression>> entry : sourceTypeGroup.entrySet()) {
            // 按类型处理
            tempExpressionDealService.dataDeal(entry.getKey(), targetData, entry.getValue(), updateList, insertList);
        }

        // 入库
        tempExpressionDealService.executeSql(updateList, insertList, customerCode);
    }

    public String expressionDataDeal() {
        String lockValueString = redisUtil.generateLockValue();

        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.TEMP_EXPRESSION_DATA_DEAL, TempConstant.TYPE_ENTERPRISE)) {
            return "不要重复调用...";
        }

        try {
            // 查询处理过的内置计算口径
            Map<String, TempExpression> systemExpression = tempExpressionDealService.getSystemExpression();


            // 获取需要处理的企业
            List<String> allCustomerCodeList = tempExpressionDealService.getAllCustomerCodeList(systemExpression.size());

            if (CollectionUtils.isEmpty(allCustomerCodeList)){
                return "没有需要处理的企业...";
            }

            log.info("需要处理的企业数：{}", allCustomerCodeList.size());

            this.threadExecutor(allCustomerCodeList, systemExpression);
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.TEMP_EXPRESSION_DATA_DEAL, TempConstant.TYPE_ENTERPRISE);
        }

        log.info("全部处理完成...");
        return "全部处理完成...";
    }

    private void threadExecutor(List<String> allCustomerCodeList, Map<String, TempExpression> systemExpression){
        //线程监控
        final CountDownLatch latch = new CountDownLatch(allCustomerCodeList.size());

        // 分企业执行
        for (String customerCode : allCustomerCodeList) {
            dealExecutor.execute(() ->{
                try {
                    this.deal(customerCode, systemExpression);
                }catch (Exception e){
                    log.error("企业：{}-出错了", customerCode, e);
                }finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("latch.await出错了", e);
        }
    }

    private void deal(String customerCode, Map<String, TempExpression> systemExpression){
        // 获取企业下所有数字类型的计算口径数据
        List<TempExpression> numberExpression = tempExpressionDealService.getNumberExpression(customerCode);

        this.allDataDeal(systemExpression, numberExpression, customerCode);
    }


    public String expressionDataDealSelf() {
        String lockValueString = redisUtil.generateLockValue();

        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.TEMP_EXPRESSION_DATA_DEAL, TempConstant.TYPE_SELF)) {
            return "不要重复调用...";
        }

        try {
            // 查询处理过的内置计算口径
            Map<String, TempExpression> systemExpression = tempExpressionDealService.getSystemExpression();

            // 获取需要处理的用户
            List<TempExpressionSelf> allGlobalIdList = tempExpressionDealSelfService.getAllGlobalIdList(systemExpression.size());


            this.threadExecutorSelf(allGlobalIdList);
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.TEMP_EXPRESSION_DATA_DEAL, TempConstant.TYPE_SELF);
        }

        log.info("全部处理完成...");
        return "全部处理完成...";
    }

    private void threadExecutorSelf(List<TempExpressionSelf> allGlobalIdList){
        //线程监控
        final CountDownLatch latch = new CountDownLatch(allGlobalIdList.size());
        // 分企业执行
        for (TempExpressionSelf expressionSelf : allGlobalIdList) {
            dealExecutor.execute(() ->{
                try {
                    this.dealSelf(expressionSelf.getSelfGlobalId(), expressionSelf.getQyCode());
                }catch (Exception e){
                    log.error("暂存：{}-出错了", expressionSelf.getSelfGlobalId(), e);
                }finally {
                    latch.countDown();
                }
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            log.error("latch.await出错了", e);
        }
    }

    private void dealSelf(Long selfGlobalId, String customerCode){
        Map<Integer, List<TempExpression>> targetTypeGroup =  tempExpressionDealService.getTypeGroup(customerCode);

        // 获取数字类型的数据
        Map<Integer, List<TempExpressionSelf>> sourceTypeGroup = tempExpressionDealSelfService.getNumberExpression(selfGlobalId);

        if (targetTypeGroup != null && sourceTypeGroup != null){
            this.allDataDealSelf(selfGlobalId, targetTypeGroup, sourceTypeGroup, customerCode);
        }
    }

    private void allDataDealSelf(Long selfGlobalId, Map<Integer, List<TempExpression>> targetTypeGroup, Map<Integer, List<TempExpressionSelf>> sourceTypeGroup, String customerCode){
        // 需要更新的数据
        List<TempExpressionSelf> updateList = new ArrayList<>();
        // 需要新增的数据
        List<TempExpressionSelf> insertList = new ArrayList<>();

        for (Map.Entry<Integer, List<TempExpressionSelf>> entry : sourceTypeGroup.entrySet()) {
            Integer type = entry.getKey();
            List<TempExpression> expressionList = targetTypeGroup.get(entry.getKey());

            if (CollectionUtils.isEmpty(expressionList)){
                continue;
            }

            Map<String, TempExpression> targetData = expressionList.parallelStream().collect(Collectors.toMap(TempExpression::getName, Function.identity(), (v1, v2) -> v2));

            // 按类型处理
            tempExpressionDealSelfService.dataDeal(type, targetData, entry.getValue(), updateList, insertList);
        }

        // 新增实体构建
        this.buildExpressionSelf(insertList, selfGlobalId, customerCode);

        // 入库
        tempExpressionDealSelfService.executeSql(updateList, insertList);
    }

    private void buildExpressionSelf(List<TempExpressionSelf> insertList, Long selfGlobalId, String customerCode){
        int size = insertList.size();
        List<Long> nextId = SnowflakeIdUtils.getNextId(size * 2);

        for (int i = 0; i < insertList.size(); i++) {
            TempExpressionSelf insertExpression = insertList.get(i);
            Long originId = insertExpression.getId();
            insertExpression.setId(nextId.get(i));
            insertExpression.setExpressionCode(insertExpression.getExpressionCode());
            insertExpression.setQyCode(customerCode);
            insertExpression.setCreateGlobalId(-100L);
            insertExpression.setIsExpression(Constants.ZbFeatureConstants.WHETHER_TRUE);
            insertExpression.setTypeCode(ExpressionTypeEnum.TYPE_NUMBER.getCode());
            insertExpression.setExpressionIsUsing(Constants.ZbFeatureConstants.WHETHER_TRUE);
            insertExpression.setExpressionCreateGlobalId(-100L);
            insertExpression.setSelfGlobalId(selfGlobalId);
            insertExpression.setOriginId(originId);
        }
    }

    /**
     * 对暂存的计算口径排序：内置的在最前面
     * @return
     * <AUTHOR>
     */
    public void reOrderExpressionSelf() {
        log.info("对暂存的计算口径排序开始...");
        LocalDateTime begin = LocalDateTime.now();
        List<Map> customerCodeGlobalIds = standardsExpressionSelfMapper.selectAllCustomerCodeGlobalIds();
        if (CollectionUtils.isEmpty(customerCodeGlobalIds)) return;

        CategoryTypeConstants.categoryTypeList.forEach(type -> {
            customerCodeGlobalIds.forEach(map -> {
                String customerCode = (String) map.get("qy_code");
                Long globalId = (Long) map.get("self_global_id");
                List<ZbStandardsExpression> selfExpressionList = standardsExpressionSelfMapper.selectSelfExpression(customerCode, globalId, type);
                if (CollectionUtils.isEmpty(selfExpressionList)) return;

                //把数据拆成系统内置 和用户自定义
                List<ZbStandardsExpression> systemExpressionList = new ArrayList<>();
                List<ZbStandardsExpression> customExpressionList = new ArrayList<>();
                selfExpressionList.forEach(expression -> {
                    if (Constants.ZbFeatureConstants.WHETHER_TRUE == expression.getExpressionIsFromSystem()) { //系统内置
                        systemExpressionList.add(expression);
                    } else { //用户自定义
                        customExpressionList.add(expression);
                    }
                });

                //合并List，把自定义的放在后面
                if (CollectionUtils.isEmpty(customExpressionList)) {
                    return;
                }
                systemExpressionList.addAll(customExpressionList);

                int order = 1;

                if (CollectionUtils.isEmpty(systemExpressionList)) {
                    return;
                }
                for (ZbStandardsExpression expression : systemExpressionList) {
                    ZbStandardsExpression updateExpression = new ZbStandardsExpression();
                    updateExpression.setId(expression.getId());
                    updateExpression.setExpressionOrd(order);
                    order++;
                    standardsExpressionSelfMapper.updateSelective(updateExpression);
                }
            });
        });
        LocalDateTime end = LocalDateTime.now();
        log.info("对暂存的计算口径排序成功，耗时={}ms", Duration.between(begin, end).toMillis());
    }

}
