package com.glodon.qydata.mapper.standard.projectOrContractInfo;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * zb_standards_unit - 主键 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Repository
public interface StandardsUnitMapper extends BaseMapper<StandardsUnitEntity> {

    List<StandardsUnitEntity> selectBuiltInData();

    List<StandardsUnitEntity> selectBuiltInDataContract();

    /**
    　　* @description: 根据企业编码，查询所有单位信息
    　　* @param  customerCode
    　　* @return  List<StandardsUnitEntity>
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/10 16:06
    　　*/
    List<StandardsUnitEntity> selectByCustomerCode(String customerCode);

    /**
     　　* @description: 删除指定企业下所有单位信息
     　　* @param  customerCode
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/10 16:06
     　　*/
    void deleteByCustomerCode(String customerCode);
}
