package com.glodon.qydata.controller.temp.expression;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: zb_standards_expression 刷计算口径数据  todo 线上执行后，可直接删除掉
 * <AUTHOR>
 * @date 2021/11/2 8:49
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TableName("zb_standards_expression")
public class TempExpression implements Serializable {

    private static final long serialVersionUID = 4189034123798733891L;

    private Long id;

    /**
     * （历史遗留）口径编码(可作为口径key,同时可固化编码进行数据处理),采用分布式生成器生成
     */
    private String expressionCode;

    /**
     * （历史遗留）计算口径值得类型（0:浮点，1:整数）
     */
    private Integer dataType;

    /**
     * （历史遗留）规则设计，预留字段
     */
    private String rule;

    /**
     * （历史遗留）
     */
    private Integer status;

    /**
     * （历史遗留）口径范围(1,个人，2，企业，0.系统)
     */
    private Integer scope;

    /**
     * 企业编号
     */
    private String qyCode;

    /**
     * 是否使用
     */
    private Integer isUsable;

    /**
     * 是否删除：1已删除；0未删除
     */
    private Integer isDeleted;

    /**
     * 工程特征名称
     */
    private String name;

    /**
     * 数据类型:值为dictionary表中type_code字段等于data_type所对应的code字段的值
     */
    private String typeCode;

    /**
     * 数据类别
     */
    private Integer type;

    /**
     * 是否计算口径：1是；0否（数值型，支持设置是否计算口径）
     */
    private Integer isExpression;

    /**
     * 单位
     */
    private String unit;

    /**
     * 计算口径排序
     */
    private Integer expressionOrd;

    /**
     * 计算口径是否启用:1启用；0未启用；
     */
    private Integer expressionIsUsing;

    /**
     * 计算口径是否系统内置：1 是； 0 否
     */
    private Integer expressionIsFromSystem;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建用户ID
     */
    private Long createGlobalId;

    /**
     * 更新用户ID
     */
    private Long updateGlobalId;

    /**
     * 计算口径创建时间
     */
    private Date expressionCreateTime;

    /**
     * 计算口径创建用户ID
     */
    private Long expressionCreateGlobalId;

    /**
     * 计算口径备注
     */
    private String expressionRemark;

}