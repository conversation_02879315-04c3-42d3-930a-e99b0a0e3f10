package com.glodon.qydata.controller.repairdata.version3;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.controller.repairdata.common.BaseRepairDataHandler;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.repairdata.TempProjectCategoryMapper;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryMapper;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description: 修复工程分类重复数据
 * @date 2023/4/21 14:14
 */
@Service
@Slf4j
public class RepairProjectCategoryErroHandler extends BaseRepairDataHandler<StandardsProjectInfoEntity> {
    @Resource
    CommonProjCategoryMapper commonProjCategoryMapper;
    @Resource
    TempProjectCategoryMapper tempProjectCategoryMapper;
    @Autowired
    private CommonProjectCategoryUsedService projectCategoryUsedService;

    HashMap<Integer, Integer> typeCountMap = new HashMap<>();
    Integer getSystemCount(int type) {
        return typeCountMap.computeIfAbsent(type, key -> commonProjCategoryMapper.selectAll(
                Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, type, Constants.CategoryConstants.WHETHER_TRUE).size());
    }
    /**
     * 检查数据是否需要修复
     * @param customerCode
     * @return 是否需要修复
     */
    @Override
    public boolean isNeedRepair(String customerCode){
        if(tempProjectCategoryMapper.selectNeedTrimCategory(customerCode) != null) {
            return true;
        }
        List<Integer> typeList = getUerTypeList(customerCode);
        return tempProjectCategoryMapper.selectInvalidCategory(customerCode, typeList) != null;
    }

    @Override
    public boolean repairData(String customerCode) {
        // 获取当前用户类型
        List<Integer> typeList = getUerTypeList(customerCode);
        // 依次处理三个类型的数据
        Arrays.asList(1, 2, 3).forEach(type -> {
            List<CommonProjCategory> categoryList = commonProjCategoryMapper.selectAll(customerCode, type, Constants.CategoryConstants.WHETHER_TRUE);
            if(categoryList == null || categoryList.size()  == 0) {
                return;
            }
            // 修复名称首尾多空格问题
            categoryList.forEach(categoryEntity -> {
                if(categoryEntity.getCategoryname().trim().equals(categoryEntity.getCategoryname())) {
                    return;
                }
                if(!categoryEntity.getCategoryname().equals("中型/小型百货 ")) {
                    throw new BusinessException("工程分类 多余空格不是中型/小型百货");
                }
                categoryEntity.setCategoryname(categoryEntity.getCategoryname().trim());
                tempProjectCategoryMapper.updateCategory(categoryEntity);
                tempProjectCategoryMapper.syncSelfCategory(categoryEntity);
            });
            if(!typeList.contains(type)) {
                // 如果和系统数据不一致不执行修复
                if(!getSystemCount(type).equals(categoryList.size())) {
                    throw new BusinessException("无效工程分类与系统默认不一致");
                }
                // 刷数据导致多余的数据直接标记为无效
                List<Integer> repeatCategoryIds = categoryList.stream().map(CommonProjCategory::getId).collect(Collectors.toList());
                tempProjectCategoryMapper.setCategoryInvalid(customerCode, repeatCategoryIds);
                tempProjectCategoryMapper.setSelfCategoryInvalid(customerCode, repeatCategoryIds);
            }
        });
        return false;
    }
    List<Integer> getUerTypeList(String customerCode){
        Integer useType = projectCategoryUsedService.getUsedCategoryType(customerCode);
        List<Integer> typeList = new ArrayList<>();
        typeList.add(1);
        if(!useType.equals(1)) {
            typeList.add(useType);
        }
        return typeList;
    }
    @Override
    public boolean checkDataAfterRepair(String customerCode) {
        if(isNeedRepair(customerCode)) {
            throw new BusinessException("工程分类修复结果不及预期");
        }
        return false;
    }
    public List<String> selectAllCustomerCode() {
        return tempProjectCategoryMapper.selectAllCustomerCode();
    }
}
