package com.glodon.qydata.insertprojectinfo.mapper;

import com.glodon.qydata.controller.temp.projectinfo.entity.Prams;
import com.glodon.qydata.insertprojectinfo.entity.Global;
import com.glodon.qydata.insertprojectinfo.entity.StandardsProjectInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * glodon
 */
@Mapper
public interface ProjectInfoMapper {
    List<Global> searchUsersWithInvalidProjectInfo();
    List<String> searchCustomerCodeWithInvalidProjectInfo();
    void deleteSelfProjectInfoRecords(List<Global> users);
    void deleteProjectInfoRecords(List<String> customerCodes);
    int queryProjectNameCount(String customerCode);
    int querySelfProjectNameCount(String customerCode, String globalId);

    List<String> searchCustomerCodeFromProjectInfo();

    List<Global> searchCustomerCodeAndGlobalIdFromProjectInfoSelf();

    int projectInfoInsert(@Param("projectInfoEntities") List<StandardsProjectInfoEntity> projectInfoEntities);

    int projectInfoSelfInsert(@Param("projectInfoEntities") List<StandardsProjectInfoEntity> projectInfoEntities);

    List<String> searchConflictCustomerCode();
    List<Global> searchConflictCustomerCodeAndGlobalId();
    int searchMaxOrdFromProjectInfo();
    int searchMaxOrdFromProjectInfoSelf();
    List<StandardsProjectInfoEntity> searchConflictInfo(@Param("tableName") String tableName);

    void updateInfo(@Param("updateList") List<StandardsProjectInfoEntity> updateList, @Param("tableName") String tableName);

    List<String> selectGlobalIdsByCustomerCode(@Param("customerCode") String customerCode);

    List<Prams> selectCustomerByCondition(@Param("standardDataType") Integer standardDataType, @Param("infoNameList") List<String> infoNameList, @Param("customerCodeList") List<String> customerCodeList);

    List<Prams> selectSelfCustomerByCondition(@Param("standardDataType") Integer standardDataType, @Param("infoNameList") List<String> infoNameList, @Param("customerCodeList") List<String> customerCodeList);

    void updateOriginIdOfSelf(@Param("updateList") List<StandardsProjectInfoEntity> updateList);

    List<StandardsProjectInfoEntity> selectByCondition(@Param("standardDataType") Integer standardDataType, @Param("customerCodeList") List<String> customerCodeList, @Param("infoNameList") List<String> infoNameList);

}
