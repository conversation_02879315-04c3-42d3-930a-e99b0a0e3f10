package com.glodon.qydata.util;


import java.sql.Timestamp;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.TimeZone;

/**
 * <b>类名</b>：DateUtils.java<br>
 * <p>
 * <b>标题</b>：广联达-工程信息事业本部-研发中心
 * </p>
 * <p>
 * <b>描述</b>： 日期工具类
 * </p>
 * <p>
 * <b>版权声明</b>：Copyright (c) 2014
 * </p>
 * <p>
 * <b>公司</b>：广联达科技股份有限公司
 * </p>
 *
 * <AUTHOR> color='blue'>hanyh-a</font>
 * @version 1.0
 * @date 2014-6-11 下午1:42:52
 */
public class DateUtils {

	public static final String DATE_FMT_1 = "yyyy-MM-dd";
	public static final String DATE_FMT_2 = "yyyyMM";
	public static final String DATE_FMT_Y_M = "yyyy年MM月";
	public static final String DATE_FMT_Y_M_D = "yyyy年MM月dd日";
	public static final String DATE_FMT_Y_M_D_H_M_S = "yyyy-MM-dd HH:mm:ss";
	public static final String DATE_FMT_Y_M_D_H_M = "yyyy-MM-dd HH:mm";
	// ORA标准时间格式
	private static final SimpleDateFormat ORA_DATE_TIME_FORMAT = new SimpleDateFormat("yyyyMMdd");
	// 带时分秒的ORA标准时间格式
	private static final SimpleDateFormat ORA_DATE_TIME_EXTENDED_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");
	/**
	 * 当前时间的毫秒数
	 */
	static long now = System.currentTimeMillis();
	/**
	 * 当前日期
	 */
	public static Date CurrTime = new Date(now);

	/**
	 * @param date
	 *            日期
	 * @return 带格式的字符串
	 * @description 将日期转化为字符串, 字符串格式("YYYY-MM-DD")，小时、分、秒被忽略
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static String DateToStringEndDay(Date date) {
		SimpleDateFormat formater = new SimpleDateFormat(DATE_FMT_1);
		String strDateTime = formater.format(date);
		return strDateTime;
	}

	/**
	 * @param date
	 *            日期
	 * @return 带格式的字符串
	 * @description 将日期转化为字符串, 字符串格式("YYYY年-mm月-dd日")，小时、分、秒被忽略
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static String DateToStringText(Date date) {
		SimpleDateFormat formater = new SimpleDateFormat(DATE_FMT_Y_M_D);
		String strDateTime = formater.format(date);
		return strDateTime;
	}

	/**
	 * @param date
	 *            日期
	 * @param pattern
	 *            日期格式
	 * @return String 类型
	 * @description 将日期转化为字符串, 字符串格式自定义
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static String DateToString(Date date, String pattern){
		String strDateTime = null;
		try {
			SimpleDateFormat formater = new SimpleDateFormat(pattern);
			strDateTime = formater.format(date);
		} catch (Exception ex) {
		    ex.printStackTrace();
		}
		return strDateTime;
	}

	/**
	 * @param year
	 *            年
	 * @param month
	 *            月
	 * @param day
	 *            日
	 * @return Date类型
	 * @description 将传入的年月日转化为Date类型
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static Date YmdToDate(int year, int month, int day) {
		Calendar calendar = Calendar.getInstance();
		calendar.set(year, month, day);
		return calendar.getTime();
	}

	/**
	 * @param date
	 *            日期
	 * @return 字符串格式("MM/dd HH:mm:ss")
	 * @description 将日期转化为字符串
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static String communityDateToString(Date date) {
		SimpleDateFormat formater = new SimpleDateFormat("MM/dd HH:mm:ss");
		String strDateTime = formater.format(date);
		return strDateTime;
	}

	/**
	 * @param str
	 *            日期格式的字符串
	 * @param pattern
	 *            日期格式
	 * @param type
	 *            类型：周日为一周开始 为 0，周一为一周的开始为1
	 * @return 数组：某一天的所在周的第一天和最后一天（周一（） 周日()）和这一天所在的为这一年的第几周
	 * @description 得到某一天的所在周的第一天和最后一天（周一（） 周日()）和这一天所在的为这一年的第几周
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static String[] getWeekParams(String str, String pattern, int type) {
		String[] three = new String[3];
		if (pattern == null || pattern.equals("")) {
			pattern = DATE_FMT_Y_M_D_H_M_S;
		}

		SimpleDateFormat formater = null;
		try {
			formater = new SimpleDateFormat(pattern);
		} catch (Exception e) {
			formater = new SimpleDateFormat(DATE_FMT_Y_M_D_H_M_S);
		}
		Date date = new Date();
		try {
			date = formater.parse(str);
		} catch (ParseException e) {
			System.out.println("Parse date Error in DateUtils");
		}
		Calendar c = Calendar.getInstance();
		c.setFirstDayOfWeek(type + 1);
		c.setTime(date);
		c.get(Calendar.WEEK_OF_YEAR);
		c.set(Calendar.DAY_OF_WEEK, 2);
		three[0] = formater.format(c.getTime());
		c.set(Calendar.DAY_OF_WEEK, 8);
		three[1] = formater.format(c.getTime());
		three[2] = String.valueOf(c.get(Calendar.WEEK_OF_YEAR));
		return three;
	}

	/**
	 * @param str
	 *            字符串格式("yyyy-MM-dd HH:mm")。
	 * @description 将字符串转化为日期。
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static Date StringToDateEndMonth(String str) {
		Date dateTime = null;
		try {
			if (!(str == null || str.equals(""))) {
				SimpleDateFormat formater = new SimpleDateFormat(DATE_FMT_Y_M_D_H_M);
				dateTime = formater.parse(str);
			}
		} catch (Exception ex) {
			System.out.println(ex.toString());
		}
		return dateTime;
	}

	/**
	 * @param str
	 *            日期字符串 必须符合 格式 例如2010-09-02 11:12:12.022111111
	 * @return Timestamp 时间戳
	 * @description 日期时间带时分秒的Timestamp表示,
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static Timestamp StringToDateHMS(String str) throws Exception {
		Timestamp time = null;
		try {
			time = Timestamp.valueOf(str);
		} catch (Exception ex) {

			System.out.print(ex.toString());
		}

		return time;

	}

	/**
	 * @param date
	 *            一个日期
	 * @return Date对象。
	 * @description 得一个date对象对应的日期的0点0分0秒时刻的Date对象。
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static Date getMinDateOfDay(Date date) {
		if (date == null) {
			return null;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMinimum(Calendar.HOUR_OF_DAY));
		calendar.set(Calendar.MINUTE, calendar.getActualMinimum(Calendar.MINUTE));
		calendar.set(Calendar.SECOND, calendar.getActualMinimum(Calendar.SECOND));
		calendar.set(Calendar.MILLISECOND, calendar.getActualMinimum(Calendar.MILLISECOND));

		return calendar.getTime();
	}

	/**
	 * @param date
	 *            一个日期
	 * @return Date对象。
	 * @description 取得一个date对象对应的日期的23点59分59秒时刻的Date对象。
	 * @date 2010-8-4
	 * <AUTHOR>
	 */
	public static Date getMaxDateOfDay(Date date) {
		if (date == null) {
			return null;
		}
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		calendar.set(Calendar.HOUR_OF_DAY, calendar.getActualMaximum(Calendar.HOUR_OF_DAY));
		calendar.set(Calendar.MINUTE, calendar.getActualMaximum(Calendar.MINUTE));
		calendar.set(Calendar.SECOND, calendar.getActualMaximum(Calendar.SECOND));
		calendar.set(Calendar.MILLISECOND, calendar.getActualMaximum(Calendar.MILLISECOND));

		return calendar.getTime();
	}

	/**
	 * @param dateString
	 *            日期字符串
	 * @param dataFormat
	 *            日期格式
	 * @return 转换后的日期
	 * @description 字符串按照格式转化日期
	 * @date 2010-7-13
	 * <AUTHOR>
	 */
	public static Date parseDate(String dateString, String dataFormat) {
		if (dataFormat == null) {
			dataFormat = DATE_FMT_1;
		}
		SimpleDateFormat fordate = new SimpleDateFormat(dataFormat);
		if (dateString == null || dateString.equals("")) {
			return null;
		}
		try {

			return fordate.parse(dateString);
		} catch (ParseException e) {
			System.out.println(e.getMessage());
		}
		return null;
	}

	/**
	 * @param dateString
	 *            日期字符串
	 * @return 转换后的日期
	 * @description 字符串按照格式转化java.sql.Date 默认格式 "yyyy-MM-dd"
	 * @date 2011-2-16
	 * <AUTHOR>
	 */
	public static java.sql.Date parseSQLDate(String dateString) {
		SimpleDateFormat fordate = new SimpleDateFormat(DATE_FMT_1);
		if (dateString == null || dateString.equals("")) {
			return null;
		}
		try {
			Date d = fordate.parse(dateString);
			return new java.sql.Date(d.getTime());
		} catch (ParseException e) {
			System.out.println(e.getMessage());
		}
		return null;
	}

	/**
	 * @param time1
	 *            要累计的时间字符串
	 * @param time2
	 *            要累计的时间字符串
	 * @return 累计后的时间字符串
	 * @description 将两个格式为HH:MM:SS的时间字符串相加，例如：00:59:06 + 01:00:59 返回 02:00:05。
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static String addTwoTimeStr(String time1, String time2) {

		String returnStr = "00:00:00";
		if (time1 != null && !time1.equalsIgnoreCase("") && time2 != null && !time2.equalsIgnoreCase("")) {
			String[] time1Array = time1.split(":");
			String[] time2Array = time2.split(":");
			int hour1 = Integer.parseInt(time1Array[0]);
			int hour2 = Integer.parseInt(time2Array[0]);
			int min1 = Integer.parseInt(time1Array[1]);
			int min2 = Integer.parseInt(time2Array[1]);
			int sec1 = Integer.parseInt(time1Array[2]);
			int sec2 = Integer.parseInt(time2Array[2]);

			String lastSec, lastMin, lastHour;

			int totalSec = sec1 + sec2;
			if (totalSec / 60 > 0) {
				min1 = min1 + totalSec / 60;
			}
			if (totalSec % 60 > 9) {
				lastSec = Integer.toString(totalSec % 60);
			} else {
				lastSec = "0" + totalSec % 60;
			}

			int totalMin = min1 + min2;
			if (totalMin / 60 > 0) {
				hour1 = hour1 + totalMin / 60;
			}
			if (totalMin % 60 > 9) {
				lastMin = Integer.toString(totalMin % 60);
			} else {
				lastMin = "0" + totalMin % 60;
			}

			int totalHour = hour1 + hour2;
			if (totalHour % 24 > 9) {
				lastHour = Integer.toString(totalHour % 24);
			} else {
				lastHour = "0" + totalHour % 24;
			}

			returnStr = lastHour + ":" + lastMin + ":" + lastSec;
		} else if (time1 != null && !time1.equalsIgnoreCase("")) {
			returnStr = time1.substring(0, 8);
		} else if (time2 != null && !time2.equalsIgnoreCase("")) {
			returnStr = time2.substring(0, 8);
		} else {
			returnStr = "00:00:00";
		}

		return returnStr;
	}

	/**
	 * @return 标准ORA时间格式的克隆
	 * @description 创建一个标准ORA时间格式的克隆
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	private static synchronized DateFormat getOraDateTimeFormat() {
		SimpleDateFormat theDateTimeFormat = (SimpleDateFormat) ORA_DATE_TIME_FORMAT.clone();
		theDateTimeFormat.setLenient(false);
		return theDateTimeFormat;
	}

	/**
	 * @return 标准ORA时间格式的克隆
	 * @description 创建一个带分秒的ORA时间格式的克隆
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	private static synchronized DateFormat getOraExtendDateTimeFormat() {
		SimpleDateFormat theDateTimeFormat = (SimpleDateFormat) ORA_DATE_TIME_EXTENDED_FORMAT.clone();
		theDateTimeFormat.setLenient(false);
		return theDateTimeFormat;
	}

	/**
	 * @return 系统当前的日期 格式为YYYY-MM-DD
	 * @description 得到系统当前的日期 格式为YYYY-MM-DD
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static String getSystemCurrentDate() {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeInMillis(System.currentTimeMillis());
		return doTransform(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH) + 1,
				calendar.get(Calendar.DAY_OF_MONTH));
	}

	/**
	 * @param year
	 *            年
	 * @param month
	 *            月
	 * @param day
	 *            日
	 * @return YYYY-MM-DD格式的字符串
	 * @description 返回格式为YYYY-MM-DD
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	private static String doTransform(int year, int month, int day) {
		StringBuffer result = new StringBuffer();
		result.append(year).append("-")
				.append(month < 10 ? "0" + month : String.valueOf(month)).append("-")
				.append(day < 10 ? "0" + day : String.valueOf(day));
		return result.toString();
	}

	/**
	 * @return 指定日期的上一天 格式:YYYY-MM-DD
	 * @description 获得昨天的日期
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static synchronized String getDayBeforeToday() {
		Date date = new Date(System.currentTimeMillis());
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(date);
		gc.add(Calendar.DATE, -1);
		return doTransform(toString(gc.getTime(), getOraExtendDateTimeFormat())).substring(0, 10);
	}

	/**
	 * @param dateStr
	 *            指定的日期 格式:YYYY-MM-DD
	 * @return
	 * @description 获得指定日期的上一天的日期
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static synchronized String getDayBeforeToday(String dateStr) {
		Date date = toDayStartDate(dateStr);
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(date);
		gc.add(Calendar.DATE, -1);
		return doTransform(toString(gc.getTime(), getOraExtendDateTimeFormat())).substring(0, 10);
	}

	/**
	 * @return 指定日期的下一天 格式:YYYY-MM-DD
	 * @description 获得明天的日期
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static synchronized String getDayAfterToday() {
		Date date = new Date(System.currentTimeMillis());
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(date);
		gc.add(Calendar.DATE, 1);
		return doTransform(toString(gc.getTime(), getOraExtendDateTimeFormat())).substring(0, 10);
	}

	/**
	 * @param dateStr
	 *            指定的日期 格式:YYYY-MM-DD
	 * @return 指定日期的下一天 格式:YYYY-MM-DD
	 * @description 获得指定日期的下一天的日期
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static synchronized String getDayAfterToday(String dateStr) {
		Date date = toDayStartDate(dateStr);
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(date);
		gc.add(Calendar.DATE, 1);
		return doTransform(toString(gc.getTime(), getOraExtendDateTimeFormat())).substring(0, 10);
	}

	/**
	 * @param months
	 *            月
	 * @return Date类型的日期
	 * @description 以当前日期为准，获得以后几个月的日期
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static synchronized Date getDayAfterMonth(int months) {
		Date date = new Date(System.currentTimeMillis());
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(date);
		gc.add(Calendar.MONTH, months);
		return gc.getTime();
	}

	/**
	 * @param dateStr
	 *            要转换的字符串
	 * @return 转换后的Date对象
	 * @description 将输入格式为2004-8-13,2004-10-8类型的字符串转换为标准的Date类型,这种Date类型 对应的日期格式为YYYY-MM-DD 00:00:00,代表一天的开始时刻
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static synchronized Date toDayStartDate(String dateStr) {
		String[] list = dateStr.split("-");
		int year = Integer.parseInt(list[0]);
		int month = Integer.parseInt(list[1]);
		int day = Integer.parseInt(list[2]);
		Calendar cale = Calendar.getInstance();
		cale.set(year, month - 1, day, 0, 0, 0);
		return cale.getTime();

	}

	/**
	 * @param scormTime1
	 *            scorm时间,格式为00:00:00(1..2).0(1..3)
	 * @param scormTime2
	 *            scorm时间,格式为00:00:00(1..2).0(1..3)
	 * @return 两个scorm时间相加的结果
	 * @description 将两个scorm时间相加
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static synchronized String addTwoScormTime(String scormTime1, String scormTime2) {
		int dotIndex1 = scormTime1.indexOf(".");
		int hh1 = Integer.parseInt(scormTime1.substring(0, 2));
		int mm1 = Integer.parseInt(scormTime1.substring(3, 5));
		int ss1 = 0;
		if (dotIndex1 != -1) {
			ss1 = Integer.parseInt(scormTime1.substring(6, dotIndex1));
		} else {
			ss1 = Integer.parseInt(scormTime1.substring(6));
		}
		int ms1 = 0;
		if (dotIndex1 != -1) {
			ms1 = Integer.parseInt(scormTime1.substring(dotIndex1 + 1));
		}

		int dotIndex2 = scormTime2.indexOf(".");
		int hh2 = Integer.parseInt(scormTime2.substring(0, 2));
		int mm2 = Integer.parseInt(scormTime2.substring(3, 5));
		int ss2 = 0;
		if (dotIndex2 != -1) {
			ss2 = Integer.parseInt(scormTime2.substring(6, dotIndex2));
		} else {
			ss2 = Integer.parseInt(scormTime2.substring(6));
		}
		int ms2 = 0;
		if (dotIndex2 != -1) {
			ms2 = Integer.parseInt(scormTime2.substring(dotIndex2 + 1));
		}

		int hh = 0;
		int mm = 0;
		int ss = 0;
		int ms = 0;

		if (ms1 + ms2 >= 1000) {
			ss = 1;
			ms = ms1 + ms2 - 1000;
		} else {
			ms = ms1 + ms2;
		}
		if (ss1 + ss2 + ss >= 60) {
			mm = 1;
			ss = ss1 + ss2 + ss - 60;
		} else {
			ss = ss1 + ss2 + ss;
		}
		if (mm1 + mm2 + mm >= 60) {
			hh = 1;
			mm = mm1 + mm2 + mm - 60;
		} else {
			mm = mm1 + mm2 + mm;
		}
		hh = hh + hh1 + hh2;

		StringBuffer sb = new StringBuffer();
		if (hh < 10) {
			sb.append("0").append(hh);
		} else {
			sb.append(hh);
		}
		sb.append(":");
		if (mm < 10) {
			sb.append("0").append(mm);
		} else {
			sb.append(mm);
		}
		sb.append(":");
		if (ss < 10) {
			sb.append("0").append(ss);
		} else {
			sb.append(ss);
		}
		sb.append(".");
		if (ms < 10) {
			sb.append(ms).append("00");
		} else if (ms < 100) {
			sb.append(ms).append("0");
		} else {
			sb.append(ms);
		}
		return sb.toString();
	}

	/**
	 * @param date
	 *            要与当前日期比较的日期
	 * @param timeType
	 *            0代表返回两个日期相差月数，1代表返回两个日期相差天数
	 * @return 根据timeType返回当前日期与传入日期的差值
	 * @description 根据timeType返回当前日期与传入日期的差值（当前日期减传入日期） 当要求返回月份的时候，date的日期必须和当前的日期相等， 否则返回0（例如：2003-2-23 和
	 *              2004-6-12由于23号和12号不是同一天，固返回0， 2003-2-23 和 2005-6-23 则需计算相差的月份，包括年，此例应返回28（个月）。 2003-2-23 和 2001-6-23
	 *              也需计算相差的月份，包括年，此例应返回-20（个月））
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static int CompareDateWithNow(Date date, int timeType) {
		Date now = Calendar.getInstance().getTime();

		Calendar calendarNow = Calendar.getInstance();
		calendarNow.setTime(now);
		calendarNow.set(Calendar.HOUR, 0);
		calendarNow.set(Calendar.MINUTE, 0);
		calendarNow.set(Calendar.SECOND, 0);

		Calendar calendarPara = Calendar.getInstance();
		calendarPara.setTime(date);
		calendarPara.set(Calendar.HOUR, 0);
		calendarPara.set(Calendar.MINUTE, 0);
		calendarPara.set(Calendar.SECOND, 0);

		float nowTime = now.getTime();
		float dateTime = date.getTime();

		if (timeType == 0) {
			if (calendarNow.get(Calendar.DAY_OF_YEAR) == calendarPara.get(Calendar.DAY_OF_YEAR)) {
				return 0;
			}
			return (calendarNow.get(Calendar.YEAR) - calendarPara.get(Calendar.YEAR)) * 12
					+ calendarNow.get(Calendar.MONTH) - calendarPara.get(Calendar.MONTH);
		} else {
			float result = nowTime - dateTime;
			long day = 24 * 60 * 60 * 1000L;
			result = result / day;
			float resultFloat = result;
			float fraction = result - (int) resultFloat;
			if (fraction > 0.5) {
				return (int) resultFloat + 1;
			} else if (fraction < -0.5) {
				return (int) resultFloat - 1;
			} else {
				return (int) resultFloat;
			}
		}
	}

	/**
	 * @param theDate
	 *            要转换的日期对象
	 * @param theDateFormat
	 *            返回的日期字符串的格式
	 * @return 转换为制定格式的日期
	 * @description 将一个日期对象转换成为指定日期、时间格式的字符串。 如果日期对象为空，返回一个空字符串对象.
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static synchronized String toString(Date theDate, DateFormat theDateFormat) {
		if (theDate == null) {
			return "";
		} else {
			return theDateFormat.format(theDate);
		}
	}

	/**
	 * @param date
	 *            输入格式为ORA标准时间格式
	 * @return 格式为YYYY-MM-DD hh:mm:ss 的时间串
	 * @description 返回格式为YYYY-MM-DD hh:mm:ss
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	private static String doTransform(String date) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(date, 0, 4);
		buffer.append("-");
		buffer.append(date, 4, 6);
		buffer.append("-");
		buffer.append(date, 6, 8);
		buffer.append(" ");
		buffer.append(date, 8, 10);
		buffer.append(":");
		buffer.append(date, 10, 12);
		buffer.append(":");
		buffer.append(date, 12, 14);

		return buffer.toString();
	}

	/**
	 * @param date
	 *            转换的日期
	 * @param type
	 *            周日为一周开始 为 0，周一为一天开始的为1
	 * @return 日期是一个星期的第几天
	 * @description 获得日期是一个星期的第几天
	 * @date 2011-1-26
	 * <AUTHOR>
	 */
	public static int dateToWeekDay(Date date, int type) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		if (type == 0) {
			return calendar.get(Calendar.DAY_OF_WEEK);
		} else if (type == 1) {
			if (calendar.get(Calendar.DAY_OF_WEEK) == 1) {
				return 7;
			} else {
				return calendar.get(Calendar.DAY_OF_WEEK) - 1;
			}
		} else {
			return -1;
		}
	}

	/**
	 * @return 当前星期开始日期
	 * @description 获得当前星期开始日期
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static Date getWeekStartDate() {
		Calendar calendar = Calendar.getInstance();
		Date firstDateOfWeek;
		int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
		calendar.add(Calendar.DAY_OF_WEEK, -(dayOfWeek - 1));
		firstDateOfWeek = calendar.getTime();
		calendar.add(Calendar.DAY_OF_WEEK, 6);
		return firstDateOfWeek;
	}

	/**
	 * @return 当前星期结束日期
	 * @description 获得当前星期结束日期
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static Date getWeekEndDate() {
		Calendar calendar = Calendar.getInstance();
		Date lastDateOfWeek;
		int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
		calendar.add(Calendar.DAY_OF_WEEK, -(dayOfWeek - 1));
		calendar.add(Calendar.DAY_OF_WEEK, 6);
		lastDateOfWeek = calendar.getTime();
		return lastDateOfWeek;
	}

	/**
	 * @return 当前月份的第一天
	 * @description 获得当前月份的第一天
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static Date getMonthStartDate() {
		SimpleDateFormat df = new SimpleDateFormat(DATE_FMT_1);
		GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
		gc.setTime(new Date(System.currentTimeMillis()));
		gc.set(Calendar.DAY_OF_MONTH, 1);
		return toDayStartDate(df.format(gc.getTime()));
	}

	/**
	 * @return Date 前月份的最后一天
	 * @description 获得当前月份的最后一天
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static Date getMonthEndDate() {
		SimpleDateFormat df = new SimpleDateFormat(DATE_FMT_1);
		Calendar cal = Calendar.getInstance();
		cal.setTime(new Date(System.currentTimeMillis()));
		cal.add(Calendar.MONTH, 1);
		cal.set(Calendar.DATE, 1);
		cal.add(Calendar.DATE, -1);
		return toDayEndDate(df.format(cal.getTime()));
	}

	/**
	 * @param dateStr
	 *            输入格式:2004-8-13,2004-10-8
	 * @return 转换后的Date对象 ，格式为YYYY-MM-DD 23:59:59
	 * @description 将输入格式为2004-8-13,2004-10-8类型的字符串转换为标准的Date类型,这种Date类型 对应的日期格式为YYYY-MM-DD 23:59:59,代表一天的结束时刻
	 * @date 2010-7-12
	 * <AUTHOR>
	 */
	public static synchronized Date toDayEndDate(String dateStr) {
		String[] list = dateStr.split("-");
		int year = Integer.parseInt(list[0]);
		int month = Integer.parseInt(list[1]);
		int day = Integer.parseInt(list[2]);
		Calendar cale = Calendar.getInstance();
		cale.set(year, month - 1, day, 23, 59, 59);
		return cale.getTime();

	}

	/**
	 * @param d
	 *            标准日期
	 * @param day
	 *            第几天
	 * @return 几天前的日期
	 * @description 得到几天前的日期
	 * @date 2010-12-30
	 * <AUTHOR>
	 */
	public static Date getDateBefore(Date d, int day) {
		Calendar now = Calendar.getInstance();
		now.setTime(d);
		now.set(Calendar.DATE, now.get(Calendar.DATE) - day);
		return now.getTime();
	}

	/**
	 * @param d
	 *            标准日期
	 * @param year
	 *            第几年
	 * @return 几年前的日期
	 * @description 得到几年前的日期
	 * @date 2010-12-30
	 * <AUTHOR>
	 */
	public static Date getYearBefore(Date d, int year) {
		Calendar now = Calendar.getInstance();
		now.setTime(d);
		now.set(Calendar.YEAR, now.get(Calendar.YEAR) - year);
		return now.getTime();
	}

	/**
	 * <b>方法名</b>：getYearAfter<br>
	 * <b>功能</b>：得到几年后的日期<br>
	 *
	 * @param d
	 *            日期
	 * @param year
	 *            第几年
	 * @return 得到几年后的日期
	 * <AUTHOR> color='blue'>hanyh-a</font>
	 * @date 2015年2月8日 下午4:25:33
	 */
	public static Date getYearAfter(Date d, int year) {
		Calendar now = Calendar.getInstance();
		now.setTime(d);
		now.set(Calendar.YEAR, now.get(Calendar.YEAR) + year);
		return now.getTime();
	}

	/**
	 * @param d
	 *            标准日期
	 * @param month
	 *            第几月
	 * @return 几月前的日期
	 * @description 得到几月前的日期
	 * @date 2010-12-30
	 * <AUTHOR>
	 */
	public static Date getMonthBefore(Date d, int month) {
		Calendar now = Calendar.getInstance();
		now.setTime(d);
		now.set(Calendar.MONTH, now.get(Calendar.MONTH) - month);
		return now.getTime();
	}

	/**
	 * <b>方法名</b>：getMonthAfter<br>
	 * <b>功能</b>：几月后的日期<br>
	 *
	 * @param d
	 *            日期
	 * @param month
	 *            第几月
	 * @return 几月后的日期
	 * <AUTHOR> color='blue'>hanyh-a</font>
	 * @date 2015年2月7日 下午3:02:28
	 */
	public static Date getMonthAfter(Date d, int month) {
		Calendar now = Calendar.getInstance();
		now.setTime(d);
		now.set(Calendar.MONTH, now.get(Calendar.MONTH) + month);
		return now.getTime();
	}

	/**
	 * @param str
	 *            毫秒字符串
	 * @return 转换后的Date
	 * @description 将毫秒字符串转换为Date的方法
	 * @date 2011-3-22
	 * <AUTHOR>
	 */
	public static Date parseDateByMilliSecondString(String str) {
		Long millisecond = Long.parseLong(str);
		return new Date(millisecond);
	}

	/**
	 * @param date
	 * @return
	 * @description 得到指定日期的七天后的日期
	 * @date 2011-6-16
	 * <AUTHOR>
	 */
	public static Date getDateAfterSevenDays(Date date) {
		String sdfStrDate = null;
		Date sevenDate = null;
		try {
			sdfStrDate = DateToString(date, DATE_FMT_1);
			Date dateOne = parseStringToSpecialDates(sdfStrDate);
			sevenDate = new Date(dateOne.getTime() + (6 * 24 * 3600 * 1000));

		} catch (Exception e) {
			e.printStackTrace();

		}
		return sevenDate;
	}

    /**
     * @Title: getDateAfterNDate
     * @Description: 得到指定日期后的指定天数的时间
     * @throws
     * @param: [date, n]
     * @return: java.util.Date
     * @auther: zhaohy-c
     * @date: 2021/4/25 10:19
     */
	public static Date getDateAfterNDate(Date date, Integer n) {
		if (date == null || n == null) {
			return null;
		}
		Date newDate = null;
		try {
			newDate = new Date(date.getTime() + (n * 24 * 3600 * 1000));
		} catch (Exception e) {
			e.printStackTrace();

		}
		return newDate;
	}

    /**
     * @Title: getDateBeforeNDate
     * @Description: 获取指定时间 指定天数前的时间
     * @throws
     * @param: [date, n]
     * @return: java.util.Date
     * @auther: zhaohy-c
     * @date: 2021/4/25 10:27
     */
    public static Date getDateBeforeNDate(Date date, Integer n) {
        if (date == null || n == null) {
            return null;
        }
        Date newDate = null;
        try {
            newDate = new Date(date.getTime() - (n * 24 * 3600 * 1000));
        } catch (Exception e) {
            e.printStackTrace();

        }
        return newDate;
	}


	public static Date parseStringToSpecialDates(String date) {
		SimpleDateFormat sdf = new SimpleDateFormat(DATE_FMT_1);
		Date dateOne = null;
		try {
			dateOne = sdf.parse(date);
		} catch (ParseException e) {
		}
		return dateOne;
	}

	public static long getNow() {
		CurrTime = new Date();
		return CurrTime.getTime();
	}

	/**
	 * 通过字符串HH:mm:ss得到毫秒值
	 *
	 * @param s
	 *            String 格式为HH:mm:ss的字符串
	 * @return long 转换后的毫秒值
	 */
	public static long getTimebyString(String s) {
		int i = s.indexOf(":");
		int j = s.lastIndexOf(":");
		long time = 0;
		if ((i != -1) && (j != -1)) {
			int hh = Integer.parseInt(s.substring(0, i));
			int mm = Integer.parseInt(s.substring(i + 1, j));
			int ss = Integer.parseInt(s.substring(j + 1, s.length()));
			time = hh * 60 * 60 * 1000L + mm * 60 * 1000L + ss * 1000L;
		}
		return time;
	}

	/**
	 * 通过字符串HH:mm得到毫秒值
	 *
	 * @param s
	 *            String 格式为HH:mm的字符串
	 * @return long 转换后的毫秒值
	 */
	public static long getTimesbyString(String s) {
		int i = s.indexOf(":");
		long time = 0;
		if ((i != -1)) {
			int hh = Integer.parseInt(s.substring(0, i));
			int mm = Integer.parseInt(s.substring(i + 1, s.length()));
			time = hh * 60 * 60 * 1000L + mm * 60 * 1000L;
		}
		return time;
	}

	/**
	 * String 格式为HH:mm的字符串 单位毫秒 根据2个时间计算时间差
	 */
	public static long getTimeDifference(String begintime, String endtime) {
		long begintimelong = getTimesbyString(begintime);
		long endtimelong = getTimesbyString(endtime);
		if (begintimelong >= endtimelong) {
			return 0;
		} else {
			return endtimelong - begintimelong;
		}
	}

	/**
	 * 格式化日期
	 *
	 * @param date
	 *            Date 日期
	 * @param string
	 *            String 格式字符串
	 * @return String 格式化后日期
	 */
	public static String FormatDate(Date date, String string) {
		SimpleDateFormat dateformat = new SimpleDateFormat(string);
		return dateformat.format(date);
	}

	/**
	 * 取得当前时间格式yyyy-MM-dd HH:mm:ss
	 *
	 * @return String
	 */
	public static String getCurrTime() {
		Date date_time = new Date();
		return FormatDate(date_time, DATE_FMT_Y_M_D_H_M_S);
	}

	public static String getYYMMDD() {
		return FormatDate(new Date(), "yyMMdd");
	}

	/**
	 * 取得当前时间格式yyyy-MM-dd HH:mm:ss:millis
	 *
	 * @return String
	 */
	public static String getCurrMillisTime() {
		long l = System.currentTimeMillis();
		String d = DateUtils.getCurrTime();
		return d + ":" + (l - DateUtils.getTimeInMillis(d, DATE_FMT_Y_M_D_H_M_S));
	}

	/**
	 * 取得当前时间格式yyyy-MM-dd HH:mm
	 *
	 * @return String
	 */
	public static String getCurrShortTime() {
		Date date = new Date();
		return FormatDate(date, DATE_FMT_Y_M_D_H_M);
	}

	/**
	 * 取得当前时间格式yyyy-MM-dd
	 *
	 * @return String
	 */
	public static String getCurrDate() {
		CurrTime = new Date();
		return FormatDate(CurrTime, DATE_FMT_1);
	}

	public static String getUnsepCurDate() {
		return FormatDate(new Date(), "yyyyMMdd");
	}

	public static String getYYMMDDHHMMSS() {
		return FormatDate(new Date(), "yyMMddHHmmss");
	}

	/**
	 * 取得当前年份
	 *
	 * @return String
	 */
	public static String getCurrYear() {
		CurrTime = new Date();
		return FormatDate(CurrTime, "yyyy");

	}

	/**
	 * 取得当前月份
	 *
	 * @return String
	 */
	public static String getCurrMonth() {
		CurrTime = new Date();
		return FormatDate(CurrTime, "MM");

	}

	/**
	 * 取得当前日
	 *
	 * @return String
	 */
	public static String getCurrDay() {
		CurrTime = new Date();
		return FormatDate(CurrTime, "dd");

	}

	/**
	 * 取得当前小时
	 *
	 * @return String
	 */
	public static String getCurrHours() {
		CurrTime = new Date();
		return FormatDate(CurrTime, "HH");
	}

	/**
	 * 取得当前分钟
	 *
	 * @return String
	 */
	public static String getCurrMinutes() {
		CurrTime = new Date();
		return FormatDate(CurrTime, "mm");
	}

	/**
	 * 取得当前秒
	 *
	 * @return String
	 */
	public static String getCurrSeconds() {
		CurrTime = new Date();
		return FormatDate(CurrTime, "ss");
	}

	/**
	 * 转换日期格式yyyy-MM-dd HH:mm:ss
	 *
	 * @param date
	 *            Date 日期
	 * @return String
	 */
	public static String dayToString(Date date) {
		return FormatDate(date, DATE_FMT_1);
	}

	/**
	 * 转换日期格式yyyy-MM-dd HH:mm:ss
	 *
	 * @param date
	 *            Date 日期
	 * @return String
	 */
	public static String dateToString(Date date) {
		return FormatDate(date, DATE_FMT_Y_M_D_H_M_S);
	}

	/**
	 * 转换日期格式yyyy-MM-dd HH:mm:ss
	 *
	 * @param date
	 *            Date 日期
	 * @return String
	 */
	public static String dateToString(java.sql.Date date) {
		return FormatDate(date, DATE_FMT_Y_M_D_H_M_S);
	}

	public static String dateToString(Date date, String formatString) {
		return FormatDate(date, formatString);
	}

	/**
	 * 转换日期格式yyyy-MM-dd HH
	 *
	 * @param date
	 *            Date 日期
	 * @return String
	 */
	public static String dateToString1(java.sql.Date date) {
		return FormatDate(date, DATE_FMT_1);
	}

	/**
	 * 转换日期格式yyyy-MM-dd HH:mm
	 *
	 * @param date
	 *            Date 日期
	 * @return String
	 */
	public static String dateToShortString(Date date) {
		return FormatDate(date, DATE_FMT_Y_M_D_H_M);
	}

	/**
	 * 说明：由时间格式的字符串获得年数
	 *
	 * @param dateString
	 *            时间格式字符串
	 * @return
	 */
	public static int getYearByString(String dateString) {
		int year = 0;
		int i = dateString.indexOf("-");
		int j = dateString.lastIndexOf("-");
		if (i != -1 && j != -1) {
			year = Integer.parseInt(dateString.substring(0, i));
		}
		return year;
	}

	/**
	 * 说明：由时间格式的字符串获得月份数
	 *
	 * @param dateString
	 *            时间格式字符串
	 * @return
	 */
	public static int getMonthByString(String dateString) {
		int month = 1;
		int i = dateString.indexOf("-");
		int j = dateString.lastIndexOf("-");
		if (i != -1 && j != -1) {
			month = Integer.parseInt(dateString.substring(i + 1, j));
		}
		return month;
	}

	/**
	 * 说明: 字符串转换为日期 (默认格式 yyyy-MM-dd)
	 *
	 * @param dateString
	 *            日期格式字符串
	 * @return 转换后的日期
	 */
	public static Date stringToDate(String dateString) {
		String sf = DATE_FMT_1;
		return stringToDate(dateString, sf);
	}

	/**
	 * 说明：字符串转换为时间（默认格式 yyyy-MM-dd HH:mm:ss)
	 *
	 * @param dateString
	 *            日期格式字符串
	 * @return 转换后的日期
	 */
	public static Date StringToTime(String dateString) {
		String sf = DATE_FMT_Y_M_D_H_M_S;
		return stringToDate(dateString, sf);
	}

	/**
	 * 日期字符串转日期
	 * @param dateStr 日期字符串
	 * @param format 格式，为空默认：yyyy-MM-dd HH:mm:ss
	 * @return
	 */
	public static Date toDate(String dateStr, String format) {
		if (format==null) {
			format = DATE_FMT_Y_M_D_H_M_S;
		}
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		try {
			return sdf.parse(dateStr);
		} catch (Exception ex) {
			return null;
		}
	}

	/**
	 * 说明：时间字符串格式转换（默认格式 yyyyMMddHHmmss)，主要为接口部分所用
	 *
	 * @param dateString
	 *            日期格式字符串 （默认格式 yyyyMMddHHmmss)
	 * @return 转换后的日期格式字符串（yyyy-MM-dd HH:mm:ss） add by zhangjie
	 */
	public static String timeStringChange(String dateString) {
		String sf = "yyyyMMddHHmmss";
		Date dt = stringToDate(dateString, sf);
		return dateToString(dt);
	}

	/**
	 * 获得当前日期的年月，形成格式为YYMM的字符串
	 *
	 * @return String 年月字符串，格式YYMM
	 */
	public static String getPublicDate() {
		Calendar c = Calendar.getInstance();
		int month = c.get(Calendar.MONTH) + 1;
		return ("" + c.get(Calendar.YEAR)).substring(2, 4) + (month > 9 ? "" + month : "0" + month);
	}

	/**
	 * 根据参数计算并给出当前日期返回制定月所在的年
	 *
	 * @param getback
	 *            int 返回制定月所在的年
	 * @return int
	 */
	public static int backyear(int getback) {
		int backYear = 0;
		CurrTime = new Date();
		int nowyear = Integer.parseInt(FormatDate(CurrTime, "yyyy"));
		int nowmonth = Integer.parseInt(FormatDate(CurrTime, "MM"));
		if (nowmonth > getback) {
			backYear = nowyear;
		} else {
			backYear = nowyear - 1;
		}
		return backYear;
	}

	/**
	 * 根据参数计算并给出当前日期返回制定月所在的年
	 *
	 * @param getback
	 *            int 返回制定月所在的年
	 * @return int
	 */
	public static int backmonth(int getback) {
		int backMonth = 0;
		CurrTime = new Date();
		int nowmonth = Integer.parseInt(FormatDate(CurrTime, "MM"));
		if (nowmonth > getback) {
			backMonth = nowmonth - getback;
		} else {
			backMonth = nowmonth - getback + 12;
		}
		return backMonth;
	}

	/**
	 * 根据参数计算并给出所需的年
	 *
	 * @param date
	 *            Date 日期
	 * @param getback
	 *            int 返回制定月所在的年
	 * @return String
	 */
	public static String dateToyear(Date date, int getback) {
		String dateyear = FormatDate(date, "yyyy");
		String datemonth = FormatDate(date, "MM");
		String newyear = "";
		int oldyear = Integer.parseInt(dateyear);
		int oldmonth = Integer.parseInt(datemonth);
		if (oldmonth > getback) {
			newyear = String.valueOf(oldyear);
		} else {
			newyear = String.valueOf(oldyear - 1);
		}
		return newyear;
	}

	/**
	 * 根据参数计算并给出所需的月
	 *
	 * @param date
	 *            Date 日期
	 * @param getback
	 *            int 返回制定月所在的年
	 * @return String
	 */
	public static String dateTomonth(Date date, int getback) {
		String datemonth = FormatDate(date, "MM");
		String newmonth = "";
		int oldmonth = Integer.parseInt(datemonth);
		if (oldmonth > getback) {
			newmonth = String.valueOf(oldmonth - getback);
		} else {
			newmonth = String.valueOf(oldmonth + 12 - getback);
		}
		return newmonth;
	}

	/**
	 * 根据给定的两个时间计算时间差
	 *
	 * @param date1
	 *            Date 日期
	 * @param date2
	 *            Date 日期
	 * @return int
	 */
	public static int subtime(Date date1, Date date2) {
		String dateyear1 = FormatDate(date1, "yyyy");
		String datemonth1 = FormatDate(date1, "MM");
		String dateyear2 = FormatDate(date2, "yyyy");
		String datemonth2 = FormatDate(date2, "MM");
		int year1 = Integer.parseInt(dateyear1);
		int year2 = Integer.parseInt(dateyear2);
		int month1 = Integer.parseInt(datemonth1);
		int month2 = Integer.parseInt(datemonth2);
		return (year2 - year1) * 12 + (month2 - month1) + 1;
	}

	/**
	 * 根据给定的两个时间字符串计算时间差
	 *
	 * @param dateString1
	 *            String 时间
	 * @param dateString2
	 *            String 时间
	 * @return long 时间差以秒为单位(dateString2-dateString1)
	 */
	public static long subsecond(String dateString1, String dateString2) {
		Date date1 = StringToTime(dateString1);
		Date date2 = StringToTime(dateString2);
		return (date2.getTime() - date1.getTime()) / 1000;
	}

	/**
	 * 将一个表示秒数的字符串转换为n小时n分钟n秒的字符串
	 *
	 * @param dateString
	 *            String 日期字符串
	 * @return String
	 */
	public static String changeFormat(String dateString) {
		int date = Integer.parseInt(dateString);
		int hh = 0;
		int mm = 0;
		int ss = 0;
		hh = date / 3600;
		mm = date % 3600 / 60;
		ss = date % 3600 % 60;
		String changedtime = hh + "小时";
		if (mm != 0) {
			changedtime += (hh + "分钟");
		}
		if (ss != 0) {
			if (mm == 0) {
				changedtime += "0分钟";
			}
			changedtime += (ss + "秒");
		}
		return changedtime;
	}

	/**
	 * 将一个表示秒数的字符串转换为天数
	 *
	 * @param timelength
	 *            long 秒数
	 * @return long
	 */
	public static long changeSecondToDay(long timelength) {
		long day = 0;
		if (timelength % (24 * 3600) == 0) {
			day = timelength / 24 / 3600;
		} else {
			day = timelength / 24 / 3600 + 1;
		}
		return day;
	}

	/**
	 * 将一个表示秒数的字符串转换为分钟
	 *
	 * @param timelengthString
	 *            String 秒数的字符串
	 * @return String
	 */
	public static String changeSecondToMinute(String timelengthString) {
		long minute = 0;
		String minuteString = "";
		long timelength = Long.parseLong(timelengthString);
		if (timelength % 60 == 0) {
			minute = timelength / 60;
		} else {
			minute = timelength / 60 + 1;
		}
		minuteString = minute + "分";
		return minuteString;
	}

	/**
	 * 改变日到分
	 *
	 * @param dayString
	 *            String 日字符串
	 * @return String
	 */
	public static String changeDayToMinute(String dayString) {
		long minute = 0;
		long day = Long.parseLong(dayString);
		String minuteString = "";
		minute = day * 24 * 60;
		minuteString = minute + "分";
		return minuteString;
	}

	/**
	 * 改变分到小时
	 *
	 * @param minuteString
	 *            String 分字符串
	 * @return String
	 */
	public static String changeMinuteToHour(String minuteString) {
		long minute = Long.parseLong(minuteString);
		long hour = 0;
		String hourString = "";
		if (minute % 60 == 0) {
			hour = minute / 60;
			hourString = hour + "小时";
		} else {
			hour = minute;
			hourString = hour + "分钟";
		}
		return hourString;
	}

	/**
	 * 字符串转换为日期
	 *
	 * @param dateString
	 *            String 日期格式字符串
	 * @param sf
	 *            String 日期格式化定义
	 * @return Date 转换后的日期
	 */
	public static Date stringToDate(String dateString, String sf) {
		ParsePosition pos = new ParsePosition(0);
		SimpleDateFormat sdf = new SimpleDateFormat(sf);
		return sdf.parse(dateString, pos);
	}

	/**
	 * 将从oracle中取出的Date类型字符串（YYYY－MM－DD HH24：MI：SS.ms）转化为YYYY-MM-DD HH24:MI:SS
	 *
	 * @param date
	 *            String 日期
	 * @return String
	 */
	public static String changeOracleDate(String date) {
		if (date.contains(".")) {
			return date.substring(0, date.indexOf("."));
		} else {
			return date;
		}
	}

	/**
	 * Modify by ppLiang 当前时间 挂起开始时间 结束时间 标志 06-06-09 11；00 06-06-09 12；00 06-06-09 13；00 0 挂起开始时间 > 当前时间 06-06-09 12；00
	 * 06-06-09 11；00 06-06-09 13；00 1 挂起开始时间 < 当前时间 06-06-09 13；00 06-06-09 11；00 06-06-09 12；00 2 挂起结束时间 < 当前时间
	 */
	public static String getBeginTimeEqualsEndTimeFlag(String beginTime, String endTime) {
		// 1.如果“挂起开始时间”<“当前时间“，并且小于“挂起结束时间”，挂起标志位为"1"
		// 2.如果“挂起结束时间”大于“当前时间”，挂起标志位为"2"
		// 3.如果“挂起开始时间”大于“当前时间”，挂起标志位为"0"

		Date currentDate = new Date();
		Date hangBeginTime = stringToDate(beginTime);
		Date hangEndTime = stringToDate(endTime);
		String flag = "";

		if (hangBeginTime.before(hangEndTime)) { // 挂起开始时间 < 挂起结束时间
			if ((hangBeginTime.before(currentDate)) && (currentDate.before(hangEndTime))) {
				// 挂起开始时间 < 当前时间 && 挂起开始时间 < 挂起结束时间
				flag = "1";
			} else if (hangEndTime.before(currentDate)) { // 挂起结束时间 < 当前时间
				flag = "2";
			} else if (currentDate.before(hangBeginTime)) { // 挂起开始时间 > 当前时间
				flag = "0";
			}
		}
		return flag;
	}

	public static String getCurrTimes() {
		Date dateTime = new Date();
		return FormatDate(dateTime, "HH:mm:ss");
	}

	/**
	 * 根据传近来的时间和时间格式得到对应的星期
	 *
	 * @param TempDate
	 *            时间
	 * @param format
	 *            "yyyy-MM-dd"
	 * @return int 1~7代表星期日到星期六
	 */
	public static int getWeekDay(String TempDate, String format) {
		int temp = 0;
		SimpleDateFormat sdf = new SimpleDateFormat(format);
		Calendar c = Calendar.getInstance();
		try {
			c.setTime(sdf.parse(TempDate));
			temp = c.get(Calendar.DAY_OF_WEEK);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return temp;
	}

	/**
	 * 根据 传入的时间、时间格式 和 天数，返回指定的日期 time 当前时间 format 时间格式 yyyy-MM-dd HH:mm:ss date 天数，如果是后3天则 -3，前3天则 是 3
	 */
	public static String getTimeToAddDate(String time, String format, int date) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeZone(TimeZone.getDefault());
		calendar.clear();
		calendar.setTime(stringToDate(time, format));// 开始时间

		calendar.add(Calendar.DAY_OF_MONTH, date);

		return FormatDate(calendar.getTime(), format);
	}

	/**
	 * 根据 传入的时间、时间格式 和 天数，返回指定时间段后多少毫秒会自动追加 time 当前时间 format 时间格式 yyyy-MM-dd HH:mm:ss timeInMillis 毫秒，表示当前时间段后多少毫秒会自动追加
	 */
	public static String getTimeInMillisToAdd(String time, String format, long timeInMillis) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeZone(TimeZone.getDefault());
		calendar.clear();
		calendar.setTime(stringToDate(time, format));// 开始时间

		calendar.setTimeInMillis(calendar.getTimeInMillis() + timeInMillis);
		return dateToString(calendar.getTime());
	}

	/**
	 * 根据 传入的时间、时间格式 ，返回毫秒
	 * <p/>
	 * time 指定时间 format 时间格式 yyyy-MM-dd HH:mm:ss
	 */
	public static long getTimeInMillis(String time, String format) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeZone(TimeZone.getDefault());
		calendar.clear();
		calendar.setTime(stringToDate(time, format));// 开始时间

		return calendar.getTimeInMillis();
	}

	/**
	 * 计算星期几的函数
	 *
	 * @param year
	 *            int
	 * @param month
	 *            int
	 * @param date
	 *            int
	 * @return int 1~7代表星期日到星期六
	 */
	public static int getDayOfWeek(int year, int month, int date) {
		Calendar cal = Calendar.getInstance();
		cal.setTimeZone(TimeZone.getDefault());
		cal.clear();
		cal.set(year, month - 1, date);
		return cal.get(Calendar.DAY_OF_WEEK);
	}

	/**
	 * 根据传进来的参数得到当前月最后一天
	 */
	public static int getLastDayOfMonth(String time, String format) {
		Calendar cDay1 = Calendar.getInstance();
		cDay1.setTime(stringToDate(time, format));
		return cDay1.getActualMaximum(Calendar.DAY_OF_MONTH);
	}

	/**
	 * 版本投产通知,获得投产时间
	 *
	 * @return
	 */
	public static String getTimeOfInform() {
		String time = null;
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(CurrTime);
		int lastDay = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
		int day = calendar.get(Calendar.DAY_OF_MONTH);
		int week = calendar.get(Calendar.DAY_OF_WEEK);
		int year = calendar.get(Calendar.YEAR);
		int month = calendar.get(Calendar.MONTH) + 1;
		if ((week - 7) == 0) {
			if ((day + 4) - lastDay > 0) {
				month += 1;
				day = (day + 4) - lastDay;
			} else {
				day = (day + 4);
			}
		} else if ((week - 4) >= 0) {
			if ((day + 7 - week) - lastDay > 0) {
				month += 1;
				day = (day + 7 - week) - lastDay;
			} else {
				day = (day + 7 - week);
			}
		} else {
			if ((day + 4 - week) - lastDay > 0) {
				month += 1;
				day = (day + 4 - week) - lastDay;
			} else {
				day = (day + 4 - week);
			}
		}
		String m = null;
		String d = null;
		if (month <= 9) {
			m = "0" + month;
		} else {
			m = String.valueOf(month);
		}
		if (day <= 9) {
			d = "0" + day;
		} else {
			d = String.valueOf(day);
		}
		time = year + "-" + m + "-" + d + " 22:00:00";
		return time;
	}

	public static String getMonthAndDay() {
		SimpleDateFormat df = new SimpleDateFormat("MM月dd日");
		return df.format(new Date());
	}

	/**
	 * 开始时间 多少个月之后，
	 * <p/>
	 * 计算从开始时间到 几个月之后，中间存在多少天
	 */
	public static int getMonthEqualsDays(String begintime, int month) {
		Calendar calendar = Calendar.getInstance();
		calendar.setTimeZone(TimeZone.getDefault());
		calendar.clear();
		calendar.setTime(stringToDate(begintime));// 开始时间
		calendar.add(Calendar.MONTH, month);

		return (int) ((calendar.getTimeInMillis() - stringToDate(begintime).getTime()) / 1000 / 24 / 60 / 60);
	}

	public static Timestamp getTimestampByString(String time) {
		Timestamp ts = new Timestamp(System.currentTimeMillis());
		ts = Timestamp.valueOf(time);
		return ts;
	}

	/**
	 * <b>方法名</b>：getDateString<br>
	 * <b>功能</b>：返回指定格式的日期字符串<br>
	 *
	 * @param ts
	 * @param pattern
	 * @return
	 * @throws Exception
	 * <AUTHOR> color='blue'>hanyh-a</font>
	 * @date 2014年12月17日 下午2:48:10
	 */
	public static String getDateString(Timestamp ts, String pattern) throws Throwable {
		return DateToString(new Date(ts.getTime()), pattern);
	}

	public static Timestamp getTimestamp() {
		return new Timestamp(System.currentTimeMillis());
	}

	/**
	 * <b>方法名</b>：timestampToDate<br>
	 * <b>功能</b>：Timestamp转换成指定格式的Date类型的日期<br>
	 *
	 * @param ts
	 * @param pattern
	 * @return
	 * @throws ParseException
	 * <AUTHOR> color='blue'>hanyh-a</font>
	 * @date 2015年1月30日 下午5:18:56
	 */
	public static Date timestampToDate(Timestamp ts, String pattern) {
		Date date = ts;
		SimpleDateFormat sdf = new SimpleDateFormat(pattern);
		try {
			return sdf.parse(sdf.format(date));
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * <b>方法名</b>：getCurrentDate<br>
	 * <b>功能</b>：获取当前日期(格式：yyyy-MM-dd)<br>
	 *
	 * @return
	 * @throws ParseException
	 * <AUTHOR> color='blue'>hanyh-a</font>
	 * @date 2015年1月30日 下午5:18:53
	 */
	public static Date getCurrentDate() {
		Date date = new Date();
		SimpleDateFormat sdf = new SimpleDateFormat(DATE_FMT_1);
		try {
			return sdf.parse(sdf.format(date));
		} catch (ParseException e) {
			return null;
		}
	}

	/**
	 * <b>方法名</b>：getDateAfterDays<br>
	 * <b>功能</b>：得到指定日期的几天后的日期<br>
	 *
	 * @param date
	 *            指定日期（格式：yyyy-MM-dd）
	 * @param days
	 *            多少天
	 * @return
	 * <AUTHOR> color='blue'>hanyh-a</font>
	 * @date 2015年2月7日 下午2:55:35
	 */
	public static Date getDateAfterDays(Date date, int days) {
		String sdfStrDate = null;
		Date ndate = null;
		try {
			sdfStrDate = DateToString(date, DATE_FMT_1);
			Date dateOne = parseStringToSpecialDates(sdfStrDate);
			ndate = new Date(dateOne.getTime() + (days * 24 * 3600 * 1000L));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return ndate;
	}

	/**
	 * 获取当前时间的后多少天 <b>方法名</b>：getNextDay<br>
	 * <b>功能</b>：<br>
	 * 
	 * <AUTHOR>
	 * @date 2016年10月17日 下午4:48:58
	 * @param date
	 *            指定日期（格式：yyyy-MM-dd HH:mm:ss）
	 * @param days
	 * @param
	 * @return Timestamp
	 */
	public static Timestamp getNextDay(Date date, int days) {
		Timestamp nextTimestamp = null;
		try {
			Calendar cla = Calendar.getInstance();
			cla.setTime(date);
			cla.add(Calendar.DAY_OF_YEAR, days);
			nextTimestamp = new Timestamp(cla.getTimeInMillis());

		} catch (Exception e) {
			e.printStackTrace();
		}
		return nextTimestamp;
	}

	/**
	 * 计算日期的星期
	 * @param date
	 * @return
	 */
	public static String getWeekStr(Date date){
		String week = "";
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		int weekNum = cal.get(Calendar.DAY_OF_WEEK);
		switch (weekNum){
			case 1:
				week = "星期日";
				break;
			case 2:
				week = "星期一";
				break;
			case 3:
				week = "星期二";
				break;
			case 4:
				week = "星期三";
				break;
			case 5:
				week = "星期四";
				break;
			case 6:
				week = "星期五";
				break;
			case 7:
				week = "星期六";
				break;
			default:
				break;
		}
		return week;
	}

	/**
	 * 日期相差多少天
	 * @param dateString1
	 * @param dateString2
	 * @return
	 */
	public static long subDays(String dateString1, String dateString2) {
		Date date1 = stringToDate(dateString1);
		Date date2 = stringToDate(dateString2);
		return (date2.getTime() - date1.getTime()) / (1000*24*60*60);
	}

    /**
     * 日期相差多少天
     * @param date1
     * @param date2
     * @return
     */
	public static long subDays(Date date1, Date date2) {
	    if (EmptyUtil.isEmpty(date1) || EmptyUtil.isEmpty(date2)) {
	        return -1;
        }
		String dateString1 = dateToString(date1, DATE_FMT_1);
        String dateString2 =  dateToString(date2, DATE_FMT_1);

		Date localDate1 = stringToDate(dateString1);
		Date localDate2 = stringToDate(dateString2);
		return (localDate2.getTime() - localDate1.getTime()) / (1000*24*60*60);
	}

    /**
     * 获取某月的最后一天
     * @param year
     * @param month
     * @return
     */
    public static String getLastDayOfYearMonth(int year,int month) {
        Calendar cal = Calendar.getInstance();
        //设置年份
        cal.set(Calendar.YEAR,year);
        //设置月份
        cal.set(Calendar.MONTH, month-1);
        //获取某月最大天数
        int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
        //设置日历中月份的最大天数
        cal.set(Calendar.DAY_OF_MONTH, lastDay);
        //格式化日期
        SimpleDateFormat sdf = new SimpleDateFormat(DATE_FMT_1);
		return sdf.format(cal.getTime());
    }

}
