package com.glodon.qydata.controller.openapi;


import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsProjectInfoService;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.projectOrContractInfo.ProjectInfoShowVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 * zb_standards_project_info - 主键 前端控制器 -对外接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@RestController
@RequestMapping("/basicInfo/openApi/standards/projectOrContractData")
@Tag(name = "项目信息控制层 -对外接口", description = "项目信息控制层 -对外接口" )
public class StandardsProjectInfoOpenApiController extends BaseController {

    @Autowired
    private IStandardsProjectInfoService standardsProjectInfoService;

    /**
     * 查询项目/合同信息列表(对外接口)
     * @param isShowDelete 是否展示删除
     * @param type  1：项目信息；2：合同信息
     * @param isSkipUserName 是否跳过用户名称赋值（0：不跳过；1：跳过；默认为跳过）
     * @param destEnterpriseId 基于委托-需要查询的企业id
     * @param trustProductSource 基于委托-委托来源 ysbz,gsbz
     * <AUTHOR>
     * @date 2021/10/15 14:30
     */
    @Operation(summary = "查询项目/合同信息列表(对外接口)")
    @GetMapping("/foreign")
    public ResponseVo<List<ProjectInfoShowVo>> getForeign(@RequestParam @Nullable Integer isShowDelete,
                                                          @RequestParam Integer type,
                                                          @RequestParam(required = false) Integer isSkipUserName,
                                                          String destEnterpriseId,
                                                          String trustProductSource,
                                                          @RequestParam(required = false) Integer queryType,
                                                          @RequestParam(required = false) Integer treeType) {

        //查询项目信息组装下参数
        if (Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO.equals(type)) {
            if (queryType == null) {
                queryType = Constants.StandardsProjectOrContractInfoConstants.SHOW_ITEM;
            }
            //treeType标识查询项目信息时候是否需要树结构，1：树结构；2：原始平铺结构
            if (treeType == null) {
                treeType = Constants.StandardsProjectOrContractInfoConstants.SHOW_ORIGINAL;
            }
            if (Constants.StandardsProjectOrContractInfoConstants.SHOW_ITEM.equals(queryType)
                    && Constants.StandardsProjectOrContractInfoConstants.SHOW_TREE.equals(treeType)) {
                throw new BusinessException("不支持按照树结构查询子项");
            }
        }
        String basicInfoType = Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO.equals(type) ? TrustConstants.TYPE_PROJECT : TrustConstants.TYPE_CONTRACTINFO;
        String customerCode = getCustomerCode(basicInfoType, destEnterpriseId, trustProductSource);
        //初始化内置数据
        List<StandardsProjectInfoEntity> list = standardsProjectInfoService.initBuiltInData(customerCode, isShowDelete, type);
        List<ProjectInfoShowVo> projectInfoShowVos = standardsProjectInfoService.convertData(list, type, true, isSkipUserName, queryType, treeType, customerCode);
        return ResponseVo.success(projectInfoShowVos);
    }
}
