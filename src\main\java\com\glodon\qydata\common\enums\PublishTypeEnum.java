package com.glodon.qydata.common.enums;

import com.glodon.qydata.common.constant.OperateConstants;

/**
 * 发布操作类型枚举
 * <AUTHOR>
 * @date 2021/11/11 11:11
 */
public enum PublishTypeEnum {
    CATEGORY(OperateConstants.CATEGORY,"工程分类"),
    PROJECT(OperateConstants.PROJECT,"项目信息"),
    CONTRACT(OperateConstants.CONTRACT,"合同信息"),
    FEATURE(OperateConstants.FEATURE,"工程特征"),
    BUILDING(OperateConstants.BUILDING,"建造标准"),

    MAIN_QUANTITY(OperateConstants.MAIN_QUANTITY,"主要量指标"),
    ;

    private String code;
    private String name;

    PublishTypeEnum(String code, String name){
        this.code = code;
        this.name = name;
    }

    /**
     * @description: 根据类型名称获取类型code
     * @param code
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/11/11 11:11
     */
    public static String getNameByCode(String code){
        for (PublishTypeEnum value : PublishTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getName();
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

}
