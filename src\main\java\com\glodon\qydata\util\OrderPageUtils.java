package com.glodon.qydata.util;

import com.glodon.qydata.dto.FrontOrderPage;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @packageName: com.glodon.qydata.util
 * @className: OrderPageUtils
 * @author: ya<PERSON><PERSON><PERSON> <EMAIL>
 * @date: 2022/8/25 11:00
 * @description: 前端逻辑分页排序
 */
public class OrderPageUtils {


    public static <T extends FrontOrderPage> void addOrderPage(List<T> dataList, int pageNum, int pageSize) {
        if (!CollectionUtils.isEmpty(dataList)) {
            int orderId = 1 + ((pageNum - 1) * pageSize);
            for (FrontOrderPage orderPage : dataList) {
                orderPage.setOrderId(orderId);
                orderId++;
            }
        }
    }

}
