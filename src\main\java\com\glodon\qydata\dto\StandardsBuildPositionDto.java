package com.glodon.qydata.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 企业标准数据-建造标准定位Dto
 */
@Data
public class StandardsBuildPositionDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     *  名称
     */
    private String name;

    /**
     * 排序
     */
    private Integer ord;

    /**
     * 工程分类编码
     */
    private String categoryCode;

    /**
     * 工程分类编码
     */
    private String categoryName;

    /**
     * 对发生档次删除或分类删除的业态列进行颜色标记
     */
    private Integer deleteFlag;

}
