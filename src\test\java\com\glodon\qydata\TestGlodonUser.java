package com.glodon.qydata;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.vo.system.EntInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;




/**
 * 用户相关测试类
 * Created by weijf on 2021/12/23.
 */
@Slf4j
@SpringBootTest
@ExtendWith(SpringExtension.class)
public class TestGlodonUser {

    @Autowired
    private IGlodonUserService glodonUserService;

    String globalId = "6876038217575805866";

    @Test
    public void testEntMemberList() throws Exception{
        JSONArray memberList = glodonUserService.getEntMemberList(globalId,null);
        for (Object o : memberList) {
            //fastjson 用的是 1.2.44  JSONArray 遍历出来的是JSONObject
            //fastjson 用的是 1.2.62 及以上 JSONArray 遍历出来的是LinkedHashMap
            JSONObject obj = (JSONObject) JSONObject.toJSON(o);
            System.err.println("member = " + obj.toJSONString());
            System.out.println(obj.getString("globalId"));
            System.out.println(obj.getString("name"));
            System.out.println(obj.getString("userName"));
        }

    }

    @Test
    public void testEnterpriseId() throws Exception{
        String enterpriseId = glodonUserService.getEnterpriseId(globalId,null);
        System.err.println("enterpriseId = " + enterpriseId);
    }

    @Test
    public void testEntInfo() throws Exception{
        EntInfoVo entInfoVo = glodonUserService.getEntInfo(globalId);
        System.err.println("entInfoVo = " + entInfoVo==null?null: JSONObject.toJSON(entInfoVo));
    }

    @Test
    public void testGetCustomerCode() throws Exception{
        String customerCode = glodonUserService.getCustomerCode(globalId);
        System.err.println("customerCode = " + customerCode);
    }

    @Test
    public void testGetUserName() throws Exception{
        String userName = glodonUserService.getUserName(globalId,null);
        System.err.println("userName = " + userName);
    }

}
