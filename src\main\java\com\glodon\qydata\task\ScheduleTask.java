package com.glodon.qydata.task;

import com.glodon.qydata.common.NotPrivateDeploymentCondition;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.controller.temp.areaRepair.AreaDiffResult;
import com.glodon.qydata.service.system.IAreaService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.SendMessageUtil;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Conditional;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
@ConditionalOnProperty(name = "area-scheduled-enabled", havingValue = "true")
@Conditional(NotPrivateDeploymentCondition.class)
public class ScheduleTask {
    @Resource
    private IAreaService areaService;
    @Resource
    private RedisUtil redisUtil;

    @Scheduled(cron = "0 0 0 ? * SUN")
    public void doCheck() {
        String lockValueString = redisUtil.generateLockValue();
        if (!redisUtil.tryLock(lockValueString, RedisKeyEnum.AREA_COMPARE_LOCK)) {
            return;
        }

        try {
            // 对比差异
            AreaDiffResult areaDiffResult = areaService.compareHandler();
            // 发送消息
            SendMessageUtil.sendMarkdownMessage(areaDiffResult.toMarkdownMsg());
        } catch (Exception e) {
            // 异常处理，并发送异常消息
            String errorMessage = "对比地区差异时发生异常: " + e.getMessage();
            SendMessageUtil.sendMarkdownMessage(errorMessage);
        } finally {
            redisUtil.unlock(lockValueString, RedisKeyEnum.AREA_COMPARE_LOCK);
        }
    }


}
