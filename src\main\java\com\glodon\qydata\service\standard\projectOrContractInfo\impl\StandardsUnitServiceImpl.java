package com.glodon.qydata.service.standard.projectOrContractInfo.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsUnitMapper;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsUnitService;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.projectOrContractInfo.StandardsUnitShowVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * zb_standards_unit - 主键 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Service
public class StandardsUnitServiceImpl extends ServiceImpl<StandardsUnitMapper, StandardsUnitEntity> implements IStandardsUnitService {
    @Autowired
    private StandardsUnitMapper standardsUnitMapper;

    /**
     * 获取项目/工程规模单位集合
     * @throws
     * @param standardsInfoId 项目/合同信息表数据id
     * <AUTHOR>
     * @return {@link List< StandardsUnitEntity>}
     * @date 2021/10/20 15:02
     */
    @Override
    public List<StandardsUnitShowVo> initAndGetUnits(Long standardsInfoId, Integer type, String customerCode) {
        List<StandardsUnitEntity> list = this.list(new LambdaQueryWrapper<StandardsUnitEntity>().eq(StandardsUnitEntity::getZbStandardsProjectInfoId, standardsInfoId)
                .eq(StandardsUnitEntity::getIsDeleted, Constants.StandardsProjectOrContractInfoConstants.IS_NOT_DELETED));

        //初始化企业下单位数据
        if (list.size() == 0) {
            Wrapper<StandardsUnitEntity> initWrapper = new LambdaQueryWrapper<StandardsUnitEntity>()
                    .eq(StandardsUnitEntity::getCustomerCode, Constants.StandardsProjectOrContractInfoConstants.BUILT_IN_DATA_CUSTOMER_CODE)
                    .eq(StandardsUnitEntity::getUnitSourceType, type);
            List<StandardsUnitEntity> initData = this.list(initWrapper);
            for (StandardsUnitEntity data : initData) {
                data.setId(SnowflakeIdUtils.getNextId());
                data.setCustomerCode(customerCode);
                data.setZbStandardsProjectInfoId(standardsInfoId);
            }
            this.saveBatch(initData);
            list.addAll(initData);
        }

        List<StandardsUnitShowVo> returnList = new ArrayList<>(list.size());
        for (StandardsUnitEntity entity : list) {
            StandardsUnitShowVo vo = new StandardsUnitShowVo();
            BeanUtils.copyProperties(entity, vo);
            //设置内置单位标识
            vo.setIsBuiltIn(vo.getCreatorId() == null ? 1 : 0);
            returnList.add(vo);
        }

        return returnList;
    }

    /**
     * 新增项目/工程规模单位
     * @throws
     * @param entity
     * <AUTHOR>
     * @return {@link StandardsUnitEntity < Void>}
     * @date 2021/10/20 15:42
     */
    @Override
    public StandardsUnitEntity addUnit(StandardsUnitEntity entity) {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        entity.setCustomerCode(customerCode);
        entity.setId(SnowflakeIdUtils.getNextId());
        entity.setCreatorId(Long.parseLong(globalId));
        entity.setCreateTime(LocalDateTime.now());
        entity.setIsDeleted(Constants.StandardsProjectOrContractInfoConstants.IS_NOT_DELETED);

        checkDupName(entity, customerCode);

        this.save(entity);

        return this.getById(entity.getId());
    }

    /**
     * 重名校验
     * @throws
     * @param entity
     * @param customerCode
     * <AUTHOR>
     * @return
     * @date 2021/10/20 16:52
     */
    public void checkDupName(StandardsUnitEntity entity, String customerCode) {
        if (StringUtils.isNotBlank(entity.getName()) && entity.getName().length() > Constants.MAX_NAME_LENGTH) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "单位长度不能超过30");
        }
        //同名的数据
        List<StandardsUnitEntity> sameNameList = this.list(new LambdaQueryWrapper<StandardsUnitEntity>().eq(StandardsUnitEntity::getName, entity.getName())
                .eq(StandardsUnitEntity::getCustomerCode, customerCode)
                .eq(StandardsUnitEntity::getUnitSourceType, entity.getUnitSourceType()));
        if (sameNameList.size() > 0) {
            //未删除的同名数据
            List<StandardsUnitEntity> notDelList = sameNameList.parallelStream().filter(obj -> Constants.StandardsProjectOrContractInfoConstants.IS_NOT_DELETED.equals(obj.getIsDeleted()))
                    .collect(Collectors.toList());
            if (notDelList.size() > 0) {
                throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "该单位已存在");
            }
            //同名已删除记录同id处理
            entity.setId(sameNameList.get(0).getId());
            //物理删除已逻辑删除的记录
            this.removeById(entity.getId());
        }
    }

    /**
     * 修改项目/工程规模单位
     * @throws
     * @param entity
     * <AUTHOR>
     * @return {@link StandardsUnitEntity}
     * @date 2021/10/20 16:15
     */
    @Override
    public StandardsUnitEntity updateUnit(StandardsUnitEntity entity) {
        String customerCode = RequestContent.getCustomerCode();
        checkDupName(entity, customerCode);
        this.update(entity, new LambdaUpdateWrapper<StandardsUnitEntity>().eq(StandardsUnitEntity::getId, entity.getId())
                .eq(StandardsUnitEntity::getIsDeleted, Constants.StandardsProjectOrContractInfoConstants.IS_NOT_DELETED));
        return entity;
    }

    /**
     * 删除项目/工程规模单位
     * @throws
     * @param id
     * <AUTHOR>
     * @return
     * @date 2021/10/20 16:41
     */
    @Override
    public void deleteUnit(Long id) {
        StandardsUnitEntity entity = this.getById(id);
        if (entity.getCreatorId() == null) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "系统内置单位不允许删除");
        }
        this.update(new LambdaUpdateWrapper<StandardsUnitEntity>().eq(StandardsUnitEntity::getId, id)
                .set(StandardsUnitEntity::getIsDeleted, Constants.StandardsProjectOrContractInfoConstants.IS_DELETED));
    }

    /**
     * 设置默认选中项目/工程规模单位
     * @throws
     * @param id
     * @param unitName
     * <AUTHOR>
     * @return
     * @date 2021/11/8 9:24
     */
    @Override
    public void setDefaultCheck(Long id, String unitName, String customerCode) {
        this.remove(new LambdaUpdateWrapper<StandardsUnitEntity>()
                .eq(StandardsUnitEntity::getZbStandardsProjectInfoId, id));

        StandardsUnitEntity entity = new StandardsUnitEntity();
        entity.setId(SnowflakeIdUtils.getNextId());
        entity.setName(unitName);
        entity.setZbStandardsProjectInfoId(id);
        entity.setIsDeleted(0);
        entity.setIsChecked(1);
        entity.setCustomerCode(customerCode);
        this.save(entity);
    }

    /**
     　　* @description: 将旧企业项目/合同标准数据复制到新企业项目/合同标准
     　　* @param  Map<Long,Long> 项目信息id关系map  key:oldId   value:newId  oldCustomerCode:原企业编码  newCustomerCode 新企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 9:55
     　　*/
    @Override
    public void initProjectInfoDate(Map<Long,Long> projectInfoIdRelationMap, String oldCustomerCode, String newCustomerCode){
        // 根据原企业编码查询所有单位信息，若为空，不处理
        List<StandardsUnitEntity> standardsUnitEntityList = standardsUnitMapper.selectByCustomerCode(oldCustomerCode);
        if(CollectionUtils.isEmpty(standardsUnitEntityList)){
            return;
        }

        // 处理数据，赋新企业信息，入库
        int size = standardsUnitEntityList.size();
        List<Long> ids = SnowflakeIdUtils.getNextId(size);
        for (int i = 0; i < standardsUnitEntityList.size(); i++) {
            StandardsUnitEntity standardsUnitEntity = standardsUnitEntityList.get(i);
            Long oldProjectInfoId = standardsUnitEntity.getZbStandardsProjectInfoId();
            if(!projectInfoIdRelationMap.containsKey(oldProjectInfoId)){
                continue;
            }
            standardsUnitEntity.setId(ids.get(i));
            standardsUnitEntity.setCustomerCode(newCustomerCode);
            standardsUnitEntity.setZbStandardsProjectInfoId(projectInfoIdRelationMap.get(oldProjectInfoId));
            standardsUnitEntity.setCreatorId(-100L);
            standardsUnitEntity.setCreateTime(LocalDateTime.now());
        }
        if(CollectionUtils.isNotEmpty(standardsUnitEntityList)){
            this.saveBatch(standardsUnitEntityList);
        }
    }

    /**
     　　* @description: 根据企业编码删除所有项目信息单位标准
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:41
     　　*/
    @Override
    public void deleteByCustomerCode(String customerCode){
        standardsUnitMapper.deleteByCustomerCode(customerCode);
    }

    /**
     * 根据项目规模/工程规模id获取默认选中的单位
     * @throws
     * @param infoId
     * <AUTHOR>
     * @return {@link String}
     * @date 2022/2/27 19:05
     */
    @Override
    public String getDefaultCheck(Long infoId, String customerCode) {
        if (infoId == null) {
            throw new BusinessException("参数不能为空");
        }

        StandardsUnitEntity entity = this.getOne(new LambdaQueryWrapper<StandardsUnitEntity>().eq(StandardsUnitEntity::getZbStandardsProjectInfoId, infoId));
        if (entity == null) {
            String defaultUnit = "m2";
            this.setDefaultCheck(infoId, defaultUnit, customerCode);
            return defaultUnit;
        }

        return entity.getName();
    }
}
