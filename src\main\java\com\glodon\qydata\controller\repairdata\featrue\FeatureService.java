package com.glodon.qydata.controller.repairdata.featrue;

import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.service.standard.feature.impl.ProjectFeatureSelfServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * packageName com.glodon.qydata.controller.repairdata.featrue
 *
 * @author: yhl
 * @DateTime: 2023/12/26 9:34
 * @Description:
 */
@Slf4j
@Service
public class FeatureService {

    @Autowired
    private ProjectFeatureSelfServiceImpl featureSelfService;

    @Autowired
    private ProjectFeatureCategoryViewMapper categoryViewMapper;

    @Autowired
    private ProjectFeatureCategoryViewSelfMapper categoryViewSelfMapper;

    @Autowired
    private ProjectFeatureSelfMapper featureSelfMapper;

    //@Transactional(rollbackFor = Exception.class)
    public void repairFeatureCategoryView(List<Long> tradeIds) {
        // 查询工程特征
        List<ProjectFeature> projectFeatures = featureSelfMapper.selectByTradeIds(tradeIds);
        if (CollectionUtils.isEmpty(projectFeatures)) {
            log.info("工程特征为空，不需要修复，tradeIds:{}", tradeIds);
            return;
        }
        // 删除分类视图
        categoryViewMapper.batchDeleteByTradeId(tradeIds);
        categoryViewSelfMapper.batchDeleteByTradeId(tradeIds);
        // 重新生成分类视图
        projectFeatures.forEach(projectFeature -> featureSelfService.addCategoryView(projectFeature, null));

    }

}
