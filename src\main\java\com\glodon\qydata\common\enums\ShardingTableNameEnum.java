package com.glodon.qydata.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * @author: luoml-b
 * @date: 2023/7/14 11:09
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
public enum ShardingTableNameEnum {


    ZB_PROJECT_FEATURE_CATEGORY_VIEW_STANDARDS("zb_project_feature_category_view_standards", 0),

    TB_COMMONPROJCATEGORY_STANDARDS("tb_commonprojcategory_standards", 1);

    private String tableName;

    private int tableType;




    public static ShardingTableNameEnum getShardingTableNameEnumByTableName(String tableName) {
        for (ShardingTableNameEnum value : ShardingTableNameEnum.values()) {
            if (value.getTableName().equals(tableName)) {
                return value;
            }
        }
        return null;
    }
}
