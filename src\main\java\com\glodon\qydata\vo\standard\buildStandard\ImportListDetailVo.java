package com.glodon.qydata.vo.standard.buildStandard;

import com.glodon.qydata.dto.FrontOrderPage;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

/**
 * @packageName: com.glodon.qydata.vo.standard.buildStandard
 * @className: ImportListDetailVo
 * @author: ya<PERSON><PERSON><PERSON> <EMAIL>
 * @date: 2022/8/22 16:48
 * @description: 建造标准详情vo
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ImportListDetailVo extends FrontOrderPage {
    private Integer id;
    private String projectName;
    private String name;
    private String projectCategoryName;
    private String provinceId;
    private String cityId;
    private String districtId;
    private String provinceName;
    private String cityName;
    private String districtName;
    // 包含分类
    private String includeCategory;
    // 测试阶段
    private Integer stage;
    // 测算阶段
    private String stageName;
    // 创建时间
    private String createDate;
    // 归档时间
    private String archiveDate;
    // 总建面单方
    private String unitIndexStructureArea;
    // 产品定位
    private String productPositioning;
    // 包含的分类信息
    private Set<Item> includeCategoryList;

    @Data
    private static class Item {
        private String categoryCode;
        private String categoryName;
        private String categoryPositioning;
    }

}
