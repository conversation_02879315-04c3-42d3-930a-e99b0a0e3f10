package com.glodon.qydata.vo.standard.feature;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 分类视图结果vo
 * <AUTHOR>
 * @date 2021/11/5 8:42
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectFeatureCategoryViewVO implements Serializable {
    private static final long serialVersionUID = -5764318925806567089L;

    /**
     * 工程专业id
     */
    private Long tradeId;

    /**
     * 工程专业名称
     */
    private String tradeName;

    /**
     * 工程专业编码
     */
    private String tradeCode;

    /**
     * 工程特征
     */
    List<ProjectFeatureResultVO> featureList;

    /**
     * 工程专业排序
     */
    private Integer tradeOrd;

    /**
     * 专业是否启用
     */
    private Integer enabled;

}