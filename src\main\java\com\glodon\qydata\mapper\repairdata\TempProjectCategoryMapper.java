package com.glodon.qydata.mapper.repairdata;

import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工程分类修复mapper（非业务使用）
 */
public interface TempProjectCategoryMapper {
    List<String> selectAllCustomerCode();
    Integer selectRepeatRecord(@Param("customerCode") String customerCode);
    String selectErroCategoryCode(@Param("customerCode") String customerCode, @Param("types") List<Integer> types);
    String selectInvalidCategory(@Param("customerCode") String customerCode, @Param("types") List<Integer> types);
    String selectNeedTrimCategory(@Param("customerCode") String customerCode);
    int updateCategory(CommonProjCategory commonProjCategory);
    int syncSelfCategory(CommonProjCategory commonProjCategory);
    void setCategoryInvalid(@Param("customerCode") String customerCode, @Param("ids") List<Integer> ids);
    void setSelfCategoryInvalid(@Param("customerCode") String customerCode, @Param("originIds") List<Integer> originIds);
}
