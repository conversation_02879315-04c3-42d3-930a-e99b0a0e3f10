package com.glodon.qydata.controller.repairdata.common;

import java.util.StringJoiner;
import static com.glodon.qydata.controller.repairdata.common.RepairConst.keyDelimiter;

public abstract class BaseRepairDataHandler<T> {
    /**
     * 根据关键字使用@分隔符合并(参考String.join源码)
     * @param elements
     * @return
     */
    protected String buildDelimiter(CharSequence... elements) {

        StringJoiner joiner = new StringJoiner(keyDelimiter);
        for (CharSequence cs: elements) {
            joiner.add(cs);
        }
        return joiner.toString();
    }


    /**
     * 修复一个企业的数据
     * @param customerCode
     * @return
     */
    public abstract boolean repairData(String customerCode);

    /**
     * 检查数据是否需要修复
     * @param customerCode
     * @return 是否需要修复
     */
    public abstract boolean isNeedRepair(String customerCode);

    /**
     * 对修复后的数据进行验证
     * @return 是否正确
     */
    public abstract boolean checkDataAfterRepair(String customerCode);

    /**
     * 修复系统内置数据
     * @return
     */
    public boolean repairSystemDataErro() {
        return false;
    }
}
