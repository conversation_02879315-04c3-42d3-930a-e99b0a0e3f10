package com.glodon.qydata.controller.temp.category;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.glodon.qydata.entity.repairdata.RepairLog;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.repairdata.TempCommonRepairMapper;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryMapper;
import com.glodon.qydata.mapper.standard.category.CommonProjCategorySelfMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.WHETHER_TRUE;
import static com.glodon.qydata.controller.repairdata.common.RepairVersionConst.repair_version_6;

@Service
@Slf4j
public class TempCategoryService {
    @Resource
    @Qualifier("delSelfExecutor")
    private ThreadPoolTaskExecutor delSelfExecutor;
    @Resource
    private TempCommonRepairMapper tempCommonRepairMapper;
    @Resource
    private CommonProjCategoryMapper commonProjCategoryMapper;
    @Resource
    private CommonProjCategorySelfMapper commonProjCategorySelfMapper;


    public void preCheck(List<String> customerCodeList) {
        log.info("开始preCheck...");
        AtomicInteger atomicInteger = new AtomicInteger();
        // 逐个企业处理
        for (String customerCode : customerCodeList) {
            delSelfExecutor.execute(() -> {
                try {
                    log.info("进度：{}/{}", atomicInteger.getAndIncrement(), customerCodeList.size());
                    if (isNext(customerCode)) {
                        this.singleCustomerCheck(customerCode);
                    }
                } catch (Exception e) {
                    errorLog(customerCode, e.getMessage());
                    log.error("刷新企业：{}的标准数据出错：{}", customerCode, e.getMessage(), e);
                }
            });
        }
        log.info("preCheck完成...");
    }


    public boolean isNext(String customerCode) {
        RepairLog entity = tempCommonRepairMapper.selectOne(
                new LambdaQueryWrapper<RepairLog>()
                        .eq(RepairLog::getCustomerCode, customerCode)
                        .eq(RepairLog::getVersion, repair_version_6));

        if (Objects.nonNull(entity) && Objects.equals(entity.getRepairStatus(), 1)) {
            return false;
        }
        if (Objects.isNull(entity)) {
            RepairLog repairLog = new RepairLog();
            repairLog.setCustomerCode(customerCode);
            repairLog.setVersion(repair_version_6);
            tempCommonRepairMapper.insert(repairLog);
        }
        return true;
    }

    public void errorLog(String customerCode, String errorMsg) {
        RepairLog repairLog = new RepairLog();
        repairLog.setRepairStatus(2);
        repairLog.setErroType(errorMsg);

        tempCommonRepairMapper.update(repairLog, new LambdaQueryWrapper<RepairLog>()
                .eq(RepairLog::getCustomerCode, customerCode)
                .eq(RepairLog::getVersion, repair_version_6));
    }

    public void successLog(String customerCode) {
        RepairLog repairLog = new RepairLog();
        repairLog.setRepairStatus(1);

        tempCommonRepairMapper.update(repairLog, new LambdaQueryWrapper<RepairLog>()
                .eq(RepairLog::getCustomerCode, customerCode)
                .eq(RepairLog::getVersion, repair_version_6));
    }

    /**
     * 1.编码重复
     * 2.编码3位
     * 3.父级已删除，子级未删除
     */
    private void singleCustomerCheck(String customerCode) {
        log.info("开始preCheck企业：{}的标准数据", customerCode);

        List<CommonProjCategory> categoryList = commonProjCategoryMapper.selectAll(customerCode, null, WHETHER_TRUE);
        if (CollUtil.isEmpty(categoryList)) {
            successLog(customerCode);
            return;
        }

        // 校验编码长度
        List<String> illegalLengthCodeList = categoryList.stream()
                .map(CommonProjCategory::getCommonprojcategoryid)
                .filter(code -> code.length() % 3 != 0)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(illegalLengthCodeList)) {
            throw new BusinessException("企业：" + customerCode + "存在不符合3位编码规则的编码：" + illegalLengthCodeList);
        }

        Map<Integer, List<CommonProjCategory>> typeGroup = categoryList.stream().collect(Collectors.groupingBy(CommonProjCategory::getType));

        for (Map.Entry<Integer, List<CommonProjCategory>> entry : typeGroup.entrySet()) {
            Integer type = entry.getKey();
            List<CommonProjCategory> categories = entry.getValue();

            // 校验编码重复
            Map<String, Long> codeCount = categories.stream().collect(Collectors.groupingBy(CommonProjCategory::getCommonprojcategoryid, Collectors.counting()));
            List<String> repeatCode = codeCount.entrySet().stream().filter(x -> x.getValue() > 1).map(x -> "【" + x.getKey() + "】-" + x.getValue()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(repeatCode)) {
                throw new BusinessException("类型:" + type + "存在编码重复：" + repeatCode);
            }

            // 校验父级已删除，子级未删除
            Map<String, CommonProjCategory> codeMap = categories.stream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, category -> category));
            for (CommonProjCategory category : categories) {
                String parentId = getParentId(category.getCommonprojcategoryid());
                if (parentId != null && codeMap.containsKey(parentId) && Boolean.TRUE.equals(codeMap.get(parentId).getIsDeleted()) && !Boolean.TRUE.equals(category.getIsDeleted())) {
                    throw new BusinessException("类型：" + type + "存在父级已删除，子级未删除的情况：父级编码" + parentId + "，子级编码" + category.getCommonprojcategoryid());
                }
            }
        }
        singleCustomerSelfCheck(customerCode);

        successLog(customerCode);
    }

    private void singleCustomerSelfCheck(String customerCode) {
        log.info("开始preCheck-self企业：{}的标准数据", customerCode);

        List<CommonProjCategory> categoryList = commonProjCategorySelfMapper.selectSelfAll(customerCode, null, WHETHER_TRUE);

        if (CollUtil.isEmpty(categoryList)) {
            successLog(customerCode);
            return;
        }

        // 校验编码长度
        List<String> illegalLengthCodeList = categoryList.stream()
                .map(CommonProjCategory::getCommonprojcategoryid)
                .filter(code -> code.length() % 3 != 0)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(illegalLengthCodeList)) {
            throw new BusinessException("self企业：" + customerCode + "存在不符合3位编码规则的编码：" + illegalLengthCodeList);
        }

        Map<Integer, List<CommonProjCategory>> typeGroup = categoryList.stream().collect(Collectors.groupingBy(CommonProjCategory::getType));

        for (Map.Entry<Integer, List<CommonProjCategory>> entry : typeGroup.entrySet()) {
            Integer type = entry.getKey();
            List<CommonProjCategory> categories = entry.getValue();

            // 校验编码重复
            Map<String, Long> codeCount = categories.stream().collect(Collectors.groupingBy(CommonProjCategory::getCommonprojcategoryid, Collectors.counting()));
            List<String> repeatCode = codeCount.entrySet().stream().filter(x -> x.getValue() > 1).map(x -> "【" + x.getKey() + "】-" + x.getValue()).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(repeatCode)) {
                throw new BusinessException("self类型:" + type + "存在编码重复：" + repeatCode);
            }

            // 校验父级已删除，子级未删除
            Map<String, CommonProjCategory> codeMap = categories.stream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, category -> category));
            for (CommonProjCategory category : categories) {
                String parentId = getParentId(category.getCommonprojcategoryid());
                if (parentId != null && codeMap.containsKey(parentId) && Boolean.TRUE.equals(codeMap.get(parentId).getIsDeleted()) && !Boolean.TRUE.equals(category.getIsDeleted())) {
                    throw new BusinessException("self类型：" + type + "存在父级已删除，子级未删除的情况：父级编码" + parentId + "，子级编码" + category.getCommonprojcategoryid());
                }
            }
        }
        successLog(customerCode);
    }

    private String getParentId(String code) {
        if (code.length() <= 3) {
            return null;
        }
        return code.substring(0, code.length() - 3);
    }

    public void repairDelStatus(List<String> customerCodeList) {
        log.info("开始repairDelStatus...");
        AtomicInteger atomicInteger = new AtomicInteger();
        // 逐个企业处理
        for (String customerCode : customerCodeList) {
            delSelfExecutor.execute(() -> {
                try {
                    log.info("进度：{}/{}", atomicInteger.getAndIncrement(), customerCodeList.size());
                    this.repairDelStatus(customerCode);
                    this.repairDelStatusSelf(customerCode);
                } catch (Exception e) {
                    log.error("刷新企业：{}的标准数据出错：{}", customerCode, e.getMessage(), e);
                }
            });
        }
        log.info("repairDelStatus完成...");
    }

    private void repairDelStatus(String customerCode) {
        log.info("开始repairDelStatus企业：{}的标准数据", customerCode);

        List<CommonProjCategory> categoryList = commonProjCategoryMapper.selectAll(customerCode, null, WHETHER_TRUE);
        if (CollUtil.isEmpty(categoryList)) {
            return;
        }

        Map<Integer, List<CommonProjCategory>> typeGroup = categoryList.stream().collect(Collectors.groupingBy(CommonProjCategory::getType));

        List<Integer> updateList = new ArrayList<>();

        for (Map.Entry<Integer, List<CommonProjCategory>> entry : typeGroup.entrySet()) {
            List<CommonProjCategory> categories = entry.getValue();
            categories.sort(Comparator.comparing(CommonProjCategory::getLevel));

            // 校验父级已删除，子级未删除
            Map<String, CommonProjCategory> codeMap = categories.stream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, category -> category));
            for (CommonProjCategory category : categories) {
                String parentId = getParentId(category.getCommonprojcategoryid());
                if (parentId != null && codeMap.containsKey(parentId) && Boolean.TRUE.equals(codeMap.get(parentId).getIsDeleted()) && !Boolean.TRUE.equals(category.getIsDeleted())) {
                    category.setIsDeleted(true);
                    updateList.add(category.getId());
                }
            }
        }
        if (CollUtil.isNotEmpty(updateList)) {
            commonProjCategoryMapper.batchUpdateDelStatus(updateList);
        }
    }

    private void repairDelStatusSelf(String customerCode) {
        log.info("开始repairDelStatus-self企业：{}的标准数据", customerCode);

        List<CommonProjCategory> categoryList = commonProjCategorySelfMapper.selectSelfAll(customerCode, null, WHETHER_TRUE);
        if (CollUtil.isEmpty(categoryList)) {
            return;
        }

        Map<Integer, List<CommonProjCategory>> typeGroup = categoryList.stream().collect(Collectors.groupingBy(CommonProjCategory::getType));

        List<Integer> updateList = new ArrayList<>();

        for (Map.Entry<Integer, List<CommonProjCategory>> entry : typeGroup.entrySet()) {
            List<CommonProjCategory> categories = entry.getValue();
            categories.sort(Comparator.comparing(CommonProjCategory::getLevel));

            // 校验父级已删除，子级未删除
            Map<String, CommonProjCategory> codeMap = categories.stream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, category -> category));
            for (CommonProjCategory category : categories) {
                String parentId = getParentId(category.getCommonprojcategoryid());
                if (parentId != null && codeMap.containsKey(parentId) && Boolean.TRUE.equals(codeMap.get(parentId).getIsDeleted()) && !Boolean.TRUE.equals(category.getIsDeleted())) {
                    category.setIsDeleted(true);
                    updateList.add(category.getId());
                }
            }
        }
        if (CollUtil.isNotEmpty(updateList)) {
            commonProjCategorySelfMapper.batchUpdateDelStatus(updateList);
        }
    }
}
