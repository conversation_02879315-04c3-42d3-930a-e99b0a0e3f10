package com.glodon.qydata.controller.standard.buildStandard;

import com.github.pagehelper.PageInfo;
import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.dto.StandardDetailDto;
import com.glodon.qydata.dto.ZbCategoryDicDto;
import com.glodon.qydata.dto.ZbProjectStandardDetailColDto;
import com.glodon.qydata.dto.ZbProjectStandardDetailTreeDto;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.entity.zbsq.Item;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.standard.buildStandard.IZbProjectStandardService;
import com.glodon.qydata.service.subjectdivision.SubjectDivisionService;
import com.glodon.qydata.util.PublishLockerUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.buildStandard.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 　　* @description: 项目标准控制层
 * 　　* <AUTHOR>
 * 　　* @date 2021/8/16 16:55
 *
 */
@RestController
@RequestMapping("/basicInfo/standards/buildStandard")
@Tag(name = "项目标准控制层", description = "项目标准控制层")
public class ZbProjectStandardController extends BaseController {

    @Autowired
    private IZbProjectStandardService zbProjectStandardService;

    @Autowired
    private PublishLockerUtil publishLockerUtil;

    @Autowired
    private SubjectDivisionService subjectDivisionService;

    //------------------------------------------------------以下是企业级别-------------------------------------------------------------------

    /**
     * 获取企业下的建造标准列表--企业级
     *
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * @return {@link ResponseVo< List<  ZbProjectStandard >>}
     * @throws
     * <AUTHOR>
     * @date 2021/8/17 9:32
     */
    @Operation(summary = "获取企业下的建造标准列表--企业级")
    @GetMapping("/standardList")
    public ResponseVo<List<ZbProjectStandard>> getStandardList(@RequestParam @Nullable Integer isShowDelete) throws BusinessException {
        String customerCode = getCustomerCode();
        return ResponseVo.success(zbProjectStandardService.standardList(customerCode, isShowDelete, Constants.ORDER_DESC, false));
    }

    /**
     * 根据建造标准id获取建造标准树形表格--企业级
     *
     * @param standardId
     * @param dataType   返回数据的结构（0：原始结构；1：树结构；默认值为 0 原始结构）
     * @return {@link ResponseVo<StandardDetailDto>>}
     * <AUTHOR>
     * @date 2021/8/18 17:06
     */
    @Operation(summary = "根据建造标准id获取建造标准树形表格--企业级")
    @GetMapping("/data/{buildStandardId}")
    public ResponseVo<StandardDetailDto> getDetailTree(@PathVariable("buildStandardId") Long standardId, @RequestParam @Nullable Integer dataType) {
        return ResponseVo.success(zbProjectStandardService.getDetailList(standardId, dataType, false, false));
    }

    /**
     * 　　* @description: 获取所有业态数据，{用户标准新建之前的业态字典信息获取}--企业级
     * 　　* @param  无参
     * 　　* <AUTHOR>
     * 　　* @date 2021/8/18 10:14
     *
     */
    @Operation(summary = "获取所有业态数据，{用户标准新建之前的业态字典信息获取}--企业级")
    @GetMapping("/categories")
    public ResponseVo<List<ZbCategoryDicDto>> getCategories() {
        String customerCode = getCustomerCode();
        return ResponseVo.success(zbProjectStandardService.getCategoryDicList(customerCode));
    }

    /**
     * 根据业态编码获取其下的建造标准--企业级
     *
     * @param code
     * @return {@link ResponseVo< List< ZbProjectStandard>>}
     * @throws
     * <AUTHOR>
     * @date 2022/1/18 10:15
     */
    @Operation(summary = "根据业态编码获取其下的建造标准--企业级")
    @GetMapping("/categoryCode")
    public ResponseVo<List<ZbProjectStandard>> getByCategoryCode(@RequestParam @NotNull String code) {
        String customerCode = getCustomerCode();
        return ResponseVo.success(zbProjectStandardService.getByCategoryCode(customerCode, code, false));
    }

    /**
     * 导入Excel内置数据（非业务接口，可废弃）
     *
     * @param file
     * @return
     * @throws
     * <AUTHOR>
     * @date 2021/11/11 10:25
     */
    @Operation(summary = "导入Excel内置数据")
    @PostMapping("/import")
    public void importBuiltInStandard(MultipartFile file) {
        zbProjectStandardService.importBuiltInStandard(file);
    }

    /**
     * 迁移数据-将历史数据迁移到标准说明（非业务接口，可废弃）
     *
     * @return
     */
    @GetMapping("/historyDetailToDesc")
    public ResponseVo historyDetailToDesc(String type) {
        if (StringUtils.isBlank(type)) {
            return ResponseVo.error("type不能为空");
        }
        zbProjectStandardService.historyDetailToDesc(type);
        return ResponseVo.success();
    }
    //------------------------------------------------------以下是个人级别-------------------------------------------------------------------

    /**
     * 获取当前用户暂存的建造标准列表--个人级
     *
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * @return {@link ResponseVo< List<  ZbProjectStandard >>}
     * @throws
     * <AUTHOR>
     * @date 2021/8/17 9:32
     */
    @Operation(summary = "获取企业下的建造标准列表--个人级")
    @Permission
    @GetMapping("/standardList/self")
    public ResponseVo<List<ZbProjectStandard>> getStandardSelfList(@RequestParam @Nullable Integer isShowDelete) throws BusinessException {
        String customerCode = getCustomerCode();
        String globalId = getGlobalId();
        publishLockerUtil.lock(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.getSelfStandardList(customerCode, isShowDelete));
    }

    /**
     * 根据建造标准id获取建造标准树形表格--个人级
     *
     * @param standardId
     * @param dataType   返回数据的结构（0：原始结构；1：树结构；默认值为 0 原始结构）
     * @return {@link ResponseVo< List<  ZbProjectStandardDetailTreeDto >>}
     * @throws
     * <AUTHOR>
     * @date 2021/8/18 17:06
     */
    @Operation(summary = "根据建造标准id获取建造标准树形表格--个人级")
    @Permission
    @GetMapping("/data/self/{buildStandardId}")
    public ResponseVo<StandardDetailDto> getSelfDetailTree(@PathVariable("buildStandardId") Long standardId,
                                                           @RequestParam @Nullable Integer dataType) throws BusinessException {
        return ResponseVo.success(zbProjectStandardService.getSelfDetailTree(standardId, dataType));
    }

    /**
     * 　　* @description: 根据标准id删除建造标准--个人级
     * 　　* @param
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR> 　　* @date 2021/8/16 16:56
     *
     */
    @Operation(summary = "根据标准id删除建造标准--个人级")
    @Permission
    @DeleteMapping("/{buildStandardId}")
    public ResponseVo delete(@PathVariable("buildStandardId") Long buildStandardId) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        zbProjectStandardService.deleteStandardRecord(buildStandardId);
        return ResponseVo.success();
    }

    /**
     * 复制建造标准--个人级
     *
     * @param zbProjectStandardBo
     * @return {@link ResponseVo<  ZbProjectStandardDetail >}
     * @throws
     * <AUTHOR>
     * @date 2021/8/17 15:11
     */
    @Operation(summary = "复制建造标准--个人级")
    @Permission
    @PostMapping("/copy")
    public ResponseVo<ZbProjectStandard> copy(@RequestBody @Validated ZbCopyProjectStandardBo zbProjectStandardBo) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.copy(zbProjectStandardBo));
    }

    /**
     * 　　* @description: 修改建造标准--个人级
     * 　　* @param
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR> 　　* @date 2021/8/16 17:07
     *
     */
    @Operation(summary = "修改建造标准--个人级")
    @Permission
    @PostMapping
    public ResponseVo<ZbProjectStandard> update(@RequestBody @Validated ZbProjectStandardBo zbProjectStandardBo) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.updateStandardRecord(zbProjectStandardBo));
    }

    /**
     * 　　* @description: 新增建造标准--个人级
     * 　　* @param
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR> 　　* @date 2021/8/16 17:07
     *
     */
    @Operation(summary = "新增建造标准--个人级")
    @Permission
    @PutMapping
    public ResponseVo<ZbProjectStandard> add(@RequestBody @Validated ZbProjectStandardBo zbProjectStandardBo) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.addStandardRecord(zbProjectStandardBo));
    }

    /**
     * 　　* @description: 新增修改建造标准-名称判重--个人级
     * 　　* @param id:修改接口的名称判重，id会存在，排除此id标准的名称再进行判重
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/8/16 17:07
     *
     */
    @Operation(summary = "新增修改建造标准-名称判重--个人级")
    @Permission
    @GetMapping("/checkName")
    public ResponseVo checkedName(@RequestParam("name") String name, @RequestParam(name = "id", required = false) Long id) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.checkedName(name, id));
    }

    /**
     * 插入建造标准行数据--个人级
     *
     * @param dto
     * @return {@link ResponseVo<  ZbProjectStandardDetail >}
     * @throws
     * <AUTHOR>
     * @date 2021/8/17 15:11
     */
    @Operation(summary = "插入建造标准行数据--个人级")
    @Permission
    @PostMapping("/data/{buildStandardId}")
    public ResponseVo<ZbProjectStandardDetail> createStandardDetailCol(@PathVariable("buildStandardId") Long standardId, @RequestBody ZbProjectStandardDetailColDto dto) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        if (zbProjectStandardService.checkLength(dto.getName(), dto.getDesc())) {
            return ResponseVo.error("名称或描述示例长度超出限制");
        }
        return ResponseVo.success(zbProjectStandardService.createStandardDetailCol(standardId, dto));
    }

    /**
     * @param deleteVo
     * @return com.glodon.qydata.vo.common.ResponseVo
     * @description: 批量删除建造标准细则行数据和标准说明--个人级
     * <AUTHOR>
     * @date 2022/8/5 10:06
     */
    @Operation(summary = "批量删除建造标准细则行数据和标准说明--个人级")
    @Permission
    @DeleteMapping("/data")
    public ResponseVo deleteStandardDetailCol(@RequestBody @Validated ZbProjectStandardDeleteVo deleteVo) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        zbProjectStandardService.deleteStandardDetailAndDesc(deleteVo);
        return ResponseVo.success();
    }

    /**
     * 修改建造标准行数据--个人级
     *
     * @param standardId
     * @return {@link ResponseVo<  ZbProjectStandardDetail >}
     * @throws
     * <AUTHOR>
     * @date 2021/8/18 10:34
     */
    @Operation(summary = "修改建造标准行数据--个人级")
    @Permission
    @PutMapping("/data/{buildStandardId}")
    public ResponseVo<ZbProjectStandardDetail> updateStandardDetailCol(@PathVariable("buildStandardId") Long standardId,
                                                                       @RequestBody ZbProjectStandardDetail detail) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        if (zbProjectStandardService.checkLength(detail.getName(), detail.getDesc())) {
            return ResponseVo.error("名称或描述示例长度超出限制");
        }
        boolean isEmptySubject = ("-1".equals(detail.getItemDivisionSubjectId()) || StringUtils.isBlank(detail.getItemDivisionSubjectId())) &&
                StringUtils.isBlank(detail.getItemDivisionSubjectName());
        if (isEmptySubject) {
            detail.setItemDivisionSubjectId(null);
            detail.setItemDivisionSubjectName(null);
        }
        return ResponseVo.success(zbProjectStandardService.updateStandardDetailCol(standardId, detail));
    }

    /**
     * 建造标准行数据上下移接口--个人级
     *
     * @param flag   上下移标识，1：上移，2：下移
     * @param detail
     * @return {@link ResponseVo< Void>}
     * @throws
     * <AUTHOR>
     * @date 2021/10/26 14:28
     */
    @Operation(summary = "建造标准行数据上下移接口--个人级")
    @Permission
    @PutMapping("/data/move/{flag}")
    public ResponseVo<List<ZbProjectStandardDetailTreeDto>> moveUpDown(@PathVariable Integer flag, @RequestBody ZbProjectStandardDetail detail) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.moveUpDown(flag, detail));
    }

    /**
     * 　　* @description: 获取所有业态数据，{用户标准新建之前的业态字典信息获取}--个人级
     * 　　* @param  无参
     * 　　* <AUTHOR>
     * 　　* @date 2021/8/18 10:14
     *
     */
    @Operation(summary = "获取所有业态数据，{用户标准新建之前的业态字典信息获取}--个人级")
    @Permission
    @GetMapping("/categories/self")
    public ResponseVo<List<ZbCategoryDicDto>> getSelfCategories() throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.getSelfCategoryDicList());
    }

    /**
     * @param addVo
     * @return com.glodon.qydata.vo.common.ResponseVo<com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc>
     * @description: 新增标准说明--个人级
     * <AUTHOR>
     * @date 2022/8/5 17:18
     */
    @Operation(summary = "新增标准说明--个人级")
    @Permission
    @PostMapping("/data/desc")
    public ResponseVo<StandardsBuildStandardDetailDescVo> addDesc(@RequestBody @Validated DetailDescAddVo addVo) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.addDesc(addVo));
    }

    @Operation(summary = "编辑标准说明--个人级")
    @Permission
    @PutMapping("/data/desc")
    public ResponseVo<StandardsBuildStandardDetailDescVo> updateDesc(@RequestBody @Validated DetailDescUpdateVo updateVo) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.updateDesc(updateVo));
    }

    @Operation(summary = "标准说明上下移--个人级")
    @Permission
    @PutMapping("/data/desc/move/{flag}")
    public ResponseVo<List<ZbStandardsBuildStandardDetailDesc>> descMoveUpDown(@PathVariable Integer flag,
                                                                               @RequestParam @NotNull Long descId,
                                                                               @RequestParam @NotNull Long standardDetailId) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success(zbProjectStandardService.descMoveUpDown(flag, descId, standardDetailId));
    }

    @Operation(summary = "发布")
    @Permission
    @PostMapping("/publish")
    public ResponseVo<Void> publish() throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        zbProjectStandardService.publish(customerCode, globalId);

        publishLockerUtil.unLock(OperateConstants.BUILDING, globalId, customerCode);
        return ResponseVo.success();
    }

    @Operation(summary = "根据历史文件导入建造标准")
    @Permission
    @PostMapping("/importFromHistoricalFile")
    public ResponseVo importFromHistoricalFile(@RequestBody @Validated ImportFromHistoricalFileVo importVo) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        String enterpriseId = getEnterpriseId();
        zbProjectStandardService.importFromHistoricalFile(importVo, enterpriseId);
        return ResponseVo.success();
    }

    @Operation(summary = "建造标准-新增-列表-调用中台能力")
    @Permission
    @PostMapping("/import/list")
    public ResponseVo<PageInfo<ImportListDetailVo>> importList(@RequestBody ImportListVo listVo) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        String enterpriseId = getEnterpriseId();
        return ResponseVo.success(zbProjectStandardService.importList(listVo, enterpriseId));
    }

    @Operation(summary = "建造标准-获取项目划分标准")
    @GetMapping("/getSubjectDivisionItemList")
    public ResponseVo<List<Item>> getProjectDivisionItemListByCategoryCode(@RequestParam @NotNull String categoryCode) {
        return ResponseVo.success(subjectDivisionService.getTemplateByCategoryWithInit(categoryCode));
    }
}
