package com.glodon.qydata.entity.standard.feature;

import com.glodon.qydata.entity.system.QYFlag;
import com.glodon.qydata.util.mover.Movable;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;


/**
 * @description: zb_project_feature_standards
 * <AUTHOR>
 * @date 2021/11/5 8:41
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectFeature extends QYFlag implements Serializable, Movable {
    private static final long serialVersionUID = 5210501216610291526L;
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 工程特征名称类型表主键id zb_standards_expression
     */
    private Long expressionId;

    /**
     * 特征层级 与类别对应关系：项目信息【1】、单项信息【2】、项目信息，单项信息【1,2】
     */
    @NotEmpty(message = "特征层级不能为空")
    private String featureLevel;

    /**
     * 是否同步：1同步；0不同步；特征层级为 项目信息，单项信息时，是否同步列存在按钮；其他状态，该列为‘-’
     */
    private Integer isSync;

    /**
     * 工程分类 工程分类:json字符串 {"projectType":"[["001","001001","001001002"],["001","001001","001001003"],["001","001001","001001004"],["001","001001","001001005"],["001","001002","001002001"],["001","001002","001002002"],["001","001002","001002003"],["001","001003","001003002"],["001","001003","001003003"],["001","001003","001003004"],["001","001004","001004001"],["001","001004","001004002"],["001","001004","001004003"],["001","001004","001004004"],["001","001005"],["001","001006","001006001"],["001","001006","001006002"],["001","001006","001006003"],["001","001006","001006004"],["001","001006","001006005"],["001","001006","001006006"],["001","001006","001006007"],["001","001007"],["002","002001","002001001"],["002","002001","002001002"],["002","002001","002001003"],["002","002002"],["002","002003"],["002","002004"],["002","002005"],["002","002006","002006001"],["002","002006","002006002"],["002","002006","002006003"],["002","002007"]]"}
     */
    @NotEmpty(message = "工程分类不能为空")
    private String projectType;

    /**
     * 是否启用：1启用；0未启用；（系统：默认启用；用户新建：新建工程特征【是否启用】状态，启用后的工程特征，才可在【新增工程特征】时搜索）
     */
    private Integer isUsing;

    /**
     * 是否默认：：开启【1】、关闭【0】
     */
    private Integer isDefault;

    /**
     * 是否必填：1 必填；0 不必填
     */
    private Integer isRequired;

    /**
     * 注释，最大300个字符
     */
    private String remark;

    /**
     * 是否由系统工程特征复制而来：1 是； 0 否
     */
    private Integer isFromSystem;

    /**
     * 源工程特征ID：对应zb_project_feature的主键id
     */
    private Long zbProjectFeatureId;

    /**
     * 企业编码
     */
    private String customerCode;

    /**
     * 是否删除：1已删除；0未删除
     */
    private Integer isDeleted;

    /**
     * 创建人ID（广联云用户体系）：-100为系统内置
     */
    private Long createGlobalId;

    /**
     * 更新人ID（广联达用户体系）
     */
    private Long updateGlobalId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 专业工程字典id，包含"-、建筑装饰工程、安装工程、园林绿化工程、仿古建筑工程、市政工程、矿山工程、构筑物工程、城市轨道交通工程、爆破工程、其他"
     */
    private Integer productProType;

    /**
     * 是否计算口径：1是；0否
     */
    private Integer isExpression;

    /**
     * 专业视图排序
     */
    private Integer ordTrade;

    /**
     * 工程专业id
     */
    private Long tradeId;

    /**
     * 专业工程名称
     */
    private String tradeName;

    /**
     * 是否为筛选项，0：不是，1：是
     */
    private Integer isSearchCondition;

    /**
     * _self 特有 关联字段
     */
    private Long originId;

    private Integer type;

    /**
     * 工程特征名称
     */
    private String name;

    /**
     * 数据类型:值为dictionary表中type_code字段等于data_type所对应的code字段的值
     */
    private String typeCode;

    /**
     * 工程特征对应的选项，与数据类型连用,数据格式为json字符串：{id:xxx，name：xxx,sort: xxx}
     */
    private String option;

    /**
     * 单位
     */
    private String unit;

    /**
     * 口径编码
     */
    private String expressionCode;

    /**
     * 是否计算口径内置，2022年12月将计算口径拆分到项目信息和工程特征的内置里，对这批内置，页面上有一下特殊的编辑限制，用此字段区分
     */
    private Integer isExpressionDefault;

    /**
     * 工程特征表expression_id字段修复前的备份字段
     */
    private Long repairExpressionId;
    /**
     * 是否无效 1 无效，空为有效
     */
    private Integer invalid;

    private String selfGlobalId;

    @Override
    public Long getUniqueIdentity() {
        return id;
    }

    @Override
    public Integer getOrdValue() {
        return ordTrade;
    }

    @Override
    public void setOrdValue(Integer ordValue) {
        this.ordTrade = ordValue;
    }
}