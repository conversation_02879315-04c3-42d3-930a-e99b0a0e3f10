package com.glodon.qydata.util;

import com.glodon.qydata.common.constant.BusinessConstants;
import lombok.extern.slf4j.Slf4j;
import org.lionsoul.ip2region.DataBlock;
import org.lionsoul.ip2region.DbConfig;
import org.lionsoul.ip2region.DbSearcher;
import org.lionsoul.ip2region.Util;

import jakarta.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.lang.reflect.Method;

@Slf4j
public class IPUtils {

	/**
	 * 获取省份
	 * @param ip
	 * @param dbPath
	 * @return
	 */
	public static String getProvinceByIp(String ip,String dbPath) {
		String cityName = getCityInfo(ip,dbPath);
		if(cityName == null ){
			return cityName;
		}
		String[] cityNames = cityName.split("\\|");
		if(cityNames.length<2){
			return cityNames[0];
		}else{
			return cityNames[1];
		}
	}

	public static String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");
		if (ipAddress == null || ipAddress.length() == 0
				|| BusinessConstants.UNKNOWN.equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0
				|| BusinessConstants.UNKNOWN.equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0
				|| BusinessConstants.UNKNOWN.equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("HTTP_CLIENT_IP");
		}
		if (ipAddress == null || ipAddress.length() == 0
				|| BusinessConstants.UNKNOWN.equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("HTTP_X_FORWARDED_FOR");
		}
		if (ipAddress == null || ipAddress.length() == 0
				|| BusinessConstants.UNKNOWN.equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getRemoteAddr();
		}
		// 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15 && ipAddress.contains(",")) { // "***.***.***.***".length()
			// = 15
			ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
		}
		return ipAddress;
	}

	/**
	 * 根据IP获取城市信息
	 * db文件下载地址，https://gitee.com/lionsoul/ip2region/tree/master/data 下载下来后解压，放在指定目录下
	 * @param ip
	 * @return
	 */
	public static String getCityInfo(String ip,String dbPath) {

		if("127.0.0.1".equals(ip) || "0:0:0:0:0:0:0:1".equals(ip)){
			return "内网IP";
		}

		File file = new File(dbPath);
		if (!file.exists()) {
			log.error("无效的ip2region.db文件，路径为：{}",dbPath);
		}
		//查询算法 B-tree
		DbSearcher searcher = null;
		try {
			int algorithm = DbSearcher.BTREE_ALGORITHM;
			DbConfig dbConfig = new DbConfig();
			searcher = new DbSearcher(dbConfig, dbPath);
			Method method = null;
			switch (algorithm) {
				case DbSearcher.BTREE_ALGORITHM:
					method = searcher.getClass().getMethod("btreeSearch", String.class);
					break;
				case DbSearcher.BINARY_ALGORITHM:
					method = searcher.getClass().getMethod("binarySearch", String.class);
					break;
				case DbSearcher.MEMORY_ALGORITYM:
					method = searcher.getClass().getMethod("memorySearch", String.class);
					break;
				default:
			}
			if (!Util.isIpAddress(ip)) {
				log.error("无效的IP地址：{}",ip);
			}
			DataBlock dataBlock = (DataBlock) method.invoke(searcher, ip);

			return formatRegion(dataBlock.getRegion());
		} catch (Exception e) {
			log.info("查询ip出错");
		}finally {
			try {
				if (searcher!=null){
					searcher.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		return null;
	}

	/**
	 * 格式化城市
	 示例：
	 中国|0|四川|成都|电信
	 中国|0|香港|0|阿里云
	 美国|0|北卡罗来纳|0|0
	 英国|0|伦敦|伦敦|0
	 美国|0|宾夕法尼亚|0|康卡斯特
	 西班牙|0|0|0|美国电话电报
	 * @param region
	 * @return
	 */
	private static String formatRegion(String region) {
		if(org.apache.commons.lang3.StringUtils.isEmpty(region)){
			return null;
		}
		String[] regions = region.split("\\|");
		StringBuffer sb = null;
		for (String str : regions) {
			if(org.apache.commons.lang3.StringUtils.isEmpty(str) || "0".equals(str)){
				continue;
			}
			if(sb == null){
				sb = new StringBuffer();
				sb.append(str);
			}else{
				if(sb.indexOf(str)!= -1){
					continue;
				}
				sb.append("|").append(str);
			}
		}
		return sb==null?null:sb.toString();
	}


	/**
	 * 判断是否是IE浏览器
	 * @param request
	 * @return
	 */
	public static boolean isMSBrowser(HttpServletRequest request) {
		String[] ieBrowserSignals = {"MSIE", "Trident", "Edge"};
		String userAgent = request.getHeader("User-Agent");
		for (String signal : ieBrowserSignals) {
			if (userAgent.contains(signal)) {
				return true;
			}
		}
		return false;
	}

}