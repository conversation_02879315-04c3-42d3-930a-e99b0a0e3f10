package com.glodon.qydata.controller.temp.feature;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.entity.repairdata.RepairLog;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.repairdata.TempCommonRepairMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.util.CategoryUtil;
import com.glodon.qydata.util.PublishLockerUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.BusinessConstants.PROJECT_TYPE_CONVERT_NEW;
import static com.glodon.qydata.common.constant.BusinessConstants.PROJECT_TYPE_CONVERT_OLD;
import static com.glodon.qydata.common.constant.Constants.CategoryConstants.WHETHER_FALSE;
import static com.glodon.qydata.common.constant.Constants.SYSTEM_ADMIN;
import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE;
import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.WHETHER_TRUE;
import static com.glodon.qydata.controller.repairdata.common.RepairVersionConst.repair_version_5;


@Service
@Slf4j
public class TempFeatureService {

    @Autowired
    @Qualifier("delSelfExecutor")
    private ThreadPoolTaskExecutor delSelfExecutor;

    @Autowired
    private PublishLockerUtil publishLockerUtil;

    @Autowired
    private TempCommonRepairMapper tempCommonRepairMapper;

    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;

    @Autowired
    private CommonProjCategoryService commonProjCategoryService;

    @Autowired
    private TempFeatureSaveService tempFeatureSaveService;

    @Autowired
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;


    public void preCheck(Set<String> customerCodeList){
       log.info("开始preCheck...");
        AtomicInteger atomicInteger = new AtomicInteger();
        // 逐个企业处理
       for (String customerCode : customerCodeList) {
           delSelfExecutor.execute(() -> {
               try {
                   log.info("进度：{}/{}", atomicInteger.getAndIncrement(), customerCodeList.size());
                   if(isNext(customerCode)){
                       this.singleCustomerCheck(customerCode);
                   }
               }catch (Exception e){
                   errorLog(customerCode, e.getMessage());
                   log.error("刷新企业：{}的标准数据出错：{}", customerCode, e.getMessage(), e);
               }
           });
       }
       log.info("preCheck完成...");
   }

   public void projectTypeJsonUpgrade(Set<String> customerCodeList){
       log.info("开始刷新标准数据...");
       AtomicInteger atomicInteger = new AtomicInteger();

       // 逐个企业处理
       for (String customerCode : customerCodeList) {
           delSelfExecutor.execute(() -> {
               try {
                   log.info("进度：{}/{}", atomicInteger.getAndIncrement(), customerCodeList.size());
                   publishLockerUtil.lock(OperateConstants.FEATURE, SYSTEM_ADMIN, customerCode);

                   if(isNext(customerCode)){
                       this.singleCustomerRefresh(customerCode);
                   }
               }catch (Exception e){
                   errorLog(customerCode, e.getMessage());
                   log.error("刷新企业：{}的标准数据出错：{}", customerCode, e.getMessage(), e);
               }finally {
                   publishLockerUtil.unLock(OperateConstants.FEATURE, SYSTEM_ADMIN, customerCode);
               }
           });
       }
       log.info("刷新标准数据完成...");
   }

    public void systemDataProjectTypeJsonUpgrade(){
        log.info("开始刷新企业：的标准数据");

        List<ProjectFeature> projectFeatures = projectFeatureMapper.selectByCustomeCode(SYSTEM_CUSTOMER_CODE, null, WHETHER_TRUE);
        if (CollUtil.isEmpty(projectFeatures)){
            return;
        }

        Set<String> objects = new HashSet<>();
        List<ProjectFeature> featureUpdateList = Lists.newArrayList();
        for (ProjectFeature feature : projectFeatures) {
            String projectType = feature.getProjectType();
            JSONObject jsonObject = JSONObject.parseObject(projectType);
            JSONArray projectTypeArr = jsonObject.getJSONArray(BusinessConstants.PROJECT_TYPE);

            List<String> projectTypeList = projectTypeArr.stream().map(o -> ((JSONArray) o).get(0).toString()).distinct().collect(Collectors.toList());
            jsonObject.put(BusinessConstants.PROJECT_TYPE, projectTypeList);

            objects.addAll(projectTypeList);

            ProjectFeature featureUpdate = new ProjectFeature();
            featureUpdate.setId(feature.getId());
            featureUpdate.setProjectType(jsonObject.toJSONString());
            featureUpdateList.add(featureUpdate);
        }

        // 插入数据库
        tempFeatureSaveService.saveToDb(SYSTEM_CUSTOMER_CODE, featureUpdateList, null);
    }

    public boolean isNext(String customerCode) {
        RepairLog entity = tempCommonRepairMapper.selectOne(
                new LambdaQueryWrapper<RepairLog>()
                        .eq(RepairLog::getCustomerCode, customerCode)
                        .eq(RepairLog::getVersion, repair_version_5));

        if(Objects.nonNull(entity) && Objects.equals(entity.getRepairStatus(), 1)) {
            return false;
        }
        if(Objects.isNull(entity)) {
            RepairLog repairLog = new RepairLog();
            repairLog.setCustomerCode(customerCode);
            repairLog.setVersion(repair_version_5);
            tempCommonRepairMapper.insert(repairLog);
        }
        return true;
    }

    public void errorLog(String customerCode, String errorMsg) {
        RepairLog repairLog = new RepairLog();
        repairLog.setRepairStatus(2);
        repairLog.setErroType(errorMsg);

        tempCommonRepairMapper.update(repairLog, new LambdaQueryWrapper<RepairLog>()
                .eq(RepairLog::getCustomerCode, customerCode)
                .eq(RepairLog::getVersion, repair_version_5));
    }

    public void successLog(String customerCode) {
        RepairLog repairLog = new RepairLog();
        repairLog.setRepairStatus(1);

        tempCommonRepairMapper.update(repairLog, new LambdaQueryWrapper<RepairLog>()
                .eq(RepairLog::getCustomerCode, customerCode)
                .eq(RepairLog::getVersion, repair_version_5));
    }

    /**
     * 1.查询的工程分类包含已删除
     * 2.工程分类被物理删除
     * 3.009刷数据增加过层级
     * 4.增加层级的数据
     * 5.一级分类被物理删除
     * 6.project_type里的数据有重复
     * 7.勾选子级，子级被物理删除
     * 8.自测的新结构数据
     * @param customerCode
     */
    private void singleCustomerCheck(String customerCode) {
        log.info("开始preCheck企业：{}的标准数据", customerCode);

        List<ProjectFeature> projectFeatures = projectFeatureMapper.selectByCustomeCode(customerCode, null, WHETHER_TRUE);
        if (CollUtil.isEmpty(projectFeatures)){
            return;
        }

        Map<Integer, List<ProjectFeature>> typeGroup = projectFeatures.stream().collect(Collectors.groupingBy(ProjectFeature::getType));
        for (Map.Entry<Integer, List<ProjectFeature>> entry : typeGroup.entrySet()) {
            Integer type = entry.getKey();
            List<CommonProjCategory> categoryList = commonProjCategoryService.getCategoryListTree(customerCode, type, WHETHER_TRUE);
            if (CollUtil.isEmpty(categoryList)){
                continue;
            }
            List<String> categoryCodeList = categoryList.stream().map(CommonProjCategory::getCommonprojcategoryid).collect(Collectors.toList());
            List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(categoryList);

            for (ProjectFeature feature : entry.getValue()) {
                String newProjectType = CategoryUtil.projectTypeConvert(categoryListTree, feature.getProjectType(), PROJECT_TYPE_CONVERT_NEW);
                String oldProjectType = CategoryUtil.projectTypeConvert(categoryListTree, newProjectType, PROJECT_TYPE_CONVERT_OLD);

//                if (Objects.equals(4793113931806670907L, feature.getId())){
//                    continue;
//                }
//                if (Objects.equals(4823719199040864382L, feature.getId())){
//                    continue;
//                }
//                if (Objects.equals(4824284564147929130L, feature.getId())){
//                    continue;
//                }

                if (!Objects.equals(oldProjectType, feature.getProjectType())){
                    JSONObject dbJsonObject = JSONObject.parseObject(feature.getProjectType());
                    JSONArray dbProjectTypeArr = dbJsonObject.getJSONArray(BusinessConstants.PROJECT_TYPE);

                    JSONObject convertJsonObject = JSONObject.parseObject(oldProjectType);
                    JSONArray convertProjectTypeArr = convertJsonObject.getJSONArray(BusinessConstants.PROJECT_TYPE);

                    boolean flag = false;
                    Iterator<Object> dbIterator = dbProjectTypeArr.iterator();
                    while (dbIterator.hasNext()){
                        Object obj = dbIterator.next();
                        JSONArray jsonArray = (JSONArray) obj;
                        Object last = CollUtil.getLast(jsonArray);
                        // 遍历dbProjectTypeArr，如果最后一个元素不在categoryCodeList中，则移除
                        if (!categoryCodeList.contains(last.toString())) {
                            dbIterator.remove();
                        }else if (Objects.equals(last.toString(), "009001")){
                            dbIterator.remove();
                            flag = true;
                        }
                    }

                    Iterator<Object> converIterator = convertProjectTypeArr.iterator();
                    while (converIterator.hasNext()){
                        Object obj = converIterator.next();
                        JSONArray jsonArray = (JSONArray) obj;
                        if (flag && jsonArray.size() >= 2) {
                            String level2CategoryCode = jsonArray.get(1).toString();
                            if (Objects.equals(level2CategoryCode, "009001")){
                                converIterator.remove();
                            }
                        }
                    }

                    // 将JSONArray转换为Set
                    Set<String> dbSet = dbProjectTypeArr.stream().map(x -> CollUtil.getLast(((JSONArray)x)).toString()).collect(Collectors.toSet());
                    Set<String> convertSet = convertProjectTypeArr.stream().map(x -> CollUtil.getLast(((JSONArray)x)).toString()).collect(Collectors.toSet());

                    // 计算差集
                    Set<String> difference1 = new HashSet<>(dbSet);
                    difference1.removeAll(convertSet);

                    Set<String> difference2 = new HashSet<>(convertSet);
                    difference2.removeAll(dbSet);

                    Iterator<String> iterator = difference1.iterator();
                    Set<String> originalDifference2 = new HashSet<>(difference2); // 创建difference2的副本
                    while (iterator.hasNext()){
                        String next = iterator.next();
                        difference2 = difference2.stream().filter(x -> !x.startsWith(next)).collect(Collectors.toSet());
                        if (!difference2.equals(originalDifference2)) {
                            iterator.remove();
                            originalDifference2 = new HashSet<>(difference2); // 更新副本
                        }
                    }

                    if (CollUtil.isEmpty(difference1) && CollUtil.isEmpty(difference2)){
                        continue;
                    }
                    // 打印差集的结果
                    throw new BusinessException("db相对于convert的差集：" + difference1 + ";convert相对于db的差集：" + difference2);
                }
            }
        }
        successLog(customerCode);
    }

    private void singleCustomerRefresh(String customerCode) {
        log.info("开始刷新企业：{}的标准数据", customerCode);

        // 暂存表的工程特征数据
        Map<Integer, List<ProjectFeature>> selfTypeGroup = Maps.newHashMap();
        List<ProjectFeature> selfProjectFeatures = projectFeatureSelfMapper.selectSelfAll(customerCode, null);
        if (CollUtil.isNotEmpty(selfProjectFeatures)){
            selfTypeGroup = selfProjectFeatures.stream().collect(Collectors.groupingBy(ProjectFeature::getType));
        }

        // 主表的工程特征数据
        Map<Integer, List<ProjectFeature>> typeGroup = Maps.newHashMap();
        List<ProjectFeature> projectFeatures = projectFeatureMapper.selectByCustomeCode(customerCode, null, WHETHER_TRUE);
        if (CollUtil.isNotEmpty(projectFeatures)){
            typeGroup = projectFeatures.stream().collect(Collectors.groupingBy(ProjectFeature::getType));
        }

        // 查询用到type的工程分类
        Set<Integer> typeSet = new HashSet<>();
        typeSet.addAll(selfTypeGroup.keySet());
        typeSet.addAll(typeGroup.keySet());
        Map<Integer, List<CommonProjCategory>> typeCategoryMap = Maps.newHashMap();
        for (Integer type : typeSet) {
            List<CommonProjCategory> categoryList = commonProjCategoryService.getCategoryListTree(customerCode, type, WHETHER_FALSE);
            if (CollUtil.isNotEmpty(categoryList)){
                List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(categoryList);
                typeCategoryMap.put(type, categoryListTree);
            }
        }

        // 暂存需要更新的数据
        List<ProjectFeature> selfFeatureUpdateList = getUpdateList(selfTypeGroup, typeCategoryMap);
        // 主表需要更新的数据
        List<ProjectFeature> featureUpdateList = getUpdateList(typeGroup, typeCategoryMap);

        // 插入数据库
        tempFeatureSaveService.saveToDb(customerCode, featureUpdateList, selfFeatureUpdateList);
    }

    private List<ProjectFeature> getUpdateList(Map<Integer, List<ProjectFeature>> typeGroup, Map<Integer, List<CommonProjCategory>> typeCategoryMap) {
        List<ProjectFeature> featureUpdateList = Lists.newArrayList();
        for (Map.Entry<Integer, List<ProjectFeature>> entry : typeGroup.entrySet()) {
            Integer type = entry.getKey();
            if (!typeCategoryMap.containsKey(type)) {
                continue;
            }
            List<CommonProjCategory> categoryListTree = typeCategoryMap.get(type);
            for (ProjectFeature feature : entry.getValue()) {
                String newProjectType = CategoryUtil.projectTypeConvert(categoryListTree, feature.getProjectType(), PROJECT_TYPE_CONVERT_NEW);
                ProjectFeature featureUpdate = new ProjectFeature();
                featureUpdate.setId(feature.getId());
                featureUpdate.setProjectType(newProjectType);
                featureUpdateList.add(featureUpdate);
            }
        }
        return featureUpdateList;
    }
}
