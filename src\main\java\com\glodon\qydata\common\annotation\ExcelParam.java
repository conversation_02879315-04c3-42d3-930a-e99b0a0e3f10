package com.glodon.qydata.common.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @className: ExcelParam
 * @description:
 * @author: zhaoyj-g
 * @date: 2020/8/12
 **/
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelParam {

    String value() default "";
    int index() default -1;
}
