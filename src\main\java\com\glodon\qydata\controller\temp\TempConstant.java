package com.glodon.qydata.controller.temp;

/**
 * <AUTHOR>
 * @description: constant       todo 线上执行后，可直接删除掉
 * @date 2022/7/21 13:54
 */
public class TempConstant {

    public static final String NAME = "产品定位";

    public static final String TYPE_CODE = "select";

    public static final String SELECT_LIST = "[" +
            "{\"name\":\"高档\",\"isDeleted\":0}," +
            "{\"name\":\"中档\",\"isDeleted\":0}," +
            "{\"name\":\"低档\",\"isDeleted\":0}]";

    public static final String MAP_KEY = NAME + "->" + TYPE_CODE;

    public static final String TARGET = "工程分类->select";

    public static final String TYPE_SELF = "self";

    public static final String TYPE_ENTERPRISE = "enterprise";

    public static final String TYPE_SYSTEM = "system";

    /**
     * 是否必填：必填
     */
    public static final Integer IS_REQUIRED = 1;


    public static final Integer STATUS_DEFAULT = 0;
    public static final Integer STATUS_SUCCESS = 1;
    public static final Integer STATUS_FAIL = 2;
    public static final Integer STATUS_CATEGORY_REPEAT = 5;
}
