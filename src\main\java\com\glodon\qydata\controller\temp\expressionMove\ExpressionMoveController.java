package com.glodon.qydata.controller.temp.expressionMove;

import com.glodon.qydata.util.ExcelUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.init.FeatureExcelVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/basicInfo/expressionMove")
public class ExpressionMoveController {

    @Autowired
    private ExpressionMoveService expressionMoveService;

    @PostMapping("/initSystemFeature")
    public ResponseVo initSystemFeature(MultipartFile file, String sheetName, String initCustomerCode){
        //1、解析文件，获得待导入的数据
        List<FeatureExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, FeatureExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)) {
            return ResponseVo.error("文件 不能为空");
        }

        expressionMoveService.initSystemFeature(excelDataList, initCustomerCode);
        log.info("---------------初始化内置工程特征数据完成-------------");
        return ResponseVo.success();
    }

    @PostMapping("/initRecord")
    public ResponseVo initRecord(){
        expressionMoveService.initRecord();
        log.info("---------------初始化记录表完成-------------");
        return ResponseVo.success();
    }

    @PostMapping("/expressionToFeature")
    public ResponseVo expressionToFeature(String initCustomerCode){
        Runnable r = () -> {
            expressionMoveService.expressionToFeature(initCustomerCode);
            log.info("---------------expressionToFeature完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @PostMapping("/initCustomerFeature")
    public ResponseVo initCustomerFeature(MultipartFile file, String sheetName, String initCustomerCode){
        //1、解析文件，获得待导入的数据
        List<FeatureExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, FeatureExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)) {
            return ResponseVo.error("文件 不能为空");
        }

        Runnable r = () -> {
            expressionMoveService.initCustomerFeature(excelDataList, initCustomerCode);
            log.info("---------------initCustomerFeature完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @PostMapping("/expressionToFeatureSelf")
    public ResponseVo expressionToFeatureSelf(Long globalId, String customerCode){
        Runnable r = () -> {
            expressionMoveService.expressionToFeatureSelf(globalId, customerCode);
            log.info("---------------expressionToFeatureSelf完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @PostMapping("/initSelfFeature")
    public ResponseVo initSelfFeature(MultipartFile file, String sheetName, Long globalId, String customerCode){
        //1、解析文件，获得待导入的数据
        List<FeatureExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, FeatureExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)) {
            return ResponseVo.error("文件 不能为空");
        }

        Runnable r = () -> {
            expressionMoveService.initSelfFeature(excelDataList, globalId, customerCode);
            log.info("---------------initSelfFeature完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @PostMapping("/refreshDataByCustomer")
    public ResponseVo refreshDataByCustomer(@RequestParam String customerCode, @RequestParam Integer limit, @RequestParam boolean isLogTime){
        Runnable r = () -> {
            expressionMoveService.refreshDataByCustomer(customerCode, limit, isLogTime);
            log.info("---------------refreshDataByCustomer完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @PostMapping("/dataStatistics")
    public ResponseVo dataStatistics(){
        Runnable r = () -> {
            expressionMoveService.dataStatistics();
            log.info("---------------数据统计完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    @PostMapping("/repairCategoryView")
    public ResponseVo repairCategoryView(@RequestParam String customerCode){
        expressionMoveService.repairCategoryView(customerCode);
        log.info("---------------修复分类视图完成-------------");
        return ResponseVo.success();
    }
}
