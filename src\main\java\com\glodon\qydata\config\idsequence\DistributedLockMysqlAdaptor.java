package com.glodon.qydata.config.idsequence;

import com.glodon.cost.bdp.component.distributedlock.core.IDistributedLock;
import com.glodon.cost.bdp.util.CloseableUtil;
import com.glodon.cost.bdp.util.NumberUtil;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.concurrent.TimeUnit;

/**
 * Mysql分布式锁适配器
 *
 * @author: xinzp
 * @date: 2022/3/11
 */
public class DistributedLockMysqlAdaptor implements IDistributedLock {
    /**
     * 数据源
     */
    private DataSource dataSource;

    /**
     * 锁定
     */
    private boolean locked = false;

    /**
     * 自动提交标识
     */
    private boolean autoCommitFlag = true;

    /**
     * 数据库连接
     */
    private Connection connection;

    /**
     * 预编译SQL语句
     */
    private PreparedStatement ps;

    /**
     * SQL查询结果
     */
    private ResultSet rs;

    /**
     * 锁的Key
     */
    private String lockKey;

    /**
     * 默认锁等待时间，防止线程饥饿，超出锁等待时间后还未获得锁，则返回false，代表锁定失败
     */
    private int timeoutMsecs = 30 * 1000;


    public DistributedLockMysqlAdaptor(String lockKey, DataSource dataSource) {
        this.lockKey = lockKey;
        this.dataSource = dataSource;
    }

    @Override
    public boolean lock() throws Exception {
        return lock(timeoutMsecs, TimeUnit.MILLISECONDS);
    }

    @Override
    public boolean lock(long time, TimeUnit unit) throws Exception {
        long timeout = unit.toMillis(time);
        ps = null;
        rs = null;

        connection = dataSource.getConnection();
        autoCommitFlag = connection.getAutoCommit();
        connection.setAutoCommit(false);
        String sql = "select * from SYS_DISTRIBUTED_LOCK_REGISTER where LOCK_NAME=? for update";
        while (timeout >= 0) {
            // 仅用于计时
            long start = System.currentTimeMillis();
            try {
                if (ps == null) {
                    ps = connection.prepareStatement(sql);
                    ps.setQueryTimeout((int) (timeout / 1000));
                }
                ps.clearParameters();
                ps.setString(1, lockKey);
                rs = ps.executeQuery();
                if (rs.next()) {
                    locked = true;
                    sql = "update SYS_DISTRIBUTED_LOCK_REGISTER set UPDATE_TIME=? where LOCK_NAME=?";
                    PreparedStatement updatePs = connection.prepareStatement(sql);
                    updatePs.setTimestamp(1, new java.sql.Timestamp(System.currentTimeMillis()));
                    updatePs.setString(2, lockKey);
                    updatePs.execute();
                    return true;
                }
            } catch (SQLException e) {
                CloseableUtil.close(rs, ps, connection);
                return false;
            }
            int sleepTime = NumberUtil.getRandomValue(1, 5);
            // 耗时等于睡眠耗时+本轮循环的耗时
            timeout = timeout - (sleepTime + System.currentTimeMillis() - start);
            // sleepTime 毫秒
            Thread.sleep(sleepTime);
        }

        return false;
    }

    @Override
    public boolean lock(long expireMsecs, long time, TimeUnit unit) throws Exception {
        return false;
    }

    @Override
    public void unlock() throws Exception {
        if (locked && connection != null) {
            connection.commit();
            connection.setAutoCommit(autoCommitFlag);

            CloseableUtil.close(rs, ps, connection);
        }
    }
}
