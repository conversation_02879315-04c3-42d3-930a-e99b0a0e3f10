package com.glodon.qydata.mapper.system;

import com.glodon.qydata.entity.system.TbArea;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TbAreaMapper {

      List<TbArea> getAreaList(String scope);

      List<TbArea> getAreaByAreaIds(List<String> areaIds);

      void hideByAreaIds(List<TbArea> areaIds);
      void batchInsert(List<TbArea> areaList);

      void batchUpdateAreaCode(@Param("list") List<TbArea> list);

      void batchUpdate(List<TbArea> areaList);
}
