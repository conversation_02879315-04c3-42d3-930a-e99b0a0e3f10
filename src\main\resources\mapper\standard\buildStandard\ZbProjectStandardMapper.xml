<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardMapper">
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="category_code" jdbcType="VARCHAR" property="categoryCode" />
    <result column="category_name" jdbcType="VARCHAR" property="categoryName" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="global_id" jdbcType="BIGINT" property="globalId" />
    <result column="is_example" jdbcType="BIT" property="isExample" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="category_deleted" jdbcType="BIT" property="categoryDeleted" />
    <result column="is_used" jdbcType="BIT" property="isUsed" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_enabled" jdbcType="TINYINT" property="isUsing" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="is_updated" jdbcType="TINYINT" property="isUpdated" />
    <result column="src_standard_id" jdbcType="BIGINT" property="srcStandardId" />

  </resultMap>

  <sql id="Base_Column_List">
    id,`name`,category_code,category_name,customer_code,global_id,is_example,is_deleted,category_deleted,is_used,create_time,update_time,is_enabled,version,src_standard_id,qy_flag,qy_code_old
  </sql>

  <sql id="Temp_Column_List">
    id,`name`,category_code,category_name,customer_code,global_id,is_example,is_deleted,category_deleted,is_used,create_time,update_time,is_enabled,is_updated,origin_id,src_standard_id
  </sql>

  <insert id="insertSelective" parameterType="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard">
    insert into zb_standards_build_standard
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="categoryCode != null">
        category_code,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="globalId != null">
        global_id,
      </if>
      <if test="isExample != null">
        is_example,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="categoryDeleted != null">
        category_deleted,
      </if>
      <if test="isUsed != null">
        is_used,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isUsing != null">
        is_enabled,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="srcStandardId != null">
        src_standard_id,
      </if>
      <if test="qyCodeOld != null">
        qy_code_old,
      </if>
      <if test="qyFlag != null">
        qy_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="globalId != null">
        #{globalId,jdbcType=BIGINT},
      </if>
      <if test="isExample != null">
        #{isExample,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="categoryDeleted != null">
        #{categoryDeleted,jdbcType=BIT},
      </if>
      <if test="isUsed != null">
        #{isUsed,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        now(),
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isUsing != null">
        #{isUsing,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="srcStandardId != null">
        #{srcStandardId,jdbcType=BIGINT},
      </if>
      <if test="qyCodeOld != null">
        #{qyCodeOld,jdbcType=VARCHAR},
      </if>
      <if test="qyFlag != null">
        #{qyFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="searchSelfRecordsByCusCodeAndName" resultMap="BaseResultMap" parameterType="java.lang.String">
    select <include refid="Temp_Column_List"/>
    FROM zb_standards_build_standard_self
    where is_deleted  = 0 and `name` = BINARY #{name} and customer_code = #{customerCode}
  </select>
  <select id="searchRecordsByCusCodeAndCateCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select <include refid="Temp_Column_List"/>
    FROM zb_standards_build_standard_self
    where is_deleted  = 0 and category_code  =#{categoryCode} and customer_code = #{customerCode}
  </select>
  <select id="selectListByCusAndCates" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    FROM zb_standards_build_standard
    where is_deleted = 0 and is_enabled = 1 and customer_code = #{customerCode,jdbcType=VARCHAR} and category_code in
    <foreach collection="categoryCodeList" index="index" item="item" separator="," open="(" close=")">
      #{item}
    </foreach>
    order by create_time DESC
  </select>

  <select id="selectByCustomerCode" resultMap="BaseResultMap" useCache="false" flushCache="true">
    select
    <include refid="Base_Column_List"/>
    from zb_standards_build_standard
    where
    <if test="isShowDelete == 0">
      is_deleted = 0 and
    </if>
    customer_code = #{customerCode,jdbcType=VARCHAR}
  </select>

  <select id="selectByCustomerCodeWithOrder" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from zb_standards_build_standard
    where
    <if test="isShowDelete == 0">
      is_deleted = 0 and
    </if>
    customer_code = #{customerCode,jdbcType=VARCHAR}
    order by is_example DESC,create_time
    <if test="createTimeAscOrDesc == 1">
       DESC
    </if>
  </select>

  <select id="selectEnabledListByCusAndCategoryCode" resultMap="BaseResultMap" parameterType="java.lang.String">
    select <include refid="Base_Column_List"/>
    FROM zb_standards_build_standard
    where customer_code = #{customerCode,jdbcType=VARCHAR} and category_code = #{categoryCode,jdbcType=VARCHAR} and is_deleted  = 0 and is_enabled = 1 order by create_time DESC
  </select>

  <select id="selectDeletedSameNameStandard" resultMap="BaseResultMap">
    select
    <include refid="Temp_Column_List"/>
    from zb_standards_build_standard_self
    where is_deleted = 1 and customer_code = #{customerCode,jdbcType=VARCHAR}
    and name = #{name,jdbcType=VARCHAR}
  </select>

  <delete id="deleteStandardById">
    delete from zb_standards_build_standard_self where id = #{id,jdbcType=BIGINT}
  </delete>

  <select id="selectSelfStandardById" resultMap="BaseResultMap">
    select
    <include refid="Temp_Column_List"/>
    from zb_standards_build_standard_self
    where id = #{id,jdbcType=BIGINT}
  </select>
  <select id="selectSelfStandard" resultMap="BaseResultMap">
    select
    <include refid="Temp_Column_List"/>
    from zb_standards_build_standard_self
    where id = #{id,jdbcType=BIGINT} AND customer_code = #{customerCode,jdbcType=VARCHAR} AND is_deleted = 0
  </select>

  <select id="selectStandardByStandardId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from zb_standards_build_standard
    where id = #{standardId,jdbcType=BIGINT}
  </select>

  <update id="updateStandardById">
    update zb_standards_build_standard
      <set>
        <if test="name != null and name != ''">
          name = #{name,jdbcType=VARCHAR},
        </if>
        <if test="categoryCode != null and categoryCode != ''">
          category_code = #{categoryCode,jdbcType=VARCHAR},
        </if>
        <if test="categoryName != null and categoryName != ''">
          category_name = #{categoryName,jdbcType=VARCHAR},
        </if>
        <if test="isUsing != null">
          is_enabled = #{isUsing,jdbcType=INTEGER},
        </if>
        <if test="isDeleted != null">
          is_deleted = #{isDeleted,jdbcType=BOOLEAN}
        </if>
        <if test="version != null">
          version = #{version,jdbcType=INTEGER}
        </if>
      </set>
      <where>
        id = #{originId,jdbcType=BIGINT}
      </where>
  </update>

  <select id="selectBuiltInStandard" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from zb_standards_build_standard
    where customer_code = '-100' and is_example = 1
  </select>
  <insert id="batchInsert">
    insert into zb_standards_build_standard
   (id, name, category_code, category_name, customer_code, global_id, is_example, is_deleted,
    category_deleted, is_used, create_time, update_time, is_enabled, version,src_standard_id,
    qy_code_old, qy_flag)
   values
   <foreach collection="list" item="item" index="index" separator=",">
    (
       #{item.id},#{item.name},#{item.categoryCode},#{item.categoryName},
       #{item.customerCode},#{item.globalId},#{item.isExample},#{item.isDeleted},
       #{item.categoryDeleted},#{item.isUsed},#{item.createTime},#{item.updateTime},#{item.isUsing},
       #{item.version},#{item.srcStandardId},#{item.qyCodeOld},#{item.qyFlag}
       )
   </foreach>
  </insert>

  <delete id="deleteByCustomerCode" parameterType="java.lang.String">
    delete from zb_standards_build_standard where customer_code = #{customerCode}
  </delete>

  <insert id="selfInsertSelective" parameterType="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard">
    insert into zb_standards_build_standard_self
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="categoryCode != null">
        category_code,
      </if>
      <if test="categoryName != null">
        category_name,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="globalId != null">
        global_id,
      </if>
      <if test="isExample != null">
        is_example,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="categoryDeleted != null">
        category_deleted,
      </if>
      <if test="isUsed != null">
        is_used,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="isUsing != null">
        is_enabled,
      </if>
      <if test="isUpdated != null">
        is_updated,
      </if>
      <if test="srcStandardId != null">
        src_standard_id,
      </if>
      <if test="qyCodeOld != null">
        qy_code_old,
      </if>
      <if test="qyFlag != null">
        qy_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null">
        #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null">
        #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="globalId != null">
        #{globalId,jdbcType=BIGINT},
      </if>
      <if test="isExample != null">
        #{isExample,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="categoryDeleted != null">
        #{categoryDeleted,jdbcType=BIT},
      </if>
      <if test="isUsed != null">
        #{isUsed,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        now(),
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isUsing != null">
        #{isUsing,jdbcType=INTEGER},
      </if>
      <if test="isUpdated != null">
        #{isUpdated,jdbcType=INTEGER},
      </if>
      <if test="srcStandardId != null">
        #{srcStandardId,jdbcType=BIGINT},
      </if>
      <if test="qyCodeOld != null">
        #{qyCodeOld,jdbcType=VARCHAR},
      </if>
      <if test="qyFlag != null">
        #{qyFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="selectSelfByCustomerCode" resultMap="BaseResultMap" useCache="false" flushCache="true">
    select
    <include refid="Temp_Column_List"/>
    from zb_standards_build_standard_self
    where
    customer_code = #{customerCode,jdbcType=VARCHAR}
    <if test="isShowDelete == 0">
      and is_deleted = 0
    </if>
    order by is_example DESC,create_time DESC
  </select>
  <insert id="selfBatchInsert">
    insert into zb_standards_build_standard_self
    (id, name, category_code, category_name, customer_code, global_id,
     is_example,is_deleted, category_deleted, is_used, create_time, update_time,
     is_enabled, origin_id, is_updated,src_standard_id,qy_code_old,qy_flag)
    values
    <foreach collection="list" item="item" index="index" separator=",">
      (
      #{item.id},#{item.name},#{item.categoryCode},#{item.categoryName},#{item.customerCode},#{item.globalId},
      #{item.isExample},#{item.isDeleted},#{item.categoryDeleted},#{item.isUsed},#{item.createTime},#{item.updateTime},
      #{item.isUsing},#{item.originId},#{item.isUpdated},#{item.srcStandardId},#{item.qyCodeOld},#{item.qyFlag}
      )
    </foreach>
  </insert>
  <delete id="deleteSelfByCustomerCode">
    delete from zb_standards_build_standard_self where customer_code = #{customerCode,jdbcType=VARCHAR}
  </delete>
  <update id="updateSelfStandardById">
    update zb_standards_build_standard_self
    <set>
      <if test="name != null and name != ''">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="categoryCode != null and categoryCode != ''">
        category_code = #{categoryCode,jdbcType=VARCHAR},
      </if>
      <if test="categoryName != null and categoryName != ''">
        category_name = #{categoryName,jdbcType=VARCHAR},
      </if>
      <if test="isUsing != null">
        is_enabled = #{isUsing,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BOOLEAN},
      </if>
      is_updated = 1
    </set>
    <where>
      id = #{id,jdbcType=BIGINT}
    </where>
  </update>

  <select id="selectByCategoryCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_standards_build_standard
    where
        customer_code = #{customerCode,jdbcType=VARCHAR}
        and category_code = #{code,jdbcType=VARCHAR}
      <if test="isShowDeleted != null and isShowDeleted == 0">
        and is_deleted = 0
      </if>
  </select>

  <update id="updateIsUpdated">
    update zb_standards_build_standard_self set is_updated = 1 where id = #{standardId,jdbcType=BIGINT}
  </update>

  <update id="batchUpdateVersion">
    <foreach collection="updateVersionList" item="item" separator=";">
      update zb_standards_build_standard set version = #{item.version} where id = #{item.id}
    </foreach>
  </update>

  <select id="selectSelfAll" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard">
    select <include refid="Temp_Column_List"/> from zb_standards_build_standard_self
  </select>
  <select id="selectSelfEnabledByCategoryCode" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard">
    select <include refid="Temp_Column_List"/> from zb_standards_build_standard_self
            where customer_code = #{customerCode,jdbcType=VARCHAR} and is_enabled = 1 and category_code = #{categoryCode,jdbcType=VARCHAR} and is_deleted = 0
  </select>
  <update id="updateSelfDisableByCategoryCode">
    update zb_standards_build_standard_self
    set is_updated = 1,is_enabled = 0
    where customer_code = #{customerCode,jdbcType=VARCHAR} and category_code = #{categoryCode,jdbcType=VARCHAR}
  </update>
  <select id="selectAllCustomerCodes" resultType="java.lang.String">
    select distinct customer_code from zb_standards_build_standard
    where customer_code not in ('-100', 'null', 'test', '-100(old)', '-1', 'standardList_TC_0.data.gldUserId', '测试基础库初始化重复-可删除-企业1', '测试基础库初始化重复-可删除-企业2', 'defaultCustomerCode');
  </select>
  <select id="selectSelfAllCustomerCodes" resultType="java.lang.String">
    select distinct customer_code from zb_standards_build_standard_self
    where customer_code not in ('-100', 'null', 'test', '-100(old)', '-1', 'standardList_TC_0.data.gldUserId', '测试基础库初始化重复-可删除-企业1', '测试基础库初始化重复-可删除-企业2', 'defaultCustomerCode');
  </select>
  <update id="batchUpdateUsingAndCategory">
    <foreach collection="updateList" item="item" separator=";">
      update zb_standards_build_standard
      <set>
        <if test="item.categoryCode != null and item.categoryCode != ''">
          category_code = #{item.categoryCode,jdbcType=VARCHAR},
        </if>
        <if test="item.categoryName != null and item.categoryName != ''">
          category_name = #{item.categoryName,jdbcType=VARCHAR},
        </if>
        <if test="item.isUsing != null">
          is_enabled = #{item.isUsing,jdbcType=INTEGER},
        </if>
      </set>
      where customer_code = #{customerCode,jdbcType=VARCHAR} and id = #{item.id}
    </foreach>
  </update>
  <update id="batchUpdateUsingAndCategorySelf">
    <foreach collection="updateList" item="item" separator=";">
      update zb_standards_build_standard_self
      <set>
        <if test="item.categoryCode != null and item.categoryCode != ''">
          category_code = #{item.categoryCode,jdbcType=VARCHAR},
        </if>
        <if test="item.categoryName != null and item.categoryName != ''">
          category_name = #{item.categoryName,jdbcType=VARCHAR},
        </if>
        <if test="item.isUsing != null">
          is_enabled = #{item.isUsing,jdbcType=INTEGER},
        </if>
        is_updated = 1
      </set>
      where customer_code = #{customerCode,jdbcType=VARCHAR} and origin_id = #{item.id}
    </foreach>
  </update>
</mapper>
