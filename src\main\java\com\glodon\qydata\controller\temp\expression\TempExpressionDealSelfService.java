package com.glodon.qydata.controller.temp.expression;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ExpressionTypeEnum;
import com.glodon.qydata.mapper.temp.TempExpressionSelfMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 刷计算口径数据  todo 线上执行后，可直接删除掉
 * @date 2022/7/22 16:44
 */
@Service
@Slf4j
public class TempExpressionDealSelfService extends ServiceImpl<TempExpressionSelfMapper, TempExpressionSelf> {

    @Autowired
    private TempExpressionSelfMapper tempExpressionSelfMapper;

    /**
     * @description: 获取数字类型的数据
     * @param
     * @return java.util.Map<java.lang.Integer, java.util.List < com.glodon.qydata.controller.temp.expression.TempExpression>>
     * <AUTHOR>
     * @date 2022/7/25 17:56
     */
    public Map<Integer, List<TempExpressionSelf>> getNumberExpression(Long selfGlobalId){
        LambdaQueryWrapper<TempExpressionSelf> queryWrapper = new LambdaQueryWrapper<TempExpressionSelf>()
                .eq(TempExpressionSelf::getSelfGlobalId, selfGlobalId)
                .eq(TempExpressionSelf::getTypeCode, ExpressionTypeEnum.TYPE_NUMBER.getCode())
                .orderByAsc(TempExpressionSelf::getExpressionOrd);

        List<TempExpressionSelf> tempExpressionSelf = tempExpressionSelfMapper.selectList(queryWrapper);

        if (CollectionUtils.isNotEmpty(tempExpressionSelf)){
            return tempExpressionSelf.stream().collect(Collectors.groupingBy(TempExpressionSelf::getType));
        }

        return null;
    }

    /**
     * @description: 获取需要处理的用户
     * @param
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/7/24 17:53
     */
    public List<TempExpressionSelf> getAllGlobalIdList(Integer systemExpressionSize){
        // 需要处理的用户
        QueryWrapper<TempExpressionSelf> allGlobalIdQuery = new QueryWrapper<>();
        allGlobalIdQuery.select("distinct self_global_id,qy_code");
        List<TempExpressionSelf> allGlobalIdList = tempExpressionSelfMapper.selectList(allGlobalIdQuery);

        QueryWrapper<TempExpressionSelf> processedGlobalIdQuery = new QueryWrapper<>();
        processedGlobalIdQuery.select("self_global_id,qy_code,COUNT(self_global_id) countNum").lambda()
                .eq(TempExpressionSelf::getExpressionIsFromSystem, Constants.ZbFeatureConstants.WHETHER_TRUE)
                .groupBy(TempExpressionSelf::getSelfGlobalId).having("countNum % "+ systemExpressionSize + " = 0");

        List<TempExpressionSelf> processedGlobalId = tempExpressionSelfMapper.selectList(processedGlobalIdQuery);

        log.info("总：{}，已处理：{}，需要处理：{}", allGlobalIdList.size(), processedGlobalId.size(), allGlobalIdList.size() - processedGlobalId.size());
        allGlobalIdList.removeAll(processedGlobalId);

        return allGlobalIdList;
    }

    public void dataDeal(Integer type, Map<String, TempExpression> targetData, List<TempExpressionSelf> oldExpressionList,
                          List<TempExpressionSelf> updateList, List<TempExpressionSelf> insertList){
        HashMap<String, TempExpression> targetDataCopy = new HashMap<>(targetData.size());
        targetDataCopy.putAll(targetData);
        targetDataCopy = this.clone(targetDataCopy);

        // 用户自定义的计算口径放在内置的后面
        int size = targetDataCopy.size();
        for (String key : targetDataCopy.keySet()) {
            TempExpression e = targetDataCopy.get(key);
            Integer ord = e.getExpressionOrd();
            if(ord!=null && ord.intValue()>size){
                size = ord.intValue();
            }
        }

        for (TempExpressionSelf oldExpression : oldExpressionList) {
            String name = oldExpression.getName();
            if (targetDataCopy.containsKey(name)) {
                // name、unit、remark、ord、expression_is_from_system
                TempExpression excelExpression = targetDataCopy.get(name);

                if(Constants.ZbFeatureConstants.WHETHER_TRUE !=oldExpression.getExpressionIsFromSystem().intValue()) {
                    TempExpressionSelf updateExpression = new TempExpressionSelf();
                    updateExpression.setId(oldExpression.getId());
                    updateExpression.setUnit(excelExpression.getUnit());
                    updateExpression.setExpressionRemark(excelExpression.getExpressionRemark());
                    updateExpression.setExpressionOrd(excelExpression.getExpressionOrd());
                    updateExpression.setIsExpression(Constants.ZbFeatureConstants.WHETHER_TRUE);
                    updateExpression.setExpressionIsFromSystem(excelExpression.getExpressionIsFromSystem());
                    updateExpression.setExpressionIsUsing(Constants.ZbFeatureConstants.WHETHER_TRUE);
                    updateExpression.setExpressionCreateGlobalId(-100L);
                    updateList.add(updateExpression);
                }
                targetDataCopy.remove(name);
            }else{
                Integer isExpression = oldExpression.getIsExpression();
                if (isExpression != null && Constants.ZbExpressionConstants.WHETHER_TRUE == isExpression){

                    TempExpressionSelf updateExpression = new TempExpressionSelf();
                    updateExpression.setId(oldExpression.getId());
                    updateExpression.setExpressionIsFromSystem(Constants.ZbFeatureConstants.WHETHER_FALSE);
                    updateExpression.setExpressionOrd(++size);

                    updateList.add(updateExpression);
                }
            }
        }

        for (TempExpression value : targetDataCopy.values()) {
            TempExpressionSelf updateExpression = new TempExpressionSelf();
            BeanUtils.copyProperties(value, updateExpression);
            updateExpression.setType(type);
            insertList.add(updateExpression);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeSql(List<TempExpressionSelf> updateList, List<TempExpressionSelf> insertList){
        if (CollectionUtils.isNotEmpty(updateList)){
            this.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(insertList)){
            this.saveBatch(insertList);
        }
    }

    /**
     * 使用对象的序列化进而实现深拷贝
     * @param obj
     * @param <T>
     * @return
     */
    private <T extends Serializable> T clone(T obj) {
        T cloneObj = null;
        try {
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            ObjectOutputStream oos = new ObjectOutputStream(bos);
            oos.writeObject(obj);
            oos.close();
            ByteArrayInputStream bis = new ByteArrayInputStream(bos.toByteArray());
            ObjectInputStream ois = new ObjectInputStream(bis);
            cloneObj = (T) ois.readObject();
            ois.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return cloneObj;
    }

}
