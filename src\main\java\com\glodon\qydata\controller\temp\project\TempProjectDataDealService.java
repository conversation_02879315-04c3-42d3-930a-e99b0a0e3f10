package com.glodon.qydata.controller.temp.project;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.controller.temp.TempConstant;
import com.glodon.qydata.mapper.temp.TempStandardsProjectInfoMapper;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 企业级---在【工程分类】下方增加一行【产品定位】，默认内置枚举值：A档、B档、C档、D档，默认启用和必填  todo 线上执行后，可直接删除掉
 * @date 2022/7/18 17:55
 */
@Service
@Slf4j
public class TempProjectDataDealService {
    @Autowired
    private TempStandardsProjectInfoMapper tempStandardsProjectInfoMapper;

    /**
     * @description: 获取需要处理的企业
     * @param
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/7/18 18:06
     */
    public List<String> getAllCustomerCodeList(){
        // 查询已经初始化，需要新增【产品定位】的企业；正式线大约3800家企业
        QueryWrapper<TempStandardsProjectInfoEntity> allCustomerCodeQuery = new QueryWrapper<>();
        allCustomerCodeQuery.select("customer_code,SUM(`name`='" + TempConstant.NAME + "') conditionSum").lambda()
                .eq(TempStandardsProjectInfoEntity::getStandardDataType, Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO)
                .groupBy(TempStandardsProjectInfoEntity::getCustomerCode).having("conditionSum < 1");

        List<TempStandardsProjectInfoEntity> allCustomerCodeList = tempStandardsProjectInfoMapper.selectList(allCustomerCodeQuery);

        if (CollectionUtils.isNotEmpty(allCustomerCodeList)){
            return allCustomerCodeList.parallelStream().map(TempStandardsProjectInfoEntity::getCustomerCode).collect(Collectors.toList());
        }

        return null;
    }

    /**
     * @description: 分企业执行
     * @param customerCode
     * @return void
     * <AUTHOR>
     * @date 2022/7/18 18:08
     */
    @Transactional(rollbackFor = Exception.class)
    public void dealData(String customerCode){
        // 获取企业的项目信息
        List<TempStandardsProjectInfoEntity> customerProjectInfo = this.getCustomerProjectInfo(customerCode);

        if (CollectionUtils.isEmpty(customerProjectInfo)){
            return;
        }

        // '名称->类型'  为key的map
        Map<String, Integer> nameTypeMap = customerProjectInfo.parallelStream()
                .collect(Collectors.toMap(x -> x.getName() + "->" + x.getTypeCode(), TempStandardsProjectInfoEntity::getOrd, (v1, v2) -> v2));

        // 已经存在【产品定位】，跳过
        if (nameTypeMap.containsKey(TempConstant.MAP_KEY)){
            return;
        }

        // 构建新增实体
        TempStandardsProjectInfoEntity standardsProjectInfoEntity = this.buildProjectInfoEntity(customerCode);

        Integer ordAndMove = this.getOrdAndMove(nameTypeMap, customerProjectInfo, customerCode);
        standardsProjectInfoEntity.setOrd(ordAndMove);

        tempStandardsProjectInfoMapper.insert(standardsProjectInfoEntity);
    }

    /**
     * @description: 获取企业的项目信息
     * @param customerCode
     * @return java.util.List<com.glodon.qydata.entity.standard.projectOrContractInfo.TempStandardsProjectInfoEntity>
     * <AUTHOR>
     * @date 2022/7/18 18:09
     */
    private List<TempStandardsProjectInfoEntity> getCustomerProjectInfo(String customerCode){
        // 查询该企业的项目信息，排序字段只维护未删除的
        QueryWrapper<TempStandardsProjectInfoEntity> customerProjectInfoQuery = new QueryWrapper<>();
        customerProjectInfoQuery.select("id, name, type_code, ord").lambda()
                .eq(TempStandardsProjectInfoEntity::getCustomerCode, customerCode)
                .eq(TempStandardsProjectInfoEntity::getStandardDataType, Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO)
                .eq(TempStandardsProjectInfoEntity::getIsDeleted, Constants.StandardsProjectOrContractInfoConstants.IS_NOT_DELETED);
        return tempStandardsProjectInfoMapper.selectList(customerProjectInfoQuery);
    }

    /**
     * @description: 构建新增实体
     * @param customerCode
     * @return com.glodon.qydata.entity.standard.projectOrContractInfo.TempStandardsProjectInfoEntity
     * <AUTHOR>
     * @date 2022/7/18 18:09
     */
    private TempStandardsProjectInfoEntity buildProjectInfoEntity(String customerCode){
        // 数据实体 默认启用和必填
        TempStandardsProjectInfoEntity standardsProjectInfoEntity = new TempStandardsProjectInfoEntity();
        standardsProjectInfoEntity.setId(SnowflakeIdUtils.getNextId());
        standardsProjectInfoEntity.setName(TempConstant.NAME);
        standardsProjectInfoEntity.setTypeCode(TempConstant.TYPE_CODE);

        standardsProjectInfoEntity.setSelectList(TempConstant.SELECT_LIST);
        standardsProjectInfoEntity.setIsUsing(Constants.StandardsProjectOrContractInfoConstants.IS_ENABLE);
        standardsProjectInfoEntity.setIsRequired(TempConstant.IS_REQUIRED);
        standardsProjectInfoEntity.setCustomerCode(customerCode);
        standardsProjectInfoEntity.setStandardDataType(Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO);
        return standardsProjectInfoEntity;
    }

    /**
     * @description: 【产品定位】放在【工程分类】后，后面的ord字段都+1
     * @param targetOrd
     * @param customerCode
     * @return void
     * <AUTHOR>
     * @date 2022/7/19 9:23
     */
    private void moveOrd(Integer targetOrd, String customerCode){
        UpdateWrapper<TempStandardsProjectInfoEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.setSql("ord = ord + 1").lambda()
                .eq(TempStandardsProjectInfoEntity::getCustomerCode, customerCode)
                .eq(TempStandardsProjectInfoEntity::getStandardDataType, Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO)
                .eq(TempStandardsProjectInfoEntity::getIsDeleted, Constants.StandardsProjectOrContractInfoConstants.IS_NOT_DELETED)
                .gt(TempStandardsProjectInfoEntity::getOrd, targetOrd);

        tempStandardsProjectInfoMapper.update(null, updateWrapper);
    }

    /**
     * @description: 获取【产品定位】的排序，如果需要，后移其他
     * @param nameTypeMap
     * @param customerProjectInfo
     * @param customerCode
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2022/7/19 10:17
     */
    private Integer getOrdAndMove(Map<String, Integer> nameTypeMap, List<TempStandardsProjectInfoEntity> customerProjectInfo, String customerCode){
        int ord;
        // 处理ord--存在【工程分类】，放在工程分类后面；不存在【工程分类】，放在最后面
        if (nameTypeMap.containsKey(TempConstant.TARGET)){
            Integer targetOrd = nameTypeMap.get(TempConstant.TARGET);
            ord = targetOrd + 1;
            this.moveOrd(targetOrd, customerCode);
        }else {
            Integer maxOrd = customerProjectInfo.parallelStream().map(TempStandardsProjectInfoEntity::getOrd).max(Comparator.comparingInt(o -> o)).orElse(0);
            ord = maxOrd + 1;
        }
        return ord;
    }
}
