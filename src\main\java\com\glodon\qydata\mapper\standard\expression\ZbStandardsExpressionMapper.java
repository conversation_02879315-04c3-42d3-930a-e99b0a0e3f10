package com.glodon.qydata.mapper.standard.expression;

import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description:
 * <AUTHOR>
 * @date 2021/11/5 8:50
 */
@Repository
public interface ZbStandardsExpressionMapper {
    /**
     * 根据主键查询
     * @param id
     * @return com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression
     * <AUTHOR>
     * @date 2021/11/5 9:00
     */
    ZbStandardsExpression selectByPrimaryKey(Long id);

    /**
     * 根据企业查询
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression>
     * <AUTHOR>
     * @date 2021/11/5 9:00
     */
    List<ZbStandardsExpression> selectListByCustomCode(String customerCode, Integer type);

    /**
     * 获取计算口径列表
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression>
     * <AUTHOR>
     * @date 2021/11/5 11:00
     */
    List<ZbStandardsExpression> selectExpression(String customerCode, Integer type);

    List<ZbStandardsExpression> selectAllExpression(String customerCode, Integer type);
    /**
     *批量保存
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2021/11/11 15:47
     */
    void batchInsert(@Param("list") List<ZbStandardsExpression> list);

    /**
     *批量修改
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2021/11/19 17:47
     */
    void batchUpdateSelective(@Param("list") List<ZbStandardsExpression> list);

    /**
     * 根据企业编码删除计算口径
     * @param customerCode
     * @return void
     * <AUTHOR>
     * @date 2021/11/9 10:42
     */
    void deleteByCustomerCode(@Param("customerCode") String customerCode,@Param("type") Integer type);

    /**
     * 查询是否存在，按名称+类型+枚举 --导入内置数据用
     * @param customerCode
     * @param name
     * @param typeCode
     * @param option
     * @return com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression
     * <AUTHOR>
     * @date 2021/11/5 8:59
     */
    ZbStandardsExpression findExistByNameTypeOptionDs(String customerCode, String name, String typeCode, String option, Integer type);

    /**
     * 查询是否存在，按名称+类型  --导入内置数据用
     * @param customerCode
     * @param name
     * @param typeCode
     * @return com.gcj.zblib.standardData.expression.entity.ZbStandardsExpression
     * <AUTHOR>
     * @date 2021/11/5 9:00
     */
    ZbStandardsExpression findExistByNameTypeDs(String customerCode, String name, String typeCode, Integer type);

    /**
     * 插入  --导入内置数据用
     * @param recordTemp
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021/11/5 8:58
     */
    Long insertSelectiveDs(ZbStandardsExpression recordTemp);

    List<ZbStandardsExpression> selectNumberListByCustomCode(String customerCode, Integer type);
}