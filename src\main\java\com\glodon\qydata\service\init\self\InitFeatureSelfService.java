package com.glodon.qydata.service.init.self;

import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.util.SpringUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 工程特征专业视图、工程特征分类视图、计算口径的暂存数据
 * @date 2022/7/11 13:45
 */
@Service
public class InitFeatureSelfService extends InitSelfService{
    @Autowired
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;

    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;

    @Autowired
    private ProjectFeatureCategoryViewSelfMapper projectFeatureCategoryViewSelfMapper;

    @Autowired
    private ProjectFeatureCategoryViewMapper projectFeatureCategoryViewMapper;

    public void initData(String customerCode, Integer type){
        super.initData(customerCode, type, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        // 暂存表完全是空的，进行初始化
        return CollectionUtils.isEmpty(projectFeatureSelfMapper.selectAll(customerCode, type));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
        InitFeatureSelfService initFeatureSelfService = SpringUtil.getBean("initFeatureSelfService");
        initFeatureSelfService.executeInitTransactional(customerCode, type, id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void executeInitTransactional(String customerCode, Integer type, Long id) {
        List<ProjectFeature> allProjectFeatures = projectFeatureMapper.selectAll(customerCode, type);

        if(CollectionUtils.isNotEmpty(allProjectFeatures)){
            Map<Long, Long> oldAndNewFeatureIdMap = new HashMap<>(allProjectFeatures.size());

            // zb_project_feature_standards
            List<Long> featureNextId = SnowflakeIdUtils.getNextId(allProjectFeatures.size());
            for (int i = 0; i < allProjectFeatures.size(); i++) {
                ProjectFeature projectFeature = allProjectFeatures.get(i);
                Long oldId = projectFeature.getId();
                Long newId = featureNextId.get(i);
                oldAndNewFeatureIdMap.put(oldId, newId);
                projectFeature.setOriginId(oldId);
                projectFeature.setId(newId);
            }

            if (CollectionUtils.isNotEmpty(allProjectFeatures)){
                projectFeatureSelfMapper.insertSelfBatch(allProjectFeatures);
            }

            // zb_project_feature_category_view_standards
            List<ProjectFeatureCategoryView> allFeatureCategoryViews = projectFeatureCategoryViewMapper.
                    selectByCategoryAndCustomerCode(null, customerCode, null);
            if (CollectionUtils.isNotEmpty(allFeatureCategoryViews)){
                List<Long> featureCategoryNextId = SnowflakeIdUtils.getNextId(allFeatureCategoryViews.size());
                for (int i = 0; i < allFeatureCategoryViews.size(); i++) {
                    ProjectFeatureCategoryView featureCategoryView = allFeatureCategoryViews.get(i);
                    featureCategoryView.setOriginId(featureCategoryView.getId());
                    featureCategoryView.setId(featureCategoryNextId.get(i));
                    featureCategoryView.setFeatureId(oldAndNewFeatureIdMap.get(featureCategoryView.getFeatureId()));
                }

                //去除空ID
                allFeatureCategoryViews = allFeatureCategoryViews.stream().filter(x -> x.getFeatureId() != null).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(allFeatureCategoryViews)){
                    projectFeatureCategoryViewSelfMapper.insertSelfBatch(allFeatureCategoryViews);
                }
            }
        }
    }
}
