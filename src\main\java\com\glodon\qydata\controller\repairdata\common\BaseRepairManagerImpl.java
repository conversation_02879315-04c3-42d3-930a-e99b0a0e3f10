package com.glodon.qydata.controller.repairdata.common;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.glodon.qydata.entity.repairdata.RepairLog;
import com.glodon.qydata.mapper.repairdata.TempCommonRepairMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 计算口径修复管理者
 */
@Service
@Slf4j
public abstract class BaseRepairManagerImpl extends IRepairDataManager {
    @Resource
    TempCommonRepairMapper tempCommonRepairMapper;
    protected abstract List<BaseRepairDataHandler> getHandlerList();

    protected abstract Integer getRepairVersion();
    protected abstract List<String> getCustomCodeList ();

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean repair(String customerCode) {
        getHandlerList().forEach(handler -> {
            handler.repairData(customerCode);
            handler.checkDataAfterRepair(customerCode);
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean repairSystemData() {
        return false;
    }

    @Override
    public void logRepairStatus(String customerCode, boolean isSuccsess) {
        UpdateWrapper<RepairLog> updateWrapper = new UpdateWrapper<>();

        if(isSuccsess) {
            updateWrapper.setSql("repair_status = 1").lambda()
                    .eq(RepairLog::getCustomerCode, customerCode)
                    .eq(RepairLog::getVersion, getRepairVersion());

        } else {
            updateWrapper.setSql("repair_status = 2").lambda()
                    .eq(RepairLog::getCustomerCode, customerCode)
                    .eq(RepairLog::getVersion, getRepairVersion());
            log.info("修复不通过企业编码:{}", customerCode);
        }


        tempCommonRepairMapper.update(null, updateWrapper);
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> logErroDataCustomerCode() {
        // 需要修复的数据
        List<String> customerCodeList = getCustomCodeList();
        customerCodeList.forEach(customerCode -> {
            if(isNeedRepair(customerCode)){
                RepairLog entity = tempCommonRepairMapper.selectOne(new LambdaQueryWrapper<RepairLog>().eq(RepairLog::getCustomerCode, customerCode)
                        .eq(RepairLog::getVersion, getRepairVersion()).isNull(RepairLog::getRepairStatus));
                if(entity != null) {
                    log.warn("已经存在待修复记录");
                    return;
                }
                RepairLog repairLog = new RepairLog();
                repairLog.setCustomerCode(customerCode);
                repairLog.setVersion(getRepairVersion());
                tempCommonRepairMapper.insert(repairLog);
            }
        });

        return customerCodeList;
    }
    private boolean isNeedRepair(String customerCode) {
        if(customerCode == null) {
            return false;
        }
        List<BaseRepairDataHandler> handlerList = getHandlerList();
        for (int i = 0; i < handlerList.size(); i++) {
            if(handlerList.get(i).isNeedRepair(customerCode)) {
                return true;
            }
        }

        return false;
    }
    @Override
    public List<String> selectErroDataCustomerCode() {
        LambdaQueryWrapper<RepairLog> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.isNull(RepairLog::getRepairStatus).eq(RepairLog::getVersion, getRepairVersion());
        List<RepairLog> list = tempCommonRepairMapper.selectList(queryWrapper);
        return list.stream().map(item -> item.getCustomerCode()).collect(Collectors.toList());
    }
}
