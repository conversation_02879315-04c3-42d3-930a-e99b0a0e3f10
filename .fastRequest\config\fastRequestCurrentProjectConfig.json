{"apiDocTemplate": "#if (${namingPolicy}=='byDoc')\n$H1 ${methodDescription}\n#else\n$H1 $!{methodName}\n\n$H3 Method description\n\n```\n$!{methodDescription}\n```\n#end\n\n> URL: $!{url}\n>\n> Origin Url: $!{originUrl}\n>\n> Type: $!{methodType}\n\n\n$H3 Request headers\n\n|Header Name| Header Value|\n|---------|------|\n#foreach( $h in ${headerList})\n|$h.type|$h.value|\n#end\n\n$H3 Parameters\n\n$H5 Path parameters\n\n| Parameter | Type | Value | Description |\n|---------|------|------|------------|\n#foreach( $node in ${pathKeyValueList})\n|$node.key|$!{node.type}|$!{node.value}|$!{node.comment}|\n#end\n\n\n$H5 URL parameters\n\n|Required| Parameter | Type | Value | Description |\n|---------|---------|------|------|------------|\n#foreach( $node in ${urlParamsKeyValueList})\n|$!{node.enabled}|$!{node.key}|$!{node.type}|$!{node.value}|$!{node.comment}|\n#end\n\n\n$H5 Body parameters\n\n$H6 JSON\n\n```\n${jsonParam}\n```\n\n$H6 JSON document\n\n```\n${jsonParamDocument}\n```\n\n\n$H5 Form URL-Encoded\n|Required| Parameter | Type | Value | Description |\n|---------|---------|------|------|------------|\n#foreach( $node in ${urlEncodedKeyValueList})\n|$!{node.enabled}|$!{node.key}|$!{node.type}|$!{node.value}|$!{node.comment}|\n#end\n\n\n$H5 Multipart\n|Required | Parameter | Type | Value | Description |\n|---------|---------|------|------|------------|\n#foreach( $node in ${multipartKeyValueList})\n|$!{node.enabled}|$!{node.key}|$!{node.type}|$!{node.value}|$!{node.comment}|\n#end\n\n\n$H3 Response\n\n$H5 Response example\n\n```\n$!{responseExample}\n```\n\n$H5 Response document\n```\n$!{returnDocument}\n```\n\n\n", "apifoxSetting": {"domain": "https://api.apifox.com", "syncAfterSave": false}, "dataList": [], "envList": [], "headerList": [], "ignoreParseFieldList": [], "maxDescriptionLength": -1, "pmCollectionId": "", "postScript": "", "preScript": "", "projectList": [], "syncModel": {"branch": "master", "domain": "https://github.com", "enabled": false, "gitToken": "", "namingPolicy": "byDoc", "owner": "", "repo": "", "repoUrl": "", "syncAfterRun": false, "type": "github"}, "syncPmAfterSave": false, "urlEncodedKeyValueList": [], "urlParamsKeyValueList": [], "urlSuffix": "", "workspaceId": ""}