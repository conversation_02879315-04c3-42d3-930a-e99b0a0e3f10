package com.glodon.qydata.util;

import com.glodon.qydata.common.constant.BusinessConstants;

import java.io.UnsupportedEncodingException;
import java.util.Arrays;

/**
 * @version V1.0
 * @{DESCRIPTION} :
 * @auther zhangzq-a
 * @create 2017/12/20
 **/
public class Base64 {
    private static final char[] CHARS = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".toCharArray();
    private static final int[] INV = new int[256];

    public Base64() {
        // 空构造方法
    }

    public static char[] getAlphabet() {
        return (char[])CHARS.clone();
    }

    public static char[] encodeToChar(String s) {
        try {
            return encodeToChar(s.getBytes(BusinessConstants.UTF_8), false);
        } catch (UnsupportedEncodingException var2) {
            return null;
        }
    }

    public static char[] encodeToChar(byte[] arr) {
        return encodeToChar(arr, false);
    }

    public static char[] encodeToChar(byte[] arr, boolean lineSeparator) {
        int len = arr != null ? arr.length : 0;
        if (len == 0) {
            return new char[0];
        } else {
            int evenlen = len / 3 * 3;
            int cnt = (len - 1) / 3 + 1 << 2;
            int destLen = cnt + (lineSeparator ? (cnt - 1) / 76 << 1 : 0);
            char[] dest = new char[destLen];
            int left = 0;
            int d = 0;
            int cc = 0;

            while(left < evenlen) {
                int i = (arr[left++] & 255) << 16 | (arr[left++] & 255) << 8 | arr[left++] & 255;
                dest[d++] = CHARS[i >>> 18 & 63];
                dest[d++] = CHARS[i >>> 12 & 63];
                dest[d++] = CHARS[i >>> 6 & 63];
                dest[d++] = CHARS[i & 63];
                if (lineSeparator) {
                    ++cc;
                    if (cc == 19 && d < destLen - 2) {
                        dest[d++] = '\r';
                        dest[d++] = '\n';
                        cc = 0;
                    }
                }
            }

            left = len - evenlen;
            if (left > 0) {
                d = (arr[evenlen] & 255) << 10 | (left == 2 ? (arr[len - 1] & 255) << 2 : 0);
                dest[destLen - 4] = CHARS[d >> 12];
                dest[destLen - 3] = CHARS[d >>> 6 & 63];
                dest[destLen - 2] = left == 2 ? CHARS[d & 63] : 61;
                dest[destLen - 1] = '=';
            }

            return dest;
        }
    }


    public static byte[] encodeToByte(String s) {
        try {
            return encodeToByte(s.getBytes(BusinessConstants.UTF_8), false);
        } catch (UnsupportedEncodingException var2) {
            return null;
        }
    }

    public static byte[] encodeToByte(byte[] arr) {
        return encodeToByte(arr, false);
    }

    public static byte[] encodeToByte(byte[] arr, boolean lineSep) {
        int len = arr != null ? arr.length : 0;
        if (len == 0) {
            return new byte[0];
        } else {
            int evenlen = len / 3 * 3;
            int cnt = (len - 1) / 3 + 1 << 2;
            int destlen = cnt + (lineSep ? (cnt - 1) / 76 << 1 : 0);
            byte[] dest = new byte[destlen];
            int left = 0;
            int d = 0;
            int cc = 0;

            while(left < evenlen) {
                int i = (arr[left++] & 255) << 16 | (arr[left++] & 255) << 8 | arr[left++] & 255;
                dest[d++] = (byte)CHARS[i >>> 18 & 63];
                dest[d++] = (byte)CHARS[i >>> 12 & 63];
                dest[d++] = (byte)CHARS[i >>> 6 & 63];
                dest[d++] = (byte)CHARS[i & 63];
                if (lineSep) {
                    ++cc;
                    if (cc == 19 && d < destlen - 2) {
                        dest[d++] = 13;
                        dest[d++] = 10;
                        cc = 0;
                    }
                }
            }

            left = len - evenlen;
            if (left > 0) {
                d = (arr[evenlen] & 255) << 10 | (left == 2 ? (arr[len - 1] & 255) << 2 : 0);
                dest[destlen - 4] = (byte)CHARS[d >> 12];
                dest[destlen - 3] = (byte)CHARS[d >>> 6 & 63];
                dest[destlen - 2] = left == 2 ? (byte)CHARS[d & 63] : 61;
                dest[destlen - 1] = 61;
            }

            return dest;
        }
    }


    public static String encodeToString(String s) {
        try {
            return new String(encodeToChar(s.getBytes(BusinessConstants.UTF_8), false));
        } catch (UnsupportedEncodingException var2) {
            return null;
        }
    }

    public static String decodeToString(String s) {
        try {
            return encodeToString(s.getBytes(BusinessConstants.UTF_8));
        } catch (UnsupportedEncodingException var2) {
            return null;
        }
    }

    public static String encodeToString(byte[] arr) {
        return new String(encodeToChar(arr, false));
    }

    public static String encodeToString(byte[] arr, boolean lineSep) {
        return new String(encodeToChar(arr, lineSep));
    }


    static {
        Arrays.fill(INV, -1);
        int i = 0;

        for(int iS = CHARS.length; i < iS; INV[CHARS[i]] = i++) {
            ;
        }

        INV[61] = 0;
    }
}
