<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsUnitMapper">

    <select id="selectBuiltInData"
            resultType="com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity">
        select * from zb_standards_unit where customer_code = '-1' and unit_source_type = 1
    </select>

    <select id="selectBuiltInDataContract"
            resultType="com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity">
        select * from zb_standards_unit where customer_code = '-1' and unit_source_type = 2
    </select>

    <select id="selectByCustomerCode"
            resultType="com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity">
        select * from zb_standards_unit where customer_code = #{customerCode}
    </select>

    <delete id="deleteByCustomerCode" parameterType="java.lang.String">
      delete from zb_standards_unit where customer_code = #{customerCode}
    </delete>

    <select id="selectUnit"
            resultType="com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity">
        select * from zb_standards_unit where customer_code = '-1' and unit_source_type = 1
    </select>
</mapper>
