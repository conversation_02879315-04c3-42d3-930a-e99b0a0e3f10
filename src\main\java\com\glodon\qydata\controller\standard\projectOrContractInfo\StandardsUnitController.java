package com.glodon.qydata.controller.standard.projectOrContractInfo;


import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsUnitService;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.projectOrContractInfo.StandardsUnitShowVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * zb_standards_unit - 主键 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@RestController
@RequestMapping("/basicInfo/standards/unit")
@Tag(name = "企业标准数据单位相关接口类", description = "企业标准数据单位相关接口类")
public class StandardsUnitController extends BaseController {
    @Autowired
    private IStandardsUnitService standardsUnitService;

    /**
     * 获取项目/工程规模单位集合
     * @throws
     * @param standardsInfoId 项目/合同信息表数据id
     * @param type 标准类型，1：项目信息，2：合同信息
     * <AUTHOR>
     * @return
     * @date 2021/10/20 14:59
     */
    @Operation(summary = "获取项目/工程规模单位集合")
    @GetMapping
    public ResponseVo<List<StandardsUnitShowVo>> get(@RequestParam Long standardsInfoId, @RequestParam Integer type) {
        String customerCode = getCustomerCode();
        return ResponseVo.success(standardsUnitService.initAndGetUnits(standardsInfoId, type, customerCode));
    }

    /**
     * 新增项目/工程规模单位
     * @throws
     * @param entity
     * <AUTHOR>
     * @return {@link ResponseVo< Void>}
     * @date 2021/10/20 15:42
     */
    @Operation(summary = "新增项目/工程规模单位")
    @PostMapping
    public ResponseVo<StandardsUnitEntity> add(@RequestBody StandardsUnitEntity entity) {
        return ResponseVo.success(standardsUnitService.addUnit(entity));
    }

    /**
     * 修改项目/工程规模单位默认选中项
     * @throws
     * @param entity
     * <AUTHOR>
     * @return {@link ResponseVo< StandardsUnitEntity>}
     * @date 2021/10/20 16:15
     */
    @Operation(summary = "修改项目/工程规模单位默认选中项")
    @PutMapping
    public ResponseVo<StandardsUnitEntity> update(@RequestBody StandardsUnitEntity entity) {
        return ResponseVo.success(standardsUnitService.updateUnit(entity));
    }

    /**
     * 删除项目/工程规模单位
     * @throws
     * @param id
     * <AUTHOR>
     * @return {@link ResponseVo< Void>}
     * @date 2021/10/20 16:40
     */
    @Operation(summary = "删除项目/工程规模单位")
    @DeleteMapping
    public ResponseVo<Void> delete(@RequestParam Long id) {
        standardsUnitService.deleteUnit(id);
        return ResponseVo.success();
    }

    /**
     * 已废弃，不再调用
     * @throws
     * @param infoId
     * @param unitName
     * <AUTHOR>
     * @return {@link ResponseVo< Void>}
     * @date 2021/11/8 9:22
     */
    //@Operation("设置默认选中项目/工程规模单位")
//    @PutMapping("/check")
    public ResponseVo<Void> setDefaultCheck(@RequestParam Long infoId, @RequestParam String unitName) {
        String customerCode = getCustomerCode();
        standardsUnitService.setDefaultCheck(infoId, unitName, customerCode);
        return ResponseVo.success();
    }

    /**
     * 已废弃，不再调用
     * @throws
     * @param infoId
     * <AUTHOR>
     * @return {@link com.glodon.qydata.vo.common.ResponseVo<java.lang.String>}
     * @date 2022/2/27 19:05
     */
    //@Operation("根据项目规模/工程规模id获取默认选中的单位")
//    @GetMapping("/getChecked")
    public ResponseVo<String> getDefaultCheck(@RequestParam Long infoId) {
        String customerCode = getCustomerCode();
        return ResponseVo.success(standardsUnitService.getDefaultCheck(infoId, customerCode));
    }
}
