package com.glodon.qydata.service.standard.feature;

import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.vo.standard.feature.*;

import java.util.List;
import java.util.Map;

/**
 * 工程特征相关接口
 * <AUTHOR>
 * @date 2021/10/21 20:15
 */
public interface IProjectFeatureSelfService {

    /**
     * 新增工程特征
     * @param featureAddVO
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/21 20:15
     */
    Long add(ProjectFeatureAddVO featureAddVO);

    /***
     * @description: 新增工程特征
     * @param featureAddVOList 1
     * @return com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 15:49
     */
    List<ProjectFeature> addList(List<ProjectFeatureAddVO> featureAddVOList, Long tradeId, String customerCode);

    /**
     * 根据工程特征ID删除工程特征
     * @param id
     * @return void
     * <AUTHOR>
     * @date 2021/10/21 20:16
     */
    void delete(ProjectFeatureDeleteVO id, String customerCode);

    /**
     * 根据项目特征id查询项目特征信息
     * @param featureId
     * @param viewType
     * @param categoryCode
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/21 20:16
     */
    ProjectFeatureResultVO getCompleteFeatureByPrimaryKey(Long featureId, Integer viewType, String categoryCode, List<CommonProjCategory> categoryListTree);
    
    /**
     *  上移下移
     * @param projectFeatureUpOrDownVO
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 8:48 
     */
    void downOrUp(ProjectFeatureUpOrDownVO projectFeatureUpOrDownVO);

    /**
     * 更新工程特征信息
     * @param updateProjectFeature
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/24 19:54
     */
    Long update(ProjectFeatureUpdateVO updateProjectFeature);

    /**
     * 查询暂存工程特征列表（专业视图）
     * @param filterVO
     * @return PageInfo<ProjectFeatureVO>
     * <AUTHOR>
     * @date 2022/2/10 14:17
     */
    List<ProjectFeatureResultVO>  getSelfFeatureTradeView(String customerCode, ProjectFeatureFilterVO filterVO);

    /**
     * 查询暂存工程特征列表
     * @param
     * @return PageInfo<ProjectFeatureVO>
     * <AUTHOR>
     * @date 2022/2/10 14:17
     */
    List<ProjectFeature> getSelfFeatureTrade(String customerCode, Integer type, Long tradeId);

    /**
     * 查询工程特征列表（分类视图）
     * @param filterVO
     * @return java.util.List<com.gcj.zblib.standardData.feature.vo.ProjectFeatureResultVO>
     * <AUTHOR>
     * @date 2021/2/10 14:19
     */
    List<ProjectFeatureCategoryViewVO> getSelfFeatureCategoryView(String customerCode, ProjectFeatureFilterVO filterVO);

    /**
     * 发布暂存数据
     */
    void publish(String customerCode, String globalId);

    /**
     *  快速复用工程特征
     * @param projectFeatureUpOrDownVO
     * @return void
     * <AUTHOR>
     * @date 2024/11/14 8:48
     */
    void batchInsertFeature(ProjectFeatureBatchAddVO projectFeatureUpOrDownVO);
}
