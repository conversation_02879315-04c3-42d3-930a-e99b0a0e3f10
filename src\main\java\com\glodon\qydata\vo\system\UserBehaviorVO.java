package com.glodon.qydata.vo.system;

import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户埋点行为参数vo
 */
@Data
public class UserBehaviorVO implements Serializable {


    @NotEmpty(message = "功能编码不能为空")
    private String fncode;

    /** 业务参数 **/
    private String queryStr;



    //---------- 非前端传入
    private String globalId;

    /**
     * 功能名称
     */
    private String fnname;

    /**
     * 功能分组
     */
    private String fngroup;

    private String userAgent;
    private String sgToken;
    private String gldToken;

    // 功能触发时长
    private String duration;

    /**
     * sessionid
     */
    private String sessionid;
}
