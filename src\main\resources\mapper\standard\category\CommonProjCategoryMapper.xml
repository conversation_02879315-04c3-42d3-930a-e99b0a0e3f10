<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.category.CommonProjCategoryMapper">
    <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.category.CommonProjCategory" >
        <id column="commonprojcategoryid" property="commonprojcategoryid" jdbcType="VARCHAR" />
        <result column="qy_code" property="qyCode" jdbcType="VARCHAR" />
        <result column="categoryname" property="categoryname" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="INTEGER" />
        <result column="categorycode1" property="categorycode1" jdbcType="VARCHAR" />
        <result column="categorycode2" property="categorycode2" jdbcType="VARCHAR" />
        <result column="categorycode3" property="categorycode3" jdbcType="VARCHAR" />
        <result column="categorycode4" property="categorycode4" jdbcType="VARCHAR" />
        <result column="category_type_code" property="categoryTypeCode" jdbcType="VARCHAR" />
        <result column="category_type_name" property="categoryTypeName" jdbcType="VARCHAR" />
        <result column="level" property="level" jdbcType="DECIMAL" />
        <result column="projcount" property="counter" jdbcType="INTEGER" />
        <result column="mainparamname" property="mainparamname" jdbcType="VARCHAR" />
        <result column="unit" property="unit" jdbcType="VARCHAR" />
        <result column="province_process" property="provinceProcess" jdbcType="VARCHAR" />
        <result column="accountname" property="accountname" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="is_usable" property="isUsable" jdbcType="INTEGER" />
        <result column="is_using" property="isUsing" jdbcType="INTEGER" />
        <result column="update_global_id" property="updateGlobalId" jdbcType="VARCHAR" />
        <result column="global_id" property="globalId" jdbcType="VARCHAR" />
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER" />
        <result column="ord" property="ord" jdbcType="INTEGER" />
    </resultMap>

    <sql id="Base_Column_List">
          id,qy_code,commonprojcategoryid,categoryname,`type`,categorycode1,categorycode2,categorycode3,categorycode4,category_type_code,category_type_name,level,
          projcount,mainparamname,unit,province_process,accountname,create_time,update_time,is_usable,global_id,is_using,update_global_id,remark,is_deleted,ord
    </sql>

    <select id="selectAll" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_commonprojcategory_standards
        where qy_code = #{customerCode} and (province_process is null or province_process != 'ShangHai')
        and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
        <if test="isShowDelete == null or isShowDelete == 0" >
            AND is_deleted = 0
        </if>
        order by ord
    </select>

    <select id="selectAllByCategoryCodeList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_commonprojcategory_standards
        where qy_code = #{customerCode} and (province_process is null or province_process != 'ShangHai')
        AND is_deleted = 0 and invalid = 0
        and commonprojcategoryid in
        <foreach collection="list" separator="," index="index" item="item" open="(" close=")">
         #{item}
        </foreach>
        <if test="type != null" >
            and `type` = #{type}
        </if>
        order by ord
    </select>

    <select id="selectInit" resultType="java.lang.Integer" useCache="false" flushCache="true">
        select count(1)
        from tb_commonprojcategory_standards
        where qy_code = #{customerCode} and global_id = '-100' and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
    </select>

    <select id="queryTruthTopCategoryList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_commonprojcategory_standards
        where qy_code = #{customerCode} and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
        and level = 1
        and province_process is null
        and is_using = 1
        and is_deleted = 0
        order by ord
    </select>

    <select id="getCategoryByPrimaryKey" resultType="com.glodon.qydata.entity.standard.category.CommonProjCategory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_commonprojcategory_standards where id = #{categoryId}
    </select>

    <insert id="saveBatchCommonProjCategory" parameterType="java.util.List" useGeneratedKeys="false">
        insert into tb_commonprojcategory_standards (id,commonprojcategoryid,categoryname,`type`,categorycode1,categorycode2,
        categorycode3,categorycode4,category_type_code,category_type_name,level,ord,is_usable,qy_code,is_deleted,
        is_using,create_time,update_time,global_id,accountname,update_global_id,remark,qy_code_old,qy_flag) values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.id},
            #{item.commonprojcategoryid},
            #{item.categoryname},
            #{item.type},
            #{item.categorycode1},
            #{item.categorycode2},
            #{item.categorycode3},
            #{item.categorycode4},
            #{item.categoryTypeCode},
            #{item.categoryTypeName},
            #{item.level},
            #{item.ord},
            #{item.isUsable},
            #{item.qyCode},
            #{item.isDeleted},
            #{item.isUsing},
            #{item.createTime},
            #{item.updateTime},
            #{item.globalId},
            #{item.accountname},
            #{item.updateGlobalId},
            #{item.remark},
            #{item.qyCodeOld},
            #{item.qyFlag}
            )
        </foreach>
    </insert>


    <select id="mapNameByTypes" resultType="com.glodon.qydata.entity.standard.category.CommonProjCategory">
        SELECT DISTINCT category_type_code categoryTypeCode,category_type_name categoryTypeName
        FROM tb_commonprojcategory_standards
        where qy_code = #{customerCode}
        and level = 1
        and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
    </select>

    <update id="batchUpdateLevelCode" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update tb_commonprojcategory_standards set commonprojcategoryid = #{item.commonprojcategoryid, jdbcType=VARCHAR}, categorycode3 = #{item.categorycode3, jdbcType=VARCHAR}
            where id = #{item.id, jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="updateLevelCode" parameterType="com.glodon.qydata.entity.standard.category.CommonProjCategory">
        update tb_commonprojcategory_standards set commonprojcategoryid = #{commonprojcategoryid, jdbcType=VARCHAR}, categorycode3 = #{categorycode3, jdbcType=VARCHAR}
        where id = #{id, jdbcType=INTEGER}
    </update>

    <select id="selectOrdTemp" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_commonprojcategory_standards_ord_temp
    </select>

    <select id="selectCustomerCode" resultType="java.lang.String">
        SELECT DISTINCT qy_code FROM tb_commonprojcategory_standards WHERE qy_code != '-100' AND qy_code IS NOT NULL and invalid = 0
    </select>

    <select id="selectByLevelAndCode" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_commonprojcategory_standards
        where qy_code = #{customerCode}
        and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
        <if test="level == null" >
            AND level = 1
        </if>
        <if test="level == 1" >
            AND categorycode1 = #{code}
            AND level = 2
        </if>
        <if test="level == 2" >
            AND categorycode2 = #{code}
            AND level = 3
        </if>
        <if test="level == 3" >
            AND categorycode3 = #{code}
            AND level = 4
        </if>
    </select>

    <delete id="deleteByCustomerCode">
        delete from tb_commonprojcategory_standards where qy_code = #{customerCode}
        <if test="type != null" >
            and `type` = #{type}
        </if>
    </delete>

    <update id="batchUpdatePublish" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update tb_commonprojcategory_standards
            <set >
                commonprojcategoryid = #{item.commonprojcategoryid,jdbcType=VARCHAR},
                categoryname = #{item.categoryname,jdbcType=VARCHAR},
                `type` = #{item.type,jdbcType=INTEGER},
                categorycode1 = #{item.categorycode1,jdbcType=VARCHAR},
                categorycode2 = #{item.categorycode2,jdbcType=VARCHAR},
                categorycode3 = #{item.categorycode3,jdbcType=VARCHAR},
                categorycode4 = #{item.categorycode4,jdbcType=VARCHAR},
                category_type_code = #{item.categoryTypeCode,jdbcType=VARCHAR},
                category_type_name = #{item.categoryTypeName,jdbcType=VARCHAR},
                `level` = #{item.level,jdbcType=INTEGER},
                mainparamname = #{item.mainparamname,jdbcType=VARCHAR},
                unit = #{item.unit,jdbcType=VARCHAR},
                ord = #{item.ord,jdbcType=INTEGER},
                province_process = #{item.provinceProcess,jdbcType=VARCHAR},
                global_id = #{item.globalId,jdbcType=VARCHAR},
                accountname = #{item.accountname,jdbcType=VARCHAR},
                is_usable = #{item.isUsable,jdbcType=INTEGER},
                is_deleted = #{item.isDeleted,jdbcType=INTEGER},
                create_time = #{item.createTime,jdbcType=TIMESTAMP},
                update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                is_using = #{item.isUsing,jdbcType=INTEGER},
                update_global_id = #{item.updateGlobalId,jdbcType=VARCHAR},
                remark = #{item.remark,jdbcType=VARCHAR},
            </set>
            where id = #{item.id,jdbcType=INTEGER}
        </foreach>
    </update>


    <update id="updatePublish" parameterType="com.glodon.qydata.entity.standard.category.CommonProjCategory">
        update tb_commonprojcategory_standards
        <set >
            commonprojcategoryid = #{commonprojcategoryid,jdbcType=VARCHAR},
            categoryname = #{categoryname,jdbcType=VARCHAR},
            `type` = #{type,jdbcType=INTEGER},
            categorycode1 = #{categorycode1,jdbcType=VARCHAR},
            categorycode2 = #{categorycode2,jdbcType=VARCHAR},
            categorycode3 = #{categorycode3,jdbcType=VARCHAR},
            categorycode4 = #{categorycode4,jdbcType=VARCHAR},
            category_type_code = #{categoryTypeCode,jdbcType=VARCHAR},
            category_type_name = #{categoryTypeName,jdbcType=VARCHAR},
            `level` = #{level,jdbcType=INTEGER},
            mainparamname = #{mainparamname,jdbcType=VARCHAR},
            unit = #{unit,jdbcType=VARCHAR},
            ord = #{ord,jdbcType=INTEGER},
            province_process = #{provinceProcess,jdbcType=VARCHAR},
            global_id = #{globalId,jdbcType=VARCHAR},
            accountname = #{accountname,jdbcType=VARCHAR},
            is_usable = #{isUsable,jdbcType=INTEGER},
            is_deleted = #{isDeleted,jdbcType=INTEGER},
            create_time = #{createTime,jdbcType=TIMESTAMP},
            update_time = #{updateTime,jdbcType=TIMESTAMP},
            is_using = #{isUsing,jdbcType=INTEGER},
            update_global_id = #{updateGlobalId,jdbcType=VARCHAR},
            remark = #{remark,jdbcType=VARCHAR},
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>


    <update id="batchUpdateOrd" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update tb_commonprojcategory_standards set ord=#{item.ord, jdbcType=INTEGER}
            where id = #{item.id, jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="updateOrd" parameterType="com.glodon.qydata.entity.standard.category.CommonProjCategory">
        update tb_commonprojcategory_standards set ord=#{ord, jdbcType=INTEGER}
        where id = #{id, jdbcType=INTEGER}
    </update>

    <select id="getByCommonprojcategoryid" resultType="com.glodon.qydata.entity.standard.category.CommonProjCategory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_commonprojcategory_standards
        <where>
            <if test="param1 != null">
                and commonprojcategoryid = #{param1}
            </if>
            <if test="customerCode != null">
                and qy_code = #{customerCode}
            </if>
            <if test="type != null" >
                and `type` = #{type}
            </if>
            and is_deleted = 0 and invalid = 0 limit 0, 1
        </where>
    </select>

    <select id="getByCodeSet" resultType="com.glodon.qydata.entity.standard.category.CommonProjCategory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_commonprojcategory_standards
        where qy_code = #{customerCode}
          and `type` = #{type}
          and invalid = 0
          and commonprojcategoryid in
        <foreach collection="codeSet" item="item" index="index" separator="," open="(" close=")">
        #{item}
        </foreach>
    </select>

    <select id="selectByOneLevelCode" resultType="com.glodon.qydata.entity.standard.category.CommonProjCategory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_commonprojcategory_standards
        where qy_code = #{customerCode}
        and `type` = #{type}
        and categorycode1 = #{oneLevelCode}
        and is_deleted = 0
        and is_using = 1
        and invalid = 0
    </select>
    <select id="selectAllCustomerCode" resultType="java.lang.String">
        select distinct qy_code from tb_commonprojcategory_standards where invalid = 0;
    </select>

    <select id="selectAllCategoryBackup" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from tb_commonprojcategory_standards_backup_081900
        <if test="size != null" >
            limit ${size}
        </if>
    </select>

    <update id="batchUpdateUpdateTime" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update tb_commonprojcategory_standards set update_time = #{item.updateTime,jdbcType=TIMESTAMP}
            where id = #{item.id, jdbcType=INTEGER}
        </foreach>
    </update>

    <update id="updateUpdateTime" parameterType="com.glodon.qydata.entity.standard.category.CommonProjCategory">
        update tb_commonprojcategory_standards set update_time = #{updateTime,jdbcType=TIMESTAMP}
        where id = #{id, jdbcType=INTEGER}
    </update>

    <update id="batchUpdateDelStatus" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update tb_commonprojcategory_standards set is_deleted = 1
            where id = #{item, jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>
