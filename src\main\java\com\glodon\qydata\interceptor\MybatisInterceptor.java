package com.glodon.qydata.interceptor;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.entity.system.QYFlag;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.MapperMethod;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.util.*;


@Intercepts(@Signature(type = Executor.class, method = "update", args = {MappedStatement.class, Object.class}))
@Component
@Slf4j
public class MybatisInterceptor implements Interceptor {

	@Autowired
	private RedisUtil redisUtil;

	@Override
	public Object intercept(Invocation invocation) throws IllegalAccessException, InvocationTargetException {
		fillField(invocation);
		return invocation.proceed();
	}

	private void fillField(Invocation invocation) {
		Object[] args = invocation.getArgs();
		SqlCommandType sqlCommandType;
		for (Object arg : args) {
			// 第一个参数处理。根据它判断是否给“操作属性”赋值。 如果是第一个参数 MappedStatement
			if (arg instanceof MappedStatement) {
				MappedStatement ms = (MappedStatement) arg;
				sqlCommandType = ms.getSqlCommandType();
				// 如果是“增加”操作，则继续进行默认操作信息赋值。否则，则退出
				if (sqlCommandType == SqlCommandType.INSERT) {
					continue;
				} else {
					return;
				}
			}

			Map<String, QYFlag> qyFlagMap = new HashMap<>();
			if (arg instanceof MapperMethod.ParamMap) { //批量操作时
				MapperMethod.ParamMap map = (MapperMethod.ParamMap) arg;
				if (!map.containsKey("list")) {
					return;
				}
				Object obj = map.get("list");
				List<?> list = (List<?>) obj;
				if (list != null) {
					for (Object o : list) {
						setParameter(o, qyFlagMap);
					}
				}
			} else {
				setParameter(arg, qyFlagMap);
			}
		}
	}

	private void setParameter(Object arg, Map<String, QYFlag> qyFlagMap) {
		Class<?> aClass = arg.getClass();
		List<Field> fieldList = new ArrayList<>();

		// 企业标识提取了公共类QYFlag, 判断QYFlag是否是父类
		if (QYFlag.class.isAssignableFrom(aClass)) {
			fieldList.addAll(Arrays.asList(aClass.getSuperclass().getDeclaredFields())); // 父类
			fieldList.addAll(Arrays.asList(aClass.getDeclaredFields()));
		} else {
			// fieldList.addAll(Arrays.asList(aClass.getDeclaredFields())); // 子类
			return; // 目前父类不是QYFlag, 直接返回
		}

		Optional<Field> customerCodeOptional = fieldList.stream().filter(x -> Constants.KEY_CUSTOMER_CODE.equals(x.getName())).findFirst();
		Optional<Field> qyCodeOptional = fieldList.stream().filter(x -> Constants.KEY_QY_CODE.equals(x.getName())).findFirst();
		Optional<Field> qyCodeOldOptional = fieldList.stream().filter(x -> Constants.KEY_QY_CODE_OLD.equals(x.getName())).findFirst();
		Optional<Field> qyFlagOptional = fieldList.stream().filter(x -> Constants.KEY_QY_FLAG.equals(x.getName())).findFirst();

		// 参数中是否存在
		if (!qyCodeOldOptional.isPresent() || !qyFlagOptional.isPresent() ||
				(!customerCodeOptional.isPresent() && !qyCodeOptional.isPresent())) {
			return;
		}

		Field qyCodeField = customerCodeOptional.orElseGet(qyCodeOptional::get);
		String qyCode = getFieldValue(qyCodeField, arg);
		QYFlag qyFlag = null;

		if (qyFlagMap.containsKey(qyCode)) {
			qyFlag = qyFlagMap.get(qyCode);
		} else {
			qyFlag = redisUtil.getObject(RedisKeyEnum.QY_FLAG_INFO, QYFlag.class, qyCode);
			qyFlagMap.put(qyCode, qyFlag);
		}

		if (qyFlag != null) {
			setFieldValue(qyCodeOldOptional.get(), arg, qyFlag.getQyCodeOld());
			setFieldValue(qyFlagOptional.get(), arg, qyFlag.getQyFlag());
		}
	}


	/**
	 * 获取对象的操作属性
	 *
	 * @param
	 */
	private String getFieldValue(Field field, Object obj) {
		try {
			field.setAccessible(true);
			Object value = field.get(obj);
			return value != null ? value.toString() : null;
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
		return null;
	}

	/**
	 * 为对象的操作属性赋值
	 *
	 * @param
	 */
	private void setFieldValue(Field field, Object obj, Object value) {
		try {
			field.setAccessible(true);
			field.set(obj, value);
		} catch (IllegalAccessException e) {
			e.printStackTrace();
		}
	}

	@Override
	public Object plugin(Object o) {
		return Plugin.wrap(o, this);
	}


	@Override
	public void setProperties(Properties properties) {
		// 空实现
	}
}

