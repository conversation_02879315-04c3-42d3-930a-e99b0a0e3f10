package com.glodon.qydata.entity.standard.buildStandard;

import com.baomidou.mybatisplus.annotation.TableName;
import com.glodon.qydata.util.mover.Movable;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
　　* @description: 项目标准细则实体
　　* <AUTHOR>
　　* @date 2021/8/16 17:22
　　*/
@Data
@TableName(value = "zb_standards_build_standard_detail")
public class ZbProjectStandardDetail implements Serializable, Movable {
    private static final long serialVersionUID = 1L;
    private Long id;

    private Long originId;

    private Long standardId;

    private String levelcode;

    private String name;

    private String desc;

    private Integer level;

    private String remark;

    private Integer ord;

    private Date createTime;

    private Date updateTime;

    private Long tradeId;

    private String tradeName;

    /**
     * 项目划分科目id
     */
    private String itemDivisionSubjectId;

    /**
     * 项目划分科目名称
     */
    private String itemDivisionSubjectName;

    @Override
    public Long getUniqueIdentity() {
        return id;
    }

    @Override
    public Integer getOrdValue() {
        return ord;
    }

    @Override
    public void setOrdValue(Integer ordValue) {
        this.ord = ordValue;
    }
}