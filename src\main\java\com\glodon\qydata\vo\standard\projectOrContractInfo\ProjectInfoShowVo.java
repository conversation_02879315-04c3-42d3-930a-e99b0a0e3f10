package com.glodon.qydata.vo.standard.projectOrContractInfo;

import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;

/**
 * <AUTHOR>
 * @ProjectName gcj_zb_company_site
 * @className ProjectInfoShowVo
 * @description 项目/合同信息展示数据载体
 * @date 2021/10/15
 **/
public class ProjectInfoShowVo extends StandardsProjectInfoEntity {
    private static final long serialVersionUID = -7627691830706080537L;

    /**
     * 是否可编辑
     */
    private Boolean isEditable = true;
    /**
     * 是否可删除
     */
    private Boolean isDeletable = true;
    /**
     * 创建人真名
     */
    private String creatorRealName;
    /**
     * 更新人真名
     */
    private String updaterRealName;

    public Boolean getIsEditable() {
        return isEditable;
    }

    public void setIsEditable(Boolean isEditable) {
        this.isEditable = isEditable;
    }

    public Boolean getIsDeletable() {
        return isDeletable;
    }

    public void setIsDeletable(Boolean isDeletable) {
        this.isDeletable = isDeletable;
    }

    public String getCreatorRealName() {
        return creatorRealName;
    }

    public void setCreatorRealName(String creatorRealName) {
        this.creatorRealName = creatorRealName;
    }

    public String getUpdaterRealName() {
        return updaterRealName;
    }

    public void setUpdaterRealName(String updaterRealName) {
        this.updaterRealName = updaterRealName;
    }
}
