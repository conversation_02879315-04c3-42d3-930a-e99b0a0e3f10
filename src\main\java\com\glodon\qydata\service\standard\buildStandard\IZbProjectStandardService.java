package com.glodon.qydata.service.standard.buildStandard;


import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.glodon.qydata.dto.*;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.vo.standard.buildStandard.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
　　* @description: 项目标准服务接口
　　* <AUTHOR>
　　* @date 2021/8/16 17:47
　　*/
public interface IZbProjectStandardService extends IService<ZbProjectStandard> {

    /**
    　　* @description: 同一个企业下，标准名称唯一
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/8/16 17:48
    　　*/
    Boolean checkedName(String name, Long id);

    /**
     * 按企业编码获取建造标准列表
     * @throws
     * @param customerCode
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * <AUTHOR>
     * @return {@link List<  ZbProjectStandard >}
     * @date 2021/8/17 9:30
     */
    List<ZbProjectStandard> standardList(String customerCode, Integer isShowDelete, Integer createTimeAscOrDesc, boolean isSetCategoryAndPosition) throws BusinessException;

    /**
     　　* @description: 末级编码获取名称路径
     　　* @param categoryCode:业态编码 categoryMap：封住业态编码与实体的map
     　　* @return 业态全路径名称
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/9/17 18:21
     　　*/
    String getCategoryNameByCode(String categoryCode, String customerCode);
    /**
     * 插入建造标准行数据
     * @throws
     * @param dto
     * @param standardId 所属建造标准id
     * <AUTHOR>
     * @return {@link ZbProjectStandardDetail}
     * @date 2021/8/17 15:10
     */
    ZbProjectStandardDetail createStandardDetailCol(Long standardId, ZbProjectStandardDetailColDto dto) throws BusinessException;

    /**
    　　* @description: 新增建造标准
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/8/17 8:47
    　　*/
    ZbProjectStandard addStandardRecord(ZbProjectStandardBo zbProjectStandardBo);
    /**
     　　* @description: 修改建造标准
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/8/17 8:47
     　　*/
    ZbProjectStandard updateStandardRecord(ZbProjectStandardBo zbProjectStandardBo);
    /**
    　　* @description: 根据标准id,删除对应标准记录，物理删除
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/8/17 15:01
    　　*/
    void deleteStandardRecord(Long buildStandardId) throws BusinessException;

    /**
     * @description: 批量删除建造标准细则和标准说明
     * @param deleteVo
     * @return void
     * <AUTHOR>
     * @date 2022/8/5 11:24
     */
    void deleteStandardDetailAndDesc(ZbProjectStandardDeleteVo deleteVo);

    /**
     * 修改建造标准行数据
     * @throws
     * @param standardId
     * @param detail
     * <AUTHOR>
     * @return {@link ZbProjectStandardDetail}
     * @date 2021/8/18 10:36
     */
    ZbProjectStandardDetail updateStandardDetailCol(Long standardId, ZbProjectStandardDetail detail) throws BusinessException;

    /**
    　　* @description: 获取所有业态
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/8/18 9:29
    　　*/
    List<ZbCategoryDicDto> getCategoryDicList(String customerCode);

    /**
     * 根据建造标准id获取建造标准树形表格
     * @throws
     * @param standardId
     * @param dataType 返回数据的结构（0：原始结构；1：树结构；默认值为 0 原始结构）
     * <AUTHOR>
     * @return {@link List<  ZbProjectStandardDetailTreeDto >}
     * @date 2021/8/18 17:06
     */
    List<ZbProjectStandardDetailTreeDto> getDetailTree(Long standardId, Integer dataType) throws BusinessException;

    /**
     * @description: 根据建造标准id获取建造标准细则
     * @param standardId
     * @param dataType
     * @param isSelf 是否暂存
     * @return com.glodon.qydata.dto.StandardDetailDto
     * <AUTHOR>
     * @date 2022/8/1 14:41
     */
    StandardDetailDto getDetailList(Long standardId, Integer dataType, boolean isSelf, boolean isSetCategoryAndPosition);

    /**
     * 校验长度
     * @throws
     * @param name
     * @param desc
     * @return
     * <AUTHOR>
     * @date 2021/8/23 10:32
     */
    boolean checkLength(String name, String desc);

    /**
     * 新建时校验行数据名称重复
     * @throws
     * @param standardId
     * @param dto
     * <AUTHOR>
     * @return {@link Boolean}
     * @date 2021/8/23 10:34
     */
    boolean checkDetailName(Long standardId, ZbProjectStandardDetailColDto dto);

    /**
     * 建造标准行数据上下移接口
     * @throws
     * @param flag 上下移标识，1：上移，2：下移
     * @param detail
     * <AUTHOR>
     * @return
     * @date 2021/10/26 14:29
     */
    List<ZbProjectStandardDetailTreeDto> moveUpDown(Integer flag, ZbProjectStandardDetail detail) throws BusinessException;

    /**
     * 导入内置标准
     * @throws
     * @param file
     * <AUTHOR>
     * @return
     * @date 2021/10/28 10:04
     */
    void importBuiltInStandard(MultipartFile file);

    /**
     　　* @description: 将旧企业特征标准数据复制到新企业特征标准
     　　* @param  oldCustomerCode:原企业编码   newCustomerCode 新企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 9:55
     　　*/
    void initProjectStandardData(String oldCustomerCode,String newCustomerCode);

    /**
    　　* @description: 根据企业编码删除所有建造标准信息
    　　* @param  customerCode 企业编码
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/9 19:22
    　　*/
    void deleteByCustomerCOde(String customerCode);

    /**
     * 复制建造标砖
     * @param zbProjectStandardBo 建造标准信息
     */
    ZbProjectStandard copy(ZbCopyProjectStandardBo zbProjectStandardBo) throws BusinessException;

    /**
     * 根据业态编码获取其下的建造标准
     * @throws
     * @param customerCode
     * @param categoryCode
     * <AUTHOR>
     * @return {@link List< ZbProjectStandard>}
     * @date 2022/1/18 10:19
     */
    List<ZbProjectStandard> getByCategoryCode(String customerCode, String categoryCode, boolean isSetCategoryAndPosition);

    /**
     * 按企业编码获取建造标准列表
     * @param customerCode
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * @return
     */
    List<ZbProjectStandard> getSelfStandardList(String customerCode, Integer isShowDelete) throws BusinessException;

    /**
     * 根据建造标准id获取暂存建造标准树形表格
     * @param standardId
     * @param dataType dataType 返回数据的结构（0：原始结构；1：树结构；默认值为 0 原始结构）
     * @return
     */
    StandardDetailDto getSelfDetailTree(Long standardId, Integer dataType) throws BusinessException;

    /**
     * 发布暂存数据
     */
    void publish(String customerCode, String globalId) throws BusinessException;

    /**
     *获取暂存所有业态
     * @return
     */
    List<ZbCategoryDicDto> getSelfCategoryDicList() throws BusinessException;

    /**
     * 根据标准id集合查询所有细则(外部接口用)
     * @param standardIdList
     * @return细则集合
     */
    List<ZbProjectStandardDetail> getDetailByStandardIdList(List<Long> standardIdList);

    /**
     * 根据分类编码获取建造标准
     * @param customerCode
     * @param categoryCodeList
     * @return
     */
    List<ZbProjectStandard> getStandardByCategoryCodes(String customerCode, List<String> categoryCodeList);

    /**
     * 根据分类编码，模糊查询建造标准
     * @param customerCode
     * @param categoryCode
     * @return
     */
    List<ZbProjectStandard> getStandardLikeCategoryCode(String customerCode, String categoryCode);

    /**
     * 处理历史数据
     * 0 企业，1暂存
     */
    void historyDetailToDesc(String type);

    /**
     * @description: 标准说明上下移
     * @param flag 上下移标识，1：上移，2：下移
     * @param descId
     * @param standardDetailId
     * @return com.glodon.qydata.vo.common.ResponseVo<java.util.List < com.glodon.qydata.dto.ZbProjectStandardDetailTreeDto>>
     * <AUTHOR>
     * @date 2022/8/4 9:42
     */
    List<ZbStandardsBuildStandardDetailDesc> descMoveUpDown(Integer flag, Long descId, Long standardDetailId);


    /**
     　　* @description: 根据企业编码与工程分类末级业态查询符合要求的数据列表
     　　* @param [customerCode, categoryCode, positionName]
     　　* @return java.util.List<com.glodon.qydata.dto.ZbProjectStandardPositionAndDetailDto>
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/8/5 16:14
     　　*/
    List<ZbProjectStandardDetailDto> getListByCategoryAndPosition(String customerCode, String categoryCode, String positionName);
    /**
     * @description: 新增标准说明
     * @param addVo
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard
     * <AUTHOR>
     * @date 2022/8/5 14:52
     */
    StandardsBuildStandardDetailDescVo addDesc(DetailDescAddVo addVo);

    /**
     * @description: 编辑标准说明
     * @param updateVo
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc
     * <AUTHOR>
     * @date 2022/8/6 22:26
     */
    StandardsBuildStandardDetailDescVo updateDesc(DetailDescUpdateVo updateVo);

    /**
     * 分页查询
     * @param standardQuery
     * @return
     */
    PageInfo<ZbProjectStandard> standardPageList(ProjectStandardQueryDto standardQuery, boolean isSetCategoryAndPosition);

    /**
     * 根据业态编码查询详情
     * @param categoryCode
     * @param dataType
     * @return
     */
    StandardDetailDto getDetailListByCategoryCode(String customerCode,String categoryCode, Integer dataType, boolean isSetCategoryAndPosition);


    /**
     * 查询建造标准列表
     * @param listVo 查询入参
     * @param enterpriseId 企业id
     * @return 列表所需分页之后的数据
     */
    PageInfo<ImportListDetailVo> importList(ImportListVo listVo, String enterpriseId);

    void importFromHistoricalFile(ImportFromHistoricalFileVo importVo, String enterpriseId);
}
