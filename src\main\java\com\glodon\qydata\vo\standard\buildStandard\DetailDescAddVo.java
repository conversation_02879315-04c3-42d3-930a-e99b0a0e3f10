package com.glodon.qydata.vo.standard.buildStandard;

import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 标准说明新增
 * @date 2022/8/5 10:03
 */
@Data
public class DetailDescAddVo {
    /** 选中的标准说明 */
    private Long checkedDescId;

    /** 标准id */
    @NotNull(message = "标准ID不能为空")
    private Long standardId;

    /** 细则id */
    @NotNull(message = "细则ID不能为空")
    private Long standardDetailId;

    /**
     * 标准名称
     */
    private String name;

    /**
     * 标准说明
     */
    private String detailDesc;

    /**
     * 编码
     */
    private String code;

    /**
     * 数据类型： text文本类，number数值类，select单选类，selects多选类
     */
    private String typeCode;

    /**
     * 枚举值
     */
    private String selectList;

    /**
     * 专业id
     */
    private Long tradeId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 各定位的值
     */
    private List<ZbStandardsBuildPositionDetail> values;
}
