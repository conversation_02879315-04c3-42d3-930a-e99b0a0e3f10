package com.glodon.qydata.service.trust;

import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.config.CommonConfig;
import com.glodon.qydata.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.text.MessageFormat;
import java.util.List;


/**
 * 委托服务外部依赖
 * @author: caidj
 */
@Slf4j
@Service
public class TrustService {

    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 查询委托数据
     * @param globalId
     * @param productSource
     * @return
     * @throws BusinessException
     */
    public List<TrustData> trustData(String globalId, List<String> productSource) throws BusinessException {
        String url = "{0}/trustInfo/allData/{1}/?productSource={2}&globalId={3}";
        String requestUrl = MessageFormat.format(url, commonConfig.getTrustUrl(), TrustConstants.DATA_SOURCE_PROJECTLIB, productSource, globalId);

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("globalId", globalId);

            HttpEntity<String> entity = new HttpEntity<>(null, headers);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(requestUrl, HttpMethod.GET, entity, JSONObject.class);
            JSONObject body = exchange.getBody();
            List<TrustData> data = null;
            if (body!=null && ResponseCode.SUCCESS.getCode().equals(body.getInteger("code")) && body.getJSONArray("data")!=null) {
                data = body.getJSONArray("data").toJavaList(TrustData.class);
            }
            return data;
        }catch (Exception e){
            log.error("访问委托服务查询委托信息trustData error,url={},globalId={},productSource={},e={}",requestUrl,globalId,productSource,e.getMessage());
            throw new BusinessException("调用委托服务查询委托信息失败"+e);
        }
    }

    public Boolean trustDataAuth(String globalId, String productSource, String dataKey, String destEnterpriseId) throws BusinessException {
        String url = "{0}/api/service/trustInfo/allData/auth/{1}?productSource={2}&originEnterpriseId={3}&dataKey={4}&globalId={5}";
        String requestUrl = MessageFormat.format(url,
                commonConfig.getTrustUrl(),
                TrustConstants.DATA_SOURCE_BASICINFO,
                productSource,
                destEnterpriseId,
                dataKey,
                globalId);
        log.info("请求委托服务：{}", requestUrl);
        try {
            HttpHeaders headers = new HttpHeaders();

            HttpEntity<String> entity = new HttpEntity<>(null, headers);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(requestUrl, HttpMethod.GET, entity, JSONObject.class);
            JSONObject body = exchange.getBody();
            log.info("返回数据：{}", body);
            Boolean data = false;
            if (body!=null && ResponseCode.SUCCESS.getCode().equals(body.getInteger("code")) && body.get("data")!=null) {
                AuthInfoVo authInfoVo = body.getObject("data", AuthInfoVo.class);
                data = authInfoVo.getHasAuth();
            }
            return data;
        }catch (Exception e){
            log.error("访问委托服务查询委托信息trustData error,url={},globalId={},productSource={},e={}",requestUrl,globalId,productSource,e.getMessage());
            throw new BusinessException("调用委托服务查询委托信息权限失败"+e);
        }
    }
}
