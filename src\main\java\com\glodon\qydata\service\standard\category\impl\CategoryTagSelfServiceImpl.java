package com.glodon.qydata.service.standard.category.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.TagTypeEnum;
import com.glodon.qydata.dto.CategoryTagColumDTO;
import com.glodon.qydata.dto.CategoryTagRelListDto;
import com.glodon.qydata.entity.standard.category.*;
import com.glodon.qydata.entity.standard.tag.TagIntegration;
import com.glodon.qydata.mapper.standard.category.*;
import com.glodon.qydata.mapper.standard.tag.TagIntegrationMapper;
import com.glodon.qydata.service.init.self.InitCategoryTagSelfService;
import com.glodon.qydata.service.standard.category.CategoryTagSelfService;
import com.glodon.qydata.service.standard.category.CommonProjCategorySelfService;
import com.glodon.qydata.util.mover.ElementMover;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.CategoryConstants.LEVEL_1;


/**
 * @author: luoml-b
 * @date: 2024/10/22 14:22
 * @description:
 */
@Service
public class CategoryTagSelfServiceImpl implements CategoryTagSelfService {

    @Autowired
    private CategoryTagSelfMapper categoryTagSelfMapper;

    @Autowired
    private TagIntegrationMapper tagIntegrationMapper;


    @Autowired
    private CategoryTagEnumSelfMapper categoryTagEnumSelfMapper;

    @Autowired
    private CategoryTagEnumMapper categoryTagEnumMapper;

    @Autowired
    private CategoryTagEnumRelSelfMapper categoryTagEnumRelSelfMapper;

    @Autowired
    private CategoryTagEnumRelMapper categoryTagEnumRelMapper;

    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;

    @Autowired
    private CommonProjCategorySelfMapper commonProjCategorySelfMapper;

    @Autowired
    private InitCategoryTagSelfService initCategoryTagSelfService;


    @Autowired
    private CommonProjCategorySelfService commonProjCategorySelfService;

    /**
     * 全局顺序
     */
    private final ThreadLocal<Integer> entiretyOrd = ThreadLocal.withInitial(() -> 1);

    /**
     * 一级工程分类的顺序
     */
    private final ThreadLocal<Integer> level1stOrd = ThreadLocal.withInitial(() -> 1);


    @Override
    public List<CategoryTag> getCategoryTag(String enterpriseId) {
        List<CategoryTag> categoryTags = categoryTagSelfMapper.selectByEnterpriseId(enterpriseId);
        List<CategoryTagEnumRel> categoryTagEnumRels = categoryTagEnumRelSelfMapper.selectByEnterpriseId(enterpriseId);
        List<Long> tagIds = categoryTagEnumRels.stream().map(CategoryTagEnumRel::getTagId).collect(Collectors.toList());
        categoryTags.forEach(x -> {
            x.setCanEdit(0);
            if (tagIds.contains(x.getId())) {
                x.setCanEdit(1);
            }
        });
        return categoryTags;
    }

    @Override
    public List<CategoryTagEnum> getCategoryTagEnum(String enterpriseId, Long tagId) {
        List<CategoryTagEnum> categoryTagEnums = categoryTagEnumSelfMapper.selectByTagId(enterpriseId, tagId);
        List<CategoryTagEnumRel> categoryTagEnumRels = categoryTagEnumRelSelfMapper.selectByEnterpriseId(enterpriseId);
        List<Long> tagEnumIds = categoryTagEnumRels.stream().map(CategoryTagEnumRel::getTagEnumId).collect(Collectors.toList());
        categoryTagEnums.forEach(x -> {
            x.setCanEdit(0);
            if (tagEnumIds.contains(x.getId())) {
                x.setCanEdit(1);
            }
        });
        return categoryTagEnums;
    }

    @Override
    public CategoryTagRelListDto getCategoryTagRelList(String enterpriseId) {
        CategoryTagRelListDto categoryTagRelListDto = new CategoryTagRelListDto();
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(enterpriseId);
        List<CommonProjCategory> categoryListTree = commonProjCategorySelfService.getSelfCategoryListTree(enterpriseId, type);
        if (CollUtil.isEmpty(categoryListTree)) {
            return categoryTagRelListDto;
        }
        categoryListTree = categoryListTree.stream()
                .filter(x -> Objects.equals(x.getLevel(), Constants.CategoryConstants.LEVEL_1.longValue())).collect(Collectors.toList());


        List<CategoryTagColumDTO> tableHead = new ArrayList<>();
        CategoryTagColumDTO categoryNameColumDTO = new CategoryTagColumDTO();
        categoryNameColumDTO.setColumnCode(Constants.CategoryTagConstants.COLUM_CODE_CATEGORY_NAME);
        categoryNameColumDTO.setColumnName(Constants.CategoryTagConstants.COLUM_CODE_CATEGORY_NAME_NAME);
        tableHead.add(categoryNameColumDTO);

        List<CategoryTagEnumRel> categoryTagEnumRels =
                ((CategoryTagSelfServiceImpl) AopContext.currentProxy()).assembleData(enterpriseId);

        List<CategoryTag> categoryTag = this.getCategoryTag(enterpriseId);
        for (CategoryTag tag : categoryTag) {
            CategoryTagColumDTO categoryTagColumDTO = new CategoryTagColumDTO();
            categoryTagColumDTO.setColumnCode(DigestUtil.md5Hex(tag.getName()));
            categoryTagColumDTO.setColumnName(tag.getName());
            categoryTagColumDTO.setTagId(tag.getId());
            tableHead.add(categoryTagColumDTO);
        }
        categoryTagRelListDto.setTableHead(tableHead);

        Map<String, List<CategoryTagEnumRel>> relMap = categoryTagEnumRels.stream().collect(Collectors.groupingBy(CategoryTagEnumRel::getCategoryCode));

        List<Map<String, String>> tableData = new ArrayList<>();
        for (CommonProjCategory category : categoryListTree) {
            Map<String, String> row = new HashMap<>();
            row.put(Constants.CategoryTagConstants.COLUM_CODE_CATEGORY_CODE, category.getCommonprojcategoryid());
            row.put(Constants.CategoryTagConstants.COLUM_CODE_CATEGORY_NAME, category.getCategoryname());
            List<CategoryTagEnumRel> currenRels = relMap.get(category.getCommonprojcategoryid());
            if (CollUtil.isNotEmpty(currenRels)) {
                for (CategoryTagColumDTO head : tableHead) {
                    currenRels.stream().
                            filter(x -> Objects.equals(x.getTagName(), head.getColumnName())).
                            findFirst().ifPresent(x -> row.put(head.getColumnCode(), x.getTagEnumName()));
                }
            }
            tableData.add(row);
        }
        categoryTagRelListDto.setTableData(tableData);
        return categoryTagRelListDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public List<CategoryTagEnumRel> assembleData(String enterpriseId) {
        //初始化
        List<CategoryTag> categoryTags = categoryTagSelfMapper.selectByEnterpriseId(enterpriseId);
        List<TagIntegration> tagIntegrations = tagIntegrationMapper.selectByEnterpriseId(enterpriseId, TagTypeEnum.CATEGORY_TAG.getTagType());
        if (CollUtil.isEmpty(categoryTags) && CollUtil.isEmpty(tagIntegrations)) {
            initCategoryTagSelfService.initData(enterpriseId);
        }
        return categoryTagEnumRelSelfMapper.selectByEnterpriseId(enterpriseId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyCategoryTag(String enterpriseId, List<CategoryTag> categoryTagList) {
        //查出之前的标签，过滤出需要删除的，关联删除掉对应的枚举
        List<CategoryTag> queryCategoryTags = categoryTagSelfMapper.selectByEnterpriseId(enterpriseId);
        if (CollUtil.isNotEmpty(queryCategoryTags)) {
            List<Long> ids = categoryTagList.stream().map(CategoryTag::getId).collect(Collectors.toList());
            List<Long> deleteTagEnums = queryCategoryTags
                    .stream().map(CategoryTag::getId)
                    .filter(id -> !ids.contains(id)).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(deleteTagEnums)) {
                categoryTagEnumSelfMapper.deleteByTagIds(enterpriseId, deleteTagEnums);
            }
        }
        //先全部删除。再新增
        categoryTagSelfMapper.deleteByEnterpriseId(enterpriseId);
        categoryTagList.forEach(x -> {
            x.setEnterpriseId(enterpriseId);
            x.setCreateTime(new Date());
            if (x.getId() == null) {
                x.setId(SnowflakeIdUtils.getNextId());
            }
        });
        categoryTagSelfMapper.saveBatch(categoryTagList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void modifyCategoryTagEnum(String enterpriseId, Long tagId, List<CategoryTagEnum> categoryTagEnumList) {
        //先全部删除。再新增
        categoryTagEnumSelfMapper.deleteByTagId(enterpriseId, tagId);
        if (CollUtil.isEmpty(categoryTagEnumList)) {
            return;
        }
        categoryTagEnumList.forEach(x -> {
            x.setCreateTime(new Date());
            x.setEnterpriseId(enterpriseId);
            x.setTagId(tagId);
            if (x.getId() == null) {
                x.setId(SnowflakeIdUtils.getNextId());
            }
        });
        categoryTagEnumSelfMapper.saveBatch(categoryTagEnumList);
    }

    /**
     * @description: 处理带有标签的情况，如果工程分类带有标签，
     *               需要构建带有标签的树，且顺序会有变化，一级分类的顺序跟着其挂接的标签走
     * @author: luoml-b
     * @date: 2024/11/15 10:02
     * @param: enterpriseId：企业id
     * @param: categoryList：工程分类列表
     * @param: selfFlag：是否查询暂存（true：查询暂存表；false：查询发布表）
     * @param: includeTag：是否包含标签（true：包含标签；false：不包含标签）
     * @param: categoryCode1: 一级分类编码,如果有值只返回一级分类编码对应的标签
     * @return: java.util.List<com.glodon.qydata.entity.standard.category.CommonProjCategory>
     **/
    @Override
    public List<CommonProjCategory> dealWithTag(String enterpriseId,
                                                List<CommonProjCategory> categoryList,
                                                boolean selfFlag,
                                                boolean includeTag,
                                                String categoryCode1) {
        List<CategoryTagEnumRel> categoryTagEnumRels;
        List<CategoryTagEnum> categoryTagEnums;
        if (selfFlag) {
            categoryTagEnumRels = categoryTagEnumRelSelfMapper.selectByEnterpriseId(enterpriseId);
            categoryTagEnums = categoryTagEnumSelfMapper.selectByTagId(enterpriseId, null);
        } else {
            categoryTagEnumRels = categoryTagEnumRelMapper.selectNameByEnterpriseId(enterpriseId);
            categoryTagEnums = categoryTagEnumMapper.selectByTagId(enterpriseId, null);
        }

        // 如果指定了一级分类编码,过滤一级分类编码
        if (!StringUtils.isEmpty(categoryCode1)){
            categoryTagEnumRels = categoryTagEnumRels.stream().filter(c->(StringUtils.equals(c.getCategoryCode(), categoryCode1))).collect(Collectors.toList());
        }
        Map<Long, List<CategoryTagEnum>> tagIdMap = categoryTagEnums.stream().collect(Collectors.groupingBy(CategoryTagEnum::getTagId));
        if (CollUtil.isEmpty(categoryTagEnumRels)) {
            //没有标签，直接返回工程分类，但是如果是查询暂存表（前端查询），需要重新生成一个全局ord，给分类预览用
            if (selfFlag) {
                wrapRebuildOrd(categoryList);
            }
            return categoryList;
        }
        List<CommonProjCategory> tagTree = assembleTagTree(categoryList, categoryTagEnumRels, tagIdMap);
        if (!includeTag) {
            //去掉tag标签级别
            List<CommonProjCategory> result = new ArrayList<>();
            removeTag(tagTree, result);
            tagTree = result;
        }
        return tagTree;
    }


    @Override
    public List<CommonProjCategory> assembleTagTree(List<CommonProjCategory> categoryList,
                                                    List<CategoryTagEnumRel> categoryTagEnumRels,
                                                    Map<Long, List<CategoryTagEnum>> tagIdMap) {
        List<CommonProjCategory> tagTree = buildTagTree(categoryTagEnumRels);
        sortTagTree(tagTree, tagIdMap);
        combineTree(tagTree, categoryList);
        tagTree.addAll(categoryList);
        wrapRebuildOrd(tagTree);
        return tagTree;
    }

    /**
     * @description: 去掉标签级
     * @author: luoml-b
     * @date: 2024/11/4 11:12
     * @param: tagTree
     * @param: result
     **/
    private void removeTag(List<CommonProjCategory> tagTree, List<CommonProjCategory> result) {
        for (CommonProjCategory commonProjCategory : tagTree) {
            if (!Objects.equals(Constants.CategoryTagConstants.DATA_TYPE_TAG, commonProjCategory.getDataType())
                    && Objects.equals(LEVEL_1.longValue(), commonProjCategory.getLevel())) {
                result.add(commonProjCategory);
            }
            if (CollUtil.isNotEmpty(commonProjCategory.getSublist())) {
                removeTag(commonProjCategory.getSublist(), result);
            }
        }
    }

    /**
     * @description: 对标签树排序，每一层及按照枚举值的顺序
     * @author: luoml-b
     * @date: 2024/11/4 11:10
     * @param: tagTree
     * @param: tagIdMap
     **/
    private void sortTagTree(List<CommonProjCategory> tagTree, Map<Long, List<CategoryTagEnum>> tagIdMap) {
        if (CollUtil.isEmpty(tagTree)) {
            return;
        }
        Long tagId = tagTree.stream().map(CommonProjCategory::getTagId).findFirst().orElse(null);
        if (tagId == null) {
            return;
        }
        List<CategoryTagEnum> currentCategoryTagEnums = tagIdMap.get(tagId);
        List<String> sortedOrder = currentCategoryTagEnums.stream()
                .map(CategoryTagEnum::getName)
                .collect(Collectors.toList());
        // 对当前层的节点按照sortedOrder排序
        tagTree.sort(Comparator.comparing(category -> {
            // 找到当前节点的name在sortedOrder中的位置，如果不存在则放在最后
            int index = sortedOrder.indexOf(category.getCategoryname());
            return index != -1 ? index : Integer.MAX_VALUE;
        }));

        for (CommonProjCategory commonProjCategory : tagTree) {
            //生成一个根据path的唯一id，变成负数是为了不和工程分类的id相等
            int hash = new HashCodeBuilder(17, 37)
                    .append(commonProjCategory.getFullPath()).toHashCode();
            commonProjCategory.setId(hash * -1);
            if (CollUtil.isNotEmpty(commonProjCategory.getSublist())) {
                    sortTagTree(commonProjCategory.getSublist(), tagIdMap);
                }
            }
        }

    /**
     * @description: 重新构建整体顺序和一级工程分类顺序
     * @author: luoml-b
     * @date: 2024/11/4 11:11
     * @param: tagTree
     **/
    private void rebuildOrd(List<CommonProjCategory> tagTree) {
        for (CommonProjCategory category : tagTree) {
            int currentOrd = entiretyOrd.get();
            int currentLevel1stOrd = level1stOrd.get();
            if (Objects.equals(LEVEL_1.longValue(), category.getLevel())) {
                category.setOrd(currentLevel1stOrd);
                level1stOrd.set(currentLevel1stOrd + 1);
            }
            category.setEntiretyOrd(currentOrd);
            entiretyOrd.set(currentOrd + 1);
            if (CollUtil.isNotEmpty(category.getSublist())) {
                rebuildOrd(category.getSublist());
            }
        }
    }

    private void wrapRebuildOrd(List<CommonProjCategory> tagTree) {
        try {
            rebuildOrd(tagTree);
        } finally {
            this.entiretyOrd.remove();
            this.level1stOrd.remove();
        }
    }

    /**
     * @description: 连接标签树和工程分类树
     * @author: luoml-b
     * @date: 2024/11/4 11:11
     * @param: tagTree
     * @param: categoryTree
     **/
    private void combineTree(List<CommonProjCategory> tagTree, List<CommonProjCategory> categoryTree) {
        for (CommonProjCategory category : tagTree) {
            if (CollUtil.isEmpty(category.getSublist()) && StringUtils.isBlank(category.getTagHasCategoryCode())) {
                return;
            }
            if (StringUtils.isNotBlank(category.getTagHasCategoryCode())) {
                List<String> categoryCodeList = Arrays.stream(category.getTagHasCategoryCode().split(","))
                        .collect(Collectors.toList());
                List<CommonProjCategory> categories = categoryTree.stream()
                        .filter(x -> categoryCodeList.contains(x.getCommonprojcategoryid())).collect(Collectors.toList());
                categoryTree.removeAll(categories);
                if (CollUtil.isNotEmpty(category.getSublist())) {
                    category.getSublist().addAll(categories);
                } else {
                    category.setSublist(categories);
                }
            }
            combineTree(category.getSublist(), categoryTree);
        }
    }

    /**
     * @description: 构建标签树
     * @author: luoml-b
     * @date: 2024/11/4 11:10
     * @param: list
     * @return: java.util.List<com.glodon.qydata.entity.standard.category.CommonProjCategory>
     **/
    private List<CommonProjCategory> buildTagTree(List<CategoryTagEnumRel> list) {

        Map<String, List<CategoryTagEnumRel>> categoryCodeTreeMap = list.
                stream().collect(Collectors.groupingBy(CategoryTagEnumRel::getCategoryCode));
        for (List<CategoryTagEnumRel> categoryList : categoryCodeTreeMap.values()) {
            if (CollUtil.isNotEmpty(categoryList)) {
                categoryList.get(categoryList.size() - 1).setMountedNode(true);
            }
        }

        List<CommonProjCategory> res = new ArrayList<>();
        Map<String, String> codeMap = new HashMap<>();
        categoryCodeTreeMap.forEach((k, v) -> {
            CategoryTagEnumRel root = v.get(0);
            CommonProjCategory rootNode = new CommonProjCategory();
            rootNode.setCategoryname(root.getTagEnumName());
            rootNode.setFullPath(root.getTagEnumName());
            rootNode.setRelIdPath(String.valueOf(root.getId()));
            rootNode.setParentPath(Constants.CategoryTagConstants.ROOT_PARENT_PATH);
            rootNode.setDataType(Constants.CategoryTagConstants.DATA_TYPE_TAG);
            String tagHasCategoryCode = "";
            if (root.getMountedNode()) {
                if (StringUtils.isNotBlank(codeMap.get(rootNode.getFullPath()))) {
                    tagHasCategoryCode = codeMap.get(rootNode.getFullPath()) + "," + root.getCategoryCode();
                } else {
                    tagHasCategoryCode = root.getCategoryCode();
                }
                codeMap.put(rootNode.getFullPath(), tagHasCategoryCode);
            }
            rootNode.setRemark(String.format("【%s】标签", root.getTagName()));
            rootNode.setTagId(root.getTagId());
            rootNode.setTagEnumId(root.getTagEnumId());
            res.add(rootNode);
            recursiveBuildTree(res, rootNode, v.subList(1, v.size()), codeMap);
        });
        List<CommonProjCategory> result = mergeTree(res);
        res.forEach(x -> x.setTagHasCategoryCode(codeMap.get(x.getFullPath())));
        return result;
    }

    private List<CommonProjCategory> mergeTree(List<CommonProjCategory> list) {
        Set<String> fullPath = new HashSet<>();
        Map<String, List<CommonProjCategory>> map = list.stream().filter(e -> fullPath.add(e.getFullPath()))
                .collect(Collectors.groupingBy(CommonProjCategory::getParentPath));
        // 拿出所有的根节点
        List<CommonProjCategory> res = map.get(Constants.CategoryTagConstants.ROOT_PARENT_PATH);
        if (CollUtil.isNotEmpty(res)) {
            res.forEach(x -> getTree(x, map));
        }
        return res;
    }

    private void getTree(CommonProjCategory category, Map<String, List<CommonProjCategory>> map) {
        String path = category.getFullPath();
        if (map.containsKey(path)) {
            category.setSublist(map.get(path));
            category.getSublist().forEach(item -> getTree(item, map));
        }
    }


    private void recursiveBuildTree(List<CommonProjCategory> res, CommonProjCategory rootNode, List<CategoryTagEnumRel> subList, Map<String, String> codeMap) {
        if (CollUtil.isEmpty(subList)) {
            return;
        }
        CategoryTagEnumRel node = subList.get(0);
        CommonProjCategory commonProjCategory = new CommonProjCategory();
        commonProjCategory.setCategoryname(node.getTagEnumName());
        commonProjCategory.setParentPath(rootNode.getFullPath());
        commonProjCategory.setFullPath(rootNode.getFullPath() + "/" + commonProjCategory.getCategoryname());
        commonProjCategory.setRelIdPath(rootNode.getRelIdPath() + "/" + node.getId());
        String tagHasCategoryCode = "";
        if (node.getMountedNode()) {
            if (StringUtils.isNotBlank(codeMap.get(commonProjCategory.getFullPath()))) {
                tagHasCategoryCode = codeMap.get(commonProjCategory.getFullPath()) + "," + node.getCategoryCode();
            } else {
                tagHasCategoryCode = node.getCategoryCode();
            }
            codeMap.put(commonProjCategory.getFullPath(), tagHasCategoryCode);
        }
        commonProjCategory.setDataType(Constants.CategoryTagConstants.DATA_TYPE_TAG);
        commonProjCategory.setRemark(String.format("【%s】标签", node.getTagName()));
        commonProjCategory.setTagId(node.getTagId());
        rootNode.setTagEnumId(node.getTagEnumId());
        res.add(commonProjCategory);
        recursiveBuildTree(res, commonProjCategory, subList.subList(1, subList.size()), codeMap);
    }

    @Override
    @Transactional
    public Boolean relSaveBatch(String enterpriseId, List<CategoryTagEnumRel> categoryTagRelList) {
        categoryTagEnumRelSelfMapper.deleteByEnterpriseId(enterpriseId);
        if (!CollUtil.isEmpty(categoryTagRelList)) {
            categoryTagRelList.forEach(rel -> {
                rel.setId(SnowflakeIdUtils.getNextId());
                rel.setEnterpriseId(enterpriseId);
                rel.setCreateTime(new Date());
            });
            categoryTagEnumRelSelfMapper.saveBatch(categoryTagRelList);
            resetCategoryOrd(enterpriseId);
        }
        return true;
    }

    public void resetCategoryOrd(String enterpriseId) {
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(enterpriseId);
        List<CommonProjCategory> categoryList = commonProjCategorySelfMapper.queryTopCategoryList(enterpriseId, type);

        List<CategoryTagEnumRel> categoryTagEnumRels = categoryTagEnumRelSelfMapper.selectByEnterpriseId(enterpriseId);

        List<CategoryTagEnum> categoryTagEnumList = categoryTagEnumSelfMapper.selectByTagId(enterpriseId, null);
        Map<Long, List<CategoryTagEnum>> tagIdMap = categoryTagEnumList.stream().collect(Collectors.groupingBy(CategoryTagEnum::getTagId));

        List<CommonProjCategory> tagTree = this.assembleTagTree(categoryList, categoryTagEnumRels, tagIdMap);

        List<CommonProjCategory> needResetOrdCategoryList = new ArrayList<>();
        filterCategory(tagTree, needResetOrdCategoryList);

        ElementMover.saveToDatabase(needResetOrdCategoryList, commonProjCategorySelfMapper);
    }

    private void filterCategory(List<CommonProjCategory> tagTree, List<CommonProjCategory> needResetOrdCategoryList) {
        if (CollectionUtils.isEmpty(tagTree)) {
            return;
        }
        tagTree.forEach(node -> {
            if (!Constants.CategoryTagConstants.DATA_TYPE_TAG.equals(node.getDataType())) {
                needResetOrdCategoryList.add(node);
            }
            if (!CollectionUtils.isEmpty(node.getSublist())) {
                filterCategory(node.getSublist(), needResetOrdCategoryList);
            }
        });
    }
}
