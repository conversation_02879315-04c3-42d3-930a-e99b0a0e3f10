package com.glodon.qydata.util;

import java.io.*;
import java.util.ArrayList;
import java.util.List;

/**
 * @program: ideaworkspace
 * @description: list相关工具
 * @author: zhaohy-c
 * @create: 2021-03-12 17:20
 */
public class ListUtils {

    /**
     * @Title: deepCopy
     * @Description: 深度拷贝 List
     * @throws
     * @param: [src]
     * @return: java.util.List<T>
     * @auther: zhaohy-c
     * @date: 2021/3/12 17:20
     */
    public static <T> List<T> deepCopy(List<T> src) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = new ObjectOutputStream(byteOut);
        out.writeObject(src);

        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        ObjectInputStream in = new ObjectInputStream(byteIn);
        @SuppressWarnings("unchecked")
        List<T> dest = (List<T>) in.readObject();
        return dest;
    }
    /**
     * 分隔list
     * @param list
     * @param splitLength
     * @param <T>
     * @return
     * @throws Exception
     */
    public static  <T> List<List<T>> subList(List<T> list,int splitLength) {
        List<List<T>> lists = new ArrayList<>();
        int size;
        if(list!=null&&(size=list.size())>splitLength){
            int cycle = size/splitLength;
            for(int i=0;i<cycle;i++){
                lists.add(list.subList(i*splitLength,splitLength+(i*splitLength)));
            }
            lists.add(list.subList(cycle*splitLength,size));
        }else {
            lists.add(list);
        }
        return lists;
    }
}
