package com.glodon.qydata.service.system;

import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.vo.system.RightInfoVo;

import java.util.List;

/**
 * 系统授权、权限相关
 * Created by weijf on 2022/1/26.
 */
public interface ISysRightInfoService {

    /**
     * 查询用户授权：rightType、remainingDays
     * 查询用户权限：canEdit
     * @param globalId 用户广联云gldUserId
     * @param sgToken 施工平台登录Token
     * @param selectCache 是否查询缓存，true：是，false：否
     * @return
     * @throws BusinessException
     * <AUTHOR>
     */
    RightInfoVo getRightInfo(String globalId, String sgToken, boolean selectCache) throws BusinessException;
    /**
     * 查询用户授权：rightType、remainingDays
     * 查询用户权限：canEdit
     * @param globalId 用户广联云gldUserId
     * @param sgToken 施工平台登录Token
     * @param selectCache 是否查询缓存，true：是，false：否
     * @param subSystemCodes 资产编码
     * @return
     * @throws BusinessException
     * <AUTHOR>
     */
    RightInfoVo getRightInfoBySubSystemCodes(String globalId, String sgToken, boolean selectCache, List<String> subSystemCodes) throws BusinessException;

    /**
     * @Description 鉴权
     * <AUTHOR>
     * @Date 2022/1/28 11:11
     * @param
     * @return void
     **/
    void authLimit() throws BusinessException;

    /**
     * @Description 编辑权限校验
     * <AUTHOR>
     * @Date 2022/1/28 13:58
     * @param
     * @return void
     **/
    void checkEditAuth() throws BusinessException;

    /**
     * 根据当前登录用户查询资产编码
     * @param globalId
     * @param gldToken
     * @return
     * <AUTHOR>
     */
    String getAssetNum(String globalId,String gldToken,String pcode);
}
