server:
  port: 8083

log:
  level: DEBUG

spring:
  datasource:
    #主数据源
    master:
      url: jdbc:mysql://**************:3306/basic_info?rewriteBatchedStatements=true&useOldAliasMetadataBehavior=true&serverTimezone=Asia/Shanghai&allowMultiQueries=true&zeroDateTimeBehavior=CONVERT_TO_NULL&sessionVariables=group_concat_max_len=204800
      username: gldzb_admin
      password: heLC0Mq%#tDj
      driverClassName: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource

  #缓存
  redis:
    host: redis-7a87ccaf-578c-443c-940d-e9e2d549c66f.cn-north-4.dcs.myhuaweicloud.com
    port: 6379
    password: 8jHMYhhppe8CHwhW
    timeout: 10000ms
    maxTotal: 300
    jedis:
      pool:
        max-wait: 18000
        max-active: 800
        max-idle: 400
        min-idle: 1
  #上传文件大小设置
  servlet:
    multipart:
      max-file-size: 80MB
      max-request-size: 100MB
config:
  #依赖服务相关配置
  depend:
    #依赖广联云服务域名
    glodonUrl: https://account.glodon.com
    glodonApiAuthUrl: https://api-auth.glodon.com
    glodonEntUrl: https://ent.glodon.com
    #数字成本平台接口URL
    digitalCostApiUrl: http://digital-cost-pre.glodon.com
    #委托服务域名
    trustUrl: https://dcost-sub-pre.glodon.com
    # 指标神器项目划分
    zbsqItemUrl: https://dcost-sub-pre.glodon.com/item
  gcw_cloud:
    APPId: gcw_qy_data_test
    secret: mV1+6Kze
    secretKey: 1d533c999a94c539c4e470615a06c819
glodon:
  getUserinfoByIdentityHost: https://account.glodon.com
  gcw_cloud_url: http://cloud-api.gldjc.com
#IP地址查询，db文件下载地址：https://gitee.com/lionsoul/ip2region/tree/master/data
ip2region:
  path: /opt/project/data/ip2region.db
basic_auth:
  service.key: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
  server.secret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC
# 授权中心服务
apiAuth:
  url: https://api-auth.glodon.com
  appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
  appSecret: EVmzN0G5ebSGh44q9NNhVlwDywvBxSXd
  g-signature: F864378123E9BA92E14E6C7862257FCC