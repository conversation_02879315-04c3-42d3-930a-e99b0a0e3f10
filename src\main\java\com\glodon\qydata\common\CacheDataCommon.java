package com.glodon.qydata.common;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName CacheDataCommon
 * <AUTHOR>
 * @Date 2019/11/1 10:13
 * 公共数据缓存服务
 **/
@Component
public class CacheDataCommon {
    @Autowired
    private ZbStandardsTradeMapper tradeMapper;


/**
 *
 *  标准打通--系统内置专业列表存缓存
 *
 *
 */
    public static final List<ZbStandardsTrade> TRADE_LIST = new ArrayList<>();
    @PostConstruct
    public void cacheTradeData() {
        List<ZbStandardsTrade> tradeListDB = tradeMapper.selectListByCustomerCodeAndType("-100", Constants.ZbStandardsTradeConstants.TradeType.SYSTEM_TRADE);
        for (ZbStandardsTrade zbStandardsTrade : tradeListDB) {
            TRADE_LIST.add(zbStandardsTrade);
        }
    }

}
