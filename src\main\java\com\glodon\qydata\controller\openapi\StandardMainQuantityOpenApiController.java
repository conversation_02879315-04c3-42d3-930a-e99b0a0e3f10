package com.glodon.qydata.controller.openapi;


import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.dto.ZbStandardsMainQuantityDto;
import com.glodon.qydata.service.standard.mainQuantity.IStandardsMainQuantityService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
　　* @description: 标准打通--主要量指标控制层 -对外接口
　　* <AUTHOR>
　　* @date 2021/10/21 19:27
　　*/
@RestController
@RequestMapping("/basicInfo/openApi/standards")
@Tag(name = "标准打通--主要量指标控制层 -对外接口", description = "标准打通--主要量指标控制层 -对外接口")
public class StandardMainQuantityOpenApiController extends BaseController {
    @Autowired
    private IStandardsMainQuantityService mainQuantityService;

    /**
     　　* @description: 对外接口
     根据企业编码与专业编码查看指定专业对应的主要量指标列表
     页面流程为：查询专业列表，再根据专业列表查询对应主要量指标列表
     　　* @param tradeId 专业id
     　　* <AUTHOR>
     　　* @date 2021/11/19 09:05
     　　*/
    @Operation(summary = "对外接口-根据企业编码与专业编码查看指定专业对应的主要量指标列表")
    @GetMapping("/getSpecAttrByTradeCode")
    public ResponseVo<ZbStandardsMainQuantityDto> selectMainQuantityList(@RequestParam String tradeCode,
                                                                         @RequestParam Integer isShowDelete,
                                                                         @RequestParam Integer callerFlag,
                                                                         String destEnterpriseId,
                                                                         String trustProductSource){
        String customerCode = getCustomerCode(TrustConstants.TYPE_MAINQUANTITY, destEnterpriseId, trustProductSource);
        List<ZbStandardsMainQuantityDto> returnList = mainQuantityService.getListByTradeCode(customerCode,tradeCode,isShowDelete);
        return new ResponseVo(ResponseCode.SUCCESS,returnList);
    }

    /**
     　　* @description: 对外接口---查询所有主要量指标列表
     　　* @param tradeId 专业id
     　　* <AUTHOR>
     　　* @date 2021/11/19 09:05
     　　*/
    @Operation(summary = "对外接口---查询所有主要量指标列表")
    @GetMapping("/getSpecAttr")
    public ResponseVo<ZbStandardsMainQuantityDto> selectAllMainQuantityList(@RequestParam Integer isShowDelete,
                                                                            @RequestParam Integer callerFlag,
                                                                            String destEnterpriseId,
                                                                            String trustProductSource){
        Long globalId = StringUtils.isEmpty(destEnterpriseId) ? getGlobalIdLong() : null;
        String customerCode = getCustomerCode(TrustConstants.TYPE_MAINQUANTITY, destEnterpriseId, trustProductSource);
        List<ZbStandardsMainQuantityDto> returnList = mainQuantityService.selectAllMainQuantityList(customerCode, isShowDelete, globalId);
        return new ResponseVo(ResponseCode.SUCCESS,returnList);
    }
}
