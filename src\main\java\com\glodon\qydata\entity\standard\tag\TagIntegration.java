package com.glodon.qydata.entity.standard.tag;

import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: luoml-b
 * @date: 2024/10/22 14:24
 * @description:
 */
@Data
@Builder
public class TagIntegration implements Serializable {

    private static final long serialVersionUID = 4178834902928417832L;
    private Long id;

    private String enterpriseId;

    private Date createTime;

    /**
     * 标签枚举类型,0:工程分类标签 1:专业标签
     */
    private Integer tagType;

}
