package com.glodon.qydata.util;

import org.apache.commons.lang3.StringUtils;

/**
 * 根据UserAgent 获取操作系统及浏览器信息
 *
 * Created by weijf on 2021/8/20.
 */
public class UserAgentUtil {

    /**
     * 获取浏览器版本
     * @param userAgent
     * @return
     */
    @SuppressWarnings("squid:S3776") // 禁止sonar复杂度检测
    public static String getBrower(String userAgent){
        String browserInfo = null;
        if(StringUtils.isEmpty(userAgent)){
            return browserInfo;
        }

        if(userAgent.contains("Firefox")){// 火狐浏览器
            browserInfo = "Firefox";
        }else if(userAgent.contains("Camino")){//
            browserInfo = "Camino";
        }else if(userAgent.contains("Konqueror")){//
            browserInfo = "Konqueror";
        }else if(userAgent.contains("Quark")){// 夸克浏览器
            browserInfo = "Quark";
        }else if(userAgent.contains("baidu")){// 百度浏览器
            browserInfo = "Baidu";
        }else if(userAgent.contains("Edge")){// Edge浏览器
            browserInfo = "Edge";
        }else if(userAgent.contains("TheWorld")){// TheWorld浏览器
            browserInfo = "TheWorld";
        }else if(userAgent.contains("WindowsWechat")){// 微信浏览器
            browserInfo = "微信-PC";
        }else if(userAgent.contains("MicroMessenger")){// 微信浏览器
            browserInfo = "微信";
        }else if(userAgent.contains("QQBrowser") || userAgent.contains("TencentTraveler") || userAgent.contains("QQTheme")){// qq浏览器
            browserInfo = "QQ";
        }else if(userAgent.contains("Avast")){// Avast Secure Browser浏览器
            browserInfo = "Avast";
        }else if(userAgent.contains("OPR")){// Opera浏览器
            browserInfo = "Opera";
        }else if(userAgent.contains("360")){// 360浏览器
            browserInfo = "360";
        }else if(userAgent.contains("LBBROWSER")){// 猎豹浏览器
            browserInfo = "猎豹";
        }else if(userAgent.contains("Maxthon")){// 遨游浏览器
            browserInfo = "遨游";
        }else if(userAgent.contains("MetaSr") || userAgent.contains("Sogou")){// 搜狗浏览器
            browserInfo = "搜狗";
        }else if(userAgent.contains("UCWEB") || userAgent.contains("UCBrowser")){// uc浏览器
            browserInfo = "UC";
        }else if(userAgent.contains("HuaweiBrowser")){// 华为浏览器
            browserInfo = "华为";
        }else if(userAgent.contains("Trident/7")){// IE 11浏览器
            browserInfo = "IE 11";
        }else if(userAgent.contains("Trident/6")){// IE 10浏览器
            browserInfo = "IE 10";
        }else if(userAgent.contains("Trident/5")){// IE 9浏览器
            browserInfo = "IE 9";
        }else if(userAgent.contains("Trident/4")){// IE 8浏览器
            browserInfo = "IE 8";
        }else if(userAgent.contains("MSIE") || userAgent.contains("Trident")){// IE浏览器
            browserInfo = "IE";
        }else if (userAgent.contains("NewsArticle")){// 今日头条
            browserInfo = "头条";
        }else if(userAgent.contains("Postman")){ //Postman
            browserInfo = "Postman";
        }else if(userAgent.contains("Moto")){ //Moto
            browserInfo = "Moto";
        }else if(userAgent.contains("NokiaBrowser")){ //Nokia浏览器
            browserInfo = "Nokia";
        }else if(userAgent.contains("Pixel")){ //
            browserInfo = "其它";
        }else if(userAgent.contains("Chrome")){// 谷歌
            browserInfo = "Chrome";
        }else if(userAgent.contains("CriOS")){// 谷歌
            browserInfo = "Chrome-IOS";
        }else if (userAgent.contains("Safari")){// Safari
            browserInfo = "Safari";
        }
        return browserInfo;

    }

    /**
     * 获取系统版本信息
     */
    public static String getSystem(String userAgent){
        String osInfo = null;
        if(StringUtils.isEmpty(userAgent)){
            return osInfo;
        }

        if(userAgent.indexOf("NT 6.1") != -1 || userAgent.indexOf("NT 5") != -1 || userAgent.indexOf("NT 6.3") != -1 || userAgent.indexOf("NT 6.2") != -1 || userAgent.indexOf("NT 6.0") != -1 || userAgent.indexOf("NT 5.1") != -1
                || userAgent.indexOf("NT 5.2") != -1 || userAgent.indexOf("NT 6.0") != -1){// win10一下的都取win7
            osInfo = "Windows7";
        }else if(userAgent.indexOf("NT 10") != -1){// Win10
            osInfo = "Windows10";
        }else if(userAgent.indexOf("iPhone") != -1){// 苹果手机
            osInfo = "苹果手机";
        }else if(userAgent.indexOf("iPad") != -1){// iPad
            osInfo = "iPad";
        }else if(userAgent.indexOf("HUAWEIEML") != -1 || userAgent.indexOf("HUAWEIOXF") != -1 || userAgent.indexOf("HarmonyOS") != -1){// 华为手机
            osInfo = "华为手机";
        }else if(userAgent.indexOf("Windows Phone") != -1){// Windows Phone
            osInfo = "Windows手机";
        }else if(userAgent.indexOf("Android") != -1){// 安卓系统
            osInfo = "安卓手机";
        }else if(userAgent.indexOf("Mac") != -1){// Mac系统
            osInfo = "Mac系统";
        }else if(userAgent.indexOf("Unix") != -1){// Unix系统
            osInfo = "Unix系统";
        }else if(userAgent.indexOf("SunOS") != -1){// Solaris系统
            osInfo = "Solaris系统";
        }else if(userAgent.indexOf("Linux") != -1){// Linux系统
            osInfo = "Linux系统";
        }else if(userAgent.indexOf("Ubuntu") != -1){// ubuntu系统
            osInfo = "Ubuntu系统";
        }else if(userAgent.indexOf("BB10") != -1 || userAgent.indexOf("PlayBook") != -1){// 黑莓系统
            osInfo = "BlackBerry系统";
        }else if(userAgent.indexOf("MeeGo") != -1){// MeeGo系统(诺基亚)
            osInfo = "MeeGo系统";
        }else if(StringUtils.isEmpty(osInfo)){

        }
        return osInfo;

    }

}
