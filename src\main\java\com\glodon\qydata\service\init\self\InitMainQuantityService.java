package com.glodon.qydata.service.init.self;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity;
import com.glodon.qydata.mapper.standard.mainQuantity.ZbStandardsMainQuantityMapper;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 主要量指标暂存
 * @date 2022/7/11 14:33
 */
@Service
public class InitMainQuantityService extends InitSelfService{

    @Autowired
    private ZbStandardsMainQuantityMapper mainQuantityMapper;

    public void initData(String customerCode, Long tradeId){
        super.initData(customerCode, null, tradeId);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long tradeId) {
        return CollectionUtils.isEmpty(mainQuantityMapper.selectSelfByCusAndTradeIdNoDelNoDel(customerCode, tradeId, Constants.CategoryConstants.WHETHER_TRUE));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long tradeId) {
        List<ZbStandardsMainQuantity> mainQuantityList = mainQuantityMapper.selectByCusAndTradeIdNoDelNoDel(customerCode, tradeId);

        if(CollectionUtils.isNotEmpty(mainQuantityList)) {
            for (ZbStandardsMainQuantity entity : mainQuantityList) {
                entity.setOriginId(entity.getId());
                entity.setId(SnowflakeIdUtils.getNextId());
            }
            mainQuantityMapper.selfBatchInsert(mainQuantityList);
        }
    }
}
