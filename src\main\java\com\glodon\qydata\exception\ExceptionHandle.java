package com.glodon.qydata.exception;

import com.alibaba.fastjson.JSON;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.vo.common.ResponseVo;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.exceptions.PersistenceException;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.http.HttpStatus;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import jakarta.servlet.http.HttpServletRequest;
import java.util.Iterator;
import java.util.List;
import java.util.Set;

/**
 * 自定义业务异常拦截
 * Created by weijf on 2017/11/29.
 */
@Slf4j
@ControllerAdvice
public class ExceptionHandle {
    
    /**
     * BusinessException 与其它Exception
     * @param e
     * @return
     */
    @ExceptionHandler(value = Exception.class)
    @ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
    @ResponseBody
    public ResponseVo<String> handle(HttpServletRequest request, Exception e) {

        //自定义错误
        if (e instanceof BusinessException) {
            log.error("【自定义异常】【请求路径】:"+request.getRequestURL()+"【参数】:"+ JSON.toJSONString(request.getParameterMap())
                    ,e);
            BusinessException be = (BusinessException) e;
            return  new ResponseVo<>(be.getCode(), be.getMessage());
            //其它未知错误
        } else if(e instanceof MethodArgumentTypeMismatchException){
            log.error("【请求参数类型异常】【请求路径】:"+request.getRequestURL()+"【参数】:"+ JSON.toJSONString(request.getParameterMap()), e);
            return new ResponseVo<>(ResponseCode.PARAMETER_ERROR, "参数类型错误");
        } else if(e instanceof MissingServletRequestParameterException){
            log.error("【请求参数为空异常】【请求路径】:"+request.getRequestURL()+"【参数】:"+ JSON.toJSONString(request.getParameterMap()), e);
            return new ResponseVo<>(ResponseCode.PARAMETER_ERROR, "参数不能为空");
        }  else if(e instanceof HttpMessageNotReadableException){
            log.error("【参数格式转换异常】【请求路径】:"+request.getRequestURL()+"【参数】:"+ JSON.toJSONString(request.getParameterMap()), e);
            return new ResponseVo<>(ResponseCode.PARAMETER_ERROR, "参数格式转换异常");
        } else if(e instanceof MyBatisSystemException && e.getCause() instanceof PersistenceException && e.getCause().getCause() instanceof BusinessException){
            BusinessException be = (BusinessException) e.getCause().getCause();
            return new ResponseVo<>(be.getCode(), be.getMessage());
        }else {
            e.printStackTrace();
            log.error("【系统异常】【请求路径】:"+request.getRequestURL()+"【参数】:"+ JSON.toJSONString(request.getParameterMap())
                    ,e);

            String errorMsg = ResponseCode.ERROR.getMessage();
            if (StringUtils.isNotBlank(e.getMessage())) {
                errorMsg = e.getMessage();
            }
            return new ResponseVo<>(ResponseCode.ERROR.getCode(), errorMsg);

        }
    }


    /**
     * 方法验证出错
     * @param e
     * @return
     */
    @ExceptionHandler(value = ConstraintViolationException.class)
    @ResponseBody
    public ResponseVo handleConstraintViolationException(ConstraintViolationException e) {
        log.error("【参数校验异常】", e);
        Set<ConstraintViolation<?>> violations =  e.getConstraintViolations();
        String expMessage = null;
        if (violations != null && !violations.isEmpty()) {
            Iterator<ConstraintViolation<?>> iterator = violations.iterator();
            expMessage = iterator.next().getMessageTemplate();
        }
        return new ResponseVo(ResponseCode.PARAMETER_ERROR.getCode(), expMessage);
    }


    /**
     * 类验证出错,拦截MethodArgumentNotValidException、BindException
     * @param e
     * @return
     */
    @ExceptionHandler(value = {MethodArgumentNotValidException.class,BindException.class})
    @ResponseBody
    public ResponseVo handleMethodArgumentNotValidException(Exception e) {
        BindingResult result;
        if (e instanceof MethodArgumentNotValidException) {
            MethodArgumentNotValidException be = (MethodArgumentNotValidException) e;
            result = be.getBindingResult();
        }else{
            BindException be = (BindException) e;
            result = be.getBindingResult();
        }

        List<ObjectError> objectErrorList = result.getAllErrors();
        StringBuilder strBuilder = new StringBuilder();
        for (ObjectError objectError : objectErrorList) {
            strBuilder.append(objectError.getDefaultMessage() + ",");
        }
        return new ResponseVo(ResponseCode.PARAMETER_ERROR.getCode(), strBuilder.substring(0, strBuilder.length() - 1));
    }


}


