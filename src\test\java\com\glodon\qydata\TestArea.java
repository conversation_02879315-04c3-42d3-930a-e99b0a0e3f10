package com.glodon.qydata;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.glodon.gcdp.sdk.monitor.service.GcostMonitorClient;
import com.glodon.qydata.entity.system.TbArea;
import com.glodon.qydata.service.system.IAreaService;
import com.glodon.qydata.util.AMapApiUtil;
import com.glodon.qydata.vo.amap.AMapDistrict;
import com.glodon.qydata.vo.amap.AMapQueryParams;
import com.glodon.qydata.vo.amap.AMapResponse;
import com.glodon.qydata.controller.temp.areaRepair.AreaDiff;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 地区相关测试类
 * Created by caidj on 2025/02/25.
 */
@Slf4j
@SpringBootTest
@ExtendWith(SpringExtension.class)
@ActiveProfiles("hw-sprint")
public class TestArea {
    @Autowired
    private IAreaService areaService;
    private static Map<String, String> nameCoverMap = new HashMap<>();

    private static Map<String, String> provinceSkipMap = new HashMap<>();
    private static Map<String, String> districtSkipMap = new HashMap<>();

    private static Map<String, String> invalidDistrictMap = new HashMap<>();
    private static Map<String, String> invalidCityMap = new HashMap<>();
    private static List<AreaDiff> districtSkipList = new ArrayList<>();


    static {
        // 可接受的名称差异
        nameCoverMap.put("台湾", "台湾省"); // todo
        nameCoverMap.put("北京市-市辖区", "北京城区");
        nameCoverMap.put("重庆市-市辖区", "重庆城区");
        nameCoverMap.put("天津市-市辖区", "天津城区");
        nameCoverMap.put("上海市-市辖区", "上海城区");
        nameCoverMap.put("香港特别行政区-香港", "香港特别行政区");
        nameCoverMap.put("澳门特别行政区-澳门", "澳门特别行政区");

        // 可接受的差异
        provinceSkipMap.put("其他地区", "");


        // todo 处理规则 省直辖县级行政单位、自治区直辖县级行政单位   只留两级
        districtSkipMap.put("河南省-济源市-济源市", "省直辖县级行政单位");

        districtSkipMap.put("湖北省-天门市-天门市", "省直辖县级行政单位");
        districtSkipMap.put("湖北省-潜江市-潜江市", "省直辖县级行政单位");
        districtSkipMap.put("湖北省-仙桃市-仙桃市", "省直辖县级行政单位");
        districtSkipMap.put("湖北省-神农架林区-神农架林区", "省直辖县级行政单位");

        // 新疆维吾尔自治区
        // 石河子市、阿拉尔市、图木舒克市、五家渠市、北屯市、铁门关市、双河市、可克达拉市、昆玉市、胡杨河市、新星市、白杨市：自治区直辖县级行政单位。
        districtSkipMap.put("新疆维吾尔自治区-石河子市-石河子市", "自治区直辖县级行政单位");
        districtSkipMap.put("新疆维吾尔自治区-图木舒克市-图木舒克市", "自治区直辖县级行政单位");
        districtSkipMap.put("新疆维吾尔自治区-五家渠市-五家渠市", "自治区直辖县级行政单位");
        districtSkipMap.put("新疆维吾尔自治区-阿拉尔市-阿拉尔市", "自治区直辖县级行政单位");

        // 海南省
        // 五指山市、文昌市、琼海市、万宁市、东方市、定安县、屯昌县、澄迈县、临高县、琼中黎族苗族自治县、保亭黎族苗族自治县、白沙黎族自治县、昌江黎族自治县、乐东黎族自治县、陵水黎族自治县：省直辖县级行政单位

        // 层级有改变，建议直接置成无效
        invalidDistrictMap.put("贵州省-毕节市-毕节市", "2011年12月27日，国务院批准撤销毕节地区和县级毕节市，设立地级毕节市，并将原县级毕节市的行政区域改为七星关区");
        invalidDistrictMap.put("广东省-肇庆市-肇庆市", "肇庆市在1988年升格为地级市之前是县级市");
        invalidDistrictMap.put("新疆维吾尔自治区-克拉玛依市-克拉玛依市", "1958年：设立为县级市;1982年：升格为地级市;1984年：被改为自治区直辖的不设区的县级市;1990年：恢复为地级市");
        invalidDistrictMap.put("浙江省-衢州市-衢州市", "衢州市在1985年之前并非省辖市，而是县级市或县级单位。1985年5月，衢州市正式升格为省辖市。");
        invalidDistrictMap.put("甘肃省-嘉峪关市-嘉峪关市", "嘉峪关市620200是甘肃省下辖的地级市，不设县区级行政区划，直接管理街道和镇");
        // 挂接错误，直接置成无效
        invalidDistrictMap.put("安徽省-阜阳市-蚌山区", "挂接错误");  // 蚌山区属于蚌埠市，从未归属过阜阳市。
        invalidDistrictMap.put("安徽省-阜阳市-淮上区", "挂接错误");  // 淮上区属于蚌埠市，从未归属过阜阳市。
        invalidDistrictMap.put("安徽省-阜阳市-龙子湖区", "挂接错误");  // 龙子湖区属于蚌埠市，从未归属过阜阳市。
        invalidDistrictMap.put("安徽省-阜阳市-禹会区", "挂接错误");  // 禹会区属于蚌埠市，从未归属过阜阳市。
        invalidDistrictMap.put("安徽省-蚌埠市-庐江县", "挂接错误");  // 庐江县属于合肥市，从未归属过蚌埠市。
        invalidDistrictMap.put("安徽省-蚌埠市-和县", "挂接错误");  // 和县属于马鞍山市，从未归属过蚌埠市。
        invalidDistrictMap.put("安徽省-蚌埠市-居巢区", "挂接错误");  // 居巢区属于巢湖市，从未归属过蚌埠市。
        invalidDistrictMap.put("安徽省-蚌埠市-无为县", "挂接错误");  // 无为县属于芜湖市，从未归属过蚌埠市。
        invalidDistrictMap.put("安徽省-蚌埠市-含山县", "挂接错误");  // 含山县属于马鞍山市，从未归属过蚌埠市。

        //行政管理区(又称“功能区”)，不属于行政区划单位。直接置成无效
        invalidDistrictMap.put("河南省-郑州市-经济技术开发区", "功能区");
        invalidDistrictMap.put("河南省-郑州市-高新开发区", "功能区");
        invalidDistrictMap.put("河南省-郑州市-郑东新区", "功能区");
        invalidDistrictMap.put("河南省-郑州市-航空港经济综合实验区", "功能区");
        invalidDistrictMap.put("河南省-郑州市-出口加工区", "功能区");
        invalidDistrictMap.put("河南省-平顶山市-高新技术开发区", "功能区");
        invalidDistrictMap.put("广东省-惠州市-大亚湾区", "功能区");
        invalidDistrictMap.put("内蒙古自治区-通辽市-经济技术开发区", "功能区");
        invalidDistrictMap.put("内蒙古自治区-锡林郭勒盟-乌拉盖管理区", "功能区");
        invalidDistrictMap.put("内蒙古自治区-鄂尔多斯市-恩格贝生态示范区", "功能区");
        invalidDistrictMap.put("内蒙古自治区-鄂尔多斯市-空港园区", "功能区");
        invalidDistrictMap.put("内蒙古自治区-鄂尔多斯市-鄂尔多斯高新技术产业开发区", "功能区");
        invalidDistrictMap.put("内蒙古自治区-呼和浩特市-经济技术开发区", "功能区");
        invalidDistrictMap.put("内蒙古自治区-呼和浩特市-林格尔新区", "功能区");
        invalidDistrictMap.put("江苏省-南通市-通州湾江海联动开发示范区", "功能区");
        invalidDistrictMap.put("江苏省-南通市-南通苏锡通科技产业园区", "功能区");
        invalidDistrictMap.put("江苏省-南通市-南通经济技术开发区", "功能区");
        invalidDistrictMap.put("江苏省-泰州市-医药高新区", "功能区");
        invalidDistrictMap.put("江苏省-宿迁市-洋河新区", "功能区");
        invalidDistrictMap.put("江苏省-宿迁市-湖滨新区", "功能区");
        invalidDistrictMap.put("江苏省-宿迁市-宿迁经济技术开发区", "功能区");
        invalidDistrictMap.put("江苏省-宿迁市-苏州宿迁工业园区", "功能区");
        invalidDistrictMap.put("江苏省-无锡市-江苏无锡经济开发区", "功能区");
        invalidDistrictMap.put("江苏省-盐城市-经济技术开发区", "功能区");
        invalidDistrictMap.put("江苏省-盐城市-盐南高新区", "功能区");
        invalidDistrictMap.put("江苏省-淮安市-淮安生态文旅区", "功能区");
        invalidDistrictMap.put("江苏省-淮安市-淮安经济技术开发区", "功能区");
        invalidDistrictMap.put("江苏省-淮安市-淮安工业园区", "功能区");
        invalidDistrictMap.put("江苏省-苏州市-新区", "功能区");
        invalidDistrictMap.put("江苏省-苏州市-开发区", "功能区");
        invalidDistrictMap.put("江苏省-苏州市-园区", "功能区");
        invalidDistrictMap.put("河北省-唐山市-高新区", "功能区");
        invalidDistrictMap.put("河北省-秦皇岛市-经济技术开发区", "功能区");
        invalidDistrictMap.put("河北省-承德市-开发区", "功能区");
        invalidDistrictMap.put("河北省-沧州市-渤海新区", "功能区");
        invalidDistrictMap.put("河北省-沧州市-沧州高新", "功能区");
        invalidDistrictMap.put("河北省-沧州市-沧州经济开发区", "功能区");
        invalidDistrictMap.put("河北省-沧州市-沧州临港经济技术开发区", "功能区");
        invalidDistrictMap.put("河北省-衡水市-高新区", "功能区");
        invalidDistrictMap.put("河北省-衡水市-滨湖新区", "功能区");
        invalidDistrictMap.put("河北省-邯郸市-冀南新区", "功能区");
        invalidDistrictMap.put("河北省-邯郸市-经济技术开发区", "功能区");
        invalidDistrictMap.put("河北省-保定市-白沟新城", "功能区");
        invalidDistrictMap.put("河北省-保定市-高新区", "功能区");
        invalidDistrictMap.put("湖北省-武汉市-经济开发区", "功能区");
        invalidDistrictMap.put("辽宁省-大连市-开发区", "功能区");
        invalidDistrictMap.put("山东省-烟台市-开发区", "功能区");
        invalidDistrictMap.put("山西省-阳泉市-阳泉高新技术产业开发区", "功能区");
        invalidDistrictMap.put("吉林省-长春市-经济技术开发区", "功能区");
        invalidDistrictMap.put("吉林省-长春市-高新技术开发区", "功能区");
        invalidDistrictMap.put("吉林省-长春市-净月潭开发区", "功能区");
        invalidDistrictMap.put("吉林省-长春市-汽车产业开发区", "功能区");
        invalidDistrictMap.put("吉林省-四平市-高新技术产业开发区", "功能区");
        invalidDistrictMap.put("西藏自治区-昌都市-西藏昌都经济开发区", "功能区");
        invalidDistrictMap.put("天津市-天津城区-经济开发区", "功能区");
        invalidDistrictMap.put("甘肃省-兰州市-兰州高新区", "功能区");
        invalidDistrictMap.put("甘肃省-兰州市-兰州经开区", "功能区");
        invalidDistrictMap.put("甘肃省-兰州市-兰州新区", "功能区");
        invalidDistrictMap.put("甘肃省-天水市-天水经开区", "功能区");
        invalidDistrictMap.put("河北省-唐山市-芦台区", "功能区"); // 河北省唐山市并没有“芦台区”这一行政区划单位，与之相关的可能是唐山市芦台经济技术开发区
        invalidDistrictMap.put("河北省-唐山市-汉沽区", "功能区"); // 河北省唐山市并没有“芦台区”这一行政区划单位，与之相关的可能是唐山市汉沽管理区
        invalidDistrictMap.put("河北省-唐山市-海港区", "功能区"); // 秦皇岛市有海港区；在唐山市的行政区划中，相关区域被称为唐山海港经济开发区
        invalidDistrictMap.put("贵州省-遵义市-新蒲新区", "功能区");
        invalidDistrictMap.put("贵州省-铜仁市-铜仁高新技术产业开发区", "功能区");
        invalidDistrictMap.put("贵州省-铜仁市-大龙经济开发区", "功能区");
        invalidDistrictMap.put("贵州省-安顺市-黄果树旅游区", "功能区");
        invalidDistrictMap.put("贵州省-安顺市-安顺经济技术开发区", "功能区");
        invalidDistrictMap.put("贵州省-贵阳市-新天园区", "功能区");
        invalidDistrictMap.put("重庆市-重庆城区-万盛经济技术开发区", "功能区");
        invalidDistrictMap.put("福建省-泉州市-清濛开发区", "功能区");
        invalidDistrictMap.put("湖南省-益阳市-益阳东部新区", "功能区");
        invalidDistrictMap.put("湖南省-益阳市-益阳高新区", "功能区");
        invalidDistrictMap.put("湖南省-长沙市-开发区", "功能区");
        invalidDistrictMap.put("湖南省-株洲市-云龙示范区", "功能区");
        invalidDistrictMap.put("湖南省-怀化市-洪江区", "功能区");
        invalidDistrictMap.put("湖南省-益阳市-大通湖区", "功能区");  // 用到的保留，没用的置无效 todo

        invalidCityMap.put("辽宁省-沈抚新区", "功能区");
        invalidCityMap.put("浙江省-舟山群岛新区", "国家级综合功能区");
        invalidCityMap.put("安徽省-巢湖", "巢湖市曾是地级市，2011年被撤销地级建制，其辖区被划归合肥、芜湖、马鞍山三市。此后设立县级巢湖市，由安徽省直辖、合肥市代管。");

        invalidCityMap.put("香港特别行政区-新界", "不是行政区划单位的规范表述，而是对香港特别行政区地理区域的非正式划分。");
        invalidCityMap.put("香港特别行政区-九龙", "不是行政区划单位的规范表述，而是对香港特别行政区地理区域的非正式划分。");
        invalidCityMap.put("香港特别行政区-香港岛", "不是行政区划单位的规范表述，而是对香港特别行政区地理区域的非正式划分。");

        invalidCityMap.put("澳门特别行政区-氹仔岛", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidCityMap.put("澳门特别行政区-澳门半岛", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidCityMap.put("澳门特别行政区-路环岛", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");

        invalidDistrictMap.put("澳门特别行政区-澳门特别行政区-路环", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidDistrictMap.put("澳门特别行政区-澳门特别行政区-凼仔", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidDistrictMap.put("澳门特别行政区-澳门特别行政区-澳门半岛", "并不是严格意义上的行政区划单位，但它们是澳门特别行政区的重要地理区域组成部分");
        invalidDistrictMap.put("澳门特别行政区-澳门特别行政区-路凼城", "“路氹”和“路凼”通常被交替使用，指代同一个区域，是澳门特别行政区的一个重要新兴区域");

        // todo 台湾处理，全部置成无效

        // city级直接挂了street级，直接置成无效
        invalidDistrictMap.put("广东省-中山市-中山港街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-五桂山街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-东区街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-石岐街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-环城街道", "乡级单位");
        invalidDistrictMap.put("广东省-中山市-西区街道", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-南城区", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-常平镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-东坑镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-企石镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-中堂镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-清溪镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-望牛墩镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-凤岗镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-茶山镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-沙田镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-寮步镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-石龙镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-麻涌镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-厚街镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-长安镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-石碣镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-石排镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-虎门镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-道滘镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-洪梅镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-谢岗镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-桥头镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-大朗镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-黄江镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-塘厦镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-万江区", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-横沥镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-樟木头镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-大岭山镇", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-莞城区", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-东城区", "乡级单位");
        invalidDistrictMap.put("广东省-东莞市-高埗镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-光村镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-东成镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-和庆镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-王五镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-大成镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-那大镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-白马井镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-南丰镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-海头镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-雅星镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-排浦镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-新州镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-峨蔓镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-兰洋镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-中和镇", "乡级单位");
        invalidDistrictMap.put("海南省-儋州市-木棠镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-玉山镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-周庄镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-花桥镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-周市镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-巴城镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-陆家镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-淀山湖镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-张浦镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-千灯镇", "乡级单位");
        invalidDistrictMap.put("江苏省-苏州市-锦溪镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-佛堂镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-上溪镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-苏溪镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-大陈镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-义亭镇", "乡级单位");
        invalidDistrictMap.put("浙江省-金华市-赤岸镇", "乡级单位");

        // todo 是否需要区分重庆城区5001**和重庆郊县5002**，编码上是有区别，但是官方并没有定义
        // 建议置成无效，编码上确实能分开
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-铜梁县", "500224", "重庆市-重庆郊县-铜梁县", "500224", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-云阳县", "500235", "重庆市-重庆郊县-云阳县", "500235", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-丰都县", "500230", "重庆市-重庆郊县-丰都县", "500230", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-巫溪县", "500238", "重庆市-重庆郊县-巫溪县", "500238", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-酉阳土家族苗族自治县", "500242", "重庆市-重庆郊县-酉阳土家族苗族自治县", "500242", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-秀山土家族苗族自治县", "500241", "重庆市-重庆郊县-秀山土家族苗族自治县", "500241", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-忠县", "500233", "重庆市-重庆郊县-忠县", "500233", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-巫山县", "500237", "重庆市-重庆郊县-巫山县", "500237", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-奉节县", "500236", "重庆市-重庆郊县-奉节县", "500236", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-石柱土家族自治县", "500240", "重庆市-重庆郊县-石柱土家族自治县", "500240", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-垫江县", "500231", "重庆市-重庆郊县-垫江县", "500231", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-城口县", "500229", "重庆市-重庆郊县-城口县", "500229", "挂到重庆郊县下"));
//        districtSkipList.add(new AreaRepair("重庆市-重庆城区-彭水苗族土家族自治县", "500243", "重庆市-重庆郊县-彭水苗族土家族自治县", "500243", "挂到重庆郊县下"));
//
//        districtSkipList.add(new AreaRepair("河南省-洛阳市-偃师市", "410381", "河南省-洛阳市-偃师区", "410307", "2021年3月，撤销县级偃师市，设立洛阳市偃师区"));
//        districtSkipList.add(new AreaRepair("河南省-洛阳市-孟津县", "410322", "河南省-洛阳市-孟津区", "410308", "2021年，撤销孟津县和吉利区，设立洛阳市孟津区"));
//        districtSkipList.add(new AreaRepair("河南省-洛阳市-吉利区", "410306", "河南省-洛阳市-孟津区", "410308", "2021年，撤销吉利区并入孟津区"));
//        districtSkipList.add(new AreaRepair("河南省-三门峡市-陕县", "411222", "河南省-三门峡市-陕州区", "411203", "2015年，撤销陕县设立陕州区"));
//        districtSkipList.add(new AreaRepair("河南省-许昌市-许昌县", "411023", "河南省-许昌市-建安区", "411003", "2014年，撤销许昌县设立建安区"));
//        districtSkipList.add(new AreaRepair("河南省-郑州市-邙山区", "410108", "河南省-郑州市-惠济区", "410109", "2005年，撤销邙山区并入惠济区"));
//        districtSkipList.add(new AreaRepair("河南省-周口市-淮阳县", "411626", "河南省-周口市-淮阳区", "411603", "2019年，撤销淮阳县设立淮阳区"));
//        districtSkipList.add(new AreaRepair("河南省-新乡市-长垣县", "410728", "河南省-新乡市-长垣市", "410781", "2019年，撤销长垣县设立县级长垣市"));
//        districtSkipList.add(new AreaRepair("河南省-开封市-金明区", "410211", "河南省-开封市-龙亭区", "410202", "2014年，撤销开封市龙亭区和金明区，设立新的开封市龙亭区"));
//        districtSkipList.add(new AreaRepair("河南省-开封市-开封县", "410221", "河南省-开封市-祥符区", "410212", "2014年，撤销开封县设立祥符区"));
//
//        districtSkipList.add(new AreaRepair("广东省-肇庆市-高要市", "441283", "广东省-肇庆市-高要区", "441204", "高要市已于2015年撤销，设立肇庆市高要区，新编码为441204"));
//        districtSkipList.add(new AreaRepair("广东省-阳江市-阳东县", "441723", "广东省-阳江市-阳东区", "441704", "阳东县已于2014年撤销，设立阳江市阳东区，新编码为441704"));
//        districtSkipList.add(new AreaRepair("广东省-茂名市-电白县", "440923", "广东省-茂名市-电白区", "440904", "2014年，电白县与茂港区合并设立茂名市电白区，新编码为440904"));
//        districtSkipList.add(new AreaRepair("广东省-茂名市-茂港区", "440903", "广东省-茂名市-电白区", "440904", "2014年，茂港区与电白县合并设立茂名市电白区，新编码为440904"));
//        districtSkipList.add(new AreaRepair("广东省-潮州市-潮安县", "445121", "广东省-潮州市-潮安区", "445103", "潮安县于2013年6月28日撤销，设立潮州市潮安区，新编码为445103"));
//        districtSkipList.add(new AreaRepair("广东省-揭阳市-揭东县", "445221", "广东省-揭阳市-揭东区", "445203", "揭东县于2013年撤销，设立揭阳市揭东区，新编码为445203"));
//        districtSkipList.add(new AreaRepair("广东省-清远市-清新县", "441827", "广东省-清远市-清新区", "441803", "清新县于2013年撤销，设立清远市清新区，新编码为441803"));
//        districtSkipList.add(new AreaRepair("广东省-韶关市-曲江县", "440221", "广东省-韶关市-曲江区", "440205", "曲江县于2013年撤销，设立韶关市曲江区，新编码为440205"));
//        districtSkipList.add(new AreaRepair("广东省-云浮市-云安县", "445323", "广东省-云浮市-云安区", "445303", "云安县于2014年撤销，设立云浮市云安区，新编码为445303"));
//
//        districtSkipList.add(new AreaRepair("黑龙江省-大兴安岭地区-漠河县", "232723", "黑龙江省-大兴安岭地区-漠河市", "232701", "漠河县已于2018年撤销，设立县级漠河市，新编码为232701"));
//        districtSkipList.add(new AreaRepair("黑龙江省-哈尔滨市-双城市", "230113", "黑龙江省-哈尔滨市-双城区", "230113", "双城市已于2014年撤销，设立哈尔滨市双城区，行政区划代码保持不变，仍为230113"));
//        districtSkipList.add(new AreaRepair("黑龙江省-哈尔滨市-动力区", "230107", "黑龙江省-哈尔滨市-香坊区", "230110", "动力区已于2004年撤销，并入香坊区，新编码为230110"));
//        districtSkipList.add(new AreaRepair("黑龙江省-哈尔滨市-太平区", "230105", "黑龙江省-哈尔滨市-南岗区", "230102", "太平区已于2004年撤销，并入南岗区，新编码为230102"));
//        districtSkipList.add(new AreaRepair("黑龙江省-伊春市-红星区", "230715", "黑龙江省-伊春市-丰林县", "230721", "红星区已于2019年撤销，与新青区、五营区合并设立丰林县，新编码为230721"));
//        districtSkipList.add(new AreaRepair("新疆维吾尔自治区-阿克苏地区-库车县", "652923", "新疆维吾尔自治区-阿克苏地区-库车市", "652902", "库车县已于2019年12月撤销，设立县级库车市，新编码为652902"));
//
//        districtSkipList.add(new AreaRepair("湖北省-十堰市-郧县", "420321", "湖北省-十堰市-郧阳区", "420304", "郧县于2014年12月撤销，设立十堰市郧阳区，新编码为420304"));
//        districtSkipList.add(new AreaRepair("湖北省-荆门市-京山县", "420821", "湖北省-荆门市-京山市", "420881", "京山县于2018年撤销，设立荆门市京山市，新编码为420881"));
//        districtSkipList.add(new AreaRepair("湖北省-荆州市-监利县", "421023", "湖北省-荆州市-监利市", "421083", "监利县于2020年撤销，设立荆州市监利市，新编码为421083"));
//
//        districtSkipList.add(new AreaRepair("辽宁省-大连市-普兰店市", "210282", "辽宁省-大连市-普兰店区", "210214", "普兰店市已于2015年撤销，设立大连市普兰店区，新编码为210214"));
//        districtSkipList.add(new AreaRepair("辽宁省-沈阳市-浑南新区", "210112", "辽宁省-沈阳市-浑南区", "210112", "浑南新区现为沈阳市浑南区，行政区划代码为210112"));
//        districtSkipList.add(new AreaRepair("辽宁省-沈阳市-辽中县", "210122", "辽宁省-沈阳市-辽中区", "210115", "辽中县已于2016年撤销，设立沈阳市辽中区，新编码为210115"));
//        districtSkipList.add(new AreaRepair("辽宁省-盘锦市-大洼县", "211121", "辽宁省-盘锦市-大洼区", "211104", "大洼县已于2014年撤销，设立盘锦市大洼区，新编码为211104"));
//
//        districtSkipList.add(new AreaRepair("山东省-东营市-垦利县", "370521", "山东省-东营市-垦利区", "370505", "垦利县已于2016年撤销，设立东营市垦利区，新编码为370505"));
//        districtSkipList.add(new AreaRepair("山东省-滨州市-沾化县", "371621", "山东省-滨州市-沾化区", "371603", "沾化县已于2014年9月撤销，设立滨州市沾化区，新编码为371603"));
//        districtSkipList.add(new AreaRepair("山东省-滨州市-邹平县", "371626", "山东省-滨州市-邹平市", "371681", "邹平县已于2018年撤销，设立滨州市邹平市，新编码为371681"));
//        districtSkipList.add(new AreaRepair("山东省-聊城市-茌平县", "371523", "山东省-聊城市-茌平区", "371503", "茌平县已于2019年9月撤销，设立聊城市茌平区，新编码为371503"));
//        districtSkipList.add(new AreaRepair("山东省-烟台市-蓬莱市", "370684", "山东省-烟台市-蓬莱区", "370614", "蓬莱市已于2020年6月撤销，设立烟台市蓬莱区，新编码为370614"));
//        districtSkipList.add(new AreaRepair("山东省-烟台市-长岛县", "370634", "山东省-烟台市-蓬莱区", "370614", "长岛县已于2020年6月撤销，合并至烟台市蓬莱区，新编码为370614"));
//        districtSkipList.add(new AreaRepair("山东省-威海市-文登市", "371081", "山东省-威海市-文登区", "371003", "文登市已于2014年撤销，设立威海市文登区，新编码为371003"));
//        districtSkipList.add(new AreaRepair("山东省-青岛市-即墨市", "370282", "山东省-青岛市-即墨区", "370215", "即墨市已于2017年撤销，设立青岛市即墨区，新编码为370215"));
//        districtSkipList.add(new AreaRepair("山东省-青岛市-四方区", "370205", "山东省-青岛市-市北区", "370203", "四方区已于2012年撤销，并入青岛市市北区，新编码为370203"));
//        districtSkipList.add(new AreaRepair("山东省-青岛市-胶南市", "370283", "山东省-青岛市-黄岛区", "370211", "胶南市已于2012年撤销，设立青岛市黄岛区，新编码为370211"));
//        districtSkipList.add(new AreaRepair("山东省-德州市-陵县", "371421", "山东省-德州市-陵城区", "371402", "陵县已于2014年撤销，设立德州市陵城区，新编码为371402"));
//        districtSkipList.add(new AreaRepair("山东省-菏泽市-定陶县", "371721", "山东省-菏泽市-定陶区", "371702", "定陶县已于2016年撤销，设立菏泽市定陶区，新编码为371702"));
//        districtSkipList.add(new AreaRepair("山东省-济宁市-市中区", "370802", "山东省-济宁市-任城区", "370811", "济宁市市中区已于2013年撤销，并入新的济宁市任城区"));
//        districtSkipList.add(new AreaRepair("山东省-济宁市-兖州市", "370881", "山东省-济宁市-兖州区", "370812", "兖州市已于2013年撤销，设立济宁市兖州区"));
//        districtSkipList.add(new AreaRepair("山东省-临沂市-苍山县", "371322", "山东省-临沂市-兰陵县", "371324", "苍山县已于2014年更名为兰陵县，新编码为371324"));
//
//        districtSkipList.add(new AreaRepair("上海市-上海城区-崇明县", "310230", "上海市-上海城区-崇明区", "310151", "崇明县已于2016年撤销，设立上海市崇明区，新编码为310151"));
//        districtSkipList.add(new AreaRepair("上海市-上海城区-闸北区", "310108", "上海市-上海城区-静安区", "310106", "闸北区已于2015年撤销，并入上海市静安区，新编码为310106"));
//        districtSkipList.add(new AreaRepair("上海市-上海城区-卢湾区", "310103", "上海市-上海城区-黄浦区", "310101", "卢湾区已于2011年撤销，并入上海市黄浦区，新编码为310101"));
//        districtSkipList.add(new AreaRepair("上海市-上海城区-南汇区", "310119", "上海市-上海城区-浦东新区", "310115", "南汇区已于2009年撤销，并入上海市浦东新区，新编码为310115"));
//
//        districtSkipList.add(new AreaRepair("贵州省-黔西南布依族苗族自治州-兴仁县", "522322", "贵州省-黔西南布依族苗族自治州-兴仁市", "522302", "兴仁县已于2018年8月撤销，设立县级兴仁市，新编码为522302"));
//        districtSkipList.add(new AreaRepair("贵州省-贵阳市-小河区", "520114", "贵州省-贵阳市-花溪区", "520111", "小河区已于2012年11月撤销，并入贵阳市花溪区，新编码为520111"));
//        districtSkipList.add(new AreaRepair("贵州省-毕节市-黔西县", "520522", "贵州省-毕节市-黔西市", "520581", "黔西县已于2021年3月撤销，设立县级黔西市，新编码为520581"));
//        districtSkipList.add(new AreaRepair("贵州省-遵义市-务川县", "520326", "贵州省-遵义市-务川仡佬族苗族自治县", "520326", "务川县在更名为务川仡佬族苗族自治县后，行政区划代码并未发生变化"));
//        districtSkipList.add(new AreaRepair("贵州省-铜仁市-玉屏县", "520622", "贵州省-铜仁市-玉屏侗族自治县", "520622", "玉屏县在1984年更名为玉屏侗族自治县，但行政区划代码未发生变化"));
//        districtSkipList.add(new AreaRepair("贵州省-铜仁市-印江县", "520625", "贵州省-铜仁市-印江土家族苗族自治县", "520625", "印江县在1986年12月更名为印江土家族苗族自治县，行政区划代码未发生变化"));
//        districtSkipList.add(new AreaRepair("贵州省-铜仁市-松桃县", "520628", "贵州省-铜仁市-松桃苗族自治县", "520628", "松桃县在1956年更名为松桃苗族自治县，行政区划代码未发生变化"));
//        districtSkipList.add(new AreaRepair("贵州省-铜仁市-沿河县", "520627", "贵州省-铜仁市-沿河土家族自治县", "520627", "沿河县在1986年更名为沿河土家族自治县，行政区划代码未发生变化"));
//
//        districtSkipList.add(new AreaRepair("安徽省-铜陵市-铜陵县", "340721", "安徽省-铜陵市-义安区", "340706", "铜陵县已于2015年撤销，设立义安区，新编码为340706"));
//        districtSkipList.add(new AreaRepair("安徽省-铜陵市-铜官山区", "340702", "安徽省-铜陵市-铜官区", "340705", "铜官山区已于2015年撤销，设立铜官区，新编码为340705"));
//        districtSkipList.add(new AreaRepair("安徽省-铜陵市-狮子山区", "340703", "安徽省-铜陵市-铜官区", "340705", "狮子山区已于2015年撤销，其区域并入铜官区，新编码为340705"));
//        districtSkipList.add(new AreaRepair("安徽省-芜湖市-繁昌县", "340222", "安徽省-芜湖市-繁昌区", "340212", "繁昌县已于2020年撤销，设立繁昌区，新编码为340212"));
//        districtSkipList.add(new AreaRepair("安徽省-宣城市-广德县", "341822", "安徽省-宣城市-广德市", "341882", "广德县已于2019年撤销，设立县级广德市，新编码为341882"));
//
//        districtSkipList.add(new AreaRepair("福建省-福州市-长乐市", "350182", "福建省-福州市-长乐区", "350112", "长乐市已于2017年撤销，设立长乐区，新编码为350112"));
//        districtSkipList.add(new AreaRepair("福建省-漳州市-龙海市", "350681", "福建省-漳州市-龙海区", "350604", "龙海市已于2021年撤销，设立龙海区，新编码为350604"));
//        districtSkipList.add(new AreaRepair("福建省-漳州市-长泰县", "350625", "福建省-漳州市-长泰区", "350605", "长泰县已于2021年撤销，设立长泰区，新编码为350605"));
//        districtSkipList.add(new AreaRepair("福建省-三明市-梅列区", "350402", "福建省-三明市-三元区", "350403", "梅列区已于2021年撤销，设立新的三元区，新编码为350403"));
//        districtSkipList.add(new AreaRepair("福建省-三明市-沙县", "350427", "福建省-三明市-沙县区", "350404", "沙县已于2021年撤销，设立沙县区，新编码为350404"));
//        districtSkipList.add(new AreaRepair("福建省-龙岩市-永定县", "350822", "福建省-龙岩市-永定区", "350803", "永定县已于2014年撤销，设立永定区，新编码为350803"));
//        districtSkipList.add(new AreaRepair("福建省-南平市-建阳市", "350784", "福建省-南平市-建阳区", "350703", "建阳市已于2014年撤销，设立建阳区，新编码为350703"));
//
//        districtSkipList.add(new AreaRepair("湖南省-永州市-祁阳县", "431121", "湖南省-永州市-祁阳市", "431181", "祁阳县已于2021年撤销，设立县级祁阳市，新编码为431181"));
//
//        districtSkipList.add(new AreaRepair("江苏省-连云港市-赣榆县", "320721", "江苏省-连云港市-赣榆区", "320707", "赣榆县已改为赣榆区，新编码为320707"));
//        districtSkipList.add(new AreaRepair("江苏省-连云港市-新浦区", "320703", "江苏省-连云港市-海州区", "320706", "新浦区已并入海州区，新编码为320706"));
//        districtSkipList.add(new AreaRepair("江苏省-南京市-白下区", "320103", "江苏省-南京市-秦淮区", "320104", "白下区已并入秦淮区，新编码为320104"));
//        districtSkipList.add(new AreaRepair("江苏省-苏州市-吴江市", "320584", "江苏省-苏州市-吴江区", "320509", "吴江市已改为吴江区，新编码为320509"));
//        districtSkipList.add(new AreaRepair("江苏省-扬州市-江都市", "321088", "江苏省-扬州市-江都区", "321012", "江都市已改为江都区，新编码为321012"));
//        districtSkipList.add(new AreaRepair("江苏省-常州市-金坛市", "320482", "江苏省-常州市-金坛区", "320413", "金坛市已改为金坛区，新编码为320413"));
//        districtSkipList.add(new AreaRepair("江苏省-常州市-戚墅堰区", "320405", "江苏省-常州市-武进区", "320412", "戚墅堰区已并入武进区，新编码为320412"));
//        districtSkipList.add(new AreaRepair("江苏省-苏州市-沧浪区", "320502", "江苏省-苏州市-姑苏区", "320508", "沧浪区已并入姑苏区，新编码为320508"));
//        districtSkipList.add(new AreaRepair("江苏省-苏州市-金阊区", "320504", "江苏省-苏州市-姑苏区", "320508", "金阊区已并入姑苏区，新编码为320508"));
//        districtSkipList.add(new AreaRepair("江苏省-苏州市-平江区", "320503", "江苏省-苏州市-姑苏区", "320508", "平江区已并入姑苏区，新编码为320508"));
//
//        districtSkipList.add(new AreaRepair("青海省-玉树藏族自治州-玉树县", "632723", "青海省-玉树藏族自治州-玉树市", "632701", "玉树县已改为玉树市，新编码为632701"));
//        districtSkipList.add(new AreaRepair("青海省-黄南藏族自治州-同仁县", "632321", "青海省-黄南藏族自治州-同仁市", "632301", "同仁县已改为同仁市，新编码为632301"));
//        districtSkipList.add(new AreaRepair("青海省-西宁市-湟中县", "630122", "青海省-西宁市-湟中区", "630109", "湟中县已改为湟中区，新编码为630109"));
//
//        districtSkipList.add(new AreaRepair("广西壮族自治区-百色市-田阳县", "451021", "广西壮族自治区-百色市-田阳区", "451002", "田阳县已改为田阳区，新编码为451002"));
//        districtSkipList.add(new AreaRepair("广西壮族自治区-百色市-平果县", "451023", "广西壮族自治区-百色市-平果市", "451082", "平果县已改为平果市，新编码为451082"));
//        districtSkipList.add(new AreaRepair("广西壮族自治区-百色市-靖西县", "451025", "广西壮族自治区-百色市-靖西市", "451081", "靖西县已改为靖西市，新编码为451081"));
//        districtSkipList.add(new AreaRepair("广西壮族自治区-河池市-宜州市", "451281", "广西壮族自治区-河池市-宜州区", "451203", "宜州市已改为宜州区，新编码为451203"));
//        districtSkipList.add(new AreaRepair("广西壮族自治区-柳州市-柳江县", "450221", "广西壮族自治区-柳州市-柳江区", "450206", "柳江县已改为柳江区，新编码为450206"));
//        districtSkipList.add(new AreaRepair("广西壮族自治区-桂林市-临桂县", "450322", "广西壮族自治区-桂林市-临桂区", "450305", "临桂县已改为临桂区，新编码为450305"));
//        districtSkipList.add(new AreaRepair("广西壮族自治区-桂林市-荔浦县", "450331", "广西壮族自治区-桂林市-荔浦市", "450382", "荔浦县已改为荔浦市，新编码为450382"));
//        districtSkipList.add(new AreaRepair("广西壮族自治区-南宁市-武鸣县", "450122", "广西壮族自治区-南宁市-武鸣区", "450110", "武鸣县已改为武鸣区，新编码为450110"));
//        districtSkipList.add(new AreaRepair("广西壮族自治区-南宁市-横县", "450127", "广西壮族自治区-南宁市-横州市", "450182", "横县已改为横州市，新编码为450182"));
//        districtSkipList.add(new AreaRepair("广西壮族自治区-梧州市-蝶山区", "450403", "广西壮族自治区-梧州市-万秀区", "450405", "蝶山区已并入万秀区，原编码为450403，新编码为450405"));
//
//        districtSkipList.add(new AreaRepair("浙江省-宁波市-奉化市", "330213", "浙江省-宁波市-奉化区", "330213", "奉化市已改为奉化区，新编码为330213"));
//        districtSkipList.add(new AreaRepair("浙江省-宁波市-江东区", "330204", "浙江省-宁波市-鄞州区", "330212", "江东区已撤销，其行政区域划归宁波市鄞州区，新编码为330212"));
//        districtSkipList.add(new AreaRepair("浙江省-台州市-玉环县", "331021", "浙江省-台州市-玉环市", "331083", "玉环县已改为玉环市，新编码为331083"));
//        districtSkipList.add(new AreaRepair("浙江省-温州市-洞头县", "330322", "浙江省-温州市-洞头区", "330305", "洞头县已改为洞头区，新编码为330305"));
//        districtSkipList.add(new AreaRepair("浙江省-金华市-市区", "330702", "浙江省-金华市-婺城区", "330702", "金华市区主要指婺城区，属于金华市的市辖区，行政区划代码为330702"));
//        districtSkipList.add(new AreaRepair("浙江省-绍兴市-上虞市", "330682", "浙江省-绍兴市-上虞区", "330604", "上虞市已改为绍兴市上虞区，新编码为330604"));
//
//        districtSkipList.add(new AreaRepair("河北省-唐山市-滦县", "130223", "河北省-唐山市-滦州市", "130281", "滦县已改为滦州市，新编码为130281"));
//        districtSkipList.add(new AreaRepair("河北省-唐山市-唐海县", "130224", "河北省-唐山市-曹妃甸区", "130209", "唐海县已改为曹妃甸区，新编码为130209"));
//        districtSkipList.add(new AreaRepair("河北省-秦皇岛市-抚宁县", "130323", "河北省-秦皇岛市-抚宁区", "130306", "抚宁县已改为抚宁区，新编码为130306"));
//        districtSkipList.add(new AreaRepair("河北省-承德市-平泉县", "130823", "河北省-承德市-平泉市", "130881", "平泉县已改为平泉市，新编码为130881"));
//        districtSkipList.add(new AreaRepair("河北省-衡水市-冀州市", "131181", "河北省-衡水市-冀州区", "131103", "冀州市已改为冀州区，新编码为131103"));
//
//        districtSkipList.add(new AreaRepair("河北省-邯郸市-永年县", "130423", "河北省-邯郸市-永年区", "130408", "永年县已改为永年区，新编码为130408"));
//        districtSkipList.add(new AreaRepair("河北省-邯郸市-从台区", "130403", "河北省-邯郸市-丛台区", "130403", "正确的名称是丛台区，编码未变"));
//        districtSkipList.add(new AreaRepair("河北省-邯郸市-肥乡县", "130428", "河北省-邯郸市-肥乡区", "130407", "肥乡县已改为肥乡区，新编码为130407"));
//
//        districtSkipList.add(new AreaRepair("河北省-保定市-新市区", "130602", "河北省-保定市-竞秀区", "130602", "新市区已更名为竞秀区，新编码为130602"));
//        districtSkipList.add(new AreaRepair("河北省-保定市-满城县", "130621", "河北省-保定市-满城区", "130607", "满城县已改为满城区，新编码为130607"));
//        districtSkipList.add(new AreaRepair("河北省-保定市-南市区", "130602", "河北省-保定市-莲池区", "130606", "南市区已并入莲池区，新编码为130606"));
//        districtSkipList.add(new AreaRepair("河北省-保定市-北市区", "130603", "河北省-保定市-莲池区", "130606", "北市区已并入莲池区，新编码为130606"));
//        districtSkipList.add(new AreaRepair("河北省-保定市-清苑县", "130622", "河北省-保定市-清苑区", "130608", "清苑县已改为清苑区，新编码为130608"));
//        districtSkipList.add(new AreaRepair("河北省-保定市-徐水县", "130625", "河北省-保定市-徐水区", "130609", "徐水县已改为徐水区，新编码为130609"));
//
//        districtSkipList.add(new AreaRepair("河北省-张家口市-万全县", "130729", "河北省-张家口市-万全区", "130708", "万全县已改为万全区，新编码为130708"));
//        districtSkipList.add(new AreaRepair("河北省-张家口市-崇礼县", "130733", "河北省-张家口市-崇礼区", "130709", "崇礼县已改为崇礼区，新编码为130709"));
//        districtSkipList.add(new AreaRepair("河北省-张家口市-宣化县", "130731", "河北省-张家口市-宣化区", "130705", "宣化县已改为宣化区，新编码为130705"));
//
//        districtSkipList.add(new AreaRepair("甘肃省-平凉市-华亭县", "620824", "甘肃省-平凉市-华亭市", "620881", "华亭县已改为华亭市，新编码为620881"));
//
//        districtSkipList.add(new AreaRepair("四川省-绵阳市-北川县", "510726", "四川省-绵阳市-北川羌族自治县", "510726", "北川县已改为北川羌族自治县，当前有效编码"));
//        districtSkipList.add(new AreaRepair("四川省-凉山彝族自治州-会理县", "513425", "四川省-凉山彝族自治州-会理市", "513481", "会理县已改为会理市，新编码为513481"));
//
//        districtSkipList.add(new AreaRepair("天津市-天津城区-蓟县", "120225", "天津市-蓟州区", "120119", "蓟县已改为蓟州区，新编码为120119"));
//        districtSkipList.add(new AreaRepair("天津市-天津城区-静海县", "120223", "天津市-静海区", "120118", "静海县已改为静海区，新编码为120118"));
//        districtSkipList.add(new AreaRepair("天津市-天津城区-宁河县", "120221", "天津市-宁河区", "120117", "宁河县已改为宁河区，新编码为120117"));
//        districtSkipList.add(new AreaRepair("天津市-天津城区-汉沽区", "120116", "天津市-滨海新区", "120116", "汉沽区已并入滨海新区，原编码为120116"));
//        districtSkipList.add(new AreaRepair("天津市-天津城区-塘沽区", "120116", "天津市-滨海新区", "120116", "塘沽区已并入滨海新区，原编码为120116"));
//        districtSkipList.add(new AreaRepair("天津市-天津城区-大港区", "120116", "天津市-滨海新区", "120116", "大港区已并入滨海新区，原编码为120116"));
//
//        districtSkipList.add(new AreaRepair("北京市-密云县", "110228", "北京市-密云区", "110118", "密云县已改为密云区，新编码为110118"));
//        districtSkipList.add(new AreaRepair("北京市-延庆县", "110229", "北京市-延庆区", "110119", "延庆县已改为延庆区，新编码为110119"));
//
//        districtSkipList.add(new AreaRepair("西藏自治区-山南市-错那县", "540524", "西藏自治区-山南市-错那市", "540530", "错那县已改为错那市，新编码为540530"));
//        districtSkipList.add(new AreaRepair("西藏自治区-山南市-乃东县", "540502", "西藏自治区-山南市-乃东区", "540502", "乃东县已改为乃东区，新编码为540502"));
//        districtSkipList.add(new AreaRepair("西藏自治区-林芝市-米林县", "540622", "西藏自治区-林芝市-米林区", "540602", "米林县已改为米林区，新编码为540602"));
//
//        districtSkipList.add(new AreaRepair("吉林省-长春市-九台市", "220113", "吉林省-长春市-九台区", "220113", "九台市已于2014年撤销，设立长春市九台区，行政区域代码保持不变"));
//
//        districtSkipList.add(new AreaRepair("山西省-大同市-大同县", "140221", "山西省-大同市-云州区", "140215", "大同县已撤销，设立大同市云州区"));
//        districtSkipList.add(new AreaRepair("山西省-大同市-矿区", "140203", "山西省-大同市-云冈区", "140214", "矿区已撤销，设立大同市云冈区"));
//        districtSkipList.add(new AreaRepair("山西省-大同市-城区", "140202", "山西省-大同市-平城区", "140213", "城区已撤销，设立大同市平城区"));
//        districtSkipList.add(new AreaRepair("山西省-大同市-南郊区", "140211", "山西省-大同市-南郊区", "140213/140214/140212", "南郊区已撤销，部分区域划归平城区（140213）、云冈区（140214）和新荣区（140212）")); // todo
//        districtSkipList.add(new AreaRepair("山西省-长治市-城区", "140402", "山西省-长治市-潞州区", "140403", "长治市城区已撤销，设立潞州区"));
//        districtSkipList.add(new AreaRepair("山西省-长治市-郊区", "140411", "山西省-长治市-潞州区", "140403", "长治市郊区已撤销，设立潞州区"));
//        districtSkipList.add(new AreaRepair("山西省-长治市-长治县", "140421", "山西省-长治市-上党区", "140404", "长治县已撤销，设立上党区"));
//        districtSkipList.add(new AreaRepair("山西省-长治市-屯留县", "140424", "山西省-长治市-屯留区", "140405", "屯留县已撤销，设立屯留区"));
//        districtSkipList.add(new AreaRepair("山西省-长治市-潞城市", "140481", "山西省-长治市-潞城区", "140406", "潞城市已撤销，设立潞城区"));
//        districtSkipList.add(new AreaRepair("山西省-晋中市-太谷县", "140721", "山西省-晋中市-太谷区", "140702", "太谷县已撤销，设立晋中市太谷区，行政区域管辖范围和政府驻地不变"));
//
//        districtSkipList.add(new AreaRepair("云南省-昭通市-水富县", "530628", "云南省-昭通市-水富市", "530681", "水富县已改为水富市"));
//        districtSkipList.add(new AreaRepair("云南省-曲靖市-马龙县", "530321", "云南省-曲靖市-马龙区", "530304", "马龙县已改为马龙区"));
//        districtSkipList.add(new AreaRepair("云南省-曲靖市-沾益县", "530320", "云南省-曲靖市-沾益区", "530303", "沾益县已改为沾益区"));
//        districtSkipList.add(new AreaRepair("云南省-红河哈尼族彝族自治州-弥勒县", "532526", "云南省-红河哈尼族彝族自治州-弥勒市", "532504", "弥勒县已改为弥勒市"));
//        districtSkipList.add(new AreaRepair("云南省-红河哈尼族彝族自治州-蒙自县", "532522", "云南省-红河哈尼族彝族自治州-蒙自市", "532503", "蒙自县已改为蒙自市"));
//        districtSkipList.add(new AreaRepair("云南省-玉溪市-江川县", "530422", "云南省-玉溪市-江川区", "530403", "江川县已改为江川区"));
//        districtSkipList.add(new AreaRepair("云南省-玉溪市-澄江县", "530423", "云南省-玉溪市-澄江市", "530481", "澄江县已改为澄江市"));
//        districtSkipList.add(new AreaRepair("云南省-迪庆藏族自治州-香格里拉县", "533426", "云南省-迪庆藏族自治州-香格里拉市", "533401", "香格里拉县已改为香格里拉市"));
//        districtSkipList.add(new AreaRepair("云南省-楚雄彝族自治州-禄丰县", "532328", "云南省-楚雄彝族自治州-禄丰市", "532301", "禄丰县已改为禄丰市"));
//        districtSkipList.add(new AreaRepair("云南省-昆明市-石林县", "530126", "云南省-昆明市-石林彝族自治县", "530126", "石林县已改为石林彝族自治县，行政区划代码未变"));
//
//        districtSkipList.add(new AreaRepair("江西省-南昌市-红谷滩新区", "360108", "江西省-南昌市-红谷滩区", "360108", "红谷滩新区于2019年12月经国务院批复设立为红谷滩区，以东湖区沙井街道、卫东街道和新建区生米镇的行政区域为红谷滩区的行政区域"));
//        districtSkipList.add(new AreaRepair("江西省-赣州市-龙南县", "360727", "江西省-赣州市-龙南市", "360783", "龙南县于2020年6月经国务院批准撤销，设立县级龙南市"));
//        districtSkipList.add(new AreaRepair("江西省-赣州市-南康市", "360782", "江西省-赣州市-南康区", "360703", "南康市于2013年11月经国务院批准撤销，设立赣州市南康区"));
//        districtSkipList.add(new AreaRepair("江西省-抚州市-东乡县", "361029", "江西省-抚州市-东乡区", "361003", "东乡县已改为东乡区，现行行政区划代码为361003"));
//        districtSkipList.add(new AreaRepair("江西省-鹰潭市-余江县", "360622", "江西省-鹰潭市-余江区", "360602", "余江县已改为余江区，现行行政区划代码为360602"));
//        districtSkipList.add(new AreaRepair("江西省-九江市-星子县", "360427", "江西省-九江市-庐山市", "360483", "星子县已改为庐山市，现行行政区划代码为360483"));
//        districtSkipList.add(new AreaRepair("江西省-九江市-庐山区", "360402", "江西省-九江市-濂溪区", "360402", "庐山区已改为濂溪区，现行行政区划代码为360402"));
//        districtSkipList.add(new AreaRepair("江西省-九江市-九江县", "360421", "江西省-九江市-柴桑区", "360406", "九江县已改为柴桑区，现行行政区划代码为360406"));
//        districtSkipList.add(new AreaRepair("江西省-上饶市-上饶县", "362323", "江西省-上饶市-广信区", "361104", "上饶县已改为广信区，现行行政区划代码为361104"));
//
//        districtSkipList.add(new AreaRepair("陕西省-汉中市-南郑县", "610721", "陕西省-汉中市-南郑区", "610703", "南郑县已改为南郑区"));
//        districtSkipList.add(new AreaRepair("陕西省-西安市-高陵县", "610126", "陕西省-西安市-高陵区", "610117", "高陵县已改为高陵区"));
//        districtSkipList.add(new AreaRepair("陕西省-咸阳市-彬县", "610429", "陕西省-咸阳市-彬州市", "610482", "彬县已改为彬州市"));
//        districtSkipList.add(new AreaRepair("陕西省-宝鸡市-凤翔县", "610322", "陕西省-宝鸡市-凤翔区", "610302", "凤翔县已改为凤翔区"));
//        districtSkipList.add(new AreaRepair("陕西省-安康市-旬阳县", "610928", "陕西省-安康市-旬阳市", "610981", "旬阳县已改为旬阳市"));
//
//        districtSkipList.add(new AreaRepair("北京市-北京城区-密云县", "110228", "北京市-北京城区-密云区", "110118", "2015年11月，国务院批复撤销密云县，设立密云区"));
//        districtSkipList.add(new AreaRepair("北京市-北京城区-延庆县", "110229", "北京市-北京城区-延庆区", "110119", "2015年11月，经国务院批准，撤销延庆县，设立延庆区"));
//
//        // 名称有异常的，可以识别出关联关系
//        districtSkipList.add(new AreaRepair("四川省-攀枝花市-西  区", "510403", "四川省-攀枝花市-西区", "510403", "名称多空格，攀枝花市西区，当前有效编码"));
//        districtSkipList.add(new AreaRepair("四川省-攀枝花市-东  区", "510402", "四川省-攀枝花市-东区", "510402", "名称多空格，攀枝花市东区，当前有效编码"));
//        districtSkipList.add(new AreaRepair("云南省-昆明市-寻甸回族彝族自治县 ", "530129", "云南省-昆明市-寻甸回族彝族自治县", "530129", "名称多空格，寻甸回族彝族自治县行政区划代码未变"));
//        districtSkipList.add(new AreaRepair("黑龙江省-齐齐哈尔市-梅里斯达斡尔区", "230208", "黑龙江省-齐齐哈尔市-梅里斯达斡尔族区", "230208", "名称不规范，梅里斯达斡尔族区（有时简称为梅里斯区，但“梅里斯达斡尔区”并非正式名称）"));
//        districtSkipList.add(new AreaRepair("内蒙古自治区-呼伦贝尔市-莫力达瓦", "150722", "内蒙古自治区-呼伦贝尔市-莫力达瓦达斡尔族自治旗", "150722", "名称不规范，正式名称：莫力达瓦达斡尔族自治旗"));
//        districtSkipList.add(new AreaRepair("河南省-郑州市-管城区", "410104", "河南省-郑州市-管城回族区", "410104", "名称不规范，管城区（全称管城回族区）编码未变更"));
//        districtSkipList.add(new AreaRepair("新疆维吾尔自治区-伊犁哈萨克自治州-察布查尔", "654022", "新疆维吾尔自治区-伊犁哈萨克自治州-察布查尔锡伯自治县", "654022", "名称不规范，正式名称：察布查尔锡伯自治县"));
//        districtSkipList.add(new AreaRepair("新疆维吾尔自治区-喀什地区-塔什库尔干", "653131", "新疆维吾尔自治区-喀什地区-塔什库尔干塔吉克自治县", "653131", "名称不规范，正式名称：塔什库尔干塔吉克自治县"));
//        districtSkipList.add(new AreaRepair("黑龙江省-齐齐哈尔市-铁峰区", "230204", "黑龙江省-齐齐哈尔市-铁锋区", "230204", "错别字，铁峰区”应为“铁锋区”的误写，因原齐齐哈尔铁路局座落于辖区内而得名，寓意“铁路先锋区”"));
//        districtSkipList.add(new AreaRepair("安徽省-阜阳市-颖上县", "341226", "安徽省-阜阳市-颍上县", "341226", "错别字，颍上县的正确写法为“颍上县”。其中，“颍”字左半部分下方是“水”，与颍河有关"));


        Map<String, String> map1 = districtSkipList.stream().collect(Collectors.toMap(AreaDiff::getNamePath, AreaDiff::getRemark));
        districtSkipMap.putAll(invalidDistrictMap);
        districtSkipMap.putAll(map1);
    }

    @Test
    public void testProvinceCityCode() {
        // 库里的数据
        List<TbArea> dbAreaList = areaService.getAreaTree(null, "ONLY_VALID");
        List<TbArea> dbProvince = dbAreaList.get(0).getChildren();
        // 高德的数据
        List<AMapDistrict> aMapProvince = AMapApiUtil.getProvince();

        List<String> dbProvinceList = dbProvince.stream().map(x -> x.getName() + x.getAreaCode()).collect(Collectors.toList());
        List<String> aMapProvinceList = aMapProvince.stream().map(x -> x.getName() + x.getAdcode()).collect(Collectors.toList());
        compareProvinceLists(dbProvinceList, aMapProvinceList);

        // 库里的和高德的--省的编码是一致的
        Map<String, TbArea> dbProvinceMap = dbProvince.stream().collect(Collectors.toMap(TbArea::getAreaCode, Function.identity()));

        for (AMapDistrict aMapProvinceNode : aMapProvince) {
            String provinceName = aMapProvinceNode.getName();
            String provinceCode = aMapProvinceNode.getAdcode();
            List<AMapDistrict> aMapCity = aMapProvinceNode.getDistricts().stream().filter(x -> x.getLevel().equals("city")).collect(Collectors.toList());
            if (Objects.equals(provinceName, "香港特别行政区") || Objects.equals(provinceName, "澳门特别行政区")){
                aMapCity.add(aMapProvinceNode);
            }

            List<TbArea> dbCity = dbProvinceMap.get(provinceCode).getChildren();

            List<String> dbCityListList = dbCity.stream().map(x -> x.getName() + x.getAreaCode()).collect(Collectors.toList());
            List<String> aMapCityList = aMapCity.stream().map(x -> x.getName() + x.getAdcode()).collect(Collectors.toList());
            compareCityLists(dbCityListList, aMapCityList, provinceName);
        }

    }

    @Test
    public void testArea() {
        // 库里的数据
        List<TbArea> dbAreaList = areaService.getAreaTree(null, "ONLY_VALID");
        List<TbArea> dbProvince = dbAreaList.get(0).getChildren();
        // 高德的数据
        List<AMapDistrict> aMapProvince = AMapApiUtil.getProvince();

        List<String> dbProvinceList = dbProvince.stream().map(TbArea::getName).collect(Collectors.toList());
        List<String> aMapProvinceList = aMapProvince.stream().map(AMapDistrict::getName).collect(Collectors.toList());
        compareProvinceLists(dbProvinceList, aMapProvinceList);

        // 库里的和高德的--省的编码是一致的
        Map<String, TbArea> dbProvinceMap = dbProvince.stream().collect(Collectors.toMap(TbArea::getAreaCode, Function.identity()));

        for (AMapDistrict aMapProvinceNode : aMapProvince) {
            String provinceName = aMapProvinceNode.getName();
            String provinceCode = aMapProvinceNode.getAdcode();
            List<AMapDistrict> aMapCity = aMapProvinceNode.getDistricts().stream().filter(x -> x.getLevel().equals("city")).collect(Collectors.toList());
            if (Objects.equals(provinceName, "香港特别行政区") || Objects.equals(provinceName, "澳门特别行政区") || Objects.equals(provinceName, "台湾省")){
                aMapCity.add(aMapProvinceNode);
            }

            List<TbArea> dbCity = dbProvinceMap.get(provinceCode).getChildren();
            for (TbArea tbArea : dbCity) {
                if (Objects.equals(tbArea.getName(), "香港") || Objects.equals(tbArea.getName(), "澳门") || Objects.equals(tbArea.getName(), "台湾")){
                    tbArea.setAreaCode(provinceCode);
                }
            }

            List<String> dbCityListList = dbCity.stream().map(TbArea::getName).collect(Collectors.toList());
            List<String> aMapCityList = aMapCity.stream().map(AMapDistrict::getName).collect(Collectors.toList());
            compareCityLists(dbCityListList, aMapCityList, provinceName);

            Map<String, TbArea> dbCityMap = dbCity.stream().collect(Collectors.toMap(TbArea::getAreaCode, Function.identity()));
            for (AMapDistrict aMapCityNode : aMapCity) {
                String cityName = aMapCityNode.getName();
                String cityCode = aMapCityNode.getAdcode();

//                List<AMapDistrict> collect3 = aMapCityNode.getDistricts().stream().filter(x -> !x.getLevel().equals("district")).collect(Collectors.toList());
//                if (CollUtil.isNotEmpty(collect3)){
//                    log.info("层级特殊省份3：{}-{}，代码：{}", provinceName, cityName, collect3);
//                }

                List<AMapDistrict> aMapDistrict = aMapCityNode.getDistricts().stream().filter(x -> x.getLevel().equals("district")).collect(Collectors.toList());
                List<TbArea> dbDistrict = dbCityMap.get(cityCode) == null ? new ArrayList<>() : dbCityMap.get(cityCode).getChildren();

                List<String> dbDistrictListList = dbDistrict.stream().map(TbArea::getName).collect(Collectors.toList());
                List<String> aMapDistrictList = aMapDistrict.stream().map(AMapDistrict::getName).collect(Collectors.toList());
                compareDistrictLists(dbDistrictListList, aMapDistrictList, provinceName, cityName);
            }
        }

        log.info("省份-城市-区数据缺失：\n{}", StrUtil.join("\n", onlyInDbList));
    }

    @Test
    public void testAMap() {
        AMapQueryParams params = new AMapQueryParams();
        params.setSubdistrict(0);
        params.setOffset(9999);

        AMapResponse district = AMapApiUtil.getDistrict(params);
        log.info(JSONUtil.toJsonStr(district));

    }
    public void compareProvinceLists(List<String> dbProvinceList, List<String> aMapProvinceList) {
        // 将两个列表转换为Set以去除重复元素，并便于后续的集合操作
        Set<String> dbProvincesSet = dbProvinceList.stream().filter(province -> !provinceSkipMap.containsKey(province)).map(x -> nameCoverMap.getOrDefault(x, x)).collect(Collectors.toSet());
        Set<String> aMapProvincesSet = aMapProvinceList.stream().filter(province -> !provinceSkipMap.containsValue(province)).collect(Collectors.toSet());

        // 计算仅存在于dbProvincesSet中的元素
        Set<String> onlyInDb = dbProvincesSet.stream()
                .filter(province -> !aMapProvincesSet.contains(province))
                .collect(Collectors.toSet());

        // 计算仅存在于aMapProvincesSet中的元素
        Set<String> onlyInAMap = aMapProvincesSet.stream()
                .filter(province -> !dbProvincesSet.contains(province))
                .collect(Collectors.toSet());

        // 输出差异
        if (CollUtil.isNotEmpty(onlyInDb)){
            log.info("仅存在于数据库中的省份\t{}", onlyInDb);
        }
        if (CollUtil.isNotEmpty(onlyInAMap)){
            log.info("仅存在于高德API中的省份\t{}", onlyInAMap);
        }
    }

    public void compareCityLists(List<String> dbCityList, List<String> aMapCityList, String provinceName) {
        // 将两个列表转换为Set以去除重复元素，并便于后续的集合操作
        Set<String> dbCitySet = dbCityList.stream().filter(cityName -> !invalidCityMap.containsKey(cityNameKey(provinceName, cityName))).map(cityName -> nameCoverMap.getOrDefault(cityNameKey(provinceName, cityName), cityName)).collect(Collectors.toSet());
        Set<String> aMapCitySet = aMapCityList.stream().filter(cityName -> !invalidCityMap.containsKey(cityNameKey(provinceName, cityName))).collect(Collectors.toSet());

        // 计算仅存在于dbProvincesSet中的元素
        Set<String> onlyInDb = dbCitySet.stream()
                .filter(city -> !aMapCitySet.contains(city))
                .collect(Collectors.toSet());

        // 计算仅存在于aMapProvincesSet中的元素
        Set<String> onlyInAMap = aMapCitySet.stream()
                .filter(city -> !dbCitySet.contains(city))
                .collect(Collectors.toSet());

        // 输出差异
        if (CollUtil.isNotEmpty(onlyInDb)){
            log.info("仅存在于数据库中的市\t{}\t{}", provinceName, onlyInDb);
        }
        if (CollUtil.isNotEmpty(onlyInAMap)){
            log.info("仅存在于高德API中的市\t{}\t{}", provinceName, onlyInAMap);
        }
    }

    public void compareDistrictLists(List<String> dbCityList, List<String> aMapCityList, String provinceName, String cityName) {
        // 将两个列表转换为Set以去除重复元素，并便于后续的集合操作
        Set<String> dbCitySet = dbCityList.stream().filter(districtName -> !districtSkipMap.containsKey(districtNameKey(provinceName, cityName, districtName)))
                .collect(Collectors.toSet());
        Set<String> aMapCitySet = aMapCityList.stream().filter(districtName -> !districtSkipMap.containsValue(districtNameKey(provinceName, cityName, districtName))).collect(Collectors.toSet());

        // 计算仅存在于dbProvincesSet中的元素
        Set<String> onlyInDb = dbCitySet.stream()
                .filter(city -> !aMapCitySet.contains(city))
                .collect(Collectors.toSet());

        // 计算仅存在于aMapProvincesSet中的元素
        Set<String> onlyInAMap = aMapCitySet.stream()
                .filter(city -> !dbCitySet.contains(city))
                .collect(Collectors.toSet());

        // 输出差异
        if (CollUtil.isNotEmpty(onlyInDb)){
            log.info("仅存在于数据库中的区\t{}-{}\t{}", provinceName, cityName, onlyInDb);
            onlyInDbList.addAll(onlyInDb.stream().map(x -> "districtSkipMap.put(\"" + provinceName + "-" + cityName + "-" + x + "\", \"\");").collect(Collectors.toList()));
        }
        if (CollUtil.isNotEmpty(onlyInAMap)){
            log.info("仅存在于高德API中的区\t{}-{}\t{}", provinceName, cityName, onlyInAMap);
        }
    }

    List<String> onlyInDbList = new ArrayList<>();

    private String districtNameKey(String provinceName, String cityName, String districtName) {
        return StrUtil.format("{}-{}-{}", provinceName, cityName, districtName);
    }

    private String cityNameKey(String provinceName, String cityName) {
        return StrUtil.format("{}-{}", provinceName, cityName);
    }

    @Test
    public void testSendMessage() {
        String provinceName = "台湾省2";
        String cityName  = "台湾省2";
        GcostMonitorClient.sendMessage(StrUtil.format("省为:[{}], 市为:[{}]", provinceName, cityName));

        ThreadUtil.sleep(10000);
    }

}


