package com.glodon.qydata.dto;

import lombok.Data;

import java.io.Serializable;

/**
 *
 * <AUTHOR>
 */
@Data
public class StandardsBuildStandardPositionDescDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业态+产品定位主键
     */
    private Long positionId;

    /**
     * 业态+产品定位主键
     */
    private Long descId;

    /**
     * 对应的标准细则id
     */
    private Long detailId;

    /**
     * 业态编码
     */
    private String categoryCode;

    /**
     * 模板中标准说明的名称
     */
    private String detailDesc;

    /**
     * 业态中标准说明的值
     */
    private String descValue;

    /**
     * 编码
     */
    private String code;

    /**
     * 数据类型： text文本类，number数值类，date日期类，select单选类，selects多选类
     */
    private String typeCode;

    /**
     * 枚举值
     */
    private String selectList;

    /**
     * 排序
     */
    private Integer ord;
}
