package com.glodon.qydata.mapper.standard.trade;

import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.util.mover.ElementMover;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 　　* @description: 专业标准mapper
 * 　　* <AUTHOR>
 * 　　* @date 2021/10/21 17:22
 *
 */
@Repository
public interface ZbStandardsTradeMapper extends ElementMover.DatabaseSaver<ZbStandardsTrade> {

    ZbStandardsTrade selectById(Long id);

    void deleteById(@Param("id") Long id);

    /**
     * 新增一条数据
     *
     * @param recordTemp
     * @return
     */
    int insert(ZbStandardsTrade recordTemp);

    /**
     * 批量新增数据
     *
     * @param list
     * @return
     */
    int batchInsert(List<ZbStandardsTrade> list);

    /**
     * 按条件新增一条数据
     *
     * @param recordTemp
     * @return
     */
    int insertSelective(ZbStandardsTrade recordTemp);

    /**
     * 　　* @description: 根据企业编码获取未被删除的所有专业数据 包括内置和自定义
     * 　　* @param  customerCode 企业编码
     * 　　* @return
     * 　　* <AUTHOR>
     * 　　* @date 2021/10/21 18:05
     *
     */
    List<ZbStandardsTrade> selectListByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 　　* @description: 根据企业编码获取所有专业数据 包括内置和自定义,包括删除与未删除
     * 　　* @param  customerCode 企业编码
     * 　　* @return
     * 　　* <AUTHOR>
     * 　　* @date 2021/10/21 18:05
     *
     */
    List<ZbStandardsTrade> selectAllListByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 　　* @description: 根据企业编码获取未被删除的企业指定类型专业数据
     * 　　* @param  customerCode 企业编码
     * 　　* @return
     * 　　* <AUTHOR>
     * 　　* @date 2021/10/21 18:05
     *
     */
    List<ZbStandardsTrade> selectListByCustomerCodeAndType(@Param("customerCode") String customerCode, @Param("type") Byte type);

    /**
     * 　　* @description: 根据企业编码以及引用专业编码获取企业自定义专业数据
     * 　　* @param  customerCode 企业编码 referTradeCode 引用专业编码
     * 　　* @return List<ZbStandardsTrade>b
     * 　　* <AUTHOR>
     * 　　* @date 2021/10/21 18:05
     *
     */
    List<ZbStandardsTrade> selectListByCusAndReferTradeCode(@Param("customerCode") String customerCode, @Param("referTradeCode") String referTradeCode);

    /**
     * 　　* @description: 根据企业编码以及专业编码获取企业自定义专业数据
     * 　　* @param  customerCode 企业编码 tradeCode 专业编码
     * 　　* @return ZbStandardsTrade
     * 　　* <AUTHOR>
     * 　　* @date 2021/10/21 18:05
     *
     */
    ZbStandardsTrade selectListByCusAndTradeCode(@Param("customerCode") String customerCode, @Param("tradeCode") String tradeCode);

    /**
     * 　　* @description: 更新一条专业信息
     * 　　* @param  ZbStandardsTrade 需要更新的专业信息
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/10/21 20:01
     *
     */
    void updateTrade(ZbStandardsTrade recordTemp);

    /**
     * 　　* @description: 更新专业删除状态
     * 　　* @param  id 需要更新的专业id ，isDeleted 删除状态 0 未删除 1 删除
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/10/21 20:01
     *
     */
    void updateTradeIsDeleted(@Param("id") Long id, @Param("isDeleted") Integer isDeleted, @Param("updaterId") Long updaterId);

    /**
     * 　* @description: 根据企业编码、标准名称查找相关记录
     * 　* @param customerCode 企业编码  name 标准名称
     * 　　* @return  ZbStandardsTrade 查询到的记录
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/10/21 20:33
     *
     */
    ZbStandardsTrade searchRecordsByCusCodeAndName(@Param("description") String description, @Param("customerCode") String customerCode);

    /**
     * 　　* @description: 物理删除指定企业编码下的专业数据
     * 　　* @param  List<String> customerCode
     * 　　* @return
     * 　　* @throws
     * 　　* <AUTHOR>
     * 　　* @date 2021/12/2 23:27
     *
     */
    void deleteByCustomerCode(@Param("customerCode") String customerCode);

    List<ZbStandardsTrade> selectDescriptionListByIds(String customerCode, List<Long> tradeIds);

    /**
     * 批量更新ord
     * @param list
     */
    void batchUpdateOrd(@Param("list") List<ZbStandardsTrade> list);

    /**
     * 批量更新ord
     * @param list
     */
    void batchUpdateEnabled(@Param("list") List<ZbStandardsTrade> list);

}