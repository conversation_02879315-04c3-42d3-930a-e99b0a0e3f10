package com.glodon.qydata.service.standard.trade;


import com.glodon.qydata.dto.StandardTradeDto;
import com.glodon.qydata.dto.StandardTradeHistoryDataDto;
import com.glodon.qydata.dto.TradeDownOrUpDTO;
import com.glodon.qydata.dto.TradeEnableDTO;
import com.glodon.qydata.entity.standard.trade.TradeTagEnum;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.vo.standard.trade.StandardReferTradeVO;
import com.glodon.qydata.vo.standard.trade.StandardTradeVO;

import java.util.List;
import java.util.Map;

/**
　　* @description: 标准打通--专业标准服务类
　　* <AUTHOR>
　　* @date 2021/10/19 9:52
　　*/
public interface IStandardsTradeService {

    /**
    　　* @description: 根据企业编码查询所有待引用专业列表
    　　* @param customerCode 企业编码
    　　* @return List<StandardReferTradeVO> 内置专业引用列表返回前端数据载体列表
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/21 17:33
    　　*/
    List<StandardReferTradeVO> getReferListByCustomerCode(String customerCode) throws BusinessException;

    /**
     　　* @description: 根据企业编码查询专业列表
     　　* @param customerCode 企业编码
     　　* @return List<StandardTradeVO> 专业列表返回前端数据载体列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:33
     　　*/
    List<StandardTradeVO> getTradeVoList(String customerCod) throws BusinessException;

    /**
     * 转换为专业树结构
     * @param customCode
     * @param tradeList
     * @return
     * @throws BusinessException
     */
    List<StandardTradeVO> convertToTradeTree(String customCode, List<StandardTradeVO> tradeList, Boolean needTag) throws BusinessException;

    /**
     * 对外出口,提供带标签的专业树结构
     * @param customCode
     * @param tradeList
     * @return
     * @throws BusinessException
     */
    List<ZbStandardsTrade> convertToStandardTradeTree(String customCode, List<ZbStandardsTrade> tradeList) throws BusinessException;

    /**
     　　* @description: 对外接口，根据企业编码查询专业列表，包括删除与未删除
     　　* @param customerCode 企业编码
     　　* @return List<StandardTradeVO> 专业列表返回前端数据载体列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:33
     　　*/
    List<ZbStandardsTrade> initAllListByCustomerCode(String customerCod) throws BusinessException;

    /**
    　　* @description: 新增专业
    　　* @param StandardTradeBo 新增专业数据载体 customerCode 企业编码 globalId 用户id
    　　* @return ZbStandardsTrade 新增入库的专业信息
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/21 17:55
    　　*/
    ZbStandardsTrade insertTrade(StandardTradeDto standardTradeDto, String customerCode, Long globalId) throws BusinessException;

    /**
     　　* @description: 编辑专业名称
     　　* @param StandardTradeBo 编辑专业数据载体 customerCode 企业编码 globalId 用户id
     　　* @return ZbStandardsTrade 更新后的入库的专业信息
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:55
     　　*/
    ZbStandardsTrade updateTrade(StandardTradeDto standardTradeDto, String customerCode, Long globalId) throws BusinessException;

    /**
     　　* @description: 同一个企业下，专业名称唯一
     id为空，企业下同名称记录存在  则重复名称
     不存在  则正常
     id有值 企业下同名称记录存在  id相同  则正常
     id不相同  则重复名称
     企业下同名称记录不存在  则正常
     　　* @param  customerCode: 企业编码 name:要判断的专业名称 id：修改接口下的编辑专业id
     　　* @return 是否存在，true存在，false不存在
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 20:30
     　　*/
    Boolean checkedName(String customerCode,String name, Long id);

    /**
     　　* @description: 删除专业
     　　* @param customerCode 所在企业 id 专业id globalId 更新人标识id
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/21 17:55
     　　*/
    void deleteTrade(String customerCode,Long id,Long globalId) throws BusinessException;

    /**
     　　* @description: 获取企业下未删除专业数据，若不存在，会复制一份系统内置返回
     　　* @param customerCode 企业编码 globalId 用户id
     　　* @return List<ZbStandardsTrade 专业列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/22 20:25
     　　*/
    List<ZbStandardsTrade> getTradeList(String customerCode);

    /**
    　　* @description: 历史数据同步
    　　* @param Map<String,List<StandardTradeHistoryDataDto>> key:企业编码 value：专业列表
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/2 23:17
    　　*/
    void insertHistoryData(Map<String,List<StandardTradeHistoryDataDto>> map);

    /**
    　　* @description: 查询系统内置的专业列表
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/6 10:41
    　　*/
    List<ZbStandardsTrade> getSystemData();

    /**
     　　* @description: 对外服务--查询指定企业下的专业列表
     　　* @param customerCode 查询的企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/6 10:41
     　　*/
    List<ZbStandardsTrade> getTradeListByCustomerCode(String customerCode) throws BusinessException;

    /**
    　　* @description: 将旧企业专业标准数据复制到新企业专业标准
    　　* @param  oldCustomerCode:原企业编码  newCustomerCode 新企业编码
    　　* @return Map<Long,Long> 新旧专业id的对应关系集合，key:oldId  value:newId
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/9 9:55
    　　*/
    Map<Long,Long> initTradeDate(String oldCustomerCode,String newCustomerCode);

    /**
    　　* @description: 根据企业编码删除所有专业标准
    　　* @param  customerCode 企业编码
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/9 19:41
    　　*/
    void deleteByCustomerCode(String customerCode);

    /**
     * 根据专业id集合获取名称集合
     * @param customerCode
     * @param tradeIds
     * @return
     */
    List<ZbStandardsTrade> selectDescriptionListByIds(String customerCode, List<Long> tradeIds);

    /**
     * 根据专业id 进行上下移操作
     * <AUTHOR>
     * @date 2023-04-03 09:42:59
     * @param customerCode
     * @param tradeDownOrUpDTO
     * @return void
     */
    void downOrUpTrade(String customerCode, String globalId, TradeDownOrUpDTO tradeDownOrUpDTO);

    /**
     * 批量更新启用状态
     * <AUTHOR>
     * @date 2023-04-03 15:31:27
     * @param customerCode
     * @param tradeEnableDTOS
     * @return void
     */
    void changeEnable(String customerCode, String globalId, List<TradeEnableDTO> tradeEnableDTOS);

    /**
     * 更新专业标签选项表
     */
    void saveTradeTag(String customerCode, List<TradeTagEnum> tradeTagEnums);

    /**
     * 获取专业标签选项
     */
    List<TradeTagEnum> getTradeOptions(String customerCode);
}
