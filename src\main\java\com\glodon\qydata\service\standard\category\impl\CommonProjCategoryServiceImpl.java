package com.glodon.qydata.service.standard.category.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.TableNameConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.config.idsequence.IDGenerator;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.category.CommonProjectCategoryUsed;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryMapper;
import com.glodon.qydata.mapper.standard.category.CommonProjCategorySelfMapper;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryUsedMapper;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionMapper;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.service.cloud.HuiGuoFeignService;
import com.glodon.qydata.service.init.enterprise.InitCategoryService;
import com.glodon.qydata.service.standard.category.CategoryTagSelfService;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.util.*;
import com.glodon.qydata.vo.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.WHETHER_FALSE;

/**
 * <AUTHOR>
 * @date 2019/12/17 16:49
 */
@Service("commonProjCategoryStandardServiceImpl")
@Slf4j
public class CommonProjCategoryServiceImpl implements CommonProjCategoryService {

    @Autowired
    private CommonProjCategoryMapper commonProjCategoryMapper;
    @Autowired
    private CommonProjCategorySelfMapper commonProjCategorySelfMapper;

    @Autowired
    private HuiGuoFeignService huiGuoFeignService;
    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;
    @Autowired
    private CommonProjCategoryUsedMapper commonProjCategoryUsedMapper;
    @Autowired
    private IGlodonUserService glodonUserService;
    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;
    @Autowired
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;
    @Autowired
    private ProjectFeatureCategoryViewMapper featureCategoryViewMapper;
    @Autowired
    private ProjectFeatureCategoryViewSelfMapper featureCategoryViewSelfMapper;
    @Autowired
    private ZbStandardsExpressionMapper expressionMapper;
    @Autowired
    private ZbStandardsExpressionSelfMapper expressionSelfMapper;
    @Autowired
    private InitCategoryService initCategoryService;
    @Autowired
    private CategoryTagSelfService categoryTagSelfService;

    /**
     * 获取所有工程类别信息
     */
    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<CommonProjCategory> getCategoryListTree(String customerCode, Integer type, Integer isShowDelete) throws BusinessException{

        List<CommonProjCategory> commonProjCategories = commonProjCategoryMapper.selectAll(customerCode, type, isShowDelete);

        if (CollectionUtils.isEmpty(commonProjCategories)){
            // 初始化
            initCategoryService.initData(customerCode, type);
            commonProjCategories = commonProjCategoryMapper.selectAll(customerCode, type, isShowDelete);
        }

        return commonProjCategories;
    }

    @Override
    public CommonProjCategory getCategoryById(Integer categoryId) {
        return commonProjCategoryMapper.getCategoryByPrimaryKey(categoryId);
    }

    @Override
    public CommonProjCategory getCategoryByCode(String categoryCode, String customerCode) {
        // 工程分类查询企业最新确定的类别
        Integer categoryType = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        return commonProjCategoryMapper.getByCommonprojcategoryid(categoryCode, customerCode, categoryType);
    }

    @Override
    public List<CommonProjCategory> getCategoryList(Integer isShowDelete, Integer dataType, Integer isUsing, String customerCode, Integer type, Integer isSkipUserName, Integer levelLimit, String categoryCode1, Boolean needTag) {
        CommonProjCategoryServiceImpl commonProjCategoryService = (CommonProjCategoryServiceImpl) AopContext.currentProxy();
        List<CommonProjCategory> categoryList = commonProjCategoryService.getCommonProjCategories(isShowDelete, customerCode, type);
        // 过滤需获取的层级内的数据
        if (Objects.nonNull(levelLimit) && 0 < levelLimit) {
            categoryList = categoryList.stream().filter(c -> c.getLevel() <= levelLimit).collect(Collectors.toList());
        }
        if (WHETHER_FALSE.equals(isSkipUserName)){
            List<String> allUsers = categoryList.stream().map(CommonProjCategory::getGlobalId).distinct().collect(Collectors.toList());
            allUsers.addAll(categoryList.stream().map(CommonProjCategory::getUpdateGlobalId).distinct().collect(Collectors.toList()));
            Map<String, String> globalIdNameMap = RequestContent.getGlobalIdNameMap(allUsers);
            // 创建人/更新人姓名
            categoryList.forEach(commonProjCategory -> CategoryUtil.fillName(commonProjCategory, globalIdNameMap));
        }

        //只返回启用的数据
        if(isUsing != null && Constants.ZbFeatureConstants.WHETHER_TRUE == isUsing) {
            //查询有效的编码
            Map<String,Boolean> validCodeMap = new HashMap();
            categoryList.stream().filter(x -> Constants.ZbFeatureConstants.WHETHER_TRUE == x.getIsUsing()).forEach(category -> {
                if(StringUtils.isNotBlank(category.getCategorycode4())) validCodeMap.put(category.getCategorycode4(),true);
                if(StringUtils.isNotBlank(category.getCategorycode3())) validCodeMap.put(category.getCategorycode3(),true);
                if(StringUtils.isNotBlank(category.getCategorycode2())) validCodeMap.put(category.getCategorycode2(),true);
                if(StringUtils.isNotBlank(category.getCategorycode1())) validCodeMap.put(category.getCategorycode1(),true);
            });

            //过滤无效的数据
            categoryList = categoryList.stream().filter(c ->validCodeMap.containsKey(c.getCommonprojcategoryid())).collect(Collectors.toList());
        }

        // 如果一级工程分类不为空只返回一级工程分类对应的工程分类
        if (!StringUtils.isEmpty(categoryCode1)) {
            categoryList = categoryList.stream().filter(c ->StringUtils.equals(c.getCategorycode1(), categoryCode1)).collect(Collectors.toList());
        }

        // 工程分类更新时间和更新人一致
        CategoryUtil.setUpdateTimeByUpdateGlobalId(categoryList);

        categoryList = categoryTagSelfService.dealWithTag(customerCode, CategoryUtil.createProjcategoryTree(categoryList),false, needTag, categoryCode1);

        if (Constants.CategoryConstants.RESULT_TREE.equals(dataType)){
            return categoryList;
        }

        // 原始结构需要分层并排序
        ArrayList<CommonProjCategory> result = new ArrayList<>();
        addCategoriesToList(categoryList, result, -1);

        result.forEach(x -> x.setSublist(null));
        return result;
    }

    private List<CommonProjCategory> filterRepeatCodeInDeleted(List<CommonProjCategory> categoryList) {
        // categoryList中commonprojectid相同的数据,如果有isDelete=0的，则保留isDelete=0，否则保留其中一个即可
        Map<String, CommonProjCategory> codeCategoryMap = new LinkedHashMap<>();
        for (CommonProjCategory category : categoryList) {
            if (codeCategoryMap.containsKey(category.getCommonprojcategoryid()) &&
                    !Boolean.TRUE.equals(codeCategoryMap.get(category.getCommonprojcategoryid()).getIsDeleted())) {
                continue;
            }
            codeCategoryMap.put(category.getCommonprojcategoryid(), category);
        }
        return new ArrayList<>(codeCategoryMap.values());
    }

    public void addCategoriesToList(List<CommonProjCategory> categoryList, List<CommonProjCategory> result, Integer pid) {
        for (CommonProjCategory category : categoryList) {
            category.setPid(pid);
            result.add(category);
            if (CollectionUtils.isNotEmpty(category.getSublist())) {
                addCategoriesToList(category.getSublist(), result, category.getId());
            }
        }
    }

    @BusinessCache(customerCode = "${customerCode}")
    public List<CommonProjCategory> getCommonProjCategories(Integer isShowDelete, String customerCode, Integer type) {
        List<CommonProjCategory> categoryList = commonProjCategoryMapper.selectAll(customerCode, type, isShowDelete);

        /**
         * 查询基础信息库内置工程分类，type = 1  无数据，则初始化
         * 非内置工程分类则为慧果内置，type  = 2 or 3  无数据表示查询type非此企业最新内置，则直接返回指定type下的系统数据
         */
        if (CollectionUtils.isEmpty(categoryList)){
            if(type == 1){
                log.info("外部产品调用工程分类进行初始化，企业编码为:{},type:{}", customerCode, type);
                initCategoryService.initData(customerCode, type);
                categoryList = commonProjCategoryMapper.selectAll(customerCode, type, isShowDelete);
            }else{
                categoryList = commonProjCategoryMapper.selectAll(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, type, isShowDelete);
            }

        }
        // 过滤已删除的同编码数据
        categoryList = filterRepeatCodeInDeleted(categoryList);
        return categoryList;
    }

    /**
     　　* @description: 查询指定企业下的所有一级工程分类
     　　* @param  customerCode 企业编码
     　　* @return List<CommonProjCategory> 符合条件的工程分类列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/11/19 18:18
     　　*/
    @Override
    public List<CommonProjCategory> getTopOneList(String customerCode, Integer type) {
        List<CommonProjCategory> returnList = commonProjCategoryMapper.queryTruthTopCategoryList(customerCode, type);
        if (EmptyUtil.isEmpty(returnList)) {
            // 给企业创建系统内置的副本
            initCategoryService.initData(customerCode, type);
            returnList = commonProjCategoryMapper.queryTruthTopCategoryList(customerCode, type);
        }
        return returnList;
    }


    /**
     * @description: 工程分类历史数据处理
     * 1.执行sql脚本，修改名称部分和新增部分（没有commonprojcategoryid、categorycode3的值）
     * 2.内置数据的ord处理
     * 3.处理历史数据，手动触发或定时任务，给企业把内置的数据复制一份，对于标准新增分类需特殊处理，确保commonprojcategoryid未被使用，维护历史数据的ord字段
     * 4.处理完历史数据后，对内置数据的新增部分的没有commonprojcategoryid进行补全，对新的企业，levelCode不存在被占用的情况
     * 5.新企业复制内置数据时，直接copy
     * @param
     * @return void
     * <AUTHOR>
     * @date 2021/11/24 17:59
     */
    @Override
    @Transactional
    public void dealHistoryData(){
        // 获取需要处理的企业
        List<String> customerCodeList = commonProjCategoryMapper.selectCustomerCode();

        // 2.内置数据的ord处理
        sysCategoryOrd();
        // 3.处理历史数据，手动触发或定时任务，给企业把内置的数据复制一份，对于标准新增分类需特殊处理，确保commonprojcategoryid未被使用，维护历史数据的ord字段
        for (String customerCode : customerCodeList) {
            dealHistoryCategory(customerCode);
        }
        // 处理完历史数据后，对内置数据的新增部分的没有commonprojcategoryid进行补全，对新的企业，levelCode不存在被占用的情况
        dealSysCategory();
    }

    /**
     * @description: 处理完历史数据后，对内置数据的新增部分的levelCode进行补全，对新的企业，levelCode不存在被占用的情况
     * <AUTHOR>
     * @date 2021/11/23 16:23
     */
    public void dealSysCategory(){
        List<CommonProjCategory> sysCategories = getSysCategories(null);
        dealSysCode(sysCategories, null, Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE);
    }

    /**
     * @description: 内置数据的ord处理
     * <AUTHOR>
     * @date 2021/11/23 17:23
     */
    public void sysCategoryOrd(){
        // 按新标准导入的数据，levelCode和历史不一致，但是名称和ord字段是可以用的，存放到临时表
        List<CommonProjCategory> ordCategories = commonProjCategoryMapper.selectOrdTemp();
        // 历史的内置分类 + 根据标准在保证不影响历史levelCode做调整（修改名称一部分 + 新增一部分），ord字段需要处理
        List<CommonProjCategory> sysCategories = getSysCategories(null);

        if (CollectionUtils.isNotEmpty(ordCategories) && CollectionUtils.isNotEmpty(sysCategories) && ordCategories.size() == sysCategories.size()){
            // 根据最新标准得到名称全路径和ord的对应关系
            Map<String, String> ordIdAndNameMap1 = getIdAndNameMap(ordCategories, Constants.CategoryConstants.LEVEL_1);
            Map<String, String> ordIdAndNameMap2 = getIdAndNameMap(ordCategories, Constants.CategoryConstants.LEVEL_2);

            Map<String, Integer> nameUrlAndOrdMap = new HashMap<>(16);
            for (CommonProjCategory ordCategory : ordCategories) {
                String nameUrl = getNameUrl(ordCategory, ordIdAndNameMap1, ordIdAndNameMap2);
                nameUrlAndOrdMap.put(nameUrl, ordCategory.getOrd());
            }
            // 组装内置数据的名称全路径，匹配ord字段
            Map<String, String> sysIdAndNameMap1 = getIdAndNameMap(sysCategories, Constants.CategoryConstants.LEVEL_1);
            Map<String, String> sysIdAndNameMap2 = getIdAndNameMap(sysCategories, Constants.CategoryConstants.LEVEL_2);

            for (CommonProjCategory sysCategory : sysCategories) {
                String nameUrl = getNameUrl(sysCategory, sysIdAndNameMap1, sysIdAndNameMap2);
                if (!nameUrlAndOrdMap.containsKey(nameUrl)){
                    log.error("新标准的和已处理历史的分类全名称路径匹配出错了：nameUrlAndOrdMap：{}，nameUrl：{}", nameUrlAndOrdMap, nameUrl);
                    throw new BusinessException(ResponseCode.FAILURE.getCode(), "新标准的和已处理历史的分类全名称路径匹配出错了...");
                }
                sysCategory.setOrd(nameUrlAndOrdMap.get(nameUrl));
            }
        }else {
            log.error("新标准的和已处理历史的分类数量不匹配：ordCategories：{}，sysCategories：{}", ordCategories, sysCategories);
            throw new BusinessException(ResponseCode.FAILURE.getCode(), "新标准的和已处理历史的分类数量不匹配...");
        }
        if (CollectionUtils.isNotEmpty(sysCategories)){
            //commonProjCategoryMapper.batchUpdateOrd(sysCategories);
            sysCategories.forEach(item ->
                    commonProjCategoryMapper.updateOrd(item));
        }
    }

    /**
     * @description: 获取某一等级的分类id和名称的关系
     * @param categories
     * @param level
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2021/11/24 9:36
     */
    private Map<String, String> getIdAndNameMap(List<CommonProjCategory> categories, Integer level){
        return categories.stream().filter(x -> x.getLevel().equals(level.longValue()))
                .collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, CommonProjCategory::getCategoryname, (v1, v2) -> v2));
    }

    /**
     * @description: 获取分类的全名称路径，如：多层住宅（≤7F）的全名称路径 = 居住建筑住宅多层住宅（≤7F）
     * @param category
     * @param idAndNameMap1
     * @param idAndNameMap2
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/11/24 9:37
     */
    private String getNameUrl(CommonProjCategory category, Map<String, String> idAndNameMap1, Map<String, String> idAndNameMap2){
        String nameUrl;
        if (category.getLevel().equals(Constants.CategoryConstants.LEVEL_1.longValue())){
            nameUrl = category.getCategoryname().trim();
        }else if (category.getLevel().equals(Constants.CategoryConstants.LEVEL_2.longValue())){
            if (!idAndNameMap1.containsKey(category.getCategorycode1())){
                log.error("1级分类匹配出错了：idAndNameMap1：{}，categoryCode1：{}", idAndNameMap1, category.getCategorycode1());
                throw new BusinessException(ResponseCode.FAILURE.getCode(), "1级分类匹配出错了...");
            }
            nameUrl = idAndNameMap1.get(category.getCategorycode1()).trim() + category.getCategoryname().trim();
        }else if (category.getLevel().equals(Constants.CategoryConstants.LEVEL_3.longValue())){
            if (!idAndNameMap1.containsKey(category.getCategorycode1()) || !idAndNameMap2.containsKey(category.getCategorycode2())){
                log.error("1级分类或2级分类匹配出错了：idAndNameMap1：{}，categoryCode1：{}，idAndNameMap2：{}，categoryCode2：{}",
                        idAndNameMap1, category.getCategorycode1(), idAndNameMap2, category.getCategorycode2());
                throw new BusinessException(ResponseCode.FAILURE.getCode(), "1级分类匹配出错了...");
            }
            nameUrl = idAndNameMap1.get(category.getCategorycode1()).trim() + idAndNameMap2.get(category.getCategorycode2()).trim() + category.getCategoryname().trim();
        }else {
            log.error("分类等级匹配出错了：category：{}", category);
            throw new BusinessException(ResponseCode.FAILURE.getCode(), "分类等级匹配出错了...");
        }
        return nameUrl;
    }

    /**
     * @description: 给有历史数据的老企业创建内置数据副本，对于新增分类需特殊处理，确保commonprojcategoryid未被使用，维护历史数据的ord字段
     * <AUTHOR>
     * @date 2021/11/23 16:23
     */
    public void dealHistoryCategory(String customerCode){
        // 内置的数据
        List<CommonProjCategory> sysCategories = getSysCategories(null);
        // 企业的历史自定义数据
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> historyCategories = commonProjCategoryMapper.selectAll(customerCode, type, Constants.CategoryConstants.WHETHER_TRUE);

        if (CollectionUtils.isEmpty(historyCategories)){
            log.info("企业：{}，没有需要处理的自定义分类历史数据...", customerCode);
            return;
        }
        // 给企业复制一份内置数据，需要避开已被自定义使用的code
        dealSysCode(sysCategories, historyCategories, customerCode);

        Integer maxOrd1 = sysCategories.stream().filter(x -> x.getLevel().equals(Constants.CategoryConstants.LEVEL_1.longValue()))
                .map(CommonProjCategory::getOrd).max(Comparator.comparing(Function.identity())).orElse(0);

        //对2级分类，按categorycode1分组并求组内最大ord
        Map<String, Optional<Integer>> categoryCode1AndMaxOrdMap = sysCategories.stream().filter(x -> x.getLevel().equals(Constants.CategoryConstants.LEVEL_2.longValue()))
                .collect(Collectors.groupingBy(CommonProjCategory::getCategorycode1, Collectors.mapping(CommonProjCategory::getOrd, Collectors.maxBy(Comparator.comparing(Function.identity())))));

        //对3级分类，按categorycode2分组并求组内最大ord
        Map<String, Optional<Integer>> categoryCode2AndMaxOrdMap = sysCategories.stream().filter(x -> x.getLevel().equals(Constants.CategoryConstants.LEVEL_3.longValue()))
                .collect(Collectors.groupingBy(CommonProjCategory::getCategorycode2, Collectors.mapping(CommonProjCategory::getOrd, Collectors.maxBy(Comparator.comparing(Function.identity())))));

        // 分等级处理历史自定义分类的ord
        for (CommonProjCategory historyCategory : historyCategories) {
            if (Constants.CategoryConstants.LEVEL_1.equals(historyCategory.getLevel().intValue())){
                historyCategory.setOrd(++maxOrd1);
            }else if (Constants.CategoryConstants.LEVEL_2.equals(historyCategory.getLevel().intValue())){
                String categoryCode1 = historyCategory.getCategorycode1();
                if (categoryCode1AndMaxOrdMap.containsKey(categoryCode1)){
                    Integer maxOrd = categoryCode1AndMaxOrdMap.get(categoryCode1).orElse(0);
                    historyCategory.setOrd(++maxOrd);
                    categoryCode1AndMaxOrdMap.put(categoryCode1, Optional.of(maxOrd));
                }else {
                    historyCategory.setOrd(1);
                    categoryCode1AndMaxOrdMap.put(categoryCode1, Optional.of(1));
                }
            }else if (Constants.CategoryConstants.LEVEL_3.equals(historyCategory.getLevel().intValue())){
                String categoryCode2 = historyCategory.getCategorycode2();
                if (categoryCode2AndMaxOrdMap.containsKey(categoryCode2)){
                    Integer maxOrd = categoryCode2AndMaxOrdMap.get(categoryCode2).orElse(0);
                    historyCategory.setOrd(++maxOrd);
                    categoryCode2AndMaxOrdMap.put(categoryCode2, Optional.of(maxOrd));
                }else {
                    historyCategory.setOrd(1);
                    categoryCode2AndMaxOrdMap.put(categoryCode2, Optional.of(1));
                }
            }else {
                log.error("跳过脏数据，没有等级的分类:{}", historyCategory);
            }
        }
        // 更新入库
        if(CollectionUtils.isNotEmpty(historyCategories)){
            //commonProjCategoryMapper.batchUpdateOrd(historyCategories);
            historyCategories.forEach(item -> commonProjCategoryMapper.updateOrd(item));
        }
    }

    /**
     * @description: 处理code，避开原先已使用
     * @param sysCategories
     * @param historyCategories
     * @return void
     * <AUTHOR>
     * @date 2021/11/24 11:24
     */
    private void dealSysCode(List<CommonProjCategory> sysCategories, List<CommonProjCategory> historyCategories, String customerCode){
        List<CommonProjCategory> wholeList = new ArrayList<>();
        List<CommonProjCategory> dealList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(historyCategories)){
            for (CommonProjCategory historyCategory : historyCategories) {
                String levelCode = historyCategory.getCommonprojcategoryid();
                historyCategory.setLastMaxNum(Integer.parseInt(levelCode.substring(levelCode.length() - Constants.CategoryConstants.SINGLE_LEVEL_LEN)));
            }
            wholeList.addAll(historyCategories);
        }

        for (CommonProjCategory commonProjCategory : sysCategories) {
            String levelCode = commonProjCategory.getCommonprojcategoryid();
            if (StringUtils.isNotEmpty(levelCode)){
                commonProjCategory.setLastMaxNum(Integer.parseInt(levelCode.substring(levelCode.length() - Constants.CategoryConstants.SINGLE_LEVEL_LEN)));
                wholeList.add(commonProjCategory);
            }else {
                dealList.add(commonProjCategory);
            }
        }

//        dealList.sort(Comparator.comparing(CommonProjCategory::getOrd));

        for (CommonProjCategory commonProjCategory : dealList) {
            Integer lastMaxNum = wholeList.stream()
                    .filter(x -> x.getLevel().equals(Constants.CategoryConstants.LEVEL_3.longValue()))
                    .filter(x -> x.getCategorycode2().equals(commonProjCategory.getCategorycode2()))
                    .max(Comparator.comparing(CommonProjCategory::getLastMaxNum)).map(CommonProjCategory::getLastMaxNum).orElse(0);

            lastMaxNum++;
            String format = String.format("%03d", lastMaxNum);
            commonProjCategory.setCommonprojcategoryid(commonProjCategory.getCategorycode2() + format);
            commonProjCategory.setCategorycode3(commonProjCategory.getCategorycode2() + format);
            commonProjCategory.setLastMaxNum(lastMaxNum);
            wholeList.add(commonProjCategory);
        }

        if (Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE.equals(customerCode)){
            // 更新入库
            if (CollectionUtils.isNotEmpty(dealList)){
                //commonProjCategoryMapper.batchUpdateLevelCode(dealList);
                dealList.forEach(item -> commonProjCategoryMapper.updateLevelCode(item));
            }
        }else {
            // 内置数据入库
            for (CommonProjCategory commonProjCategory : sysCategories) {
                commonProjCategory.setGlobalId(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE);
                commonProjCategory.setQyCode(customerCode);
                commonProjCategory.setCreateTime(null);
                commonProjCategory.setUpdateTime(null);
                commonProjCategory.setId(IDGenerator.getNextIntId(TableNameConstants.TB_COMMONPROJCATEGORY_STANDARDS));
            }
            if (CollectionUtils.isNotEmpty(sysCategories)){
                commonProjCategoryMapper.saveBatchCommonProjCategory(sysCategories);
            }
        }
    }

    @Override
    public List<CommonProjCategory> getAllCategoryList(String customerCode, Integer type, Integer isShowDelete) {
        List<CommonProjCategory> returnList = commonProjCategoryMapper.selectAll(customerCode, type, isShowDelete);
        if (CollectionUtils.isEmpty(returnList)){
            if(type == 1){
                initCategoryService.initData(customerCode, type);
                returnList = commonProjCategoryMapper.selectAll(customerCode, type, isShowDelete);
            }else {
                returnList = commonProjCategoryMapper.selectAll(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, type, isShowDelete);
            }
        }
        return returnList;
    }

    @Override
    public List<CommonProjCategory> selectByLevelAndCode(String customerCode, String code, Integer level) {
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        return commonProjCategoryMapper.selectByLevelAndCode(customerCode, type, code, level);
    }

    /**
     　　* @description: 将旧企业工程分类数据复制到新企业工程分类标准
     　　* @param  oldCustomerCode:原企业编码  newCustomerCode 新企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 9:55
     　　*/
    @Override
    public void initCategoryData(String oldCustomerCode,String newCustomerCode){
        // 旧企业编码查找所有工程分类数据，若为空，不处理
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(oldCustomerCode);
        List<CommonProjCategory> categoryList = commonProjCategoryMapper.selectAll(oldCustomerCode, type,1);
        if (CollectionUtils.isEmpty(categoryList)) {
            return;
        }
        int size = categoryList.size();
        for (int i = 0; i < size; i++) {
            CommonProjCategory category = categoryList.get(i);
            category.setGlobalId("-100");
            category.setAccountname(null);
            category.setQyCode(newCustomerCode);
            category.setCreateTime(new Date());
            category.setUpdateTime(null);
            category.setUpdateGlobalId(null);
            category.setId(IDGenerator.getNextIntId(TableNameConstants.TB_COMMONPROJCATEGORY_STANDARDS));
        }

        // 入库保存
        commonProjCategoryMapper.saveBatchCommonProjCategory(categoryList);
    }

    /**
     　　* @description: 根据企业编码删除所有工程分类标准
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:41
     　　*/
    @Override
    public void deleteByCustomerCode(String customerCode){
        commonProjCategoryMapper.deleteByCustomerCode(customerCode, null);
    }

    @Override
    public Map<String, List<List<String>>> getSystemCategory() {
        // 系统内置的分类
        List<CommonProjCategory> categoryList = getSysCategories(null);
        if (CollectionUtils.isEmpty(categoryList)){
            throw new BusinessException("未获取到内置工程分类...");
        }
        // 一级分类code和名称的映射关系
        // Map<String, String> codeNameMap = categoryList.stream().filter(x -> x.getLevel().equals(1L)).collect(
        //        Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, CommonProjCategory::getCategoryname, (v1, v2) -> v2));

        // 树状结构
        List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(categoryList);
        // 获取所有叶子节点
        List<CommonProjCategory> lastCategoryList = CategoryUtil.getLastCategoryList(categoryListTree);

        // json
        Map<String, List<List<String>>> map = new HashMap<>(16);
        for (CommonProjCategory commonProjCategory : lastCategoryList) {
            List<String> list = new ArrayList<>();
            String categorycode1 = commonProjCategory.getCategorycode1();
            String categorycode2 = commonProjCategory.getCategorycode2();
            String categorycode3 = commonProjCategory.getCategorycode3();
            list.add(categorycode1);
            if (StringUtils.isNotEmpty(categorycode2)){
                list.add(categorycode2);
            }
            if (StringUtils.isNotEmpty(categorycode3)){
                list.add(categorycode3);
            }
            // String categoryName = codeNameMap.get(categorycode1);
            if (map.containsKey(categorycode1)){
                map.get(categorycode1).add(list);
            }else {
                List<List<String>> listList = new ArrayList<>();
                listList.add(list);
                map.put(categorycode1, listList);
            }
        }
        return map;
    }

    /**
     * 获取企业的code和分类对象的关系
     */
    @Override
    public Map<String, CommonProjCategory> getCommonProjCategoryMap(String customerCode){
        Map<String, CommonProjCategory> codeCategoryMap = new HashMap<>(16);
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> allCategoryList = getAllCategoryList(customerCode, type, Constants.CategoryConstants.WHETHER_TRUE);
        if (CollectionUtils.isNotEmpty(allCategoryList)){
            codeCategoryMap = allCategoryList.stream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, Function.identity(), (v1, v2) -> v2));
        }
        return codeCategoryMap;
    }

    private List<CommonProjCategory> getSysCategories(Integer type) {
        return commonProjCategoryMapper.selectAll(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, type, Constants.CategoryConstants.WHETHER_FALSE);
    }

    @Override
    public ResponseVo<String> deleteHuiguoData() {
//        List<CommonProjectCategoryUsed> projectCategoryUsedList = getProjectCategoryUsedList();
        List<CommonProjectCategoryUsed> projectCategoryUsedList = commonProjCategoryUsedMapper.selectAll();
        for (CommonProjectCategoryUsed categoryUsed: projectCategoryUsedList) {
            String customerCode = categoryUsed.getQyCode();
            Integer type = categoryUsed.getType();
            // 拥有type的企业相当于同步拥有2、3工程分类
            if (categoryUsed.getType() == 3) {
                // 企业数据
                commonProjCategoryMapper.deleteByCustomerCode(customerCode, type);
                expressionMapper.deleteByCustomerCode(customerCode, type);
                projectFeatureMapper.deleteByCustomerCode(customerCode, type);
                featureCategoryViewMapper.deleteByCustomerCode(customerCode, type);
                // 个人数据
                commonProjCategorySelfMapper.deleteSelfByCustomerCode(customerCode,type);
                expressionSelfMapper.deleteSelfByCustomerCode(customerCode, type);
                projectFeatureSelfMapper.deleteBySelfCustomerCode(customerCode, type);
                featureCategoryViewSelfMapper.deleteBySelfCustomerCode(customerCode, type);
            }
            // 企业数据
            commonProjCategoryMapper.deleteByCustomerCode(customerCode, 2);
            expressionMapper.deleteByCustomerCode(customerCode, 2);
            projectFeatureMapper.deleteByCustomerCode(customerCode, 2);
            featureCategoryViewMapper.deleteByCustomerCode(customerCode, 2);
            // 个人数据
            commonProjCategorySelfMapper.deleteSelfByCustomerCode(customerCode,2);
            expressionSelfMapper.deleteSelfByCustomerCode(customerCode, 2);
            projectFeatureSelfMapper.deleteBySelfCustomerCode(customerCode, 2);
            featureCategoryViewSelfMapper.deleteBySelfCustomerCode(customerCode, 2);
        }
        // 处理type = 2
        commonProjCategoryMapper.deleteByCustomerCode(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, 2);
        expressionMapper.deleteByCustomerCode(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, 2);
        projectFeatureMapper.deleteByCustomerCode(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, 2);
        // 处理type = 3
        commonProjCategoryMapper.deleteByCustomerCode(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, 3);
        expressionMapper.deleteByCustomerCode(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, 3);
        projectFeatureMapper.deleteByCustomerCode(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, 3);

        return ResponseVo.success("清除慧果分类数据完成！");
    }

    @Override
    public ResponseVo<String> migrationHuiguoData() {
        log.info("开始慧果数据迁移...");
        // 1. 获取慧果工程分类字典信息
        log.info("获取慧果工程分类数据...");
        List<List<CommonProjCategory>> guiGuoTemplates = getGuiGuoTemplates();
        if (guiGuoTemplates.size() != 2 || guiGuoTemplates.get(0).isEmpty() || guiGuoTemplates.get(1).isEmpty()) {
            return ResponseVo.success("迁移失败 [获取慧果工程分类数据失败]...");
        }

        // 2. 模板数据入库
        log.info("模板模板数据入库...");
        saveHuiGuoTemplates(guiGuoTemplates);

        // 3. 获取慧果租户信息
        log.info("查询慧果租户信息...");
        List<CommonProjectCategoryUsed> projectCategoryUsedList = getProjectCategoryUsedList();
        log.info("慧果租户信息入库...");
        if (commonProjectCategoryUsedService.size() == 0) {
            commonProjCategoryUsedMapper.batchInsert(projectCategoryUsedList);
        } else {
            log.info("租户信息已存在");
        }

        // 4.企业数据入库
        log.info("慧果企业工程分类入库...");
        saveEnterpriseProjCategory(guiGuoTemplates, projectCategoryUsedList);

        log.info("慧果数据迁移迁移完成...");

        return ResponseVo.success("迁移完成！");
    }

    private List<CommonProjectCategoryUsed> getProjectCategoryUsedList() {
        String tenants = huiGuoFeignService.tenantAll();
        tenants = EBQZipUtils.inflate(tenants);
        try {
            tenants = Base64Util.decryptBASE64(tenants);
        } catch (Exception e) {
            throw new BusinessException(ResponseCode.FAILURE.getCode(), "解密慧果租户信息失败...");
        }
        List<CommonProjectCategoryUsed> projectCategoryUsedList = new ArrayList<>();
        JSONObject object = JSON.parseObject(tenants);
        JSONArray dataArray = object.getJSONArray("data");
        if (dataArray == null || dataArray.size() == 0) return projectCategoryUsedList;

        for (int i = 0; i < dataArray.size(); i++) {
            JSONObject tenantObject = dataArray.getJSONObject(i);
            CommonProjectCategoryUsed projectCategoryUsed = new CommonProjectCategoryUsed();
            String tenantGlobalId = tenantObject.getString("tenantGlobalId");
            String customerCode = glodonUserService.getCustomerCode(tenantGlobalId);
            projectCategoryUsed.setQyCode(customerCode);
            projectCategoryUsed.setTenantglobalId(tenantGlobalId);

            projectCategoryUsed.setTenantId(tenantObject.getString("tenantId"));
            Integer type = Integer.parseInt(tenantObject.getString("type"));
            projectCategoryUsed.setType(type);
            Integer total = Integer.parseInt(tenantObject.getString("total"));
            projectCategoryUsed.setTotal(total);

            projectCategoryUsedList.add(projectCategoryUsed);
        }
        log.info("共有企业{}...", projectCategoryUsedList.size());
        return projectCategoryUsedList;
    }

    private List<List<CommonProjCategory>> getGuiGuoTemplates() {
        MultiValueMap<String, String> params = new LinkedMultiValueMap<>();
        params.add("tenantId", "");
        String tenantData = huiGuoFeignService.tenantData(params);
        tenantData = EBQZipUtils.inflate(tenantData);
        try {
            tenantData = Base64Util.decryptBASE64(tenantData);
        } catch (Exception e) {
            throw new BusinessException(ResponseCode.FAILURE.getCode(), "解密慧果工程分类信息失败...");
        }
        List<List<CommonProjCategory>> templates = new ArrayList<>();
        JSONObject object = JSON.parseObject(tenantData);

        JSONArray dataArray = object.getJSONArray("data");
        if (dataArray == null || dataArray.size() == 0) return templates;

        JSONObject firstDataObject = dataArray.getJSONObject(0);
        JSONArray categoryArray = firstDataObject.getJSONArray("categoryList");
        Map<String, String> nameCodeMap = new HashMap<>();
        nameCodeMap.put("建筑工程", "8001");
        nameCodeMap.put("土木工程", "8001");
        nameCodeMap.put("机电工程", "8001");
        nameCodeMap.put("房屋建筑工程", "8001");
        nameCodeMap.put("市政工程", "8009");
        nameCodeMap.put("城市轨道交通工程", "8003");
        nameCodeMap.put("房屋修缮工程", "8001");
        List<CommonProjCategory> template2 = new ArrayList<>();
        List<CommonProjCategory> template3 = new ArrayList<>();
        for (int i = 0; i < categoryArray.size(); i++) {
            CommonProjCategory category = new CommonProjCategory();
            JSONObject categoryObject = categoryArray.getJSONObject(i);
            category.setAccountname(categoryObject.getString("accountname"));
            category.setCategoryTypeName(categoryObject.getString("categoryTypeName"));
            if (nameCodeMap.containsKey(category.getCategoryTypeName())) {
                category.setCategoryTypeCode(nameCodeMap.get(category.getCategoryTypeName()));
            } else {
                category.setCategoryTypeCode("8007");
            }

            category.setType(Integer.parseInt(categoryObject.getString("type")));
            category.setCategorycode1(categoryObject.getString("categorycode1"));
            String categorycode2 = categoryObject.getString("categorycode2");
            if (!categorycode2.equals("")) {
                category.setCategorycode2(categorycode2);
            }
            String categorycode3 = categoryObject.getString("categorycode3");
            if (!categorycode3.equals("")) {
                category.setCategorycode3(categorycode3);
            }
            String categorycode4 = categoryObject.getString("categorycode4");
            if (!categorycode4.equals("")) {
                category.setCategorycode4(categorycode4);
            }
            category.setCategoryname(categoryObject.getString("categoryname"));
            category.setCommonprojcategoryid(categoryObject.getString("commonprojcategoryid"));
            category.setGlobalId(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE);
            String strDelete = categoryObject.getString("isDeleted");
            if (strDelete.equals("1")) {
                category.setIsDeleted(Boolean.TRUE);
            } else {
                category.setIsDeleted(Boolean.FALSE);
            }

            String strUsing = categoryObject.getString("isUsing");
            if (strUsing.equals("0")) {
                category.setIsUsing(0);
            } else {
                category.setIsUsing(1);
            }

            Integer level = Integer.parseInt(categoryObject.getString("level"));
            category.setLevel(level.longValue());

            Integer ord = Integer.parseInt(categoryObject.getString("ord"));
            category.setOrd(ord);

            category.setQyCode(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE);
            category.setIsUsable(1);
            category.setId(IDGenerator.getNextIntId(TableNameConstants.TB_COMMONPROJCATEGORY_STANDARDS));

            if (category.getType() == 2) {
                template2.add(category);
            } else {
                template3.add(category);
            }
        }

        templates.add(template2);
        templates.add(template3);
        return templates;
    }

    private void saveHuiGuoTemplates(List<List<CommonProjCategory>> templates) {
        for (List<CommonProjCategory> categoryList: templates) {
            if (categoryList.isEmpty()) {
                continue;
            }
            if (commonProjCategoryMapper.selectInit(categoryList.get(0).getQyCode(), categoryList.get(0).getType()) > 0) {
                log.info("已存在模板类型{}的工程分类...", categoryList.get(0).getType());
                continue;
            }

            commonProjCategoryMapper.saveBatchCommonProjCategory(categoryList);
        }
    }

    private void saveEnterpriseProjCategory(List<List<CommonProjCategory>> templates, List<CommonProjectCategoryUsed> categoryUsedInfos) {
        for (int i = 0; i < categoryUsedInfos.size(); i++) {
            CommonProjectCategoryUsed item = categoryUsedInfos.get(i);
            log.info("开始初始化企业[{}/{}],企业编码{}...", i+1, categoryUsedInfos.size(), item.getQyCode());
            if (commonProjCategoryMapper.selectInit(item.getQyCode(), item.getType()) > 0) {
                log.info("企业已存在工程分类...");
                continue;
            }

            // 初始化类别为2的类别
            saveOneEnterpriseProjCategory(item, templates.get(0), 2);

            // 初始化类别为3的类别
            if (item.getTotal() == 2) {
                saveOneEnterpriseProjCategory(item, templates.get(1), 3);
            }
        }
    }

    private void saveOneEnterpriseProjCategory(CommonProjectCategoryUsed categoryUsed,
                                               List<CommonProjCategory> categoryList, int type) {
        for (CommonProjCategory category: categoryList) {
            category.setQyCode(categoryUsed.getQyCode());
            category.setType(type);
            category.setId(IDGenerator.getNextIntId(TableNameConstants.TB_COMMONPROJCATEGORY_STANDARDS));
        }
        commonProjCategoryMapper.saveBatchCommonProjCategory(categoryList);
    }

    /**
     * Description: <br> 获取工程分类
     * @Param: [categoryList]
     * @Return: String
     * @Author: lisp-c
     * @Date: 2022/3/11 17:57
     * @version V1.0
     */
    @Override
    public String  getSystemBuiltCategory(List<CommonProjCategory> categoryList) {
        List<List<String>> projectTypeList = new ArrayList();
        // 一级分类code和名称的映射关系
        //Map<String, String> codeNameMap = categoryList.stream().filter(x -> x.getLevel().equals(1L)).collect(
        //        Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, CommonProjCategory::getCategoryname, (v1, v2) -> v2));
        // 树状结构
        List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(categoryList);
        // 获取所有叶子节点
        List<CommonProjCategory> lastCategoryList = CategoryUtil.getLastCategoryList(categoryListTree);
        // json
        Map<String, List<List<String>>> map = new HashMap<>(16);
        for (CommonProjCategory commonProjCategory : lastCategoryList) {
            List<String> list = new ArrayList<>();
            String categorycode1 = commonProjCategory.getCategorycode1();
            String categorycode2 = commonProjCategory.getCategorycode2();
            String categorycode3 = commonProjCategory.getCategorycode3();
            String categorycode4 = commonProjCategory.getCategorycode4();
            list.add(categorycode1);
            if (StringUtils.isNotEmpty(categorycode2)){
                list.add(categorycode2);
            }
            if (StringUtils.isNotEmpty(categorycode3)){
                list.add(categorycode3);
            }
            if (StringUtils.isNotEmpty(categorycode4)){
                list.add(categorycode4);
            }
            //String categoryName = codeNameMap.get(categorycode1);
            if (map.containsKey(categorycode1)){
                map.get(categorycode1).add(list);
            }else {
                List<List<String>> listList = new ArrayList<>();
                listList.add(list);
                map.put(categorycode1, listList);
            }
        }

        for (List<List<String>> value : map.values()) {
            projectTypeList.addAll(value);
        }
        Map<String,List<List<String>>> projectTypeMap = new HashMap<>();
        projectTypeMap.put("projectType",projectTypeList);
        return JSONObject.toJSONString(projectTypeMap);
    }

    @Override
    public List<CommonProjCategory> getCategoryByCodes(Set<String> categoryCodeSet, String customerCode) {
        if (CollectionUtils.isEmpty(categoryCodeSet)){
            return null;
        }
        // 工程分类查询企业最新确定的类别
        Integer categoryType = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        return commonProjCategoryMapper.getByCodeSet(categoryCodeSet, customerCode, categoryType);
    }

    @Override
    public List<CommonProjCategory> selectByOneLevelCode(String customerCode, String oneLevelCode) {
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        return commonProjCategoryMapper.selectByOneLevelCode(customerCode, type, oneLevelCode);
    }

    @Override
    public List<String> getAllGlobalIds(List<CommonProjCategory> categories) {
        List<String> globals = categories.stream().map(CommonProjCategory::getGlobalId).collect(Collectors.toList());
        globals.addAll(categories.stream().map(CommonProjCategory::getUpdateGlobalId).collect(Collectors.toList()));
        return globals.stream().distinct().collect(Collectors.toList());
    }
}
