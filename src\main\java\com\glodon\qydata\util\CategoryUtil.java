package com.glodon.qydata.util;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/**
 *
 *
 * <AUTHOR>
 */
@SuppressWarnings("Duplicates")
@Slf4j
public class CategoryUtil {
    public static List<CommonProjCategory> createProjcategoryTree(List<CommonProjCategory> commomprojcategories) {
        if (CollUtil.isNotEmpty(commomprojcategories)){
            commomprojcategories.sort(Comparator.comparing(CommonProjCategory::getOrd));
        }
        List<CommonProjCategory> result = new ArrayList<>();
        Map<String, List<CommonProjCategory>> secondCategories = new HashMap<>();
        Map<String, List<CommonProjCategory>> thirdCategories = new HashMap<>();
        Map<String, List<CommonProjCategory>> fourthCategories = new HashMap<>();
        for (int i = 0; i < commomprojcategories.size(); i++) {
            CommonProjCategory category = commomprojcategories.get(i);
            if (category.getLevel() == 1) {
                result.add(category);
            }
            else if (category.getLevel() == 2) {
                String parentCode = category.getCommonprojcategoryid().substring(0,3);
                List<CommonProjCategory> tempList = secondCategories.get(parentCode);
                if (tempList == null) {
                    tempList = new ArrayList<>();
                    secondCategories.put(parentCode, tempList);
                }
                tempList.add(category);
            }
            else if (category.getLevel() == 3) {
                String parentCode = category.getCommonprojcategoryid().substring(0,6);
                List<CommonProjCategory> tempList = thirdCategories.get(parentCode);
                if (tempList == null) {
                    tempList = new ArrayList<>();
                    thirdCategories.put(parentCode, tempList);
                }
                tempList.add(category);
            }
            else if (category.getLevel() == 4) {
                String parentCode = category.getCommonprojcategoryid().substring(0,9);
                List<CommonProjCategory> tempList = fourthCategories.get(parentCode);
                if (tempList == null) {
                    tempList = new ArrayList<>();
                    fourthCategories.put(parentCode, tempList);
                }
                tempList.add(category);
            }
        }

        for (CommonProjCategory firstElement: result) { // 一级
            List<CommonProjCategory> secondElements = secondCategories.get(firstElement.getCommonprojcategoryid());
            if (secondElements == null) {
                firstElement.setSublist(new ArrayList<>());
                continue;
            }
            firstElement.setSublist(secondElements);

            for (CommonProjCategory secondElement: secondElements) { // 二级
                List<CommonProjCategory> thirdElements = thirdCategories.get(secondElement.getCommonprojcategoryid());
                if (thirdElements == null) {
                    secondElement.setSublist(new ArrayList<>());
                    continue;
                }
                secondElement.setSublist(thirdElements);

                for (CommonProjCategory thirdElement: thirdElements) { // 三级
                    List<CommonProjCategory> fourthElements = fourthCategories.get(thirdElement.getCommonprojcategoryid());
                    if (fourthElements == null) {
                        thirdElement.setSublist(new ArrayList<>());
                        continue;
                    }
                    thirdElement.setSublist(fourthElements);
                }
            }
        }

        return result;
    }

    /**
     * 根据树状结构获取所有叶子节点
     * <AUTHOR>
     */
    public static List<CommonProjCategory> getLastCategoryList(List<CommonProjCategory> categoryListTree){
        List<CommonProjCategory> lastCategoryList = new ArrayList<>();
        for (CommonProjCategory commonProjCategory : categoryListTree) {
            List<CommonProjCategory> sublist = commonProjCategory.getSublist();
            if (CollectionUtils.isNotEmpty(sublist)){
                lastCategoryList.addAll(getLastCategoryList(sublist));
            }else {
                lastCategoryList.add(commonProjCategory);
            }
        }
        return lastCategoryList;
    }

    /**
     * @description: 填充更新人和创建人姓名
     * @param category
     * @param globalIdNameMap
     * @return void
     * <AUTHOR>
     * @date 2021/11/2 11:39
     */
    public static void fillName(CommonProjCategory category, Map<String, String> globalIdNameMap){
        if (Objects.isNull(globalIdNameMap) || globalIdNameMap.isEmpty() || Objects.isNull(category)){
            return;
        }
        if (category.getGlobalId() != null && globalIdNameMap.containsKey(category.getGlobalId())){
            category.setAccountname(globalIdNameMap.get(category.getGlobalId()));
        }
        if (category.getUpdateGlobalId() != null && globalIdNameMap.containsKey(category.getUpdateGlobalId())){
            category.setUpdateAccountName(globalIdNameMap.get(category.getUpdateGlobalId()));
        }
    }

    public static String buildProjectType(List<CommonProjCategory> categoryList, List<String> topCategoryNameList){
        categoryList = rangeCode(categoryList, topCategoryNameList);

        categoryList = CategoryUtil.createProjcategoryTree(categoryList);
        List<List<String>> codeList = new ArrayList<>();
        getCategoryLeafCode(categoryList, codeList);
        String codeString = JSONObject.toJSONString(codeList);
        return "{\"projectType\":" + codeString + "}";
    }

    public static boolean deDuplication(List<CommonProjCategory> categoryList){
        boolean removeFlag = false;
        Set<String> codeSet = new HashSet<>();

        Iterator<CommonProjCategory> iterator = categoryList.iterator();
        while (iterator.hasNext()){
            CommonProjCategory category = iterator.next();
            String code = category.getCommonprojcategoryid();
            if (!codeSet.add(code)){
                iterator.remove();
                removeFlag = true;
            }
        }

        return removeFlag;
    }

    private static List<CommonProjCategory> rangeCode(List<CommonProjCategory> categoryList, List<String> topCategoryNameList){
        if (CollectionUtils.isEmpty(categoryList) || CollectionUtils.isEmpty(topCategoryNameList)){
            return categoryList;
        }

        Map<String, String> topNameMap = categoryList.parallelStream()
                .filter(x -> x.getLevel() == 1)
                .collect(Collectors.toMap(CommonProjCategory::getCategoryname, CommonProjCategory::getCommonprojcategoryid));

        Set<String> topCategoryIdSet = new HashSet<>();
        for (String topCategoryName : topCategoryNameList) {
            if (topNameMap.containsKey(topCategoryName)){
                topCategoryIdSet.add(topNameMap.get(topCategoryName));
            }
        }

        return categoryList.stream().filter(x -> topCategoryIdSet.contains(x.getCategorycode1())).collect(Collectors.toList());
    }

    private static void getCategoryLeafCode(List<CommonProjCategory> categoryList, List<List<String>> codeList){
        categoryList.forEach(item -> {
            if (item.getSublist() != null && item.getSublist().size() > 0) {
                getCategoryLeafCode(item.getSublist(), codeList);
            } else {
                List<String> categorycodeList = new ArrayList<>();
                if (StringUtils.isNotEmpty(item.getCategorycode1())) {
                    categorycodeList.add(item.getCategorycode1());
                }
                if (StringUtils.isNotEmpty(item.getCategorycode2())) {
                    categorycodeList.add(item.getCategorycode2());
                }
                if (StringUtils.isNotEmpty(item.getCategorycode3())) {
                    categorycodeList.add(item.getCategorycode3());
                }
                if (StringUtils.isNotEmpty(item.getCategorycode4())) {
                    categorycodeList.add(item.getCategorycode4());
                }
                codeList.add(categorycodeList);
            }
        });
    }

    /**
     * @description: 解析projectType获取一级工程分类
     * @param projectType
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2021/10/25 11:06
     */
    public static Set<String> getCategoryCode(String projectType){
        HashSet<String> categoryCodeSet = new HashSet<>();
        if (StringUtils.isNotEmpty(projectType)){
            Map<String, Object> parse = JSONObject.parseObject(projectType, Map.class);
            if (Objects.nonNull(parse)){
                Object projectTypeJson = parse.get("projectType");
                if(Objects.nonNull(projectTypeJson)){
                    JSONArray jsonArray = JSONArray.parseArray(projectTypeJson.toString());
                    for (Object o : jsonArray) {
                        String o1 = o.toString();
                        JSONArray objects = JSONArray.parseArray(o1);
                        String categoryCode = (String) objects.get(0);
                        categoryCodeSet.add(categoryCode);
                    }
                }
            }
        }
        return categoryCodeSet;
    }

    /**
     * @description: 工程分类更新时间和更新人一致
     * @param categoryList
     * @return void
     * <AUTHOR>
     * @date 2023/08/28 11:39
     */
    public static void setUpdateTimeByUpdateGlobalId(List<CommonProjCategory> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return;
        }

        for (CommonProjCategory category : categoryList) {
            if (StringUtils.isEmpty(category.getUpdateGlobalId())) {
                category.setUpdateTime(null);
            }
        }
    }

    /**
     * @description: 工程分类更新时间和更新人一致
     * @param category
     * @return void
     * <AUTHOR>
     * @date 2023/08/28 11:39
     */
    public static void setUpdateTimeByUpdateGlobalId(CommonProjCategory category) {
        if (category != null && StringUtils.isEmpty(category.getUpdateGlobalId())) {
            category.setUpdateTime(null);
        }
    }




    /**
     * @description:  获取当前业态的所有末级业态
     * @author: luoml-b
     * @date: 2024/5/30 16:58
     * @param: categoryList：所有业态列表
     * @param: categoryCode：当前业态code
     * @return: java.util.List<com.glodon.qydata.entity.standard.category.CommonProjCategory>
     **/

    public static List<CommonProjCategory> getAllLeafNodes(List<CommonProjCategory> categoryList, String categoryCode) {
        List<CommonProjCategory> result = new ArrayList<>();

        CommonProjCategory currentNode = findNodeById(categoryList, categoryCode);

        if (currentNode != null) {
            getAllLeafNodesHelper(currentNode, result);
        }

        return result;
    }

    private static void getAllLeafNodesHelper(CommonProjCategory node, List<CommonProjCategory> result) {
        if (CollUtil.isEmpty(node.getSublist())) {
            result.add(node);
        } else {
            for (CommonProjCategory child : node.getSublist()) {
                getAllLeafNodesHelper(child, result);
            }
        }
    }

    private static CommonProjCategory findNodeById(List<CommonProjCategory> list, String categoryCode) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        for (CommonProjCategory category : list) {
            if (categoryCode.equals(category.getCommonprojcategoryid())) {
                return category;
            } else {
                CommonProjCategory foundNode = findNodeById(category.getSublist(), categoryCode);
                if (foundNode != null) {
                    return foundNode;
                }
            }
        }
        return null;
    }



//    public static List<CommonProjCategory> getLeafCategory(String categoryCode, List<CommonProjCategory> categoryList){
//        List<CommonProjCategory> matchCategory = categoryList.stream()
//                .filter(x -> categoryCode.equals(x.getCommonprojcategoryid())  || hasMatchingDescendant(x, categoryCode)).collect(Collectors.toList());
//        if (CollUtil.isEmpty(matchCategory)) {
//            return Collections.emptyList();
//        }
//        return flattenCategories(matchCategory)
//                .filter(category -> CollUtil.isEmpty(category.getSublist()))
//                .collect(Collectors.toList());
//
//    }
//
//    private static Stream<CommonProjCategory> flattenCategories(List<CommonProjCategory> categories) {
//        return categories.stream()
//                .flatMap(category -> Stream.concat(Stream.of(category), flattenCategories(category.getSublist())));
//    }
//
//    private static boolean hasMatchingDescendant(CommonProjCategory category, String categoryCode) {
//        if (CollUtil.isEmpty(category.getSublist())) {
//            return false;
//        }
//
//        for (CommonProjCategory subCategory : category.getSublist()) {
//            if (categoryCode.equals(subCategory.getCommonprojcategoryid()) || hasMatchingDescendant(subCategory, categoryCode)) {
//                return true;
//            }
//        }
//
//        return false;
//    }

    /**
     * @description: 工程特征的工程分类json转换
     * 老结构：{"projectType":[["003","003001","003001001"],["003","003003"],["026","026001"]]}
     * 新结构：{"projectType":["026","027","003001","003003"]}
     * 老结构是二维数组，存的是末级业态，同时存了自己所有的父，但是给末级业态新增子业态会导致数据没有存到最子集，所以转换成新结构
     * 新结构是一维数组，存的是尽可能往父结构靠的数据，例如，父下面所有子都被选了就存父级
     * @author: luoml-b
     * @date: 2024/5/28 16:08
     * @param: categoryListTree：用户的工程分类列表数据
     * @param: projectType：带转换的工程分类json字符串
     * @param: convertType: 转换方式（1：新结构转老结构；2：老结构转新结构）
     * @return: java.lang.String：转换之后的工程分类json
     **/
    public static String projectTypeConvert(List<CommonProjCategory> categoryListTree,
                                            String projectType,
                                            String convertType) {
        if (StringUtils.isBlank(projectType)) {
            return StringUtils.EMPTY;
        }
        JSONObject jsonObject = JSONObject.parseObject(projectType);
        JSONArray projectTypeArr = jsonObject.getJSONArray(BusinessConstants.PROJECT_TYPE);
        JSONObject switchProjectType = new JSONObject();
        if (CollUtil.isEmpty(projectTypeArr)) {
            switchProjectType.put(BusinessConstants.PROJECT_TYPE, Collections.emptyList());
            return switchProjectType.toJSONString();
        }
        if (BusinessConstants.PROJECT_TYPE_CONVERT_OLD.equals(convertType)) {
            //新数据转成老数据
            convertOldProjectType(categoryListTree, projectTypeArr, switchProjectType);
        } else if (BusinessConstants.PROJECT_TYPE_CONVERT_NEW.equals(convertType)) {
            //老数据转新数据
            convertNewProjectType(categoryListTree, projectTypeArr, switchProjectType);
        } else {
            throw new BusinessException("不支持projectType的转换类型，convertType: " + convertType);
        }
        return switchProjectType.toJSONString();
    }

    private static void convertNewProjectType(List<CommonProjCategory> categoryListTree, JSONArray projectTypeArr, JSONObject switchProjectType) {
        if (!verifyProjectTypeIsOld(projectTypeArr)) {
            switchProjectType.put(BusinessConstants.PROJECT_TYPE, projectTypeArr);
            return;
        }
        List<String> res = assembleNewProjectType(categoryListTree, projectTypeArr);
        List<String> sortedRes = sortNewProjectType(res);
        switchProjectType.put(BusinessConstants.PROJECT_TYPE, sortedRes);
    }

    private static void convertOldProjectType(List<CommonProjCategory> categoryListTree, JSONArray projectTypeArr, JSONObject switchProjectType) {
        if (verifyProjectTypeIsOld(projectTypeArr)) {
            switchProjectType.put(BusinessConstants.PROJECT_TYPE, projectTypeArr);
            return;
        }
        List<List<String>> res = assembleOldProjectType(categoryListTree, projectTypeArr);
        List<List<String>> sortedRes = sortOldProjectType(categoryListTree, res);
        switchProjectType.put(BusinessConstants.PROJECT_TYPE, sortedRes);
    }

    private static List<String> assembleNewProjectType(List<CommonProjCategory> categoryListTree, JSONArray projectTypeArr) {
        //重新组装数据，因为有些bug数据没有选到最末级
        JSONArray projectTypeArrNew = recombinationData(categoryListTree, projectTypeArr);

        //组装业态与末级业态的map
        Map<String, List<String>> categoryRelationMap = assembleCategoryRelationMap(projectTypeArrNew);

        //组装新结构的结果数据
        return assembleResult(categoryListTree, categoryRelationMap);
    }

    private static List<String> assembleResult(List<CommonProjCategory> categoryListTree, Map<String, List<String>> map) {
        List<String> res = new ArrayList<>();
        List<String> exludeList = new ArrayList<>();
        map.forEach((k, v) -> {
            List<CommonProjCategory> leafCategory = CategoryUtil.getAllLeafNodes(categoryListTree, k);
            List<String> leafCode = leafCategory.stream().map(CommonProjCategory::getCommonprojcategoryid).collect(Collectors.toList());
            //以下判断工程分类json数据中当前工程分类的末级是不是和用户工程分类列表数据当前工程分类的末级相等
            //因为map是按照父到子的顺序构建的，所有先判断父
            if (k.length() == 3 && leafCode.size() == 1 && leafCode.get(0).equals(k)) {
                //只有一级分类，则全部需要
                res.add(k);
            }  else if (v.size() == leafCode.size() && new HashSet<>(leafCode).containsAll(v)) {
                //如果工程分类json数据和工程分类列表数据相等，意味选了所有子，就把当前父存入数据，前提是父节点不包含在k中，包含证明父已经被收集了，不需要子了
                if (exludeList.stream().noneMatch(k::startsWith)) {
                    //需要删除掉自己的子，因为可能一级存了子二级三级需要的是父级
                    res.removeAll(v);
                    res.add(k);
                    exludeList.add(k);
                }
            } else {
                //如果工程分类json数据和工程分类列表数据不相等，意味着没有选所有子，则存自己
                res.addAll(v);
            }
        });
        return res;
    }

    private static Map<String, List<String>> assembleCategoryRelationMap(JSONArray projectTypeArrNew) {
        // 使用LinkedHashMap是为了有顺序，父在前面子在后面，之后组装数据的时候好判断
        Map<String, List<String>> categoryRelationMap = new LinkedHashMap<>();
        List<JSONArray> distinctList = new ArrayList<>();
        projectTypeArrNew.forEach(o -> {
            JSONArray item = (JSONArray) o;
            //有相同的脏数据，跳过
            if (distinctList.contains(item)) {
                return;
            }
            distinctList.add(item);
            String lastCategory = item.get(item.size() - 1).toString();

            //遍历老结构的里层数组，拿到每层的业态和末级业态组成map
            for (Object value : item) {
                String currentCategory = value.toString();
                //末级业态不需要
                if (!currentCategory.equals(lastCategory) || currentCategory.length() == 3) {
                    List<String> lastCategoryRes = categoryRelationMap.getOrDefault(currentCategory, new ArrayList<>());
                    if (CollUtil.isNotEmpty(lastCategoryRes)) {
                        lastCategoryRes.add(lastCategory);
                        categoryRelationMap.put(currentCategory, lastCategoryRes);
                    } else {
                        categoryRelationMap.put(currentCategory, new ArrayList<>(Collections.singletonList(lastCategory)));
                    }
                }
            }
        });
        return categoryRelationMap;
    }

    private static JSONArray recombinationData(List<CommonProjCategory> categoryListTree, JSONArray projectTypeArr) {
        JSONArray projectTypeArrNew = new JSONArray();
        projectTypeArr.forEach(o -> {
            JSONArray item = (JSONArray) o;
            String lastCategory = item.get(item.size() - 1).toString();
            List<CommonProjCategory> lastCategoryLeaf = CategoryUtil.getAllLeafNodes(categoryListTree, lastCategory);
            if (CollUtil.isNotEmpty(lastCategoryLeaf)) {
                List<String> leafCode = lastCategoryLeaf.stream().map(CommonProjCategory::getCommonprojcategoryid).collect(Collectors.toList());
                List<List<String>> itemList = splitOldProjectType(leafCode);
                itemList.forEach(list -> projectTypeArrNew.add(JSON.parseArray(JSON.toJSONString(list))));
            } else {
                projectTypeArrNew.add(item);
            }
        });
        return projectTypeArrNew;
    }

    private static List<List<String>> splitOldProjectType(List<String> leafCode) {
        return leafCode.stream()
                .map(s -> {
                    List<String> splitList = new ArrayList<>();
                    for (int i = 0; i < s.length(); i += 3) {
                        //每次从头开始，取值三个的倍数
                        splitList.add(s.substring(0, i + 3));
                    }
                    return splitList;
                })
                .collect(Collectors.toList());
    }

    private static List<List<String>> assembleOldProjectType(List<CommonProjCategory> categoryListTree, JSONArray projectTypeArr) {
        List<List<String>> res = new ArrayList<>();
        projectTypeArr.forEach(o -> {
            String categoryCode = o.toString();
            //拿到所有末级
            List<CommonProjCategory> leafCategory = CategoryUtil.getAllLeafNodes(categoryListTree, categoryCode);
            List<String> leafCode = leafCategory.stream().map(CommonProjCategory::getCommonprojcategoryid).collect(Collectors.toList());
            //将所有末级业态组装成父子结构，即：[001, 001001, 001001001]
            res.addAll(splitOldProjectType(leafCode));
        });
        return res;
    }

    private static List<String> sortNewProjectType(List<String> res) {
        return res.stream()
                .distinct()
                .sorted(Comparator.comparing(String::length).thenComparing(Comparator.naturalOrder()))
                .collect(Collectors.toList());
    }

    /**
     * @description: 将构造的老数据结构按照工程分类的顺序排序，取里层数组的最后一个元素对比工程分类的顺序
     * @author: luoml-b
     * @date: 2024/5/31 9:35
     * @param: categoryListTree
     * @param: res
     * @return: java.util.List<java.util.List<java.lang.String>>
     **/
    private static List<List<String>> sortOldProjectType(List<CommonProjCategory> categoryListTree, List<List<String>> res) {
        // 平铺展开工程分类列表
        List<CommonProjCategory> flatList = CategoryUtil.flatCategoryTree(categoryListTree);
        // 创建一个业态code到索引位置的映射
        Map<String, Integer> idToIndexMap = IntStream.range(0, flatList.size())
                .boxed()
                .collect(Collectors.toMap(i -> flatList.get(i).getCommonprojcategoryid(), i -> i, (existing, replacement) -> existing));

        return res.stream()
                .sorted((list1, list2) -> {
                    // 获取第一个列表的最后一个元素
                    String lastElement1 = list1.get(list1.size() - 1);
                    // 获取第二个列表的最后一个元素
                    String lastElement2 = list2.get(list2.size() - 1);
                    // 获取第一个列表最后一个元素对应的索引
                    Integer index1 = idToIndexMap.getOrDefault(lastElement1, Integer.MAX_VALUE);
                    // 获取第二个列表最后一个元素对应的索引
                    Integer index2 = idToIndexMap.getOrDefault(lastElement2, Integer.MAX_VALUE);
                    // 按照索引位置排序
                    return index1.compareTo(index2);
                })
                .collect(Collectors.toList());
    }


    private static List<CommonProjCategory> flatCategoryTree(List<CommonProjCategory> categoryListTree) {
        if (CollUtil.isEmpty(categoryListTree)) {
            return Collections.emptyList();
        }
        return categoryListTree.stream()
                .flatMap(category -> Stream.concat(Stream.of(category), flatCategoryTree(category.getSublist()).stream()))
                .collect(Collectors.toList());
    }

    /**
     * @description: 校验是新数据还是老数据，老数据是二维数组，新数据是一维数组
     * @author: luoml-b
     * @date: 2024/5/31 10:27
     * @param: projectTypeArr
     * @return: boolean
     **/
    private static boolean verifyProjectTypeIsOld(JSONArray projectTypeArr) {
        return projectTypeArr.stream().anyMatch(obj -> obj instanceof JSONArray);
    }


}
