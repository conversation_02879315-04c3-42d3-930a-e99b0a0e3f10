package com.glodon.qydata.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @description: 调用授权平台签名认证动态生成工具类
 *
 *
 * @date 2023-08-16 14:11
 * @email <EMAIL>
 */
@Slf4j
public class SignGeneratorUtils {

    private SignGeneratorUtils() {
        throw new IllegalStateException("Utility class");
    }

    private static final String AND_SEPARATOR = "&";
    private static final String EQUAL_SEPARATOR = "=";
    private static final String PATH_SEPARATOR = "?";
    private static final String EXCLUDE_PARAM = "g-signature";
    private static final String SIGNATURE_PARAM = "&g-signature=";

    /**
     * 全路径
     * <AUTHOR>
     * @date 2023-08-17 11:11:52
     * @param fullPathStr
     * @param body
     * @return java.lang.String
     */
    @SuppressWarnings("all")
    public static String signTheRequest(String fullPathStr, String body) {
        return new StringBuilder(fullPathStr)
                .append(SIGNATURE_PARAM)
                .append(getSign(fullPathStr, body))
                .toString();
    }

    /**
     * 传入url -> 返回 全路径
     * <AUTHOR>
     * @date 2023-08-17 11:12:03
     * @param url
     * @param body
     * @return java.lang.String
     */
    @SuppressWarnings("all")
    public static String signTheUrl(String url, String body) {
        return new StringBuilder(url)
                .append(SIGNATURE_PARAM)
                .append(getSign(getFullPath(url), body))
                .toString();
    }

    public static String getSign(String fullPathStr, String body) {
        String path = StrUtil.subBefore(fullPathStr, PATH_SEPARATOR, Boolean.FALSE);
        String param = StrUtil.subAfter(fullPathStr, PATH_SEPARATOR, Boolean.FALSE);
        StringBuilder sb = new StringBuilder(path).append(AND_SEPARATOR);
        Map<String, String> paramMap = parseParam(fullPathStr, param);
        if (CollUtil.isNotEmpty(paramMap)) {
            sb.append(Joiner.on(AND_SEPARATOR).join(paramMap.values()));
        }
        if (StringUtils.isNotBlank(body)) {
            sb.append(AND_SEPARATOR).append(body);
        }
        //最后加上Secret
        sb.append(AND_SEPARATOR).append(getAppSecret());
        byte[] bytes = sb.toString().getBytes(StandardCharsets.UTF_8);
        //使用MD5签名
        return DigestUtils.md5Hex(bytes).toUpperCase();
    }

    /**
     * 获取AppSecret
     * <AUTHOR>
     * @date 2023-08-16 17:24:38
     * @return java.lang.String
     */
    private static String getAppSecret() {
        return SpringUtil.getApplicationContext().getEnvironment().getProperty("apiAuth.appSecret");
    }

    private static String getFullPath(String url) {
        return  getHost() + url;
    }

    private static String getHost() {
        return SpringUtil.getApplicationContext().getEnvironment().getProperty("apiAuth.url");
    }

    private static Map<String, String> parseParam(String fullPathStr, String params) {
        if (StringUtils.isBlank(params)) {
            log.info("当前请求[url:{}]的请求参数未空", fullPathStr);
            return Maps.newHashMap();
        }
        TreeMap<String, String> paramMap = new TreeMap<>();
        for (String singleParam : Splitter.on(AND_SEPARATOR).split(params)) {
            String[] paramArray = singleParam.split(EQUAL_SEPARATOR);
            if (paramArray.length == 2 && !paramArray[0].equals(EXCLUDE_PARAM) && Strings.isNotBlank(paramArray[1])) {
                paramMap.put(paramArray[0], paramArray[1]);
            }
        }
        return paramMap;
    }
}

