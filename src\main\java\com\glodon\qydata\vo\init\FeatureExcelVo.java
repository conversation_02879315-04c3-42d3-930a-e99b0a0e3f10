package com.glodon.qydata.vo.init;

import com.glodon.qydata.common.annotation.ExcelParam;
import lombok.Data;

import java.io.Serializable;

/**
 * 导入工程特征的数据请求VO
 */
@Data
public class FeatureExcelVo implements Serializable {

    @ExcelParam(value = "序号", index = 0)
    private String indexNum;

    @ExcelParam(value = "名称", index = 1)
    private String name;

    @ExcelParam(value = "数据类型", index = 2)
    private String typeCode;

    @ExcelParam(value = "单位", index = 3)
    private String unit;

    @ExcelParam(value = "枚举值", index = 4)
    private String option;

    @ExcelParam(value = "工程分类", index = 5)
    private String category;

    @ExcelParam(value = "适用专业", index = 6)
    private String trade;

    @ExcelParam(value = "是否启用", index = 7)
    private String isEnable;

    @ExcelParam(value = "是否必填", index = 8)
    private String isRequired;

    @ExcelParam(value = "是否计算口径", index = 9)
    private String isExpression;

    @ExcelParam(value = "创建账号", index = 10)
    private String createName;

    @ExcelParam(value = "创建时间", index = 11)
    private String createDate;

    @ExcelParam(value = "更改账号", index = 12)
    private String updateName;

    @ExcelParam(value = "更改时间", index = 13)
    private String updateDate;

    @ExcelParam(value = "备注", index = 14)
    private String remark;

}
