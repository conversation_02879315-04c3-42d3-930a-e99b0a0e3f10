package com.glodon.qydata.vo.standard.buildStandard;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 建造标准细则和标准说明批量删除
 * @date 2022/8/5 10:03
 */
@Data
public class ZbProjectStandardDeleteVo {

    /** 标准id */
    @NotNull(message = "标准ID不能为空")
    private Long standardId;

    /** 细则id列表 */
    private List<Long> standardDetailIds;

    /** 标准说明列表 */
    private List<Long> standardDetailDescIds;
}
