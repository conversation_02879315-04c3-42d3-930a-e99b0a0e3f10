package com.glodon.qydata.vo.standard.buildStandard;

import jakarta.validation.constraints.NotNull;
import lombok.Data;


/**
 * <AUTHOR>
 * @description: 标准说明新增
 * @date 2022/8/5 10:03
 */
@Data
public class ImportFromHistoricalFileVo {

    /** 标准id */
    @NotNull(message = "标准ID不能为空")
    private Long standardId;

    /** 历史工程id */
    @NotNull(message = "历史工程ID不能为空")
    private Long historicalId;

    /** 工程分类编码 */
    @NotNull(message = "工程分类编码不能为空")
    private String categoryCode;

    /** 工程分类名称 */
    @NotNull(message = "工程分类名称不能为空")
    private String categoryName;

    /** 返回数据的结构（0：原始结构；1：树结构；默认值为 0 原始结构） */
    private Integer dataType;
}
