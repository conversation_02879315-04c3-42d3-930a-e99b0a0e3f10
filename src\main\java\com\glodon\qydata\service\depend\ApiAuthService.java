package com.glodon.qydata.service.depend;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.constant.DigitalCostConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.config.CommonConfig;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.util.EmptyUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @className: CloudService
 * @description: 授权中心服务层
 * @author: zhaoyj-g
 * @date: 2020/9/24
 **/
@Service("apiAuthService")
@Slf4j
@SuppressWarnings("squid:S1192") // 禁止sonar魔法值检测
public class ApiAuthService {
    @Autowired
    private ApiAuthFeignService apiAuthFeignService;
    @Autowired
    private CommonConfig commonConfig;

    /**
     * 根据用户企业id获取企业编码
     * @param enterpriseId
     * @return String
     * <AUTHOR>
     */
    public String getQyCodeByEnterpriseId(String enterpriseId) throws BusinessException {
        if (EmptyUtil.isEmpty(enterpriseId)) {
            return null;
        }
        try {
            JSONObject jsonObject = apiAuthFeignService.getQyDataByEnterpriseId(enterpriseId, commonConfig.appKey,commonConfig.gSignature);
            if (jsonObject == null || jsonObject.getIntValue(DigitalCostConstants.RESULT_KEY_CODE) != 0) {
                throw new BusinessException(ResponseCode.ERROR,"调用授权中心获取企业编码接口返回结果状态不正确");
            }
            if (jsonObject.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA) == null || jsonObject.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA).getJSONArray("customerChannelEntitys") == null) {
                throw new BusinessException(ResponseCode.ERROR,"调用授权中心获取企业编码接口返回结果为空");
            }
            JSONArray customerCodeJsonArr = jsonObject.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA).getJSONArray("customerChannelEntitys");
            JSONObject customerCodeJSON = customerCodeJsonArr.getJSONObject(0);
            return customerCodeJSON.getString("channelCustomerId");
        } catch (Exception e) {
            log.error("调用授权中心获取企业编码接口返回结果状态不正确，enterpriseId = {},错误信息为:{}",enterpriseId,e);
        }
        return null;
    }

    /**
     * 根据用户企业id获取企业名称
     * @param enterpriseId
     * @return String
     * <AUTHOR>
     */
    public String getCompanyNaByEnterpriseId(String enterpriseId) throws BusinessException {
        if (EmptyUtil.isEmpty(enterpriseId)) {
            return null;
        }
        try {
            JSONObject jsonObject = apiAuthFeignService.getQyDataByEnterpriseId(enterpriseId, commonConfig.appKey,commonConfig.gSignature);
            if (jsonObject == null || jsonObject.getIntValue(DigitalCostConstants.RESULT_KEY_CODE) != 0) {
                throw new BusinessException(ResponseCode.ERROR,"调用授权中心获取企业编码接口返回结果状态不正确");
            }
            if (jsonObject.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA) == null || jsonObject.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA).getJSONArray("customerChannelEntitys") == null) {
                throw new BusinessException(ResponseCode.ERROR,"调用授权中心获取企业编码接口返回结果为空");
            }
            return jsonObject.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA).getString("customerName");
        } catch (Exception e) {
            log.error("调用授权中心获取企业编码接口返回结果状态不正确，enterpriseId = {},错误信息为:{}",enterpriseId,e);
        }
        return null;
    }
    /**
     * 根据用户企业id获取权限集合
     * @param enterpriseGlobalId, gmsPids, memberGlobalId
     * @return String
     * <AUTHOR>
     */
    public JSONObject getRightsByEnterpriseId(String enterpriseGlobalId, String gmsPids, String memberGlobalId) throws BusinessException {
        if (EmptyUtil.isEmpty(enterpriseGlobalId)) {
            return new JSONObject();
        }
        try {
            JSONObject jsonObject = apiAuthFeignService.getRightsByEnterpriseId(enterpriseGlobalId, commonConfig.appKey,commonConfig.gSignature,gmsPids,memberGlobalId);
            if (jsonObject != null && jsonObject.getIntValue(DigitalCostConstants.RESULT_KEY_CODE) == 13001) { // 无授权成员
                log.warn("调用授权中心获取权限集合接口返回member_not_found,13001,enterpriseGlobalId = {},gmsPids = {},memberGlobalId = {}",enterpriseGlobalId,gmsPids,memberGlobalId);
                return new JSONObject();
            }
            if (jsonObject == null || jsonObject.getIntValue(DigitalCostConstants.RESULT_KEY_CODE) != 0) {
                throw new BusinessException(ResponseCode.ERROR,"调用授权中心获取权限集合接口返回结果状态不正确");
            }
            return jsonObject.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA);
        } catch (Exception e) {
            log.error("访问广联云授权接口 /api/public/v1/customer/{enterpriseGlobalId}/product/rights error,,userId={},gmsPids={},e={}",enterpriseGlobalId,gmsPids,e.getMessage());
        }
        return new JSONObject();
    }
}
