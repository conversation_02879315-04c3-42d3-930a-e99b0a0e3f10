package com.glodon.qydata.service.subjectdivision;

import com.glodon.qydata.config.feign.SubjectDivisionFeignConfiguration;
import com.glodon.qydata.entity.zbsq.ItemData;
import com.glodon.qydata.entity.zbsq.ZbsqCategory;
import com.glodon.qydata.vo.common.ResponseVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * glodon
 */
@FeignClient(name = "subject-division",url = "${config.depend.zbsqItemUrl}",configuration = SubjectDivisionFeignConfiguration.class)
public interface SubjectDivisionFeignService {

    @GetMapping("/openApi/getTemplateByCategory")
    ResponseVo<ItemData> getTemplateByCategory(@RequestParam String categoryCode, @RequestParam Integer releaseState);

    /**
     * 用于触发项目划分初始化
     */
    @GetMapping("/openApi/getAllCategory")
    ResponseVo<List<ZbsqCategory>> getAllCategory(@RequestParam Integer deleteFlag);
}
