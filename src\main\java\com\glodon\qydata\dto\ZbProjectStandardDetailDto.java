package com.glodon.qydata.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 　　* @description: 建造标准将业态与产品定位下的数据转化为树状结构的数据载体
 * 　　* <AUTHOR>
 * 　　* @date 2022/8/5 15:53
 */
@Data
public class ZbProjectStandardDetailDto implements Serializable {
    private static final long serialVersionUID = -632344920146493588L;
    /**
     * 对应细则id
     */
    private Long detailId;

    /**
     * 科目名称
     */
    private String name;

    /**
     * 层级编码
     */
    private String levelcode;

    /**
     * 业态编码
     */
    private String categoryCode;

    /**
     * 项目划分科目id
     */
    private String itemDivisionSubjectId;

    /**
     * 项目划分科目名称
     */
    private String itemDivisionSubjectName;

    /**
     * 标准说明
     */
    private List<StandardsBuildStandardPositionDescDto> description;
}
