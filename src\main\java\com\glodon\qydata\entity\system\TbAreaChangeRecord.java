package com.glodon.qydata.entity.system;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "tb_area_change_record")
@NoArgsConstructor
public class TbAreaChangeRecord implements Serializable {
    private Long id;
    private String user;
    private String param;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    public TbAreaChangeRecord(String user, String param) {
        this.user = user;
        this.param = param;
    }
}
