package com.glodon.qydata.interceptor;

import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.system.ISysRightInfoService;
import com.glodon.qydata.vo.system.RightInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.servlet.http.HttpServletRequest;

/**
 * @className: PermissionAspect
 * @description: 权限控制切面
 * @author: wangq-ao
 * @date: 2022/2/28
 **/
@Aspect
@Component
@Slf4j
public class PermissionAspect {

    @Autowired
    private ISysRightInfoService sysRightInfoService;

    @Around(value = "execution(* com.glodon..controller..*.*(..))")
    public Object invoke(ProceedingJoinPoint joinPoint) throws Throwable {
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        Permission permission = methodSignature.getMethod().getAnnotation(Permission.class);
        //接口上没有写permission注解，则直接放行
        if (null == permission) {
            return joinPoint.proceed();
        }

        //写了注解就去鉴权
        if (this.validatePermission()) {
            return joinPoint.proceed();
        } else {
            throw new BusinessException(ResponseCode.ERROR.getCode(), permission.message());
        }
    }
    private boolean validatePermission() {

        RequestAttributes ra = RequestContextHolder.getRequestAttributes();
        ServletRequestAttributes sra = (ServletRequestAttributes)ra;
        HttpServletRequest request = sra.getRequest();
        String globalId = request.getHeader("globalId");
        String sgToken = request.getHeader("sgToken");

        try {
            RightInfoVo rightInfo =  sysRightInfoService.getRightInfo(globalId, sgToken, true);
            Boolean canEdit = null;
            if (rightInfo!=null){
                canEdit = rightInfo.getCanEdit();
            }

            //有维护权限，直接返回
            return canEdit != null && canEdit;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("注解@Permission中，权限校验出错",e);
        }
        return false;
    }
}
