package com.glodon.qydata.config;

import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.ImportSelector;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotationMetadata;

/**
 * 自定义的ImportSelector，根据配置决定是否导入ShardingSphere的配置类
 *
 * <AUTHOR>
 */
public class CustomImportSelector implements ImportSelector {
    private final Environment environment;
    public CustomImportSelector(Environment environment) {
        this.environment = environment;
    }

    @SuppressWarnings("NullableProblems")
    @Override
    public String[] selectImports(AnnotationMetadata importingClassMetadata) {
        // 从配置类中获取配置值
        String privateDeployment = environment.getProperty("privateDeployment");
        // 私有化环境下，不导入ShardingSphere的配置类
        boolean notUseShardingSphere = Boolean.TRUE.toString().equalsIgnoreCase(privateDeployment);
        return notUseShardingSphere ? new String[]{} : new String[]{BasicShardingSphereAutoConfiguration.class.getName()};
    }
}