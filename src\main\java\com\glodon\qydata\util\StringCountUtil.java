package com.glodon.qydata.util;

public class StringCountUtil {

    public static void main(String[] args) {

        String str = "我爱你abcd123中国 #!";
        int count = getCount(str);
        System.out.println(count);
    }

    /**
     * 中文字符
     */
    private static  int chCharacter = 0;

    /**
     * 英文字符
     */
    private static int enCharacter = 0;

    /**
     * 空格
     */
    private static int spaceCharacter = 0;

    /**
     * 数字
     */
    private static int numberCharacter = 0;

    /**
     * 其他字符
     */
    private static int otherCharacter = 0;


    /***
     　　* 统计字符串中中文，英文，数字，空格等字符个数
     　　* @param str 需要统计的字符串
     　　*/
    public static Integer getCount(String str) {
        if (EmptyUtil.isEmpty(str)) {
            return 0;
        }
        int count = 0;
        for (int i = 0; i < str.length(); i++) {
            char tmp = str.charAt(i);
            if ((tmp >= 'A' && tmp <= 'Z') || (tmp >= 'a' && tmp <= 'z')) {
                enCharacter++;
                count++;
            } else if ((tmp >= '0') && (tmp <= '9')) {
                numberCharacter++;
                count++;
            } else if (tmp == ' ') {
                spaceCharacter++;
                count++;
            } else if (isChinese(tmp)) {
                chCharacter++;
                count = count + 2;
            } else {
                otherCharacter++;
                count++;
            }
        }
        System.out.println("总数为：" + count+"  中文字符有:" + chCharacter + "个"+"  英文字符有:" + enCharacter + "个"
        +"  数字有:" + numberCharacter + "个"+"  空格有:" + spaceCharacter + "个"+"  其他字符有:" + otherCharacter + "个");
        return count;
    }

    /***
     　　　　* 判断字符是否为中文
     　　　　* @param ch 需要判断的字符
     　　　　* @return 中文返回true，非中文返回false
     　　　　*/
    private static boolean isChinese(char ch) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(ch);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION;

    }
}