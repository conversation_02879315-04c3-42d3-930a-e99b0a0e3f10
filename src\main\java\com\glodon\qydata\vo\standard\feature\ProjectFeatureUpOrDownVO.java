package com.glodon.qydata.vo.standard.feature;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;


/**
 * @description: 工程特征上移下移参数载体
 * <AUTHOR>
 * @date 2021/10/22 14:53
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectFeatureUpOrDownVO implements Serializable {

    private static final long serialVersionUID = 8607558105213154168L;

    /**
     * 工程专业id
     */
    @NotNull(message = "工程专业id不能为空")
    private Long tradeId;

    /**
     * 工程特征id
     */
    @NotNull(message = "工程特征id不能为空")
    private Long featureId;

    /**
     * 操作类型
     */
    @NotEmpty(message = "操作类型不能为空")
    private String operate;

    /**
     * 视图类型
     */
    @NotNull(message = "视图类型不能为空")
    private Integer viewType;

    /**
     * 分类编码
     */
    private String categoryCode;

    /**
     * 数据分类
     */
    private Integer type;

}
