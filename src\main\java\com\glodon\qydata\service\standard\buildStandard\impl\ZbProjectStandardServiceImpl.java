package com.glodon.qydata.service.standard.buildStandard.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageInfo;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.DigitalCostConstants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.dto.*;
import com.glodon.qydata.entity.standard.buildStandard.*;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.entity.zbsq.Item;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.buildStandard.*;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.cloud.MiddleGroundFeignService;
import com.glodon.qydata.service.cloud.middleGround.BuildStandard;
import com.glodon.qydata.service.cloud.middleGround.HistoricalBuildStandard;
import com.glodon.qydata.service.init.enterprise.InitBuildStandardService;
import com.glodon.qydata.service.init.self.InitBuildStandardDetailSelfService;
import com.glodon.qydata.service.init.self.InitBuildStandardSelfService;
import com.glodon.qydata.service.standard.buildStandard.*;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsProjectInfoService;
import com.glodon.qydata.service.subjectdivision.SubjectDivisionService;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.util.EmptyUtil;
import com.glodon.qydata.util.OrderPageUtils;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.util.tree.TreeNode;
import com.glodon.qydata.util.tree.TreeNodeHandler;
import com.glodon.qydata.util.tree.TreeUtils;
import com.glodon.qydata.vo.common.MiddleGroundPage;
import com.glodon.qydata.vo.standard.buildStandard.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.EncryptedDocumentException;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.glodon.qydata.common.constant.Constants.BuildStandardConstants.*;
import static com.glodon.qydata.common.constant.Constants.CategoryConstants.SINGLE_LEVEL_LEN;
import static com.glodon.qydata.common.constant.Constants.ZbExpressionConstants.OPERATE_ENABLE;

/**
　　* @description: 建造标准服务实现类
　　* <AUTHOR>
　　* @date 2021/8/16 17:49
　　*/
@Service
@Slf4j
public class ZbProjectStandardServiceImpl extends ServiceImpl<ZbProjectStandardMapper, ZbProjectStandard> implements IZbProjectStandardService {

    @Autowired
    private ZbProjectStandardMapper zbProjectStandardMapper;
    @Autowired
    private ZbProjectStandardDetailMapper zbProjectStandardDetailMapper;
    @Autowired
    private CommonProjCategoryService commonProjCategoryService;
    @Autowired
    private ZbStandardsTradeMapper zbStandardsTradeMapper;
    @Autowired
    private InitBuildStandardService initBuildStandardService;
    @Autowired
    private InitBuildStandardSelfService initBuildStandardSelfService;
    @Autowired
    private InitBuildStandardDetailSelfService initBuildStandardDetailService;
    @Autowired
    private ZbStandardsBuildStandardDetailDescMapper zbStandardsBuildStandardDetailDescMapper;
    @Autowired
    private ZbStandardsBuildPositionMapper zbStandardsBuildPositionMapper;
    @Autowired
    private ZbStandardsBuildPositionDetailMapper zbStandardsBuildPositionDetailMapper;
    @Autowired
    private IStandardsProjectInfoService standardsProjectInfoService;
    @Autowired
    private ZbStandardsBuildStandardDetailDescService standardsBuildStandardDetailDescService;
    @Autowired
    private ZbStandardsBuildPositionService standardsBuildPositionService;
    @Autowired
    private ZbStandardsBuildCategoryService standardsBuildCategoryService;
    @Autowired
    private ZbStandardsBuildPositionDetailService standardsBuildPositionDetailService;
    @Autowired
    private MiddleGroundFeignService middleGroundFeignService;
    @Autowired
    private SubjectDivisionService subjectDivisionService;
    @Resource
    private PublishInfoService publishInfoServiceImpl;

    final ThreadPoolExecutor dataExecutor = new ThreadPoolExecutor(5, 10, 60, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());


    /**
    　　* @description: 同一个企业下，标准名称唯一
     id为空，企业下同名称记录存在  则重复名称
                          不存在  则正常
     id有值 企业下同名称记录存在  id相同  则正常
                                id不相同  则重复名称
            企业下同名称记录不存在  则正常
    　　* @param  name:要判断的标准名称 id：修改接口下的编辑标准id
    　　* @return 是否存在，true存在，false不存在
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/8/16 17:50
    　　*/
    @Override
    public Boolean checkedName(String name, Long id) {
        String customerCode = RequestContent.getCustomerCode();

        if (this.checkedNameBySystem(name, id)){
            return true;
        }

        ZbProjectStandard zbProjectStandard = zbProjectStandardMapper.searchSelfRecordsByCusCodeAndName(customerCode, name);

        if(EmptyUtil.isEmpty(zbProjectStandard)){
            return false;
        }
        return EmptyUtil.isEmpty(id) || zbProjectStandard.getId().compareTo(id) != 0;
    }

    /**
     * @description: 和系统内置的标准进行名称判重
     * @param name
     * @param id
     * @return boolean 是否存在，true存在，false不存在
     * <AUTHOR>
     * @date 2022/8/9 17:01
     */
    private boolean checkedNameBySystem(String name, Long id) {
        if (StringUtils.isEmpty(name)){
            return false;
        }

        List<ZbProjectStandard> systemProjectStandards = zbProjectStandardMapper
                .selectByCustomerCode(Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE, Constants.CategoryConstants.WHETHER_FALSE);

        if (CollectionUtils.isEmpty(systemProjectStandards)){
            return false;
        }

        return systemProjectStandards.parallelStream().anyMatch(x -> !x.getId().equals(id) && name.equals(x.getName()));
    }

    /**
     　　* @description: 同一个企业下，标准业态唯一
     id为空，企业下同业态编码记录存在  则重复
                            不存在  则正常
     id有值 企业下同业态编码记录存在  id相同  则正常
                                   id不相同  则重复
         企业下同业态编码记录不存在  则正常
     　　* @param  categoryCode:要判断的标准业态编码 id：修改接口下的编辑标准id
     　　* @return 是否存在，true存在，false不存在
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/8/16 17:50
     　　*/
    private Boolean checkedCategoryCode(String categoryCode, Long id) {
        String customerCode  = RequestContent.getCustomerCode();
        ZbProjectStandard zbProjectStandard = zbProjectStandardMapper.searchRecordsByCusCodeAndCateCode(customerCode, categoryCode);
        if(EmptyUtil.isEmpty(zbProjectStandard)){
            return false;
        }
        return EmptyUtil.isEmpty(id) || zbProjectStandard.getId().compareTo(id) != 0;
    }

    /**
     * @description: 只校验一级分类是否存在，不校验是否重复
     * @param categoryCode
     * @return java.lang.Boolean
     * <AUTHOR>
     * @date 2022/8/2 17:43
     */
    private Boolean checkedCategoryCodeExist(String categoryCode, String customerCode) {
        return commonProjCategoryService.getCategoryByCode(categoryCode, customerCode) != null;
    }

    /**
     　　* @description: 新增建造标准
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/8/17 8:47
     　　*/
    @Override
    @Transactional
    public ZbProjectStandard addStandardRecord(ZbProjectStandardBo zbProjectStandardBo){
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();

        // 参数校验
        this.addParamValidated(zbProjectStandardBo);
        this.categoryValidated(zbProjectStandardBo, customerCode);

        // 实体赋值，入库
        Long newStandardId = SnowflakeIdUtils.getNextId();
        ZbProjectStandard zbProjectStandard = this.buildZbProjectStandard(newStandardId, zbProjectStandardBo, customerCode, globalId);
        zbProjectStandardMapper.selfInsertSelective(zbProjectStandard);

        // 是否引用模板
        this.referenceTemplate(newStandardId, zbProjectStandardBo.getSrcStandardId(), globalId);

        return this.returnNewDBData(zbProjectStandard.getId(), customerCode);
    }

    /**
     * @description: 引用模板
     * a、引用下拉列中显示的模板为已启用并且已发布到企业标准的建造标准模板；
     * b、引用后新增的建造标准默认为未启用和未发布状态；
     * c、新增的模板在点击发布之前，不能启用，点击发布时，新增的模板自动变为启用状态；
     * @param newStandardId
     * @param srcStandardId
     * @return void
     * <AUTHOR>
     * @date 2022/8/3 11:24
     */
    private void referenceTemplate(Long newStandardId, Long srcStandardId, String globalId){
        if (srcStandardId == null){
            return;
        }

        // 查询被引用的建造标准 zb_standards_build_standard
        ZbProjectStandard srcStandard = zbProjectStandardMapper.selectStandardByStandardId(srcStandardId);

        if (srcStandard == null){
            throw new BusinessException("引用的模板不存在!");
        }

//        if(srcStandard.getIsUsing() == null || Constants.ZbFeatureConstants.WHETHER_TRUE != srcStandard.getIsUsing()){
//            throw new BusinessException("引用的模板未启用!");
//        }

        // copy被引用建造标准的细则 并返回新旧standardDetailId的对应关系
        HashMap<Long, Long> oldAndNewDetailId = this.copyStandardDetail(newStandardId, srcStandardId, globalId);

        // copy被引用建造标准的标准说明
        this.copyDetailDesc(newStandardId, srcStandardId, oldAndNewDetailId,Long.valueOf(globalId));

    }

    /**
     * @description: copy被引用建造标准的细则 并返回新旧standardDetailId的对应关系
     * @param newStandardId
     * @param srcStandardId
     * @param globalId
     * @return java.util.HashMap<java.lang.Long, java.lang.Long>
     * <AUTHOR>
     * @date 2022/8/3 17:31
     */
    private HashMap<Long, Long> copyStandardDetail(Long newStandardId, Long srcStandardId, String globalId){
        // 查询 zb_standards_build_standard_detail
        List<ZbProjectStandardDetail> standardDetailsList = zbProjectStandardDetailMapper.selectByStandardId(srcStandardId);

        if (CollectionUtils.isEmpty(standardDetailsList)){
            return null;
        }

        // 新旧standardDetailId的对应关系
        HashMap<Long, Long> oldAndNewId = new HashMap<>(standardDetailsList.size());

        // 复制
        for (ZbProjectStandardDetail standardDetail : standardDetailsList) {
            Long newStandardDetailId = SnowflakeIdUtils.getNextId();
            oldAndNewId.put(standardDetail.getId(), newStandardDetailId);

            standardDetail.setId(newStandardDetailId);
            standardDetail.setStandardId(newStandardId);
            standardDetail.setCreateTime(null);
            standardDetail.setUpdateTime(null);
            standardDetail.setOriginId(null);
        }

        // 入库zb_standards_build_standard_detail_self
        zbProjectStandardDetailMapper.saveSelfBatch(standardDetailsList);

        return oldAndNewId;
    }

    /**
     * @description: copy被引用建造标准的标准说明
     * @param newStandardId
     * @param srcStandardId
     * @param oldAndNewDetailId
     * @return void
     * <AUTHOR>
     * @date 2022/8/3 17:31
     */
    private void copyDetailDesc(Long newStandardId, Long srcStandardId, HashMap<Long, Long> oldAndNewDetailId, Long globalId){
        if (oldAndNewDetailId == null){
            return;
        }

        // 查询 zb_standards_build_standard_detail_desc
        List<ZbStandardsBuildStandardDetailDesc> descList = zbStandardsBuildStandardDetailDescMapper.selectByStandardId(srcStandardId);

        if (CollectionUtils.isEmpty(descList)){
            return;
        }

        // 复制
        List<ZbStandardsBuildStandardDetailDesc> insertList = new ArrayList<>();

        for (ZbStandardsBuildStandardDetailDesc desc : descList) {
            if(!oldAndNewDetailId.containsKey(desc.getStandardDetailId())){
                continue;
            }
            desc.setId(SnowflakeIdUtils.getNextId());
            desc.setStandardId(newStandardId);
            desc.setStandardDetailId(oldAndNewDetailId.get(desc.getStandardDetailId()));
            desc.setOriginId(null);
            desc.setCreateTime(null);
            desc.setUpdateTime(null);

            insertList.add(desc);
        }

        // 入库 zb_standards_build_standard_detail_desc_self
        if (CollectionUtils.isNotEmpty(insertList)){
            zbStandardsBuildStandardDetailDescMapper.batchSaveSelf(insertList);
        }
    }

    /**
     * @description: 建造标准最新数据库数据
     * @param standardId
     * @param customerCode
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard
     * <AUTHOR>
     * @date 2022/8/3 10:46
     */
    private ZbProjectStandard returnNewDBData(Long standardId, String customerCode){
        ZbProjectStandard standard = zbProjectStandardMapper.selectSelfStandardById(standardId);
        this.setCategoryName(customerCode, standard.getCategoryCode(), standard);
        return standard;
    }

    /**
     * @description: 实体赋值
     * @param newStandardId
     * @param zbProjectStandardBo
     * @param customerCode
     * @param globalId
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard
     * <AUTHOR>
     * @date 2022/8/3 10:36
     */
    private ZbProjectStandard buildZbProjectStandard(Long newStandardId, ZbProjectStandardBo zbProjectStandardBo, String customerCode, String globalId){
        ZbProjectStandard zbProjectStandard = new ZbProjectStandard();

        zbProjectStandard.setCategoryCode(zbProjectStandardBo.getCategoryCode());
        this.setCategoryName(customerCode, zbProjectStandardBo.getCategoryCode(), zbProjectStandard);
        zbProjectStandard.setGlobalId(Long.parseLong(globalId));
        zbProjectStandard.setId(newStandardId);
        zbProjectStandard.setName(zbProjectStandardBo.getName());
        zbProjectStandard.setIsExample(0);
        zbProjectStandard.setIsDeleted(false);
        zbProjectStandard.setCategoryDeleted(false);
        zbProjectStandard.setIsUsed(false);
        zbProjectStandard.setIsUsing(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbProjectStandard.setCustomerCode(customerCode);
        zbProjectStandard.setSrcStandardId(zbProjectStandardBo.getSrcStandardId());

        return zbProjectStandard;
    }

    /**
     * @description: 新增建造标准-参数校验
     * @param zbProjectStandardBo
     * @return void
     * <AUTHOR>
     * @date 2022/8/2 17:59
     */
    private void addParamValidated(ZbProjectStandardBo zbProjectStandardBo){
        Boolean doubleName = this.checkedName(zbProjectStandardBo.getName(), zbProjectStandardBo.getId());
        if(Boolean.TRUE.equals(doubleName)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "重复名称，请核对");
        }

        if (zbProjectStandardBo.getName().length() > 30) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "名称长度不能超过30");
        }
    }

    private void categoryValidated(ZbProjectStandardBo zbProjectStandardBo, String customerCode){
        Boolean existCategoryCode = this.checkedCategoryCodeExist(zbProjectStandardBo.getCategoryCode(), customerCode);
        if(Boolean.FALSE.equals(existCategoryCode)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "工程分类不存在，请核对");
        }
    }

    /**
     * 新增或修改后的标准与已删除标准同名时，新增或修改后的标准id与与删除标准id一致并物理删除已删除标准
     * @throws
     * @param name
     * @param customerCode
     * @param zbProjectStandard
     * <AUTHOR>
     * @return
     * @date 2021/10/25 15:49
     */
    public void checkDelSameNameStandard(String name, String customerCode, ZbProjectStandard zbProjectStandard) {
        ZbProjectStandard delSameName = zbProjectStandardMapper.selectDeletedSameNameStandard(name, customerCode);
        if (delSameName != null) {
            zbProjectStandard.setId(delSameName.getId());
            zbProjectStandardMapper.deleteStandardById(delSameName.getId());
        }
    }

    /**
     　　* @description: 更新建造标准
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/8/17 8:47
     　　*/
    @Override
    @Transactional
    public ZbProjectStandard updateStandardRecord(ZbProjectStandardBo zbProjectStandardBo){
        String customerCode = RequestContent.getCustomerCode();
        if(EmptyUtil.isEmpty(zbProjectStandardBo.getId())){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "标准id为空，请核对");
        }
        List<String> operateTypeList = Arrays.asList(OPERATE_TYPE_RENAME, OPERATE_TYPE_SET_USING, OPERATE_TYPE_SET_USING_WITH_CHECK);
        String operateType = zbProjectStandardBo.getOperateType();
        if(StringUtils.isEmpty(operateType) || !operateTypeList.contains(operateType)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "更新的操作类型不正确，请核对");
        }
        // 构建更新对象
        ZbProjectStandard zbProjectStandard = new ZbProjectStandard();
        zbProjectStandard.setId(zbProjectStandardBo.getId());
        if (OPERATE_TYPE_RENAME.equals(operateType)){
            // 校验名称
            this.addParamValidated(zbProjectStandardBo);
            zbProjectStandard.setName(zbProjectStandardBo.getName());
        }else if (OPERATE_TYPE_SET_USING.equals(operateType) || OPERATE_TYPE_SET_USING_WITH_CHECK.equals(operateType)){
            if (Objects.equals(OPERATE_ENABLE, zbProjectStandardBo.getIsUsing())){
                // 校验工程分类和定位在字典中是否有删除的
                checkCategoryAndPosition(zbProjectStandardBo);
                // 校验是否存在已启用的同业态的其他标准
                List<ZbProjectStandard> enabledByCategoryCode = zbProjectStandardMapper.selectSelfEnabledByCategoryCode(customerCode, zbProjectStandardBo.getCategoryCode());
                boolean isExistOtherStandard = enabledByCategoryCode.stream().anyMatch(x -> !Objects.equals(x.getId(), zbProjectStandardBo.getId()));
                if (isExistOtherStandard) {
                    if (OPERATE_TYPE_SET_USING_WITH_CHECK.equals(operateType)){
                        throw new BusinessException(ResponseCode.STANDARD_ENABLE_CHECK_EXIST);
                    }else {
                        zbProjectStandardMapper.updateSelfDisableByCategoryCode(customerCode, zbProjectStandardBo.getCategoryCode());
                    }
                }
            }
            zbProjectStandard.setIsUsing(zbProjectStandardBo.getIsUsing());
        }

        zbProjectStandardMapper.updateSelfStandardById(zbProjectStandard);

        return this.returnNewDBData(zbProjectStandard.getId(), customerCode);
    }

    /**
     * /校验工程分类和定位在字典中是否有删除的
     * @param zbProjectStandardBo
     */
    private void checkCategoryAndPosition(ZbProjectStandardBo zbProjectStandardBo) {
        Integer isUsing = zbProjectStandardBo.getIsUsing();
        if(OPERATE_ENABLE != isUsing){
            return;
        }
        List<Long> standIds = Lists.newArrayList(zbProjectStandardBo.getId());
        //查询建造标准下所有的定位信息
        List<ZbStandardsBuildPosition> buildPositions = standardsBuildPositionService.selectSelfByStandardIds(standIds);
        if(CollectionUtils.isEmpty(buildPositions)){
            return;
        }
        //检查是否存在数据字典已经删除的产品定位
        checkDelPosition(buildPositions);
        //查询建造标准下所有的业态信息
        List<ZbStandardsBuildCategory> buildCategories = standardsBuildCategoryService.selectSelfByStandardIds(standIds);
        if(CollectionUtils.isEmpty(buildCategories)){
            return;
        }
        //校验是否有数据字典中已经删除的工程分类
        checkDelCategory(buildCategories);
    }

    /**
     * 建造标准关联的业态名称赋值
     * @throws
     * @param customerCode
     * @param categoryCode
     * @param zbProjectStandard
     * <AUTHOR>
     * @return
     * @date 2022/2/8 14:25
     */
    private void setCategoryName(String customerCode, String categoryCode, ZbProjectStandard zbProjectStandard) {
        // 业态名称修改
        String pathName = this.getCategoryNameByCode(categoryCode, customerCode);
        zbProjectStandard.setCategoryDeleted(pathName == null);
        zbProjectStandard.setCategoryName(pathName);
    }


    /**
     　　* @description: 根据标准id,删除对应标准记录，物理删除
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/8/17 15:01
     　　*/
    @Override
    public void deleteStandardRecord(Long buildStandardId) throws BusinessException{

        if(EmptyUtil.isEmpty(buildStandardId)){
            return;
        }
        ZbProjectStandard zbProjectStandard = zbProjectStandardMapper.selectSelfStandardById(buildStandardId);
        if(EmptyUtil.isEmpty(zbProjectStandard)){
            return;
        }
        zbProjectStandard.setIsDeleted(true);
        zbProjectStandardMapper.updateSelfStandardById(zbProjectStandard);
    }

    @Override
    @Transactional
    public void deleteStandardDetailAndDesc(ZbProjectStandardDeleteVo deleteVo) {
        // 标准id
        Long standardId = deleteVo.getStandardId();
        String globalId = RequestContent.getGlobalId();

        // 删除细则
        this.deleteStandardDetailCol(standardId, globalId, deleteVo.getStandardDetailIds());

        // 删除标准说明
        this.deleteDesc(deleteVo.getStandardDetailDescIds());
    }

    /**
     * @description: 删除细则
     * @param standardId
     * @param globalId
     * @param standardDetailIds
     * @return void
     * <AUTHOR>
     * @date 2022/8/5 11:06
     */
    private boolean deleteStandardDetailCol(Long standardId, String globalId, List<Long> standardDetailIds){
        if (CollectionUtils.isEmpty(standardDetailIds)){
            return false;
        }

        //所有行数据
        List<ZbProjectStandardDetail> all = zbProjectStandardDetailMapper.selectSelfByStandardId(standardId);

        // 删除
        zbProjectStandardDetailMapper.deleteSelfByIds(standardDetailIds);

        // 删除细则后剩余细则重排序
        reOrderAfterDelete(standardDetailIds, all);

        return true;
    }

    /**
     * @description: 刪除标准说明
     * @param standardDetailDescIds
     * @return boolean
     * <AUTHOR>
     * @date 2022/8/5 11:22
     */
    private boolean deleteDesc(List<Long> standardDetailDescIds){
        if (CollectionUtils.isEmpty(standardDetailDescIds)){
            return false;
        }

        // 刪除zb_standards_build_standard_detail_desc_self
        zbStandardsBuildStandardDetailDescMapper.batchDelSelfById(standardDetailDescIds);
        // 删除zb_standards_build_position_detail_self
        zbStandardsBuildPositionDetailMapper.batchDelSelfByDescId(standardDetailDescIds);

        return true;
    }

    /**
     * @description: 对应标准标记为已被编辑;修改项目标准关系表中updateFlag字段
     * @param standardId
     * @return void
     * <AUTHOR>
     * @date 2022/8/5 11:25
     */
    private void updateStandardFlag(Long standardId){
        // 对应标准标记为已被编辑
        zbProjectStandardMapper.updateIsUpdated(standardId);
    }

    /**
     * 删除细则后剩余细则重排序
     * @throws
     * @param toDelList
     * @param all
     * <AUTHOR>
     * @return
     * @date 2021/11/11 16:15
     */
    public void reOrderAfterDelete(List<Long> toDelList, List<ZbProjectStandardDetail> all) {
        List<ZbProjectStandardDetail> updateOrdList = new ArrayList<>();
        int reduction = 0;
        for (ZbProjectStandardDetail detail : all) {
            if (toDelList.contains(detail.getId())) {
                reduction ++;
                continue;
            }
            if (reduction > 0) {
                ZbProjectStandardDetail updateOrdDetail = new ZbProjectStandardDetail();
                updateOrdDetail.setOrd(detail.getOrd() - reduction);
                updateOrdDetail.setId(detail.getId());
                updateOrdList.add(updateOrdDetail);
            }
        }
        if (updateOrdList.size() > 0) {
            zbProjectStandardDetailMapper.updateOrdByList(updateOrdList);
        }
    }

    @Override
    public ZbProjectStandardDetail updateStandardDetailCol(Long standardId, ZbProjectStandardDetail detail) throws BusinessException {

        if (checkLength(detail.getName(), detail.getDesc())) {
            throw new BusinessException(500, "参数长度异常！");
        }

        if (!checkSubjectDivisionRepeat(standardId, detail)) {
            throw new BusinessException(500, "已有建造标准关联的相同项目划分科目，请调整后再试");
        }

        if (detail.getTradeId() != null) {
            //专业名称复制
            ZbStandardsTrade zbStandardsTrade = Optional.ofNullable(zbStandardsTradeMapper.selectById(detail.getTradeId())).orElse(new ZbStandardsTrade());
            detail.setTradeName(zbStandardsTrade.getDescription());
        }

        List<ZbProjectStandardDetail> sons = zbProjectStandardDetailMapper.selectSons(standardId, detail.getLevelcode());
        zbProjectStandardDetailMapper.selectById(detail.getId());

        if (sons.size() > 0) {
            //有子项则描述示例置空
            detail.setDesc(null);
                //专业工程改变则子专业工程也同步修改
            zbProjectStandardDetailMapper.updateSonsTrade(detail, standardId);
        }

        zbProjectStandardDetailMapper.updateSelfStandardDetailColData(standardId, detail);

        // 对应标准标记为已被编辑
        zbProjectStandardMapper.updateIsUpdated(standardId);

        // 修改项目标准关系表中updateFlag字段
        ZbProjectStandard standard = zbProjectStandardMapper.selectSelfStandardById(standardId);
        if(standard == null){
            return null;
        }
        return zbProjectStandardDetailMapper.selectById(detail.getId());
    }

    private boolean checkSubjectDivisionRepeat(Long standardId, ZbProjectStandardDetail detail) {
        if (StringUtils.isBlank(detail.getItemDivisionSubjectId())) {
            return true;
        }
        ZbProjectStandardDetail zbProjectStandardDetail
                = zbProjectStandardDetailMapper.selectSubjectDivision(standardId, detail.getItemDivisionSubjectId(), detail.getId());
        return ObjectUtil.isNull(zbProjectStandardDetail);
    }

    @Override
    public boolean checkLength(String name, String desc) {
        //名称长度小于等于500，描述长度小于等于200
        boolean nameBool = name.length() > 500;
        if (desc != null) {
            boolean descBool = desc.length() > 200;
            return nameBool || descBool;
        }
        return nameBool;
    }

    @Override
    public boolean checkDetailName(Long standardId, ZbProjectStandardDetailColDto dto) {
        String globalId = RequestContent.getGlobalId();
        List<ZbProjectStandardDetail> details = zbProjectStandardDetailMapper.selectSelfByStandardId(standardId);
        if (details == null || details.size() == 0) {
            return false;
        }
        Optional<ZbProjectStandardDetail> refOptional = details.parallelStream().filter(detail -> detail.getId().equals(dto.getRefId())).findFirst();
        ZbProjectStandardDetail ref = null;
        if (refOptional.isPresent()) {
            ref = refOptional.get();
        } else {
            if (Constants.BROTHER.equals(dto.getType())) {
                Optional<ZbProjectStandardDetail> optional = details.stream().filter(i -> Integer.valueOf(1).equals(i.getLevel())).filter(i -> Objects.nonNull(i.getName()) && i.getName().equals(dto.getName())).findFirst();
                return optional.isPresent();
            } else {
                throw new BusinessException(500,"请先选择引用的标准");
            }
        }
        String refLevelCode = ref.getLevelcode();
        String fatherLevelCode;
        if (Constants.BROTHER.equals(dto.getType())) {
            fatherLevelCode = refLevelCode.substring(0 ,refLevelCode.length() - 3);
            if (fatherLevelCode.length() == 0) {
                //为一级行
                List<String> matchName = details.parallelStream().filter(detail -> detail.getLevel() == 1).map(ZbProjectStandardDetail::getName).filter(name -> name.equals(dto.getName())).collect(Collectors.toList());
                return matchName.size() > 0;
            }
        } else {
            fatherLevelCode = refLevelCode;
        }

        List<String> matchName = details.parallelStream().filter(detail -> detail.getLevelcode().startsWith(fatherLevelCode)
                && !detail.getLevelcode().equals(fatherLevelCode) && detail.getLevel().equals(dto.getLevel())).map(ZbProjectStandardDetail::getName)
                .filter(name -> name.equals(dto.getName())).collect(Collectors.toList());
        return matchName.size() > 0;
    }

    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<ZbProjectStandard> standardList(String customerCode, Integer isShowDelete, Integer createTimeAscOrDesc, boolean isSetCategoryAndPosition) throws BusinessException {
        initBuildStandardService.initData(customerCode);
        List<ZbProjectStandard> returnList = zbProjectStandardMapper.selectByCustomerCodeWithOrder(customerCode, 1, createTimeAscOrDesc);

        Map<Long,ZbProjectStandard> map = returnList.stream().collect(Collectors.toMap(ZbProjectStandard::getId, x->x));

        for (ZbProjectStandard zbProjectStandard : returnList) {
            String categoryCode = zbProjectStandard.getCategoryCode();
            setCategoryName(customerCode, categoryCode, zbProjectStandard);
            Long srcStandardId = zbProjectStandard.getSrcStandardId();
            if(srcStandardId == null){
                continue;
            }
            ZbProjectStandard srcProjectStandard = map.get(srcStandardId);
            if(srcProjectStandard == null){
                continue;
            }
            zbProjectStandard.setSrcStandardName(srcProjectStandard.getName());
        }
        //默认不展示已删除，isShowDelete默认为0
        if(!Objects.equals(isShowDelete, 1)){
            returnList = returnList.stream().filter(x->!x.getIsDeleted()).collect(Collectors.toList());
        }

        //查询产品定位列表
        standardsBuildPositionService.setCategoryAndPositions(returnList, customerCode, isSetCategoryAndPosition);
        return returnList;
    }

    /**
    　　* @description: 末级编码获取名称路径
    　　* @param categoryCode:业态编码 categoryMap：封住业态编码与实体的map
    　　* @return 业态全路径名称
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/9/17 18:21
    　　*/
    @Override
    public String getCategoryNameByCode(String categoryCode, String customerCode){
        Map<String, CommonProjCategory> categoryMap = commonProjCategoryService.getCommonProjCategoryMap(customerCode);

        if (categoryMap.containsKey(categoryCode)) {
            if(categoryCode.length() > 6){
                String parentCodeFirst = categoryCode.substring(0,3);
                String parentCodeSecond = categoryCode.substring(0,6);
                if(categoryMap.get(parentCodeFirst) == null){
                    return "";
                }
                return categoryMap.get(parentCodeFirst).getCategoryname() + "/" +
                        categoryMap.get(parentCodeSecond).getCategoryname() + "/" +
                        categoryMap.get(categoryCode).getCategoryname();
            }else if(categoryCode.length() > 3){
                String parentCodeFirst = categoryCode.substring(0,3);
                if(categoryMap.get(parentCodeFirst) == null){
                    return "";
                }
                String parentCodeFirstName = categoryMap.get(parentCodeFirst).getCategoryname();
                String parentCodeSecondName = categoryMap.get(categoryCode).getCategoryname();
                return parentCodeFirstName + "/" + parentCodeSecondName;
            }
            if(categoryMap.get(categoryCode) == null){
                return "";
            }
            return categoryMap.get(categoryCode).getCategoryname();
        }
        return null;
    }

    @Override
    public ZbProjectStandardDetail createStandardDetailCol(Long standardId, ZbProjectStandardDetailColDto dto) throws BusinessException {

        String desc = dto.getDesc();
        String name = dto.getName();

        ZbProjectStandardDetail detail = new ZbProjectStandardDetail();
        detail.setId(SnowflakeIdUtils.getNextId());
        detail.setDesc(desc);
        detail.setLevel(dto.getLevel());
        detail.setName(name);
        detail.setRemark(dto.getRemark());
        detail.setStandardId(standardId);
        detail.setTradeId(dto.getTradeId());
        if (dto.getTradeId() != null) {
            //专业名称复制
            ZbStandardsTrade zbStandardsTrade = Optional.ofNullable(zbStandardsTradeMapper.selectById(dto.getTradeId())).orElse(new ZbStandardsTrade());
            detail.setTradeName(zbStandardsTrade.getDescription());
        }

        ZbProjectStandard standard = zbProjectStandardMapper.selectSelfStandardById(standardId);
        if (standard == null) {
            return null;
        }

        //待插入行的兄弟节点
        List<ZbProjectStandardDetail> brothers;
        //待插入行的父节点
        String fatherLevelCode;
        Long refId = dto.getRefId();
        if (refId == null) {
            List<ZbProjectStandardDetail> details = zbProjectStandardDetailMapper.selectSelfByStandardId(standardId);
            OptionalInt num = details.stream().filter(i -> Integer.valueOf(1).equals(i.getLevel())).map(i -> {
                try {
                    return Integer.parseInt(i.getLevelcode());
                } catch (Exception e) {
                    return 0;
                }
            }).mapToInt(i -> i).max();
            int number = 0;
            if (num.isPresent()) {
                number = num.getAsInt();
            }
            //没有参照行说明插入的是该标准下的第一条细则
            detail.setLevelcode(String.format("%03d",number+1));
            detail.setOrd(number+1);
            zbProjectStandardDetailMapper.insert(detail);
            // 对应标准标记为已被编辑
            zbProjectStandardMapper.updateIsUpdated(detail.getStandardId());
            return detail;
        }
        //获取参照行
        ZbProjectStandardDetail ref = zbProjectStandardDetailMapper.selectById(refId);

        String refLevelCode = ref.getLevelcode();
        int ord;
        //获取参照行的所有下属节点行
        List<ZbProjectStandardDetail> allSons = zbProjectStandardDetailMapper.selectAllSons(standardId, ref.getLevelcode());
        if (allSons.size() == 0) {
            ord = ref.getOrd() + 1;
        } else {
            Integer maxOrdOnePlus = allSons.get(allSons.size() - 1).getOrd() + 1;
            detail.setOrd(maxOrdOnePlus);
            ord = maxOrdOnePlus;
        }
        if (Constants.BROTHER.equals(dto.getType())) {
            fatherLevelCode = refLevelCode.substring(0 ,refLevelCode.length() - 3);
            detail.setOrd(ord);
        } else {
            fatherLevelCode = refLevelCode;
        }

        //清空父节点的描述示例
        zbProjectStandardDetailMapper.updateDescBlankByLevelCodeAndStandardId(standardId, fatherLevelCode);

        //获取所有兄弟节点
        brothers = zbProjectStandardDetailMapper.selectBrothersById(fatherLevelCode, standardId);
        if (brothers.size() > 0) {
            //根据levelCode升序排列
            List<ZbProjectStandardDetail> levelCodeSorted = brothers.parallelStream().sorted(Comparator.comparing(ZbProjectStandardDetail::getLevelcode)).collect(Collectors.toList());
            //取最大的levelCode
            String maxLevelCode = levelCodeSorted.get(levelCodeSorted.size() - 1).getLevelcode();

            int length = maxLevelCode.length();
            String lastThreeCode = maxLevelCode.substring(length - 3, length);
            int intCode = Integer.parseInt(lastThreeCode);

            String levelCode;
            if (intCode < 9) {
                levelCode = "00" + (intCode + 1);
            } else if (intCode < 99) {
                levelCode = "0" + (intCode + 1);
            } else {
                levelCode = "" + (intCode + 1);
            }

            levelCode = maxLevelCode.substring(0, length - 3) + levelCode;
            detail.setLevelcode(levelCode);
        } else {
            //无兄弟节点说明是插入子项且参照行原本无子节点
            detail.setLevelcode(fatherLevelCode + "001");
            detail.setOrd(ord);
        }
        //更新后面行数据的ord
        zbProjectStandardDetailMapper.updateOnePlusOrdByStandardIdAndOrd(standardId, ord);
        //存库
        zbProjectStandardDetailMapper.insert(detail);
        // 对应标准标记为已被编辑
        zbProjectStandardMapper.updateIsUpdated(detail.getStandardId());
        return detail;
    }

    /**
     　　* @description: 获取所有业态
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/8/18 9:29
     　　*/
    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<ZbCategoryDicDto> getCategoryDicList(String customerCode){
       Map<String, CommonProjCategory> categoryMap = commonProjCategoryService.getCommonProjCategoryMap(customerCode);
       if(MapUtils.isEmpty(categoryMap)){
           return null;
       }

        List<ZbProjectStandard> standardList = zbProjectStandardMapper.selectList(new LambdaQueryWrapper<ZbProjectStandard>()
                .eq(ZbProjectStandard::getCustomerCode, customerCode)
                .eq(ZbProjectStandard::getIsDeleted,0).groupBy(ZbProjectStandard::getCategoryCode));

        return this.getCategoryDicListByCommons(categoryMap.values(),standardList);
    }

    @Override
    public List<ZbProjectStandardDetailTreeDto> getDetailTree(Long standardId, Integer dataType) throws BusinessException {

        List<ZbProjectStandardDetail> details = zbProjectStandardDetailMapper.selectByStandardId(standardId);
        List<ZbProjectStandardDetailTreeDto> returnList = new ArrayList<>(details.size());
        for (ZbProjectStandardDetail detail : details) {
            returnList.add(new ZbProjectStandardDetailTreeDto(detail));
        }
        if (dataType != null && dataType.equals(Constants.ORIGINAL_STRUCTURE)) {
            return returnList;
        }
        return TreeUtils.toTreeListNew(returnList, true);
    }

    /**
    　　* @description: 集合类型转化为父子级结构  CommonProjCategory类型转化为ZbCategoryDicDto
    　　* @param standardList 库中所有标准数据，用来对比业态是否已经在标准中被使用
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/8/18 9:45
    　　*/
    private List<ZbCategoryDicDto> getCategoryDicListByCommons(Collection<CommonProjCategory> categories, List<ZbProjectStandard> standardList){
       Set<String> categoryUsedSet = standardList.parallelStream().map(ZbProjectStandard::getCategoryCode).collect(Collectors.toSet());
       // 父级业态编码为key ,所有相邻子集为value
       Map<String,List<ZbCategoryDicDto>>  categoryMap = new HashMap<>(categories.size());
        // 当前业态编码为key ,对应业态实体为value
        Map<String, ZbCategoryDicDto>  categoryDicDtoKeyMap = new HashMap<>(categories.size());
        // 排序
        categories = categories.parallelStream()
                .sorted(Comparator.comparing(CommonProjCategory::getCommonprojcategoryid)).collect(Collectors.toList());
        for (CommonProjCategory category : categories) {
            String categoryCode = category.getCommonprojcategoryid();
            String pCategoryCode = categoryCode.substring(0,categoryCode.length() - 3);
            ZbCategoryDicDto dicDto = new ZbCategoryDicDto();
            dicDto.setCode(categoryCode);
            dicDto.setFirstCode(category.getCategorycode1());
            dicDto.setLevel(Integer.valueOf(category.getLevel().toString()));
            dicDto.setName(category.getCategoryname());
            dicDto.setOrd(category.getOrd());
            dicDto.setIsUsed(categoryUsedSet.contains(categoryCode));
            dicDto.setIsDeleted(category.getIsDeleted());
            dicDto.setIsUsing(category.getIsUsing());
            if(categoryMap.containsKey(pCategoryCode)){
                List<ZbCategoryDicDto> zbCategoryDicDtos = categoryMap.get(pCategoryCode);
                zbCategoryDicDtos.add(dicDto);
                // 排序
                zbCategoryDicDtos = zbCategoryDicDtos.parallelStream().sorted(Comparator.comparing(ZbCategoryDicDto::getOrd)).collect(Collectors.toList());
                categoryMap.put(pCategoryCode, zbCategoryDicDtos);
                categoryDicDtoKeyMap.get(pCategoryCode).setChildren(categoryMap.get(pCategoryCode));
            }
            categoryMap.put(categoryCode,new ArrayList<>());
            categoryDicDtoKeyMap.put(categoryCode,dicDto);
        }

        // 移出一级
        List<ZbCategoryDicDto> returnList = new ArrayList<>(categoryDicDtoKeyMap.values());
        returnList = returnList.stream()
                .filter(dto->dto.getLevel() == 1)
                .sorted(Comparator.comparing(ZbCategoryDicDto::getOrd))
                .collect(Collectors.toList());
        return returnList;
    }

    /**
     * 建造标准行数据上下移接口
     * @throws
     * @param flag 上下移标识，1：上移，2：下移
     * @param detail
     * <AUTHOR>
     * @return
     * @date 2021/10/26 14:29
     */
    @Override
    public List<ZbProjectStandardDetailTreeDto> moveUpDown(Integer flag, ZbProjectStandardDetail detail) throws BusinessException {

        ZbProjectStandardDetail anchor = moveUpDownValidityCheck(flag, detail);
        //锚点的所有子项id包括本身
        List<Long> anchorIds = getIds(anchor);
        //所选行的所有子项id包括本身
        List<Long> selectedIds = getIds(detail);

        if (flag.equals(Constants.MOVE_UP)) {
            //更新所选行以及所有子项的ord-anchorIds.size()
            zbProjectStandardDetailMapper.updateOneMinusOrdByIds(selectedIds, anchorIds.size());
            //更新锚点以及所有子项的ord+selectedIds.size()
            zbProjectStandardDetailMapper.updateOnePlusOrdByIds(anchorIds, selectedIds.size());
        } else {
            //更新所选行以及所有子项的ord+anchorIds.size()
            zbProjectStandardDetailMapper.updateOnePlusOrdByIds(selectedIds, anchorIds.size());
            //更新锚点以及所有子项的ord-selectedIds.size()
            zbProjectStandardDetailMapper.updateOneMinusOrdByIds(anchorIds, selectedIds.size());
        }

        // 对应标准标记为已被编辑
        zbProjectStandardMapper.updateIsUpdated(detail.getStandardId());

        anchorIds.addAll(selectedIds);
        //查询所有受影响数据
        List<ZbProjectStandardDetail> effectedList = zbProjectStandardDetailMapper.selectSelfByIds(anchorIds);

        //转换为树形结构
        List<ZbProjectStandardDetailTreeDto> returnList = new ArrayList<>(effectedList.size());
        for (ZbProjectStandardDetail detail1 : effectedList) {
            returnList.add(new ZbProjectStandardDetailTreeDto(detail1));
        }

        return TreeUtils.toTreeListNew(returnList, true);
    }

    /**
     * 导入内置标准
     * @throws
     * @param file
     * <AUTHOR>
     * @return
     * @date 2021/10/28 10:04
     */
    @Override
    public void importBuiltInStandard(MultipartFile file) {
        ZbProjectStandard standard = new ZbProjectStandard();
        standard.setId(SnowflakeIdUtils.getNextId());
        standard.setIsUsing(1);
        standard.setCategoryCode("001");
        standard.setCategoryDeleted(false);
        standard.setCategoryName("居住建筑");
        standard.setCustomerCode("-100");
        standard.setIsDeleted(false);
        standard.setIsExample(1);
        standard.setIsUsed(false);
        standard.setName("居住建筑");
        zbProjectStandardMapper.insert(standard);
        try {
            InputStream inputStream = file.getInputStream();
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            List<ZbProjectStandardDetail> details = new ArrayList<>(sheet.getLastRowNum());
            for (int i = 0; i < sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                ZbProjectStandardDetail detail = new ZbProjectStandardDetail();
                Cell cell = row.getCell(0);
                Cell cell1 = row.getCell(1);
                Cell cell2 = row.getCell(2);
                cell.setCellType(CellType.STRING);
                cell1.setCellType(CellType.STRING);
                if (cell2 != null) {
                    cell2.setCellType(CellType.STRING);
                }
                String code = cell.getStringCellValue();
                String levelCode = this.convertToLevelCode(code);
                detail.setLevelcode(levelCode);
                detail.setLevel(code.split("\\.").length);
                detail.setOrd(i + 1);
                detail.setName(cell1.getStringCellValue());
                detail.setDesc(cell2 == null ? "" : cell2.getStringCellValue());
                detail.setId(SnowflakeIdUtils.getNextId());
                detail.setStandardId(standard.getId());
                details.add(detail);
            }
            zbProjectStandardDetailMapper.saveBatch(details);
        } catch (IOException | EncryptedDocumentException e) {
            e.printStackTrace();
        }
    }
    private String convertToLevelCode(String code) {
        String[] split = code.split("\\.");
        StringBuilder stringBuilder = new StringBuilder();
        for (String s : split) {
            if (s.length() == 1) {
                stringBuilder.append("00").append(s);
            } else {
                stringBuilder.append("0").append(s);
            }
        }
        return stringBuilder.toString();
    }

    /**
     * 获取子项id集合
     * @throws
     * @param detail
     * <AUTHOR>
     * @return {@link List< Long>}
     * @date 2021/10/27 16:39
     */
    public List<Long> getIds(ZbProjectStandardDetail detail) {
        List<ZbProjectStandardDetail> anchorSons = zbProjectStandardDetailMapper.selectAllSons(detail.getStandardId(), detail.getLevelcode());
        anchorSons.add(detail);
        return anchorSons.parallelStream().map(ZbProjectStandardDetail::getId).collect(Collectors.toList());
    }

    /**
     * 上下移合法性校验
     * @throws
     * @param flag
     * @param detail
     * <AUTHOR>
     * @return {@link ZbProjectStandardDetail}
     * @date 2021/10/26 16:01
     */
    private ZbProjectStandardDetail moveUpDownValidityCheck(Integer flag, ZbProjectStandardDetail detail) {
        //ord升序获取同级的数据
        List<ZbProjectStandardDetail> list = zbProjectStandardDetailMapper.selectSameLevelByOrd(detail);
        if (flag.equals(Constants.MOVE_UP)) {
            if (list.get(0).getId().equals(detail.getId())) {
                throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "同级的第一行数据无法上移");
            }
            int index = 0;
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getId().equals(detail.getId())) {
                    index = i - 1;
                    break;
                }
            }
            //返回用户所选行同级的前一行数据
            return list.get(index);
        } else {
            if (list.get(list.size() - 1).getId().equals(detail.getId())) {
                throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "同级的最后一行数据无法下移");
            }
            int index = 0;
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i).getId().equals(detail.getId())) {
                    index = i + 1;
                    break;
                }
            }
            //返回用户所选行同级的后一行数据
            return list.get(index);
        }
    }

    /**
     　　* @description: 将旧企业特征标准数据复制到新企业特征标准
     　　* @param  oldCustomerCode:原企业编码   newCustomerCode 新企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 9:55
     　　*/
    @Override
    public void initProjectStandardData(String oldCustomerCode,String newCustomerCode){
        // 根据企业编码获取删除、未删除状态下的建造标准数据，若为空，不处理
        List<ZbProjectStandard> standardList = zbProjectStandardMapper.selectByCustomerCode(oldCustomerCode,1);
        if (CollectionUtils.isEmpty(standardList)) {
            return;
        }


        // oldIdList 原标准id集合
        List<Long> oldIdList = new ArrayList<>();
        // 建造标准新旧id对应关系集合  idMap key:原标准id,value:新标准id
        Map<Long, Long> idMap = new HashMap<>();

        int size = standardList.size();
        List<Long> newIdList = SnowflakeIdUtils.getNextId(size);
        for (int i = 0; i < size; i++) {
            ZbProjectStandard zbProjectStandard = standardList.get(i);
            // 封装载体数据
            Long oldId = zbProjectStandard.getId();
            Long newId = newIdList.get(i);
            oldIdList.add(oldId);
            idMap.put(oldId, newId);
            // 重置字段值
            zbProjectStandard.setId(newId);
            zbProjectStandard.setGlobalId(-100L);
            zbProjectStandard.setIsUsed(false);
            zbProjectStandard.setCreateTime(new Date());
            zbProjectStandard.setUpdateTime(null);
            zbProjectStandard.setCustomerCode(newCustomerCode);
        }

        // 入库保存
        zbProjectStandardMapper.batchInsert(standardList);


        /*
        处理标准细则数据，若库中数据为空，不处理，直接返回
         */
        List<ZbProjectStandardDetail> detailList = zbProjectStandardDetailMapper.selectByIds(oldIdList);
        if (CollectionUtils.isEmpty(detailList)) {
            return;
        }

        int sizeDetail = detailList.size();
        List<Long> newDetailIdList = SnowflakeIdUtils.getNextId(sizeDetail);
        for (int i = 0; i < sizeDetail; i++) {
            ZbProjectStandardDetail detail = detailList.get(i);
            Long standardId = detail.getStandardId();
            if (!idMap.containsKey(standardId)) {
                continue;
            }

            detail.setId(newDetailIdList.get(i));
            detail.setStandardId(idMap.get(standardId));
            detail.setCreateTime(new Date());
            detail.setUpdateTime(null);
        }


        // 入库保存
        zbProjectStandardDetailMapper.saveBatch(detailList);
    }

    /**
     　　* @description: 根据企业编码删除所有建造标准信息
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:22
     　　*/
    @Override
    public void deleteByCustomerCOde(String customerCode){
        // 根据企业编码查询所有建造标准信息，若为空，不处理
        List<ZbProjectStandard> standardList = zbProjectStandardMapper.selectByCustomerCode(customerCode,1);
        if(CollectionUtils.isEmpty(standardList)){
            return;
        }

        List<Long> ids = standardList.stream().map(ZbProjectStandard::getId).collect(Collectors.toList());
        zbProjectStandardMapper.deleteByCustomerCode(customerCode);
        zbProjectStandardDetailMapper.deleteByStandardIds(ids);
    }

    /**
     * 复制建造标准
     * @param zbProjectStandardBo 建造标准信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ZbProjectStandard copy(ZbCopyProjectStandardBo zbProjectStandardBo) throws BusinessException {
        Long standardId = zbProjectStandardBo.getId();
        ZbProjectStandard zbProjectStandard  = zbProjectStandardMapper.selectSelfStandardById(standardId);
        List<ZbProjectStandardDetail> details = zbProjectStandardDetailMapper.selectSelfByStandardId(standardId);
        Long newStandardId = SnowflakeIdUtils.getNextId();
        zbProjectStandard.setId(newStandardId);
        zbProjectStandard.setName(zbProjectStandardBo.getName());
        zbProjectStandard.setCategoryCode(zbProjectStandardBo.getCategoryCode());
        zbProjectStandard.setCategoryName(zbProjectStandardBo.getCategoryName());

        Boolean doubleName = this.checkedName(zbProjectStandardBo.getName(), newStandardId);
        if(Boolean.TRUE.equals(doubleName)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "重复名称，请核对");
        }
        String customerCode  = RequestContent.getCustomerCode();
        String categoryCode = zbProjectStandardBo.getCategoryCode();
        Boolean doubleCategoryCode = this.checkedCategoryCode(categoryCode, newStandardId);
        if(Boolean.TRUE.equals(doubleCategoryCode)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "同一个业态只能创建一条标准，请核对");
        }

        zbProjectStandardMapper.selfInsertSelective(zbProjectStandard);
        ZbProjectStandard standard = zbProjectStandardMapper.selectSelfStandardById(newStandardId);
        setCategoryName(customerCode, standard.getCategoryCode(), standard);

        if (details == null || details.size() == 0) {
            return standard;
        }
        for (ZbProjectStandardDetail detail : details) {
            detail.setId(SnowflakeIdUtils.getNextId());
            detail.setStandardId(newStandardId);
        }
        zbProjectStandardDetailMapper.saveSelfBatch(details);
        return standard;
    }

    @Override
    public List<ZbProjectStandard> getSelfStandardList(String customerCode, Integer isShowDelete) throws BusinessException {
        isShowDelete = isShowDelete == null ? 0 : isShowDelete;
        List<ZbProjectStandard> selfList = zbProjectStandardMapper.selectSelfByCustomerCode(customerCode, isShowDelete);
        if (CollectionUtils.isEmpty(selfList)) {
            initBuildStandardSelfService.initData(customerCode);
            selfList = zbProjectStandardMapper.selectSelfByCustomerCode(customerCode, isShowDelete);
        }

        Map<Long,ZbProjectStandard> map = new HashMap<>();
        List<ZbProjectStandard> qyList = zbProjectStandardMapper.selectByCustomerCode(customerCode, Constants.CategoryConstants.WHETHER_TRUE);
        if(CollectionUtils.isNotEmpty(qyList) && CollectionUtils.isNotEmpty(selfList)){
            qyList.addAll(selfList);
            map = qyList.stream().collect(Collectors.toMap(ZbProjectStandard::getId, x->x));
        }

        for (ZbProjectStandard zbProjectStandard : selfList) {
            String categoryCode = zbProjectStandard.getCategoryCode();
            setCategoryName(customerCode, categoryCode, zbProjectStandard);
            ZbProjectStandard srcProjectStandard = map.get(zbProjectStandard.getSrcStandardId());
            if(Objects.nonNull(srcProjectStandard)){
                zbProjectStandard.setSrcStandardName(srcProjectStandard.getName());
            }
        }
        return selfList;
    }

    @Override
    public StandardDetailDto getSelfDetailTree(Long standardId, Integer dataType) throws BusinessException {
        List<ZbProjectStandardDetail> selfDetails = zbProjectStandardDetailMapper.selectSelfByStandardId(standardId);
        if(CollectionUtils.isEmpty(selfDetails)){
            initBuildStandardDetailService.initData(standardId);
        }
        return getDetailList(standardId,dataType,true,false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void publish(String customerCode, String globalId) throws BusinessException {
        // 当前用户暂存的建造标准
        List<ZbProjectStandard> selfList = zbProjectStandardMapper.selectSelfByCustomerCode(customerCode, Constants.CategoryConstants.WHETHER_TRUE);
        // 新企业无暂存数据
        if (CollectionUtils.isEmpty(selfList)) {
            return;
        }

        // 企业下所有用户暂存的建造标准
        List<ZbProjectStandard> all = zbProjectStandardMapper.selectSelfByCustomerCode(customerCode, Constants.CategoryConstants.WHETHER_TRUE);

        List<Long> standIds = selfList.parallelStream().map(ZbProjectStandard::getId).collect(Collectors.toList());

        // 更新版本号集合
        List<ZbProjectStandard> updateVersionList = new ArrayList<>();
        Map<Long, Integer> oldIdVersionMap = new HashMap<>();
        List<Long> originIds = selfList.parallelStream().filter(standard -> standard.getOriginId() != null).map(ZbProjectStandard::getOriginId).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(originIds)){
            List<ZbProjectStandard> zbProjectStandards = zbProjectStandardMapper.selectBatchIds(originIds);
            oldIdVersionMap = zbProjectStandards.parallelStream().collect(Collectors.toMap(ZbProjectStandard::getId, ZbProjectStandard::getVersion));
        }

        List<ZbProjectStandard> insertStandardList = new ArrayList<>();
        for (ZbProjectStandard entity : selfList) {
            Long originId = entity.getOriginId();
            if (originId == null) {
                // 新增标准默认版本号为0
                entity.setVersion(0);
                insertStandardList.add(entity);
            } else {
                if (entity.getIsUpdated() != null && entity.getIsUpdated() == Constants.ZbFeatureConstants.WHETHER_TRUE) {
                    ZbProjectStandard standard = new ZbProjectStandard();
                    standard.setId(originId);
                    standard.setVersion(oldIdVersionMap.get(originId) + 1);
                    updateVersionList.add(standard);
                    zbProjectStandardMapper.updateStandardById(entity);
                }
            }
        }
        // 插入新增数据
        if(!CollectionUtils.isEmpty(insertStandardList)) {
            zbProjectStandardMapper.batchInsert(insertStandardList);
        }
        List<ZbProjectStandardDetail> details = zbProjectStandardDetailMapper.selectSelfByStandardIdList(standIds);

        Map<Long, Long> idOriginIdMap = selfList.parallelStream().filter(standard -> standard.getOriginId() != null).collect(Collectors.toMap(ZbProjectStandard::getId, ZbProjectStandard::getOriginId));
        // 待删除原有细则standard_id
        List<Long> allOriginIds = details.parallelStream().map(detail -> idOriginIdMap.get(detail.getStandardId())).distinct().collect(Collectors.toList());

        for (ZbProjectStandardDetail entity : details) {
            Long publishedStandardId = idOriginIdMap.get(entity.getStandardId());
            if (publishedStandardId != null) {
                entity.setStandardId(publishedStandardId);
            }
        }
        checkSameDetails(details, selfList);
        // 企业下所有用户暂存标准id的集合
        List<Long> allIds = all.parallelStream().map(ZbProjectStandard::getId).collect(Collectors.toList());
        //已经删除的建造标准
        List<Long> delStandIds = selfList.stream().filter(ZbProjectStandard::getIsDeleted).map(ZbProjectStandard::getId).collect(Collectors.toList());
        //发布标准说明
        pushDetailDesc(standIds, idOriginIdMap, allIds, delStandIds, details, selfList);
        //发布业态备注信息
        pushBuildCategory(standIds, idOriginIdMap, allIds, delStandIds, details);
        //发布产品定位信息
        pushBuildPosition(standIds, idOriginIdMap, allIds, delStandIds, details);
        //发布产品定位值的信息
        pushBuildPositionDetail(standIds, idOriginIdMap, allIds, details);
        if(CollectionUtils.isNotEmpty(allOriginIds)){
            zbProjectStandardDetailMapper.deleteByStandardIds(allOriginIds);
        }
        // 全量替换该企业下已发布标准细则表数据
        if(!CollectionUtils.isEmpty(details)) {
            zbProjectStandardDetailMapper.publishInsert(details);
        }

        if (CollectionUtils.isNotEmpty(updateVersionList)) {
            // 批量更新已发布标准版本号
            zbProjectStandardMapper.batchUpdateVersion(updateVersionList);
        }
        if(CollectionUtils.isNotEmpty(allIds)){
            zbProjectStandardDetailMapper.deleteSelfByStandardIds(allIds);
        }
        zbProjectStandardMapper.deleteSelfByCustomerCode(customerCode);
        publishInfoServiceImpl.updateVersion(customerCode, globalId, OperateConstants.BUILDING);
    }

    /**
     * 过滤掉已经删除一级工程分类的建造标准
     * @param selfList
     */
    private void filterDelCategoryStandards(List<ZbProjectStandard> selfList) {
        if(CollectionUtils.isEmpty(selfList)){
            return;
        }
        String customerCode = RequestContent.getCustomerCode();
        Set<String> buildCategories = selfList.stream().map(ZbProjectStandard::getCategoryCode).collect(Collectors.toSet());
        List<CommonProjCategory> categories = commonProjCategoryService.getCategoryByCodes(buildCategories, customerCode);
        if(CollectionUtils.isEmpty(categories)){
            return;
        }
        Set<String> set = categories.stream().filter(x -> x.getIsDeleted() || x.getIsUsing() == 0).map(CommonProjCategory::getCommonprojcategoryid).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(set)){
            return;
        }
        List<ZbProjectStandard> removeList = selfList.stream().filter(x -> set.contains(x.getCategoryCode())).collect(Collectors.toList());
        selfList.removeAll(removeList);
        log.info("建造标准列表={}",selfList.size());
    }

    /**
     * 发布产品定位值的信息
     * @param standIds
     * @param idOriginIdMap
     * @param allSelfStandardIds
     */
    private void pushBuildPositionDetail(List<Long> standIds,
                                         Map<Long, Long> idOriginIdMap,
                                         List<Long> allSelfStandardIds,
                                         List<ZbProjectStandardDetail> detailList) {
        List<ZbStandardsBuildPositionDetail> positionDetails = standardsBuildPositionDetailService.selectSelfByStandardIds(standIds);
        // 待删除原有细则standard_id
        if(CollectionUtils.isNotEmpty(detailList)){
            List<Long> list = detailList.stream().map(ZbProjectStandardDetail::getStandardId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(positionDetails)){
                List<Long> allCategoryIds = positionDetails.stream().map(ZbStandardsBuildPositionDetail::getStandardId).distinct().collect(Collectors.toList());
                list.removeAll(allCategoryIds);
                //删除企业下position_detail的数据
                standardsBuildPositionDetailService.delByStandardIds(list);
            }else {
                //删除企业下position_detail的数据
                standardsBuildPositionDetailService.delByStandardIds(list);
                return;
            }
        }
        List<Long> allOriginIds = positionDetails.parallelStream().map(detail -> idOriginIdMap.get(detail.getStandardId())).distinct().collect(Collectors.toList());
        for (ZbStandardsBuildPositionDetail positionDetail : positionDetails){
            Long standardId = positionDetail.getStandardId();
            Long publishedStandardId = idOriginIdMap.get(standardId);
            if (publishedStandardId != null) {
                positionDetail.setStandardId(publishedStandardId);
            }
        }

        //删除企业下position_detail的数据
        standardsBuildPositionDetailService.delByStandardIds(allOriginIds);
        //保存新数据
        standardsBuildPositionDetailService.batchSave(positionDetails);
        //删除暂存表数据
        standardsBuildPositionDetailService.delSelfByStandardIds(allSelfStandardIds);
    }

    /**
     * 发布产品定位信息
     */
    private void pushBuildPosition(List<Long> standIds,
                                   Map<Long, Long> idOriginIdMap,
                                   List<Long> allSelfStandardIds,
                                   List<Long> delStandIds,
                                   List<ZbProjectStandardDetail> detailList) {
        //查询建造标准下所有的定位信息
        List<ZbStandardsBuildPosition> buildPositions = standardsBuildPositionService.selectSelfByStandardIds(standIds);
        // 待删除原有细则standard_id
        if(CollectionUtils.isNotEmpty(detailList)){
            List<Long> list = detailList.stream().map(ZbProjectStandardDetail::getStandardId).distinct().collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(buildPositions)){
                List<Long> allCategoryIds = buildPositions.stream().map(ZbStandardsBuildPosition::getStandardId).distinct().collect(Collectors.toList());
                list.removeAll(allCategoryIds);
                //删除企业下bulid_position的数据
                standardsBuildPositionService.delByStandardIds(list);
            }else {
                //删除企业下bulid_position的数据
                standardsBuildPositionService.delByStandardIds(list);
                return;
            }
        }
        if(CollectionUtils.isNotEmpty(delStandIds)){
            checkDelPosition(buildPositions.stream().filter(x -> !delStandIds.contains(x.getStandardId())).collect(Collectors.toList()));
        }else {
            //检查是否存在数据字典已经删除的产品定位
            checkDelPosition(buildPositions);
        }
        List<Long> allOriginIds = buildPositions.parallelStream().map(detail -> idOriginIdMap.get(detail.getStandardId())).distinct().collect(Collectors.toList());
        for (ZbStandardsBuildPosition position : buildPositions){
            Long standardId = position.getStandardId();
            Long publishedStandardId = idOriginIdMap.get(standardId);
            if (publishedStandardId != null) {
                position.setStandardId(publishedStandardId);
            }
        }
        //删除企业下bulid_position的数据
        standardsBuildPositionService.delByStandardIds(allOriginIds);
        //保存新数据
        standardsBuildPositionService.batchSave(buildPositions);
        //删除暂存表数据
        standardsBuildPositionService.delSelfByStandardIds(allSelfStandardIds);
    }

    /**
     * 检查是否存在已经删除的产品定位
     * @param buildPositions
     */
    private void checkDelPosition(List<ZbStandardsBuildPosition> buildPositions) {
        if (CollectionUtils.isEmpty(buildPositions)){
            return;
        }
        String customerCode = RequestContent.getCustomerCode();
        // 查询项目信息【产品定位】
        List<String> selectList = standardsProjectInfoService.getProductPositioning(customerCode);
        if (CollUtil.isEmpty(selectList)) {
            return;
        }
        List<ZbStandardsBuildPosition> filterList = buildPositions.stream()
                .filter(x -> StringUtils.isNotBlank(x.getName()) && !selectList.contains(x.getName())).collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(filterList)){
            throw new BusinessException("产品定位已更改，请更新建造标准");
        }
    }

    /**
     * 发布业态备注表信息
     */
    private void pushBuildCategory(List<Long> standIds,
                                   Map<Long, Long> idOriginIdMap,
                                   List<Long> allSelfStandardIds,
                                   List<Long> delStandIds,
                                   List<ZbProjectStandardDetail> detailList) {
        //查询建造标准下所有的业态信息
        List<ZbStandardsBuildCategory> buildCategories = standardsBuildCategoryService.selectSelfByStandardIds(standIds);
        // 待删除原有细则standard_id
        if(CollectionUtils.isNotEmpty(detailList)){
             List<Long> list = detailList.stream().map(ZbProjectStandardDetail::getStandardId).distinct().collect(Collectors.toList());
             if(CollectionUtils.isNotEmpty(buildCategories)){
                 List<Long> allCategoryIds = buildCategories.stream().map(ZbStandardsBuildCategory::getStandardId).distinct().collect(Collectors.toList());
                 list.removeAll(allCategoryIds);
                 //删除企业下bulid_category的数据
                 standardsBuildCategoryService.delByStandardIds(list);
             }else {
                 //删除企业下bulid_category的数据
                 standardsBuildCategoryService.delByStandardIds(list);
                 return;
             }
        }

        List<Long> allOriginIds = buildCategories.parallelStream().map(detail -> idOriginIdMap.get(detail.getStandardId())).distinct().collect(Collectors.toList());
        for (ZbStandardsBuildCategory buildCategory : buildCategories){
            Long standardId = buildCategory.getStandardId();
            Long publishedStandardId = idOriginIdMap.get(standardId);
            if (publishedStandardId != null) {
                buildCategory.setStandardId(publishedStandardId);
            }
        }
        //删除企业下bulid_category的数据
        standardsBuildCategoryService.delByStandardIds(allOriginIds);
        //保存新数据
        standardsBuildCategoryService.batchSave(buildCategories);
        //删除暂存表数据
        standardsBuildCategoryService.delSelfByStandardIds(allSelfStandardIds);
    }

    /**
     * 校验工程分类是否已经删除
     * @param buildCategories
     */
    private void checkDelCategory(List<ZbStandardsBuildCategory> buildCategories) {
        if(CollectionUtils.isEmpty(buildCategories)){
            return;
        }
        String customerCode = RequestContent.getCustomerCode();
        Set<String> categoryCodeSet = buildCategories.stream().map(ZbStandardsBuildCategory::getCategoryCode).collect(Collectors.toSet());
        List<CommonProjCategory> categories = commonProjCategoryService.getCategoryByCodes(categoryCodeSet, customerCode);
        if(CollectionUtils.isEmpty(categories)){
            return;
        }
        List<CommonProjCategory> categoryList = categories.stream()
                .filter(x-> x.getIsDeleted() || 0 == x.getIsUsing()).collect(Collectors.toList());

        if(CollectionUtils.isNotEmpty(categoryList)){
            throw new BusinessException("工程分类已更改，请更新建造标准");
        }

    }

    /**
     * 将标准说明暂存表数据发布到企业
     * @param standIDs
     * @param idOriginIdMap
     */
    private List<ZbStandardsBuildStandardDetailDesc>  pushDetailDesc(List<Long> standIDs, Map<Long, Long> idOriginIdMap,
                                                                     List<Long> allSelfStandardIds,List<Long> delStandIds,
                                                                     List<ZbProjectStandardDetail> details, List<ZbProjectStandard> selfList) {
        //查询建造标准下所有标准说明数据
        List<ZbStandardsBuildStandardDetailDesc> detailDescList = standardsBuildStandardDetailDescService.selectSelfByStandardIds(standIDs);
        if(CollectionUtils.isEmpty(detailDescList)){
            return Lists.newArrayList();
        }
        // 校验同级是否有同名说明项
        checkSameDesc(detailDescList, details, selfList);
        if(CollectionUtils.isNotEmpty(delStandIds)){
            List<ZbStandardsBuildStandardDetailDesc> noDelData = detailDescList.stream().filter(item -> !delStandIds.contains(item.getStandardId())).collect(Collectors.toList());
            //校验枚举值
            checkSelects(noDelData);
        }else {
            //校验枚举值
            checkSelects(detailDescList);
        }
        // 待删除原有细则standard_id
        List<Long> allOriginIds = detailDescList.parallelStream().map(detailDesc -> idOriginIdMap.get(detailDesc.getStandardId())).distinct().collect(Collectors.toList());

        for(ZbStandardsBuildStandardDetailDesc detailDesc : detailDescList){
            Long standardId = detailDesc.getStandardId();
            Long publishedStandardId = idOriginIdMap.get(standardId);
            if (publishedStandardId != null) {
                detailDesc.setStandardId(publishedStandardId);
            }
        }

        //删除企业下desc的数据
        standardsBuildStandardDetailDescService.delByStandardIds(allOriginIds);
        //保存新数据
        standardsBuildStandardDetailDescService.saveBatch(detailDescList);
        //删除暂存表数据
        standardsBuildStandardDetailDescService.delSelfByStandardIds(allSelfStandardIds);
        return detailDescList;
    }

    private void checkSameDetails(List<ZbProjectStandardDetail> details, List<ZbProjectStandard> selfList) {
        if (CollectionUtils.isEmpty(details)) {
            return;
        }
        List<ZbProjectStandard> unDelStandardList = selfList.stream().filter(item -> !Boolean.TRUE.equals(item.getIsDeleted())).collect(Collectors.toList());
        // selfList 转成 id: name的map
        Map<Long, String> selfMap = unDelStandardList.stream().collect(Collectors.toMap(item->item.getOriginId() == null ? item.getId(): item.getOriginId(), ZbProjectStandard::getName));
        details.stream().filter(detail -> selfMap.containsKey(detail.getStandardId())) // 过滤掉删除的建造标准
                .collect(Collectors.groupingBy(ZbProjectStandardDetail::getStandardId)).forEach((standardId, detailList)->{
            Map<String, List<ZbProjectStandardDetail>> sameParentList =
                    detailList.stream().collect(Collectors.groupingBy(detail->detail.getLevelcode().substring(0, detail.getLevelcode().length() - 3)));
            sameParentList.forEach((parentCode, list)->{
                Set<String> nameSet = new HashSet<>();
                list.forEach(item -> {
                    if (StringUtils.isBlank(item.getName())) {
                        throw new BusinessException(String.format("建造标准[%s]中存在空的标准名称，请调整后发布", selfMap.get(standardId)));
                    }
                    if (nameSet.contains(item.getName())) {
                        throw new BusinessException(String.format("建造标准[%s]中的标准名称[%s]同级下存在重复，请调整后发布", selfMap.get(standardId), item.getName()));
                    }
                    nameSet.add(item.getName());
                });
            });
        });
    }

    private void checkSameDesc(List<ZbStandardsBuildStandardDetailDesc> detailDescList,
                               List<ZbProjectStandardDetail> details,
                               List<ZbProjectStandard> selfList) {
        if(CollectionUtils.isEmpty(detailDescList)){
            return;
        }
        // details转成 id: name的map
        Map<Long, String> detailMap = details.stream().collect(Collectors.toMap(ZbProjectStandardDetail::getId, ZbProjectStandardDetail::getName));
        // selfList 转成 id: name的map
        List<ZbProjectStandard> unDelStandardList = selfList.stream().filter(item -> !Boolean.TRUE.equals(item.getIsDeleted())).collect(Collectors.toList());
        Map<Long, String> selfMap = unDelStandardList.stream().collect(Collectors.toMap(ZbProjectStandard::getId, ZbProjectStandard::getName));

        // 如果相同 standardId 下存在同名的 descName 则抛出异常
        detailDescList.stream().filter(desc -> selfMap.containsKey(desc.getStandardId())) // 过滤掉删除的建造标准
                .collect(Collectors.groupingBy(ZbStandardsBuildStandardDetailDesc::getStandardId)).forEach((standardId,descList)->{
            descList.stream().collect(Collectors.groupingBy(ZbStandardsBuildStandardDetailDesc::getStandardDetailId)).forEach((detailId, descList2)->{
                // 如果descList2中存在同名的descName 则抛出异常
                Set<String> descNameSet = new HashSet<>();
                descList2.forEach(desc ->{
                    if (descNameSet.contains(desc.getDetailDesc())) {
                        throw new BusinessException(String.format("建造标准[%s]中的[%s]中存在相同的标准说明项，请调整后发布", selfMap.get(standardId), detailMap.get(detailId)));
                    }
                    descNameSet.add(desc.getDetailDesc());
                });
            });
        });
    }

    private void checkSelects(List<ZbStandardsBuildStandardDetailDesc> detailDescList) {
        if(CollectionUtils.isEmpty(detailDescList)){
            return;
        }
        Optional<ZbStandardsBuildStandardDetailDesc> detailDesc = detailDescList.stream()
                .filter(x->Constants.ZbExpressionConstants.TYPE_SELECT.equals(x.getTypeCode()) || Constants.ZbExpressionConstants.TYPE_SELECTS.equals(x.getTypeCode()))
                .filter(x->StringUtils.isBlank(x.getSelectList())).findFirst();
        if(detailDesc.isPresent()){
            throw new BusinessException("枚举值不可为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<ZbCategoryDicDto> getSelfCategoryDicList() throws BusinessException {

        String customerCode  = RequestContent.getCustomerCode();
        Map<String, CommonProjCategory> categoryMap = commonProjCategoryService.getCommonProjCategoryMap(customerCode);
        if(MapUtils.isEmpty(categoryMap)){
            return null;
        }
        List<ZbProjectStandard> standardList = getSelfStandardList(customerCode, 0);
        LinkedHashMap <String, ZbProjectStandard> standardHash = new LinkedHashMap();
        for (ZbProjectStandard entity : standardList){
            String tempCustomerCode = entity.getCustomerCode();
            if(!standardHash.containsKey(tempCustomerCode)) {
                standardHash.put(tempCustomerCode, entity);
            }
        }
        return this.getCategoryDicListByCommons(categoryMap.values(), new ArrayList<>(standardHash.values()));
    }

    @Override
    public List<ZbProjectStandardDetail> getDetailByStandardIdList(List<Long> standardIdList) {
        return zbProjectStandardDetailMapper.selectByStandardIdList(standardIdList);
    }

    @Override
    public List<ZbProjectStandard> getStandardByCategoryCodes(String customerCode, List<String> categoryCodeList) {
        initBuildStandardService.initData(customerCode);
        List<String> topCategoryCodeList = categoryCodeList.stream().map(x -> x.substring(0, SINGLE_LEVEL_LEN)).collect(Collectors.toList());
        return zbProjectStandardMapper.selectListByCusAndCates(customerCode, topCategoryCodeList);
    }

    @Override
    public List<ZbProjectStandard> getStandardLikeCategoryCode(String customerCode, String categoryCode) {
        initBuildStandardService.initData(customerCode);
        String topCategoryCode = categoryCode.substring(0, SINGLE_LEVEL_LEN);
        return zbProjectStandardMapper.selectEnabledListByCusAndCategoryCode(customerCode, topCategoryCode);
    }

    /**
     * 根据业态编码获取其下的建造标准
     * @throws
     * @param customerCode
     * @param categoryCode
     * <AUTHOR>
     * @return {@link List< ZbProjectStandard>}
     * @date 2022/1/18 10:20
     */
    @Override
    public List<ZbProjectStandard>  getByCategoryCode(String customerCode, String categoryCode, boolean isSetCategoryAndPosition) {
        initBuildStandardService.initData(customerCode);
        String topCategoryCode = categoryCode.substring(0, SINGLE_LEVEL_LEN);
        return zbProjectStandardMapper.selectByCategoryCode(customerCode, topCategoryCode, 0);
    }

    /**
     * @description: 根据建造标准id获取建造标准细则
     * @param standardId
     * @param dataType
     * @param isSelf 是否为 暂存数据
     * @return com.glodon.qydata.dto.StandardDetailDto
     * <AUTHOR>
     * @date 2022/8/1 17:53
     */
    @Override
    public StandardDetailDto getDetailList(Long standardId, Integer dataType, boolean isSelf, boolean isSetCategoryAndPosition) {
        StandardDetailDto standardDetailDto = new StandardDetailDto();

        // 查询建造标准 zb_standards_build_standard
        ZbProjectStandard standard = this.getZbProjectStandard(standardId, isSelf);

        if (standard == null){
            if (isSelf){
                throw new BusinessException("建造标准已由其他账号更新发布，请刷新后再试。");
            }
            throw new BusinessException("建造标准不存在!");
        }

        standardDetailDto.setStandardId(standardId);
        standardDetailDto.setStandardName(standard.getName());
        // 动态表头
        standardDetailDto.setDynamicCol(this.getDynamicCol(standardId, isSelf));
        // 列表数据
        standardDetailDto.setStandardDetail(this.getDetailTreeDtoList(standardId, dataType, isSelf, standard.getCategoryCode()));
        //查询产品定位列表
        if (isSetCategoryAndPosition){
            standardDetailDto.setCategoryAndPosition(standardsBuildPositionService.getPositionList(standardId, standard.getCustomerCode(), isSelf));
        }

       return standardDetailDto;
    }

    /**
     * @description: 根据id查询建造标准
     * @param standardId
     * @param isSelf
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard
     * <AUTHOR>
     * @date 2022/8/3 19:00
     */
    private ZbProjectStandard getZbProjectStandard(Long standardId, boolean isSelf){
        if (standardId == null){
            throw new BusinessException("standardId不能为空");
        }

        if (isSelf){
            return zbProjectStandardMapper.selectSelfStandardById(standardId);
        }

        return zbProjectStandardMapper.selectStandardByStandardId(standardId);
    }

    /**
     * @description: 动态表头
     * @param standardId
     * @return java.util.List<com.glodon.qydata.dto.StandardsBuildPositionDto>
     * <AUTHOR>
     * @date 2022/8/2 16:24
     */
    private List<StandardsBuildPositionDto> getDynamicCol(Long standardId,boolean isSelf){
        List<StandardsBuildPositionDto> dynamicCol = new ArrayList<>();

        List<ZbStandardsBuildPosition> positionList = this.getPositionByStandardId(standardId, isSelf);

        if (CollectionUtils.isEmpty(positionList)){
            return null;
        }

        Set<String> categoryCodeSet = positionList.parallelStream().map(ZbStandardsBuildPosition::getCategoryCode).collect(Collectors.toSet());

        positionList.forEach(item -> dynamicCol.add(this.convertBuildPositionToDto(item, RequestContent.getCustomerCode(), categoryCodeSet)));

        return dynamicCol;
    }

    /**
     * @description: 指定编码的工程分类
     * @param categoryCodeSet
     * @param customerCode
     * @return java.util.Map<java.lang.String, com.glodon.qydata.entity.standard.category.CommonProjCategory>
     * <AUTHOR>
     * @date 2022/8/2 16:24
     */
    private Map<String, CommonProjCategory> getCodeAndCategory(Set<String> categoryCodeSet, String customerCode){
        List<CommonProjCategory> categoryList = commonProjCategoryService.getCategoryByCodes(categoryCodeSet, customerCode);

        if (CollectionUtils.isNotEmpty(categoryList)){
            return categoryList.parallelStream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, Function.identity(), (v1, v2) -> v2));
        }

        return null;
    }

    /**
     * @description: 动态表头dto装换
     * @param zbStandardsBuildPosition
     * @param customerCode
     * @param categoryCodeSet
     * @return com.glodon.qydata.dto.StandardsBuildPositionDto
     * <AUTHOR>
     * @date 2022/8/2 16:24
     */
    private StandardsBuildPositionDto convertBuildPositionToDto(ZbStandardsBuildPosition zbStandardsBuildPosition, String customerCode, Set<String> categoryCodeSet){
        StandardsBuildPositionDto positionDto = new StandardsBuildPositionDto();
        positionDto.setId(zbStandardsBuildPosition.getId());
        positionDto.setOrd(zbStandardsBuildPosition.getOrd());

        Integer type = zbStandardsBuildPosition.getType();
        if (Constants.BuildStandardConstants.POSITION_GRADE.equals(type)) {
            positionDto.setName(zbStandardsBuildPosition.getName());
        }

        String categoryCode = zbStandardsBuildPosition.getCategoryCode();
        positionDto.setCategoryCode(categoryCode);

        // 查询工程分类
        Map<String, CommonProjCategory> codeAndCategory = this.getCodeAndCategory(categoryCodeSet, customerCode);

        CommonProjCategory commonProjCategory = null;
        if (codeAndCategory != null && codeAndCategory.containsKey(categoryCode)){
            commonProjCategory = codeAndCategory.get(categoryCode);
            positionDto.setCategoryName(commonProjCategory.getCategoryname());
        }

        // 查询项目信息【产品定位】
        List<String> selectList = standardsProjectInfoService.getProductPositioning(customerCode);

        if (commonProjCategory != null && !commonProjCategory.getIsDeleted() && Constants.STATUS_VALID.equals(commonProjCategory.getIsUsing())){
            if (Constants.BuildStandardConstants.POSITION_CATEGORY.equals(type)) {
                positionDto.setDeleteFlag(Constants.ZbStandardsMainQuantityConstants.DeletedStatus.NO_DELETED);
            }else {
                positionDto.setDeleteFlag(selectList != null && selectList.contains(zbStandardsBuildPosition.getName())
                        ? Constants.ZbStandardsMainQuantityConstants.DeletedStatus.NO_DELETED : Constants.ZbStandardsMainQuantityConstants.DeletedStatus.DELETED);
            }
        }else {
            positionDto.setDeleteFlag(Constants.ZbStandardsMainQuantityConstants.DeletedStatus.DELETED);
        }
        return positionDto;
    }

    /**
     * @description: 列表数据
     * @param standardId
     * @param dataType
     * @return java.util.List<com.glodon.qydata.dto.ZbProjectStandardDetailTreeDto>
     * <AUTHOR>
     * @date 2022/8/1 17:56
     */
    private List<ZbProjectStandardDetailTreeDto> getDetailTreeDtoList(Long standardId, Integer dataType, boolean isSelf, String categoryCode){
        List<ZbProjectStandardDetailTreeDto> returnList = new ArrayList<>();

        // 根据标准id查询标准细则
        List<ZbProjectStandardDetail> detailByStandardId = this.getDetailByStandardId(standardId, isSelf);

        // 查询标准细则对应的标准说明
        Map<Long, List<ZbStandardsBuildStandardDetailDesc>> detailIdAndDesc = this.getDetailDesc(standardId, isSelf);

        // 标准说明各定位的值
        Map<Long, List<ZbStandardsBuildPositionDetail>> descIdAndPositionDetail = this.getDetailPositionDetail(standardId, isSelf);

        // 数据组装
        for (ZbProjectStandardDetail zbProjectStandardDetail : detailByStandardId) {
            ZbProjectStandardDetailTreeDto returnDto = new ZbProjectStandardDetailTreeDto(zbProjectStandardDetail);
            returnDto.setDescription(this.getDescription(zbProjectStandardDetail.getId(), detailIdAndDesc, descIdAndPositionDetail));
            returnList.add(returnDto);
        }

        // 数据结构
        if (dataType != null && dataType.equals(Constants.ORIGINAL_STRUCTURE)) {
            return returnList;
        }

        //校验项目划分科目（只有暂存表校验）
        if (isSelf && CollUtil.isNotEmpty(returnList)) {
            verifySubjectDivision(returnList, categoryCode);
        }

        return TreeUtils.toTreeListNew(returnList, true);
    }

    private void verifySubjectDivision(List<ZbProjectStandardDetailTreeDto> detailList, String categoryCode) {


//        CommonProjCategory categoryByCode = commonProjCategoryService.getCategoryByCode(categoryCode, customerCode);
//        if (ObjectUtil.isNull(categoryByCode)) {
//            return;
//        }

        List<Item> templateByCategory;
        try {
            templateByCategory = subjectDivisionService.getTemplateByCategory(categoryCode);
        } catch (Exception e) {
            if (e instanceof BusinessException && DigitalCostConstants.RESULT_CODE_409.equals(((BusinessException) e).getCode())){
                return;
            }
            throw e;
        }

        detailList.forEach(treeDto -> {
            Item matchingItem = templateByCategory.stream()
                    .filter(item -> item.getId().equals(treeDto.getItemDivisionSubjectId()))
                    .findFirst()
                    .orElse(null);

            treeDto.setItemDivisionSubjectId(matchingItem != null ? matchingItem.getId() : null);
            treeDto.setItemDivisionSubjectName(matchingItem != null ?
                    subjectDivisionService.getFullItemName(matchingItem.getId(), templateByCategory) : null);
        });
        //更新项目划分科目信息
        zbProjectStandardDetailMapper.batchSubjectDivisionName(detailList);
    }



    /**
     * @description: 标准说明
     * @param detailId
     * @param detailIdAndDesc
     * @param descIdAndPositionDetail
     * @return java.util.List<com.glodon.qydata.dto.StandardsBuildStandardDetailDescDto>
     * <AUTHOR>
     * @date 2022/8/1 17:56
     */
    private List<StandardsBuildStandardDetailDescDto> getDescription(Long detailId, Map<Long, List<ZbStandardsBuildStandardDetailDesc>> detailIdAndDesc,
                                                                     Map<Long, List<ZbStandardsBuildPositionDetail>> descIdAndPositionDetail){

        if (detailIdAndDesc == null || !detailIdAndDesc.containsKey(detailId)){
            return null;
        }

        List<StandardsBuildStandardDetailDescDto> description = new ArrayList<>();

        detailIdAndDesc.get(detailId).forEach(detailDesc -> {
            StandardsBuildStandardDetailDescDto descDto = new StandardsBuildStandardDetailDescDto();
            BeanUtils.copyProperties(detailDesc, descDto);
            descDto.setValues(this.getValues(detailDesc.getId(), descIdAndPositionDetail));
            description.add(descDto);
        });

        return description;
    }

    /**
     * @description: 标准说明的值
     * @param descId
     * @param descIdAndPositionDetail
     * @return java.util.List<com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail>
     * <AUTHOR>
     * @date 2022/8/1 17:57
     */
    private List<ZbStandardsBuildPositionDetail> getValues(Long descId, Map<Long, List<ZbStandardsBuildPositionDetail>> descIdAndPositionDetail){
        if (descIdAndPositionDetail != null && descIdAndPositionDetail.containsKey(descId)){
            return descIdAndPositionDetail.get(descId);
        }

        return null;
    }

    /**
     * @description: 根据标准id查询标准细则
     * @param standardId
     * @return void
     * <AUTHOR>
     * @date 2022/8/1 15:14
     */
    private List<ZbProjectStandardDetail> getDetailByStandardId(Long standardId,boolean isSelf){
        if (standardId == null){
            throw new BusinessException("standardId不能为空");
        }
        if(isSelf){
            return zbProjectStandardDetailMapper.selectSelfByStandardId(standardId);
        }
        return zbProjectStandardDetailMapper.selectByStandardId(standardId);
    }

    /**
     * @description: 获取某标准下的所有细则和标准说明的对应关系
     * @param standardId
     * @return java.util.Map<java.lang.Long, java.util.List < com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc>>
     * <AUTHOR>
     * @date 2022/8/1 15:45
     */
    private Map<Long, List<ZbStandardsBuildStandardDetailDesc>> getDetailDesc(Long standardId,boolean isSelf){
        List<ZbStandardsBuildStandardDetailDesc> descByStandardId = this.getDescByStandardId(standardId, isSelf);

        if (CollectionUtils.isNotEmpty(descByStandardId)){
             return descByStandardId.parallelStream()
                    .collect(Collectors.groupingBy(ZbStandardsBuildStandardDetailDesc::getStandardDetailId));
        }

        return null;
    }

    /**
     * @description: 查询某标准下的所有标准说明
     * @param standardId
     * @return void
     * <AUTHOR>
     * @date 2022/8/1 15:23
     */
    private List<ZbStandardsBuildStandardDetailDesc> getDescByStandardId(Long standardId,boolean isSelf){
        if (standardId == null){
            throw new BusinessException("standardId不能为空");
        }
        if(isSelf){
            return zbStandardsBuildStandardDetailDescMapper.selectSelfByStandardId(standardId);
        }

        return zbStandardsBuildStandardDetailDescMapper.selectByStandardId(standardId);
    }

    /**
     * @description: 动态表头
     * @param standardId
     * @return java.util.List<com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPosition>
     * <AUTHOR>
     * @date 2022/8/1 17:54
     */
    private List<ZbStandardsBuildPosition> getPositionByStandardId(Long standardId, boolean isSelf) {
        if (standardId == null){
            throw new BusinessException("standardId不能为空");
        }
        if(isSelf){
            return zbStandardsBuildPositionMapper.selectSelfByStandardId(standardId);
        }

        return zbStandardsBuildPositionMapper.selectByStandardId(standardId);
    }

    /**
     * @description: 标准说明的值
     * @param standardId
     * @return java.util.List<com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail>
     * <AUTHOR>
     * @date 2022/8/1 17:58
     */
    private List<ZbStandardsBuildPositionDetail> getPositionDetailByStandardId(Long standardId,boolean isSelf) {
        if (standardId == null){
            throw new BusinessException("standardId不能为空");
        }
        if(isSelf){
            return zbStandardsBuildPositionDetailMapper.selectSelfByStandardId(standardId);
        }
        return zbStandardsBuildPositionDetailMapper.selectByStandardId(standardId);
    }

    /**
     * @description: 获取某标准下的所有标准说明和标准说明值的对应关系
     * @param standardId
     * @return java.util.Map<java.lang.Long, java.util.List < com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail>>
     * <AUTHOR>
     * @date 2022/8/1 17:58
     */
    private Map<Long, List<ZbStandardsBuildPositionDetail>> getDetailPositionDetail(Long standardId,boolean isSelf){
        List<ZbStandardsBuildPositionDetail> positionDetailByStandardId = this.getPositionDetailByStandardId(standardId, isSelf);

        if (CollectionUtils.isNotEmpty(positionDetailByStandardId)){
            return positionDetailByStandardId.parallelStream()
                    .collect(Collectors.groupingBy(ZbStandardsBuildPositionDetail::getStandardDetailDescId));
        }

        return null;
    }

    /**
     * 将历史数据迁移到标准说明
     */
    @Override
    //@Transactional
    public void historyDetailToDesc(String type) {
        //查询所有的建造标准
        List<ZbProjectStandard> all = null;
        if("0".equals(type)){
            all = this.list();
            if(CollectionUtils.isNotEmpty(all)){
                all = all.stream().filter(item->!"-100".equals(item.getCustomerCode())&&!"1".equals(item.getIsExample())).collect(Collectors.toList());
            }
        }else {
            all = zbProjectStandardMapper.selectSelfAll();
        }
        if(CollectionUtils.isEmpty(all)){
            return;
        }
        for (ZbProjectStandard zbProjectStandard : all){
            try {
                List<ZbProjectStandardDetail> standardDetails = null;
                if("0".equals(type)){
                    standardDetails = zbProjectStandardDetailMapper.selectByStandardId(zbProjectStandard.getId());
                }else {
                    standardDetails = zbProjectStandardDetailMapper.selectSelfByStandardId(zbProjectStandard.getId());
                }
                if(CollectionUtils.isEmpty(standardDetails)){
                    continue;
                }
                List<ZbProjectStandardDetailTreeDto> list = new ArrayList<>();
                standardDetails.stream().forEach(x->{
                    ZbProjectStandardDetailTreeDto treeDto = new ZbProjectStandardDetailTreeDto();
                    BeanUtils.copyProperties(x,treeDto);
                    list.add(treeDto);
                });
                List<ZbStandardsBuildStandardDetailDesc> leafList =filterChild(list,zbProjectStandard);
                if(CollectionUtils.isEmpty(leafList)){
                    continue;
                }
                if("0".equals(type)){
                    standardsBuildStandardDetailDescService.remove(new LambdaQueryWrapper<ZbStandardsBuildStandardDetailDesc>()
                            .eq(ZbStandardsBuildStandardDetailDesc::getStandardId,zbProjectStandard.getId()));
                    standardsBuildStandardDetailDescService.saveBatch(leafList);
                }else {
                    zbStandardsBuildStandardDetailDescMapper.batchDelSelfByStandardIds(Lists.newArrayList(zbProjectStandard.getId()));
                    zbStandardsBuildStandardDetailDescMapper.batchSaveSelf(leafList);
                }
            }catch (Exception e){
                log.error("数据迁移出错standardId={}, error={}",zbProjectStandard.getId(),e);
            }
        }
        log.info("迁移数据成功");
    }

    /**
     * 过滤末级科目
     * @param list
     * @param zbProjectStandard
     * @return
     * @throws Exception
     */
    private List<ZbStandardsBuildStandardDetailDesc> filterChild(List<ZbProjectStandardDetailTreeDto> list, ZbProjectStandard zbProjectStandard) throws Exception{
        List<ZbStandardsBuildStandardDetailDesc> leafList = new ArrayList<>();
        TreeUtils.traverseTopDown2(list, true, new TreeNodeHandler<ZbProjectStandardDetailTreeDto>() {
            @Override
            public void process(TreeNode<ZbProjectStandardDetailTreeDto> treeNode, List<TreeNode<ZbProjectStandardDetailTreeDto>> brothers) throws Exception {
                    ZbProjectStandardDetailTreeDto treeDto = treeNode.getData();
                    if(StringUtils.isEmpty(treeDto.getDesc())){
                        return;
                    }
                    ZbStandardsBuildStandardDetailDesc detailDesc = new ZbStandardsBuildStandardDetailDesc();
                    BeanUtils.copyProperties(treeDto,detailDesc);
                    detailDesc.setStandardDetailId(treeDto.getId());
                    detailDesc.setStandardId(zbProjectStandard.getId());
                    detailDesc.setDetailDesc(treeDto.getDesc());
                    String levelCode = treeDto.getLevelcode();
                    String code = levelCode.substring((levelCode.length()/3-1)*3);
                    detailDesc.setName("标准说明1");
                    detailDesc.setCode(code);
                    detailDesc.setTypeCode("text");
                    detailDesc.setTradeId(treeDto.getTradeId());
                    detailDesc.setTradeName(treeDto.getTradeName());
                    detailDesc.setRemark(treeDto.getRemark());
                    leafList.add(detailDesc);
            }
        });
        return  leafList;
    }

    /**
     * @description: 标准说明上下移
     * @param flag 上下移标识，1：上移，2：下移
     * @param descId
     * @param standardDetailId
     * @return com.glodon.qydata.vo.common.ResponseVo<java.util.List < com.glodon.qydata.dto.ZbProjectStandardDetailTreeDto>>
     * <AUTHOR>
     * @date 2022/8/4 9:42
     */
    @Override
    @Transactional
    public List<ZbStandardsBuildStandardDetailDesc> descMoveUpDown(Integer flag, Long descId, Long standardDetailId) {
        // 标准说明上下移
        Long standardId = standardsBuildStandardDetailDescService.descMoveUpDown(flag, descId, standardDetailId);

        // 对应标准标记为已被编辑
        zbProjectStandardMapper.updateIsUpdated(standardId);

        // 返回标准说明列表
        return zbStandardsBuildStandardDetailDescMapper.selectSelfByDetailIdIAsc(standardDetailId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StandardsBuildStandardDetailDescVo addDesc(DetailDescAddVo addVo) {
        StandardsBuildStandardDetailDescVo zbStandardsBuildStandardDetailDesc = standardsBuildStandardDetailDescService.addDesc(addVo);
        this.updateStandardFlag(addVo.getStandardId());
        return zbStandardsBuildStandardDetailDesc;
    }
    /**
     　　* @description: 根据企业编码与工程分类末级业态查询符合要求的数据列表
     　　* @param [customerCode, categoryCode, positionName]
     　　* @return java.util.List<com.glodon.qydata.dto.ZbProjectStandardPositionAndDetailDto>
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/8/5 16:14
     　　*/
    @Override
    public List<ZbProjectStandardDetailDto> getListByCategoryAndPosition(String customerCode, String categoryCodes, String positionName){
        List<ZbProjectStandardDetailDto> returnList = new ArrayList<>();
        List<String> categoryCodeList = Stream.of(categoryCodes.split(",")).distinct().collect(Collectors.toList());
        initBuildStandardService.initData(customerCode);
        CountDownLatch latch = new CountDownLatch(categoryCodeList.size());
        dataExecutor.execute(() -> {
            for (String categoryCode : categoryCodeList) {
                try {
                    // 获取ZbProjectStandardDetailDto集合
                    List<ZbProjectStandardDetailDto> dtoList = getDetailDto(customerCode, categoryCode, positionName);
                    // 不同层级填充categoryCode
                    if (CollectionUtils.isNotEmpty(dtoList)) {
                        for (ZbProjectStandardDetailDto zbProjectStandardDetailDto : dtoList) {
                            zbProjectStandardDetailDto.setCategoryCode(categoryCode);
                            List<StandardsBuildStandardPositionDescDto> descDtos = zbProjectStandardDetailDto.getDescription();
                            if (CollectionUtils.isNotEmpty(descDtos)) {
                                descDtos.forEach(dto->dto.setCategoryCode(categoryCode));
                            }
                        }
                        returnList.addAll(dtoList);
                    }
                } finally {
                latch.countDown();
              }
             }
        });
            try {
                latch.await();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        return returnList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public StandardsBuildStandardDetailDescVo updateDesc(DetailDescUpdateVo updateVo) {
        StandardsBuildStandardDetailDescVo zbStandardsBuildStandardDetailDesc = standardsBuildStandardDetailDescService.updateDesc(updateVo);
        this.updateStandardFlag(updateVo.getStandardId());
        return zbStandardsBuildStandardDetailDesc;
    }

    @Override
    public PageInfo<ZbProjectStandard> standardPageList(ProjectStandardQueryDto standardQuery, boolean isSetCategoryAndPosition) {
        int oldPageNum = standardQuery.getPageNum();
        int pageSize = standardQuery.getPageSize();
        List<ZbProjectStandard>  list = this.standardList(standardQuery.getCustomerCode(),standardQuery.getIsShowDelete(), Constants.ORDER_ASC, isSetCategoryAndPosition);
        PageInfo<ZbProjectStandard> pageInfo = new PageInfo<>();
        if(CollectionUtils.isEmpty(list)){
            return pageInfo;
        }
        String name = standardQuery.getName();
        if(StringUtils.isNotBlank(name)){
            list = list.stream().filter(x->x.getName().contains(name)).collect(Collectors.toList());
        }
        String categoryCode = standardQuery.getCategoryCode();
        if(StringUtils.isNotBlank(categoryCode)&&CollectionUtils.isNotEmpty(list)){
            String topCategoryCode = categoryCode.substring(0, SINGLE_LEVEL_LEN);
            list = list.stream().filter(x->topCategoryCode.equals(x.getCategoryCode())).collect(Collectors.toList());
        }
        String position = standardQuery.getPositionCategoryCode();
        if(StringUtils.isNotBlank(position)&&CollectionUtils.isNotEmpty(list)){
            list = list.stream().filter(x->StringUtils.isNotBlank(x.getPositionCategoryCodes())&&x.getPositionCategoryCodes()
                    .contains(position)).collect(Collectors.toList());
        }
        Date startTime = standardQuery.getCreateStartTime();
        Date endTime = standardQuery.getCreateEndTime();
        if(startTime != null &&CollectionUtils.isNotEmpty(list)){
            list = list.stream().filter(x->x.getCreateTime().compareTo(startTime)>0).collect(Collectors.toList());
        }
        if(endTime != null &&CollectionUtils.isNotEmpty(list)){
            list = list.stream().filter(x->x.getCreateTime().compareTo(endTime)<0).collect(Collectors.toList());
        }
        Integer isUsing= standardQuery.getIsUsing();
        if(isUsing != null&&CollectionUtils.isNotEmpty(list) ){
            list = list.stream().filter(x->isUsing.equals(x.getIsUsing())).collect(Collectors.toList());
        }
        if(CollectionUtils.isNotEmpty(list)){
            pageInfo.setTotal(list.size());
            pageInfo.setPageSize(pageSize);
            pageInfo.setPageNum(oldPageNum);
            pageInfo.setList(list.stream().skip((long) (oldPageNum - 1) * pageSize).limit(pageSize).collect(Collectors.toList()));
        }
        return pageInfo;
    }

    @Override
    public StandardDetailDto getDetailListByCategoryCode(String customerCode, String categoryCode, Integer dataType, boolean isSetCategoryAndPosition) {
        initBuildStandardService.initData(customerCode);
        String topCategoryCode = categoryCode.substring(0, SINGLE_LEVEL_LEN);
        List<ZbProjectStandard> zbProjectStandards = zbProjectStandardMapper.selectByCategoryCode(customerCode, topCategoryCode, 0);

        if (CollUtil.isNotEmpty(zbProjectStandards)) {
            Optional<ZbProjectStandard> optional = zbProjectStandards.stream()
                    .filter(x -> Constants.STATUS_VALID.equals(x.getIsUsing()))
                    .max(Comparator.comparing(ZbProjectStandard::getUpdateTime, Comparator.nullsLast(Comparator.naturalOrder())));

            if (optional.isPresent()) {
                return this.getDetailList(optional.get().getId(), dataType, false, isSetCategoryAndPosition);
            }
        }

        return new StandardDetailDto();
    }

    /**
     * 查询建造标准列表
     * @param listVo 查询入参
     * @param enterpriseId 企业id
     * @return 列表所需分页之后的数据
     */
    @Override
    public PageInfo<ImportListDetailVo> importList(ImportListVo listVo, String enterpriseId) {
        MiddleGroundResp<MiddleGroundPage<ImportListDetailVo>> resp = middleGroundFeignService.importList(enterpriseId, listVo);
        if (HttpStatus.OK.value() == resp.getCode()) {
            MiddleGroundPage<ImportListDetailVo> data = resp.getData();
            PageInfo<ImportListDetailVo> pageInfo = new PageInfo<>();
            pageInfo.setTotal(data.getPage().getTotalCount());
            pageInfo.setPageSize(listVo.getPageSize());
            pageInfo.setPageNum(listVo.getPageNumTemp());
            pageInfo.setList(resp.getData().getList());
            OrderPageUtils.addOrderPage(resp.getData().getList(), listVo.getPageNum(), listVo.getPageSize());
            return pageInfo;
        }
        throw new BusinessException("中台服务异常,请稍后重试");
    }

    /**
     　　* @description: 根据企业编码与工程分类末级业态查询符合要求的数据列表
     　　* @param [customerCode, categoryCode, positionName]
     　　* @return java.util.List<com.glodon.qydata.dto.ZbProjectStandardPositionAndDetailDto>
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/8/5 16:14
     　　*/
    private List<ZbProjectStandardDetailDto> getDetailDto(String customerCode, String categoryCode, String positionName) {
        if (StringUtils.isEmpty(categoryCode) || categoryCode.length() < SINGLE_LEVEL_LEN){
            return null;
        }
        String topCategoryCode = categoryCode.substring(0, SINGLE_LEVEL_LEN);
        // 查询建造标准
        List<ZbProjectStandard> projectStandardList = this.getEnabledListByCusAndCategoryCode(customerCode, topCategoryCode);
        if (CollectionUtils.isEmpty(projectStandardList)) {
            return null;
        }
        // 查询符合条件的新建造标准下业态与产品定位,如有多条取最后创建的一条
        List<Long> standardIdList = projectStandardList.stream().map(ZbProjectStandard::getId).collect(Collectors.toList());
        ZbStandardsBuildPosition position = standardsBuildPositionService.getPositionByCategoryAndStandardsIds(categoryCode, positionName, standardIdList);
        List<ZbStandardsBuildPositionDetail> positionDetailList = null;
        Long standardId = null;
        if (position != null) {
            standardId = position.getStandardId();
            positionDetailList = zbStandardsBuildPositionDetailMapper.selectByPositionId(position.getId());
        }
        // 无对应业态+产品定位的建造标准 ，查询业态对应的历史建造标准集合，如有多条取最后创建的一条
        List<ZbProjectStandard> equalCategoryCodeStandardList = projectStandardList.stream().filter(x -> x.getCategoryCode().equals(topCategoryCode)).collect(Collectors.toList());
        if (position == null && CollectionUtils.isNotEmpty(equalCategoryCodeStandardList)) {
            ZbProjectStandard zbProjectStandard = equalCategoryCodeStandardList.get(0);
            standardId = zbProjectStandard.getId();
        }
        // 业态+产品定位、历史建造标准均匹配不到，则匹配创建时间
        if (standardId == null) {
            ZbProjectStandard zbProjectStandard = projectStandardList.get(0);
            standardId = zbProjectStandard.getId();
        }
        if (standardId == null) {
            return null;
        }
        // 查询对应标准细则、标准说明、说明细则 并组装树状结构返回
        List<ZbProjectStandardDetail> detailList = zbProjectStandardDetailMapper.selectByStandardId(standardId);
        List<ZbStandardsBuildStandardDetailDesc> detailDescList = zbStandardsBuildStandardDetailDescMapper.selectByStandardId(standardId);
        return this.assemblePositionAndDetailDtoList(detailList, detailDescList, positionDetailList);
    }
    /**
     　　* @description: 根据企业编码与工程分类编码获取标准集合
     　　* @param [customerCode, categoryCode]
     　　* @return java.util.List<com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard>
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/8/5 17:44
     　　*/
    private List<ZbProjectStandard> getEnabledListByCusAndCategoryCode(String customerCode, String categoryCode) {
        return zbProjectStandardMapper.selectEnabledListByCusAndCategoryCode(customerCode, categoryCode);
    }

    /**
     　　* @description: 根据建造标准细则、标准说明表、建造标准定位-细则列表组装ZbProjectStandardPositionAndDetailDto集合
     　　* @param [detailList, detailDescList]
     　　* @return java.util.List<com.glodon.qydata.dto.ZbProjectStandardPositionAndDetailDto>
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/8/7 12:08
     　　*/
    private List<ZbProjectStandardDetailDto> assemblePositionAndDetailDtoList(List<ZbProjectStandardDetail> detailList,
                                                                              List<ZbStandardsBuildStandardDetailDesc> detailDescList,
                                                                              List<ZbStandardsBuildPositionDetail> positionDetailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return null;
        }
        Map<Long,List<StandardsBuildStandardPositionDescDto>> dtoByDetailIdMap = this.getDtoMapByDetailAndDescList(detailDescList,positionDetailList);
        List<ZbProjectStandardDetailDto> returnList = new ArrayList<>();
        for (ZbProjectStandardDetail detail : detailList) {
            ZbProjectStandardDetailDto dtoForDetail = new ZbProjectStandardDetailDto();
            returnList.add(dtoForDetail);
            Long detailId = detail.getId();
            String levelCode = detail.getLevelcode();
            dtoForDetail.setDetailId(detailId);
            dtoForDetail.setLevelcode(levelCode);
            dtoForDetail.setName(detail.getName());
            dtoForDetail.setItemDivisionSubjectId(detail.getItemDivisionSubjectId());
            dtoForDetail.setItemDivisionSubjectName(detail.getItemDivisionSubjectName());
            List<StandardsBuildStandardPositionDescDto> detailDtoList = null;
            // 业态+产品定位未匹配到值，并且存在标准说明表的时候，使用标准说明表的信息
            if (dtoByDetailIdMap != null && dtoByDetailIdMap.containsKey(detailId)) {
                detailDtoList = dtoByDetailIdMap.get(detailId);
                dtoForDetail.setDescription(detailDtoList);
            } else {
                // 用户在建造标准页面不点击标准说明单元格，不会主动创建标准说明记录，别的部件调用时为了保证可编辑性，需要新建一个模拟标准说明
                StandardsBuildStandardPositionDescDto newDto = new StandardsBuildStandardPositionDescDto();
                newDto.setDetailId(detailId);
                newDto.setCode("001");
                newDto.setTypeCode("text");
                detailDtoList = Collections.singletonList(newDto);
            }
            dtoForDetail.setDescription(detailDtoList);
        }
        return returnList;
    }

    /**
     　　* @description: 根据标准说明 与 业态+产品定位 获取以detailId为key的map
     　　* @param [detailDescList,positionDetailList]
     　　* @return Map<Long,List<ZbProjectStandardPositionAndDetailDto>>
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2022/8/7 13:06
     　　*/
    private Map<Long,List<StandardsBuildStandardPositionDescDto>>  getDtoMapByDetailAndDescList(List<ZbStandardsBuildStandardDetailDesc> detailDescList,
                                                                                     List<ZbStandardsBuildPositionDetail> positionDetailList){
        if (CollectionUtils.isEmpty(detailDescList)) {
            return null;
        }
        // 返回以标准细则为key,顺序排列的标准说明集合为value的map
        Map<Long,List<ZbStandardsBuildStandardDetailDesc>> detailDescByDetailIdMap =  detailDescList.stream()
                .sorted(Comparator.comparing(ZbStandardsBuildStandardDetailDesc::getOrd,Comparator.nullsFirst(Comparator.naturalOrder())))
                .collect(Collectors.groupingBy(ZbStandardsBuildStandardDetailDesc::getStandardDetailId,LinkedHashMap::new,Collectors.toList()));
        Map<Long,ZbStandardsBuildPositionDetail> positionDetailByDetailDescIdMap = null;
        if (CollectionUtils.isNotEmpty(positionDetailList)) {
            positionDetailByDetailDescIdMap = positionDetailList.stream().collect(Collectors.toMap(ZbStandardsBuildPositionDetail::getStandardDetailDescId, Function.identity(), (v1, v2) -> v2));
        }
        List<StandardsBuildStandardPositionDescDto> positionAndDetailDtoList = new ArrayList<>();
        // 业态+产品定位未匹配到值，并且存在标准说明表的时候，使用标准说明表的信息
        for (Long detailId : detailDescByDetailIdMap.keySet()) {
            List<ZbStandardsBuildStandardDetailDesc> detailDescs = detailDescByDetailIdMap.get(detailId);
            // 循环标准描述细则，获取数据类型、枚举值、层级编码（需要根据排序新生成）
            for (int i = 0; i < detailDescs.size(); i++) {
                ZbStandardsBuildStandardDetailDesc detailDesc = detailDescs.get(i);
                Long detailDescId = detailDesc.getId();
                StandardsBuildStandardPositionDescDto positionDescDto = new StandardsBuildStandardPositionDescDto();
                positionAndDetailDtoList.add(positionDescDto);
                positionDescDto.setDescId(detailDescId);
                positionDescDto.setDetailId(detailId);
                positionDescDto.setDetailDesc(detailDesc.getDetailDesc());
                positionDescDto.setTypeCode(detailDesc.getTypeCode());
                positionDescDto.setSelectList(detailDesc.getSelectList());
                positionDescDto.setCode(detailDesc.getCode());
                positionDescDto.setOrd(detailDesc.getOrd());
                if (positionDetailByDetailDescIdMap != null && positionDetailByDetailDescIdMap.containsKey(detailDescId)) {
                    ZbStandardsBuildPositionDetail positionDetail = positionDetailByDetailDescIdMap.get(detailDescId);
                    positionDescDto.setPositionId(positionDetail.getId());
                    positionDescDto.setDescValue(positionDetail.getValue());
                }
            }
        }
        return positionAndDetailDtoList.stream().collect(Collectors.groupingBy(StandardsBuildStandardPositionDescDto::getDetailId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importFromHistoricalFile(ImportFromHistoricalFileVo importVo, String enterpriseId) {
        Long globalId = RequestContent.getGlobalIdLong();
        Long standardId = importVo.getStandardId();

        // 验证能否导入并返回导入的工程分类编码
        String importCategoryCode = this.verifyImport(standardId, importVo.getCategoryCode(), importVo.getCategoryName());

        // 从中台获取历史工程的建造标准详情
        HistoricalBuildStandard historicalBuildStandard = this.getHistoricalBuildStandard(enterpriseId, importVo.getHistoricalId(), importVo.getCategoryCode());
        if (historicalBuildStandard == null){
            throw new BusinessException("没有获取到建造标准详情...");
        }

        // 清空建造标准的旧数据
        this.clearOldSelfData(standardId);

        // 导入
        this.importData(standardId, importCategoryCode, historicalBuildStandard.getStandardList());
    }

    /**
     * @description: 验证能否导入并返回导入的工程分类编码
     * @param standardId
     * @param importCategoryCode
     * @param importCategoryName
     * @return void
     * <AUTHOR>
     * @date 2022/8/24 16:25
     */
    private String verifyImport(Long standardId, String importCategoryCode, String importCategoryName){
        ZbProjectStandard zbProjectStandard = zbProjectStandardMapper.selectSelfStandardById(standardId);

        if (zbProjectStandard == null){
            throw new BusinessException("该建造标准不存在...");
        }

        // 建造标准的工程分类
        String zbProjectStandardCategoryCode = zbProjectStandard.getCategoryCode();

        // 历史建造标准工程分类升级成一级分类
        boolean updateFlag = false;
        if (StringUtils.isNotEmpty(zbProjectStandardCategoryCode) && zbProjectStandardCategoryCode.length() > Constants.CategoryConstants.LEVEL_1_LEN){
            updateFlag = true;
            zbProjectStandardCategoryCode = zbProjectStandardCategoryCode.substring(0, Constants.CategoryConstants.LEVEL_1_LEN);
        }

        // 查询一级分类下的所有工程分类（未删除、启用）
        List<CommonProjCategory> categoryList = commonProjCategoryService
                .selectByOneLevelCode(RequestContent.getCustomerCode(), zbProjectStandardCategoryCode);

        if (CollectionUtils.isNotEmpty(categoryList)){
            Map<String, CommonProjCategory> categoryMap = categoryList.parallelStream()
                    .collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, Function.identity(), (v1, v2) -> v2));

            if (updateFlag && categoryMap.containsKey(zbProjectStandardCategoryCode)){
                // 历史建造标准工程分类升级成一级分类
                this.updateStandardCategory(zbProjectStandard.getId(), zbProjectStandardCategoryCode, categoryMap.get(zbProjectStandardCategoryCode).getCategoryname());
            }

            if (categoryMap.containsKey(importCategoryCode)) {
                // 按编码匹配
                return importCategoryCode;
            }

            // 按名称匹配
            Map<String, CommonProjCategory> categoryNameMap = categoryList.parallelStream()
                    .collect(Collectors.toMap(CommonProjCategory::getCategoryname, Function.identity(),
                            (v1, v2) -> v1.getLevel().compareTo(v2.getLevel()) > 0 ? v2 : v1));

            if (categoryNameMap.containsKey(importCategoryName)){
                return categoryNameMap.get(importCategoryName).getCommonprojcategoryid();
            }
        }

        throw new BusinessException("拟导入的业态在当前建造标准一级工程分类下不存在，请在当前建造标准一级工程分类下添加拟导入的业态后再试。");
    }

    /**
     * @description: 历史建造标准工程分类升级成一级分类
     * @param standardId
     * @param categoryCode
     * @param categoryName
     * @return void
     * <AUTHOR>
     * @date 2022/8/29 16:48
     */
    private void updateStandardCategory(Long standardId, String categoryCode, String categoryName){
        ZbProjectStandard update = new ZbProjectStandard();
        update.setId(standardId);
        update.setCategoryCode(categoryCode);
        update.setCategoryName(categoryName);
        zbProjectStandardMapper.updateSelfStandardById(update);
    }

    /**
     * @description: 导入
     * @param historicalStandardList
     * @return void
     * <AUTHOR>
     * @date 2022/8/24 14:49
     */
    private void importData(Long standardId, String importCategoryCode, List<BuildStandard> historicalStandardList){
        if (CollectionUtils.isEmpty(historicalStandardList)){
            return;
        }

        // 层级编码和细则id
        Map<String, Long> levelCodeAndDetailIdMap = new HashMap<>();
        // 细则的排序
        int detailOrd = 1;
        // 层级编码和说明排序
        Map<String, Integer> levelCodeAndDescOrdMap = new HashMap<>();

        // 产品业态
        Long buildCategoryId = SnowflakeIdUtils.getNextId();
        ZbStandardsBuildCategory buildCategory = this.buildCategorySelf(standardId, buildCategoryId, importCategoryCode);
        standardsBuildCategoryService.batchSaveSelf(new ArrayList<>(Collections.singletonList(buildCategory)));

        // 产品定位
        Long buildPositionId = SnowflakeIdUtils.getNextId();
        ZbStandardsBuildPosition buildPosition = this.buildPositionSelf(standardId, buildCategoryId, importCategoryCode, buildPositionId);
        zbStandardsBuildPositionMapper.batchSaveSelf(new ArrayList<>(Collections.singletonList(buildPosition)));

        // 入库集合
        List<ZbProjectStandardDetail> detailSelfList = new ArrayList<>();
        List<ZbStandardsBuildStandardDetailDesc> detailDescSelfList = new ArrayList<>();
        List<ZbStandardsBuildPositionDetail> positionDetailSelfList = new ArrayList<>();

        for (BuildStandard historicalStandard : historicalStandardList) {
            // 层级编码
            String templateLevelCode = historicalStandard.getTemplateLevelCode();
            if (StringUtils.isEmpty(templateLevelCode)){
                continue;
            }

            // 生成细则id
            Long standardDetailId = SnowflakeIdUtils.getNextId();

            // 标准细则
            if (levelCodeAndDetailIdMap.putIfAbsent(templateLevelCode, standardDetailId) == null){
                ZbProjectStandardDetail detailSelf = this.buildDetailSelf(standardId, standardDetailId, historicalStandard);
                detailSelf.setOrd(detailOrd ++);
                detailSelfList.add(detailSelf);
            }

            if (StringUtils.isNotEmpty(historicalStandard.getStandardDescription()) || StringUtils.isNotEmpty(historicalStandard.getShowType())){
                Long currDetailId = levelCodeAndDetailIdMap.get(templateLevelCode);

                // 标准说明
                Long descId = SnowflakeIdUtils.getNextId();
                ZbStandardsBuildStandardDetailDesc detailDescSelf =
                        this.buildDetailDescSelf(standardId, historicalStandard, descId, currDetailId, levelCodeAndDescOrdMap);
                detailDescSelfList.add(detailDescSelf);

                // 产品定位值
                ZbStandardsBuildPositionDetail positionDetail =
                        this.buildPositionDetailSelf(standardId, currDetailId, descId, buildPositionId, historicalStandard.getStandardValue());
                positionDetailSelfList.add(positionDetail);
            }
        }

        // 入库
        if (CollectionUtils.isNotEmpty(detailSelfList)){
            zbProjectStandardDetailMapper.saveSelfBatch(detailSelfList);
        }
        if (CollectionUtils.isNotEmpty(detailDescSelfList)){
            zbStandardsBuildStandardDetailDescMapper.batchSaveSelf(detailDescSelfList);
        }
        if (CollectionUtils.isNotEmpty(positionDetailSelfList)){
            standardsBuildPositionDetailService.batchSaveSelf(positionDetailSelfList);
        }
    }

    /**
     * @description: 构建标准说明
     * @param standardId
     * @param buildStandard
     * @param descId
     * @param detailId
     * @param levelCodeAndDescOrdMap
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc
     * <AUTHOR>
     * @date 2022/8/24 15:12
     */
    private ZbStandardsBuildStandardDetailDesc buildDetailDescSelf(Long standardId,
                                                                   BuildStandard buildStandard,
                                                                   Long descId,
                                                                   Long detailId,
                                                                   Map<String, Integer> levelCodeAndDescOrdMap){
        String templateLevelCode = buildStandard.getTemplateLevelCode();

        ZbStandardsBuildStandardDetailDesc detailDescSelf = new ZbStandardsBuildStandardDetailDesc();
        detailDescSelf.setId(descId);
        detailDescSelf.setDetailDesc(buildStandard.getStandardDescription());
        detailDescSelf.setStandardId(standardId);
        detailDescSelf.setStandardDetailId(detailId);

        // typeCode
        detailDescSelf.setTypeCode(this.convertTypeCode(buildStandard.getShowType(), buildStandard.getValueType()));

        // 枚举值
        detailDescSelf.setSelectList(this.convertSelectList(buildStandard.getDefaultStandardValue()));

        // ord
        if (levelCodeAndDescOrdMap.containsKey(templateLevelCode)) {
            Integer ord = levelCodeAndDescOrdMap.get(templateLevelCode);
            detailDescSelf.setOrd(++ ord);
            levelCodeAndDescOrdMap.put(templateLevelCode, ord);
        }else {
            detailDescSelf.setOrd(1);
            levelCodeAndDescOrdMap.put(templateLevelCode, 1);
        }

        return detailDescSelf;
    }

    /**
     * @description: 构建建造标准细则
     * @param standardId
     * @param standardDetailId
     * @param buildStandard
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail
     * <AUTHOR>
     * @date 2022/8/24 15:04
     */
    private ZbProjectStandardDetail buildDetailSelf(Long standardId, Long standardDetailId, BuildStandard buildStandard){
        ZbProjectStandardDetail detailSelf = new ZbProjectStandardDetail();
        detailSelf.setId(standardDetailId);
        detailSelf.setStandardId(standardId);
        detailSelf.setLevelcode(buildStandard.getTemplateLevelCode());
        detailSelf.setName(buildStandard.getName());
        detailSelf.setLevel(buildStandard.getTemplateLevelCode().length() / SINGLE_LEVEL_LEN);
        detailSelf.setRemark(buildStandard.getRemark());
        return detailSelf;
    }

    /**
     * @description: 产品业态
     * @param standardId
     * @param buildCategoryId
     * @param importCategoryCode
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildCategory
     * <AUTHOR>
     * @date 2022/8/24 17:58
     */
    private ZbStandardsBuildCategory buildCategorySelf(Long standardId, Long buildCategoryId, String importCategoryCode){
        ZbStandardsBuildCategory buildCategory = new ZbStandardsBuildCategory();
        buildCategory.setId(buildCategoryId);
        buildCategory.setStandardId(standardId);
        buildCategory.setCategoryCode(importCategoryCode);
        buildCategory.setOrd(1);
        return buildCategory;
    }

    private ZbStandardsBuildPosition buildPositionSelf(Long standardId, Long buildCategoryId, String importCategoryCode, Long buildPositionId){
        ZbStandardsBuildPosition buildPosition = new ZbStandardsBuildPosition();
        buildPosition.setId(buildPositionId);
        buildPosition.setBuildCategoryId(buildCategoryId);
        buildPosition.setStandardId(standardId);
        buildPosition.setType(Constants.CATEGORY);
        buildPosition.setCategoryCode(importCategoryCode);
        return buildPosition;
    }

    private ZbStandardsBuildPositionDetail buildPositionDetailSelf(Long standardId, Long detailId, Long descId, Long buildPositionId, String value){
        ZbStandardsBuildPositionDetail positionDetail = new ZbStandardsBuildPositionDetail();
        positionDetail.setId(SnowflakeIdUtils.getNextId());
        positionDetail.setPositionId(buildPositionId);
        positionDetail.setStandardDetailDescId(descId);
        positionDetail.setStandardDetailId(detailId);
        positionDetail.setStandardId(standardId);
        positionDetail.setValue(value);
        return positionDetail;
    }

    /**
     * 标准说明数据类型：基础信息库->目标成本：
     * select->radio(从showType中取)  selects->checkBox(从showType中取)  text->text(从valueType中取)  number->number(从valueType中取)
     * 基础信息库数据类型： text文本类，number数值类，select单选类，selects多选类
     * @param showType 前端显示 组件类型：checkBox、radio、select、 input
     * @param valueType 值类型 text number percent
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/8/24 14:15
     */
    private String convertTypeCode(String showType, String valueType){
        log.info("showType:{},valueType:{}",showType,valueType);
        if (StringUtils.isNotEmpty(showType)){
            if ("radio".equals(showType)){
                return Constants.ZbExpressionConstants.TYPE_SELECT;
            }

            if ("checkBox".equals(showType)){
                return Constants.ZbExpressionConstants.TYPE_SELECTS;
            }
        }

        if ("number".equals(valueType)){
            return Constants.ZbExpressionConstants.TYPE_NUMBER;
        }

        return Constants.ZbExpressionConstants.TYPE_TEXT;
    }

    /**
     * @description: 枚举值装换
     * 目标成本：[{"label":"test1","value":"test1"},{"label":"test2","value":"test2"}]
     * 基础信息库：[{"name":"铝板幕墙","isDeleted":0}]
     * @param defaultStandardValue
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/8/24 14:44
     */
    private String convertSelectList(String defaultStandardValue){
        if (StringUtils.isBlank(defaultStandardValue)){
            return null;
        }
        log.info("convertSelectList defaultStandardValue:{}", defaultStandardValue);

        List<JSONObject> defaultStandardValueList = new ArrayList<>();
        List<JSONObject> selectList = new ArrayList<>();
        try {
            defaultStandardValueList = JSONArray.parseArray(defaultStandardValue, JSONObject.class);
        } catch (Exception e) {
            log.error("convertSelectList 转换异常:{}", defaultStandardValue);
        }
        if (CollUtil.isEmpty(defaultStandardValueList)) {
            return selectList.toString();
        }
        for (JSONObject defaultSelectValue : defaultStandardValueList) {
            JSONObject selectValue = new JSONObject();
            if (StringUtils.isNotBlank(defaultSelectValue.getString("value"))) {
                selectValue.put("name", defaultSelectValue.getString("value"));
                selectValue.put("isDeleted", Constants.DEL_STATUS_NO_DEL);
            }
            selectList.add(selectValue);
        }

        return selectList.toString();
    }

    private HistoricalBuildStandard getHistoricalBuildStandard(String enterpriseId, Long historicalId, String categoryCode){
        HistoricalBuildStandard historicalBuildStandard = null;
        // 从中台获取历史工程的建造标准详情
        try {
            log.info("调用中台获取建造标准详情入参：enterpriseId：{}，historicalId：{}，categoryCode：{}", enterpriseId, historicalId, categoryCode);
            MiddleGroundResp<HistoricalBuildStandard> buildStandardDetail = middleGroundFeignService.getBuildStandardDetail(enterpriseId, historicalId, categoryCode, 0);
            log.info("调用中台获取建造标准详情返回值：{}", buildStandardDetail);
            if (ResponseCode.SUCCESS.getCode().equals(buildStandardDetail.getCode())) {
                historicalBuildStandard = buildStandardDetail.getData();
            }
        }catch (Exception e){
            log.error("调用中台获取建造标准详情出错了", e);
        }

        return historicalBuildStandard;
    }

    /**
     * @description: 清空建造标准的旧数据
     * @param standardId
     * @return void
     * <AUTHOR>
     * @date 2022/8/22 17:36
     */
    private void clearOldSelfData(Long standardId){
        // 清空建造标准的旧数据
        List<Long> selfStandardIds = new ArrayList<>(Collections.singleton(standardId));
        // 删除-标准细则-暂存表数据
        zbProjectStandardDetailMapper.deleteSelfByStandardIds(selfStandardIds);
        // 删除-标准说明-暂存表数据
        standardsBuildStandardDetailDescService.delSelfByStandardIds(selfStandardIds);
        // 删除-业态备注信息-暂存表数据
        standardsBuildCategoryService.delSelfByStandardIds(selfStandardIds);
        // 删除-产品定位-暂存表数据
        standardsBuildPositionService.delSelfByStandardIds(selfStandardIds);
        // 删除-产品定位值-暂存表数据
        standardsBuildPositionDetailService.delSelfByStandardIds(selfStandardIds);
    }

}
