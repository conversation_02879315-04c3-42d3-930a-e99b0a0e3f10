package com.glodon.qydata.controller.temp.autoteam;

import com.glodon.qydata.vo.common.ResponseVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/basicInfo/openApi/autoTeam")
public class AutoTeamDeleteErrorDataController {

    @Autowired
    private AutoTeamDeleteErrorDataService autoTeamDeleteErrorDataService;

    /**
     * 自动化跑数据会导致工程特征的分类视图数据错误的问题。自动化团队暂时定位不到原因，故临时提供给一个删除工程特征的接口，使数据重新走初始化。
     * 涉及工程专业、工程特征、主要量指标的主表及暂存数据
     */
    @GetMapping("/deleteFeatureErrorRelationData")
    public ResponseVo deleteFeatureErrorRelationData(){
        autoTeamDeleteErrorDataService.deleteFeatureErrorRelationData();
        return ResponseVo.success();
    }
}
