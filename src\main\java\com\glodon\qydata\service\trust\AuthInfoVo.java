package com.glodon.qydata.service.trust;/**
 * @author: yhl
 * @DateTime: 2023/2/14 16:35
 * @Description:
 */

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: yhl
 * @DateTime: 2023/2/14 16:35
 * @Description:
 */
@Data
@Schema(name = "数据权限", description = "数据权限")
public class AuthInfoVo implements Serializable {
    private static final long serialVersionUID = -5649830710277094793L;

    @Schema(name ="是否有权限", description = "是否有权限，true:有权限，false：无权限")
    private Boolean hasAuth;
}
