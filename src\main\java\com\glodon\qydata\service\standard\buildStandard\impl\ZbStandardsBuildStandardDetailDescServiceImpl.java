package com.glodon.qydata.service.standard.buildStandard.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ExpressionTypeEnum;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildStandardDetailDescMapper;
import com.glodon.qydata.service.standard.buildStandard.ZbStandardsBuildPositionDetailService;
import com.glodon.qydata.service.standard.buildStandard.ZbStandardsBuildStandardDetailDescService;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.buildStandard.DetailDescAddVo;
import com.glodon.qydata.vo.standard.buildStandard.DetailDescUpdateVo;
import com.glodon.qydata.vo.standard.buildStandard.SelectListVO;
import com.glodon.qydata.vo.standard.buildStandard.StandardsBuildStandardDetailDescVo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.DEL_STATUS_NO_DEL;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_standard_detail_desc((企业标准数据-建造标准-标准说明表))】的数据库操作Service实现
* @createDate 2022-07-29 19:03:33
*/
@Service
public class ZbStandardsBuildStandardDetailDescServiceImpl extends ServiceImpl<ZbStandardsBuildStandardDetailDescMapper, ZbStandardsBuildStandardDetailDesc>
implements ZbStandardsBuildStandardDetailDescService {

    private static final int MAX_SELECT_LIST_SIZE = 20;

    private static final int MAX_SELECT_LIST_ITEM_SIZE = 2000;
    @Autowired
    private ZbStandardsBuildStandardDetailDescMapper zbStandardsBuildStandardDetailDescMapper;
    @Autowired
    private ZbStandardsBuildPositionDetailService zbStandardsBuildPositionDetailService;

    /**
     * @description: 标准说明上下移
     * @param flag 上下移标识，1：上移，2：下移
     * @param descId
     * @param standardDetailId
     * @return com.glodon.qydata.vo.common.ResponseVo<java.util.List < com.glodon.qydata.dto.ZbProjectStandardDetailTreeDto>>
     * <AUTHOR>
     * @date 2022/8/4 9:42
     */
    @Override
    public Long descMoveUpDown(Integer flag, Long descId, Long standardDetailId) {
        // 查询细则下的标准说明ord升序
        List<ZbStandardsBuildStandardDetailDesc> descList = zbStandardsBuildStandardDetailDescMapper.selectSelfByDetailIdIAsc(standardDetailId);

        if (CollectionUtils.isEmpty(descList)){
            return null;
        }

        Map<Long, ZbStandardsBuildStandardDetailDesc> idAndOrd = descList.stream()
                .collect(Collectors.toMap(ZbStandardsBuildStandardDetailDesc::getId, Function.identity(), (v1, v2) -> v2));

        List<Long> idList = descList.stream().map(ZbStandardsBuildStandardDetailDesc::getId).collect(Collectors.toList());

        // 排序校验并返回互换位置的id
        Long anchorId = this.descMoveUpDownValidityCheck(flag, descId, idList);

        // 互换ord
        ZbStandardsBuildStandardDetailDesc desc = idAndOrd.get(descId);
        ZbStandardsBuildStandardDetailDesc anchor = idAndOrd.get(anchorId);

        Integer tempOrd = desc.getOrd();
        desc.setOrd(anchor.getOrd());
        anchor.setOrd(tempOrd);

        List<ZbStandardsBuildStandardDetailDesc> updateList = new ArrayList<>();
        updateList.add(desc);
        updateList.add(anchor);

        zbStandardsBuildStandardDetailDescMapper.batchUpdateOrd(updateList);

        return desc.getStandardId();
    }

    @Override
    public List<ZbStandardsBuildStandardDetailDesc> selectSelfByStandardIds(List<Long> standIds) {
        return zbStandardsBuildStandardDetailDescMapper.selectSelfByStandardIds(standIds);
    }

    @Override
    public void delByStandardIds(List<Long> allOriginIds) {
        zbStandardsBuildStandardDetailDescMapper.batchDelByStandardIds(allOriginIds);
    }

    @Override
    public void delSelfByStandardIds(List<Long> allSelfStandardIds) {
        zbStandardsBuildStandardDetailDescMapper.batchDelSelfByStandardIds(allSelfStandardIds);
    }

    /**
     * @description: 排序校验并返回互换位置的id
     * @param flag
     * @param selectedId
     * @param idList
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2022/8/4 11:16
     */
    private Long descMoveUpDownValidityCheck(Integer flag, Long selectedId, List<Long> idList) {

        if (flag.equals(Constants.MOVE_UP)) {
            if (idList.get(0).equals(selectedId)) {
                throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "同级的第一行数据无法上移");
            }
            int index = 0;
            for (int i = 0; i < idList.size(); i++) {
                if (idList.get(i).equals(selectedId)) {
                    index = i - 1;
                    break;
                }
            }
            //返回用户所选行同级的前一行数据
            return idList.get(index);
        } else {
            if (idList.get(idList.size() - 1).equals(selectedId)) {
                throw new BusinessException(ResponseCode.PARAMETER_ERROR.getCode(), "同级的最后一行数据无法下移");
            }
            int index = 0;
            for (int i = 0; i < idList.size(); i++) {
                if (idList.get(i).equals(selectedId)) {
                    index = i + 1;
                    break;
                }
            }
            //返回用户所选行同级的后一行数据
            return idList.get(index);
        }
    }

    /**
     * @description: 新增标准说明
     * @param addVo
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard
     * <AUTHOR>
     * @date 2022/8/5 14:53
     */
    @Override
    public StandardsBuildStandardDetailDescVo addDesc(DetailDescAddVo addVo) {
        Long standardDetailId = addVo.getStandardDetailId();
        // 查询细则下的标准说明
        List<ZbStandardsBuildStandardDetailDesc> descList = zbStandardsBuildStandardDetailDescMapper.selectSelfByDetailIdIAsc(standardDetailId);

        Long id = SnowflakeIdUtils.getNextId();

        // 判重
        if (StringUtils.isNotEmpty(addVo.getDetailDesc())){
            this.checkRepeat(id, addVo.getDetailDesc(), descList);
        }

        ZbStandardsBuildStandardDetailDesc newDesc = new ZbStandardsBuildStandardDetailDesc();
        BeanUtils.copyProperties(addVo, newDesc);
        newDesc.setId(id);
        newDesc.setStandardId(addVo.getStandardId());
        newDesc.setStandardDetailId(standardDetailId);
        if (StringUtils.isEmpty(newDesc.getTypeCode())){
            newDesc.setTypeCode(ExpressionTypeEnum.TYPE_TEXT.getCode());
        }
        newDesc.setOrd(this.setDescOrd(addVo.getCheckedDescId(), descList));
        newDesc.setCode(this.setDescCode(descList));
        newDesc.setName(this.setDescName(descList));

        zbStandardsBuildStandardDetailDescMapper.insertSelf(newDesc);

        // 插入或修改定位的值
        zbStandardsBuildPositionDetailService.insertOrUpdateSelf(addVo.getStandardId(), standardDetailId, id, addVo.getValues());

        return this.returnDescVo(id);
    }

    /**
     * @description: 设置标准说明ord
     * @param descId
     * @param descList
     * @return int
     * <AUTHOR>
     * @date 2022/8/5 16:08
     */
    private int setDescOrd(Long descId, List<ZbStandardsBuildStandardDetailDesc> descList){
        int ord = 0;

        if (CollectionUtils.isNotEmpty(descList)) {
            // 没有选中标准说明就放到最后
            if (descId == null){
                ord = descList.stream().map(ZbStandardsBuildStandardDetailDesc::getOrd).max(Comparator.comparingInt(o -> o)).orElse(0);
            }else {
                // 有选中标准说明，新增的放到选中的后面，后面的标准说明ord+1
                int tempOrd = 0;
                for (ZbStandardsBuildStandardDetailDesc standardDetailDesc : descList) {
                    standardDetailDesc.setOrd(++tempOrd);

                    if (standardDetailDesc.getId().equals(descId)) {
                        ord = tempOrd++;
                    }
                }

                zbStandardsBuildStandardDetailDescMapper.batchUpdateOrd(descList);
            }
        }

        return ord + 1;
    }

    /**
     * @description: 设置标准说明code
     * @param descList
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/8/5 16:09
     */
    private String setDescCode(List<ZbStandardsBuildStandardDetailDesc> descList){
        int code = 0;

        if (CollectionUtils.isNotEmpty(descList)) {
            code = descList.parallelStream()
                    .map(ZbStandardsBuildStandardDetailDesc::getCode)
                    .filter(Objects::nonNull)
                    .filter(string -> string.matches("^[0-9]{3}$"))
                    .map(Integer::parseInt).max(Comparator.comparingInt(o -> o)).orElse(0);
        }

        return String.format("%03d", code + 1);
    }

    private String setDescName(List<ZbStandardsBuildStandardDetailDesc> descList){
        int code = 0;

        if (CollectionUtils.isNotEmpty(descList)) {
            code = descList.parallelStream().map(ZbStandardsBuildStandardDetailDesc::getName)
                    .filter(string -> string != null && string.matches("^" + Constants.DESC_PREFIX + "[0-9]{0,3}$"))
                    .map(string -> string.replace(Constants.DESC_PREFIX, ""))
                    .filter(string -> string.equals(Integer.parseInt(string) + ""))
                    .map(Integer::parseInt)
                    .max(Comparator.comparingInt(o -> o)).orElse(0);
        }

        return Constants.DESC_PREFIX + ++code ;
    }

    @Override
    public StandardsBuildStandardDetailDescVo updateDesc(DetailDescUpdateVo updateVo) {
         Long descId = updateVo.getId();

         // 该细则下的所有标准说明
         Long standardDetailId = updateVo.getStandardDetailId();

         List<ZbStandardsBuildStandardDetailDesc> descList = zbStandardsBuildStandardDetailDescMapper.selectSelfByDetailIdIAsc(standardDetailId);

         if (CollectionUtils.isEmpty(descList)){
            throw new BusinessException("该标准细则下没有标准说明");
         }

         Map<Long, ZbStandardsBuildStandardDetailDesc> idAndDesc = descList.parallelStream()
                 .collect(Collectors.toMap(ZbStandardsBuildStandardDetailDesc::getId, Function.identity(), (v1, v2) -> v2));

         if (!idAndDesc.containsKey(descId)){
            throw new BusinessException("该标准细则下不存在此标准说明");
         }

         // 标准说明判重
         this.checkRepeat(descId, updateVo.getDetailDesc(), descList);

        // 更新入库
        ZbStandardsBuildStandardDetailDesc detailDesc = new ZbStandardsBuildStandardDetailDesc();
        BeanUtils.copyProperties(updateVo, detailDesc);
        zbStandardsBuildStandardDetailDescMapper.updateByPrimaryKey(detailDesc);

        // 插入或修改定位的值
        zbStandardsBuildPositionDetailService.insertOrUpdateSelf(updateVo.getStandardId(), standardDetailId, descId, updateVo.getValues());

        // 返回vo
        return this.returnDescVo(descId);
    }

    /**
     * 检查建造标准枚举值是否合规
     * <AUTHOR>
     * @date 2023-05-15 11:22:41
     * @param updateVo
     * @return void
     */
    private void checkAndFilterSelectList(DetailDescUpdateVo updateVo){
        if (StringUtils.isBlank(updateVo.getSelectList())) {
            return;
        }
        List<SelectListVO> selectListVOList = JSON.parseArray(updateVo.getSelectList(), SelectListVO.class);
        if (CollectionUtils.isEmpty(selectListVOList)) {
            return;
        }
        List<SelectListVO> noDeleted =
                selectListVOList.stream().filter(e -> Objects.equals(DEL_STATUS_NO_DEL, e.getIsDeleted())).collect(Collectors.toList());
        if (noDeleted.size() > Constants.MAX_OPTION_NUM) {
            throw new BusinessException("未删除枚举值个数超过上限:" + Constants.MAX_OPTION_NUM);
        }
        int noDeletedItemSize = noDeleted.stream().filter(e -> StringUtils.isNotBlank(e.getName())).mapToInt(e -> e.getName().length()).sum();
        if (noDeletedItemSize > Constants.MAX_OPTION_LENGTH) {
            throw new BusinessException("未删除枚举值总字符数" + noDeletedItemSize + "超过上限:" + Constants.MAX_OPTION_LENGTH);
        }
        try {
            updateVo.setSelectList(new ObjectMapper().writeValueAsString(noDeleted));
        } catch (JsonProcessingException e){
            throw new BusinessException("转换成字符串失败");
        }
    }

    private StandardsBuildStandardDetailDescVo returnDescVo(Long descId){
        StandardsBuildStandardDetailDescVo vo = new StandardsBuildStandardDetailDescVo();
        ZbStandardsBuildStandardDetailDesc newDetailDesc = zbStandardsBuildStandardDetailDescMapper.selectSelfById(descId);
        BeanUtils.copyProperties(newDetailDesc, vo);
        vo.setValues(zbStandardsBuildPositionDetailService.selectSelfByDescId(descId));
        return vo;
    }

    private void checkRepeat(Long descId, String detailDescStr, List<ZbStandardsBuildStandardDetailDesc> descList){
        if (StringUtils.isNotEmpty(detailDescStr)){
            long count = descList.parallelStream()
                    .filter(x -> !descId.equals(x.getId()) && detailDescStr.equals(x.getDetailDesc())).count();
            if (count != 0){
                throw new BusinessException("该标准细则下已存在重复的标准说明");
            }
        }
    }

}
