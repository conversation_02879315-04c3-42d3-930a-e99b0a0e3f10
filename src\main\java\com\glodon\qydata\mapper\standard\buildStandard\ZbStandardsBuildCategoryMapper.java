package com.glodon.qydata.mapper.standard.buildStandard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildCategory;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_category】的数据库操作Mapper
* @createDate 2022-08-01 10:57:17
* @Entity generator.domain.ZbStandardsBuildCategory
*/
@Repository
public interface ZbStandardsBuildCategoryMapper extends BaseMapper<ZbStandardsBuildCategory> {

    void batchSaveSelf(List<ZbStandardsBuildCategory> list);

    /**
     * 根据建造标准查询数据
     * @param originStandardId
     * @return
     */
    List<ZbStandardsBuildCategory> selectByStandardId(Long originStandardId);
    /**
     * 根据建造标准查询数据 暂存信息
     * @param standardId
     * @return
     */
    List<ZbStandardsBuildCategory> selectSelfByStandardId(@Param("standardId") Long standardId);

    /**
     * 根据建造标准id删除备注表
     * @param standardId
     */
    void delSelfByStandardId(Long standardId);

    /**
     * 根据建造标准id批量查询数据
     * @param standIds
     * @return
     */
    List<ZbStandardsBuildCategory> selectSelfByStandardIds(@Param("list") List<Long> standIds);

    /**
     * 根据建造标准id批量删除数据
     * @param standardIds
     */
    void delByStandardIds(List<Long> standardIds);

    /**
     * 批量保存企业数据
     * @param buildCategories
     */
    void batchSave(List<ZbStandardsBuildCategory> buildCategories);

    /**
     * 批量删除
     * @param standardIds
     */
    void delSelfByStandardIds(List<Long> standardIds);

    List<ZbStandardsBuildCategory> selectByStandardIds(List<Long> standardIds);
}




