package com.glodon.qydata.vo.system;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class EventTracking implements Serializable {
	private static final long serialVersionUID = 3240080939433419386L;
	private Integer id;

	private String fngroup;  //功能分组

	private String fnname;  //功能名称

	private Integer fncode;  //功能码

	private String productName;  //产品名称

	private String utilityRatio;  //应用率

	@DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
	private Date createAt;  //添加时间
}
