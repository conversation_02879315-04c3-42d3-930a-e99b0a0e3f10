package com.glodon.qydata.mapper.standard.mainQuantity;

import com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity;
import com.glodon.qydata.util.mover.ElementMover;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 *标准打通--主要量指标mapper
 * <AUTHOR>
 */
@Repository
public interface ZbStandardsMainQuantityMapper extends ElementMover.DatabaseSaver<ZbStandardsMainQuantity> {
    /**
    　　* @description: 单条数据插入
    　　* @param  ZbStandardsMainQuantity 要插入主要量指标记录
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/26 11:22
    　　*/
    int insert(ZbStandardsMainQuantity recordTemp);

    /**
     　　* @description: 有条件单条数据插入
     　　* @param  ZbStandardsMainQuantity 要插入主要量指标记录
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/26 11:22
     　　*/
    int insertSelective(ZbStandardsMainQuantity recordTemp);

    /**
    　　* @description: 根据企业编码与名称查找记录
    　　* @param  customerCode:企业编码 name：名称  tradeId:专业id
    　　* @return ZbStandardsMainQuantity 查找到的记录条数
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/26 15:33
    　　*/
    ZbStandardsMainQuantity searchByTradeIdAndName(@Param("description") String name,@Param("tradeId") Long tradeId);

    /**
    　　* @description: 根据专业id与企业编码查询未删除的主要量指标列表
    　　* @param  String customerCode Long tradeId
    　　* @return List<ZbStandardsMainQuantity> 查询到的主要量指标列表
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/26 16:31
    　　*/
    List<ZbStandardsMainQuantity> selectByCusAndTradeIdNoDelNoDel(@Param("customerCode") String customerCode,@Param("tradeId") Long tradeId);

    /**
     　　* @description: 根据专业编码与企业编码查询未删除的主要量指标列表
        * @param  tradeCode  专业编码
       * @param  customerCode  企业编码
     　　* @return List<ZbStandardsMainQuantity> 查询到的主要量指标列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/26 16:31
     　　*/
    List<ZbStandardsMainQuantity> selectByCusAndTradeCodeNoDelNoDel(@Param("customerCode") String customerCode,@Param("tradeCode") String tradeCode);

    /**
     　　* @description: 根据专业id与企业编码查询所有状态下的主要量指标列表
     * @param  tradeCode  专业编码
     * @param  customerCode  企业编码
     　　* @return List<ZbStandardsMainQuantity> 查询到的主要量指标列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/26 16:31
     　　*/
    List<ZbStandardsMainQuantity> selectAllByCusAndTradeCode(@Param("customerCode") String customerCode,@Param("tradeCode") String tradeCode);
    /**
    　　* @description: 批量保存
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/26 16:40
    　　*/
    void batchInsert(List<ZbStandardsMainQuantity> list);

    /**
     　　* @description: 批量更新
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/26 16:40
     　　*/
    void batchUpdate(List<ZbStandardsMainQuantity> list);

    /**
    　　* @description: 单个逻辑删除主要量指标数据
    　　* @param  id
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/28 8:45
    　　*/
    void deleteByIds(String[] ids);

    /**
     * 删除数据时更新后面的所有数据ord-1
     * @throws
     * @param mainQuantity
     * @param customerCode
     * <AUTHOR>
     * @return
     * @date 2021/11/22 11:36
     */
    void updateOrdOneMinusBehind(@Param("entity") ZbStandardsMainQuantity mainQuantity, @Param("customerCode") String customerCode);

    /**
    　　* @description: 根据企业编码集合删除集合数据
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/12/3 0:08
    　　*/
    void deleteByCustomerCode(@Param("customerCode") String customerCode);

    /**
     　　* @description: 删除专业下所有主要量指标
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/3 0:08
     　　*/
    void deleteByTradeId(@Param("tradeId") Long tradeId);

    List<ZbStandardsMainQuantity> selectSelfById(String[] ids);

    /**
     　　* @description: 根据专业id与企业编码查询未删除的主要量指标列表
     　　* @param  String customerCode Long tradeId
     　　* @return List<ZbStandardsMainQuantity> 查询到的主要量指标列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/26 16:31
     　　*/
    List<ZbStandardsMainQuantity> selectSelfByCusAndTradeIdNoDelNoDel(@Param("customerCode") String customerCode,
                                                                      @Param("tradeId") Long tradeId,
                                                                      @Param("isShowDelete") Integer isShowDelete);

    void updateSelfById(ZbStandardsMainQuantity entity);

    /**
     * 发布更新数据
     * @param list
     */
    void batchUpdatePublish(List<ZbStandardsMainQuantity> list);

    /**
     * 删除暂存数据
     * @param customerCode
     */
    void deleteSelfByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 批量插入暂存表
     * @param list
     */
    void selfBatchInsert(List<ZbStandardsMainQuantity> list);

    /**
     * 批量更新ord
     * @param list
     */
    @Override
    void batchUpdateOrd(@Param("list") List<ZbStandardsMainQuantity> list);
}