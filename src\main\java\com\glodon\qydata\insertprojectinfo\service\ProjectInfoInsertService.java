package com.glodon.qydata.insertprojectinfo.service;

import com.glodon.qydata.controller.temp.projectinfo.entity.Prams;
import com.glodon.qydata.insertprojectinfo.entity.Global;
import com.glodon.qydata.insertprojectinfo.entity.StandardsProjectInfoEntity;
import com.glodon.qydata.insertprojectinfo.mapper.ProjectInfoMapper;
import com.glodon.qydata.util.EmptyUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
@SuppressWarnings("squid:S1192") // 禁止sonar魔法值检测
public class ProjectInfoInsertService {
    private static final List<String> PROJECT_INFO_INSERT_NAME_LIST = Arrays.asList(
            "建设用地面积", "建筑占地面积", "总建筑面积", "精装修面积", "可售比", "总计容面积", "规划容积率",
            "建筑密度", "建筑限高", "总户数", "抗震设防度数", "装配率", "PC单体建筑预制率", "绿色建筑等级",
            "市政道路面积", "景观园林面积", "园区出入口数量", "绿地率", "车位数", "车位配比", "充电桩数量", "耐火等级");
    private static final int BATCH_COUNT = 1000;
    private static final String TABLE_INFO = "zb_standards_project_info";
    private static final String TABLE_INFO_SELF = "zb_standards_project_info_self";

    public void delInvalidProjectInfo() {
        delSelfInvalidProjectInfo();
        delMainInvalidProjectInfo();
    }

    private void delMainInvalidProjectInfo() {
        int totalCount = 0;
        int finishCount = 0;
        List<String> invalidCustomers = new ArrayList<>();
        List<String> customerCodeList = projectInfoMapper.searchCustomerCodeWithInvalidProjectInfo();
        totalCount = customerCodeList.size();
        log.info("共需处理企业数量：{}", totalCount);
        for (String customerCode : customerCodeList) {
            if (isValidCustomer(customerCode)) {
                log.info("***发现企业是有效数据，企业编码为：{}", customerCode);
                continue;
            }
            invalidCustomers.add(customerCode);
            if (invalidCustomers.size() >= BATCH_COUNT) {
                projectInfoMapper.deleteProjectInfoRecords(invalidCustomers);
                finishCount += invalidCustomers.size();
                invalidCustomers.clear();
                log.info("已处理企业数量：{}", finishCount);
            }
        }
        projectInfoMapper.deleteProjectInfoRecords(invalidCustomers);
        log.info("全部处理完成，共处理数量：{}", finishCount + invalidCustomers.size());
    }

    private void delSelfInvalidProjectInfo() {
        int totalCount = 0;
        int finishCount = 0;
        List<Global> invalidUsers = new ArrayList<>(BATCH_COUNT);
        List<Global> users = projectInfoMapper.searchUsersWithInvalidProjectInfo();
        totalCount = users.size();
        log.info("共需处理用户数量：{}", totalCount);
        for (Global user : users) {
            if (isValidUser(user)) {
                log.info("***发现用户是有效数据，企业编码为：{}, globalId为：{}", user.getCustomerCode(), user.getGlobalId());
                continue;
            }
            invalidUsers.add(user);
            if (invalidUsers.size() >= BATCH_COUNT) {
                projectInfoMapper.deleteSelfProjectInfoRecords(invalidUsers);
                finishCount += invalidUsers.size();
                invalidUsers.clear();
                log.info("已处理用户数量：{}", finishCount);
            }
        }
        projectInfoMapper.deleteSelfProjectInfoRecords(invalidUsers);
        log.info("全部处理完成，共处理用户数量：{}", finishCount + invalidUsers.size());
    }

    private boolean isValidUser(Global user) {
        int count = projectInfoMapper.querySelfProjectNameCount(user.getCustomerCode(), user.getGlobalId());
        return count >= 1;
    }

    private boolean isValidCustomer(String customerCode) {
        int count = projectInfoMapper.queryProjectNameCount(customerCode);
        return count >= 1;
    }

    @Autowired
    private ProjectInfoMapper projectInfoMapper;
    public void insertProjectInfo() {
        //获取需要插入的项目信息数据
        ArrayList<StandardsProjectInfoEntity> projectInfoList = getStandardsProjectInfoEntities();

        //获取zb_standards_project_info表中所有的customerCode，并去除与本次插入数据有冲突的customerCode，冲突企业单独处理
        List<String> customerCodeList = projectInfoMapper.searchCustomerCodeFromProjectInfo();
        log.info("zb_standards_project_info表中的企业数量：{}", customerCodeList.size());
        List<String> conflictCustomerCodeList = projectInfoMapper.searchConflictCustomerCode();
        log.info("zb_standards_project_info表中的冲突的企业数量：{}", conflictCustomerCodeList.size());
        for (String customerCode : conflictCustomerCodeList) {
            customerCodeList.remove(customerCode);
        }

        //获取zb_standards_project_info_self表中所有的customerCode对应的globalId，并去除本次添加有冲突的数据的customerCode,冲突企业单独处理
        Map<String, List<Global>> customerCodeMap = projectInfoMapper.searchCustomerCodeAndGlobalIdFromProjectInfoSelf().stream().collect(Collectors.groupingBy(Global::getCustomerCode));
        log.info("zb_standards_project_info_self表中的企业数量：{}", customerCodeMap.size());
        List<Global> globals = projectInfoMapper.searchConflictCustomerCodeAndGlobalId();
        log.info("zb_standards_project_info_self表中的冲突的企业个人账号数量：{}", customerCodeMap.size());
        for (Global global : globals) {
            customerCodeMap.remove(global.getCustomerCode());
        }


        int maxOrdOfInfo = projectInfoMapper.searchMaxOrdFromProjectInfo() + 5;
        int maxOrdOfSelf = projectInfoMapper.searchMaxOrdFromProjectInfoSelf() + 5;

        List<StandardsProjectInfoEntity> projectInfoList1 = new ArrayList<>();
        List<StandardsProjectInfoEntity> projectInfoList2 = new ArrayList<>();

        //冲突企业单独处理
        handleConflictData(conflictCustomerCodeList, projectInfoList, maxOrdOfInfo, maxOrdOfSelf);

        //zb_standards_project_info表与zb_standards_project_info_self表中所有企业循序插入数据
        int i = 0;
        int j = 0;
        for (String customerCode : customerCodeList) {
            i++;
            List<Long> ids = insertIntoProjectInfo(projectInfoList, maxOrdOfInfo, projectInfoList1, customerCode);
            if (i % 10000 == 0) {
                j++;
                log.info("执行了一万家企业的数据了！！！！！！！！！！");
                log.info("第j次:{}", j);
            }
            if (!customerCodeMap.containsKey(customerCode)) {
                continue;
            }
            insertIntoProjectInfoSelf(projectInfoList, customerCodeMap, maxOrdOfSelf, projectInfoList2, customerCode, ids);
        }

        if (!projectInfoList1.isEmpty()) {
            projectInfoMapper.projectInfoInsert(projectInfoList1);
        }

        if (!projectInfoList2.isEmpty()) {
            projectInfoMapper.projectInfoSelfInsert(projectInfoList2);
        }
        log.info("数据处理结束！***********************************");
    }

    private void handleConflictData(List<String> conflictCustomerCodeList, ArrayList<StandardsProjectInfoEntity> projectInfoList, int maxOrdOfInfo, int maxOrdOfSelf) {
        if (EmptyUtil.isEmpty(conflictCustomerCodeList)) {
            return;
        }

        //内置的项目信息名与序号的对应关系
        Map<String, Integer> projectInfoNameMap = new HashMap<>();
        for (int i = 0; i < PROJECT_INFO_INSERT_NAME_LIST.size(); i++) {
            projectInfoNameMap.put(PROJECT_INFO_INSERT_NAME_LIST.get(i), i);
        }

        //不同customerCode下对应的冲突项目信息集合
        Map<String, List<StandardsProjectInfoEntity>> collect1 = projectInfoMapper.searchConflictInfo(TABLE_INFO).stream().collect(Collectors.groupingBy(StandardsProjectInfoEntity::getCustomerCode));
        Map<String, List<StandardsProjectInfoEntity>> collect2 = projectInfoMapper.searchConflictInfo(TABLE_INFO_SELF).stream().collect(Collectors.groupingBy(StandardsProjectInfoEntity::getCustomerCode));


        List<StandardsProjectInfoEntity> updateList1 = new ArrayList<>();
        List<StandardsProjectInfoEntity> updateList2 = new ArrayList<>();
        List<StandardsProjectInfoEntity> insertList1 = new ArrayList<>();
        List<StandardsProjectInfoEntity> insertList2 = new ArrayList<>();

        //遍历zb_standards_project_info表中的有冲突的项目信息，重名重类型的更新原有内置项，只重名的跳过，剩余的直接插入
        for (String customerCode : collect1.keySet()) {

            //zb_standards_project_info表处理逻辑
            //需要update的冲突项目信息
            List<StandardsProjectInfoEntity> updateInfoList1 = collect1.get(customerCode);
            Set<String> updateInfoNameSet = collect1.get(customerCode).stream().map(StandardsProjectInfoEntity::getName).collect(Collectors.toSet());
            updateListAdd(projectInfoList, projectInfoNameMap, updateList1, updateInfoList1);
            //需要insert的新的项目信息
            Map<Integer, Long> idsMap = new HashMap<>();
            int ord1 = maxOrdOfInfo;
            insertListAddToInfo(projectInfoList, insertList1, customerCode, updateInfoNameSet, idsMap, ord1);

            if (!collect2.containsKey(customerCode)) {
                continue;
            }

            //zb_standards_project_info_self表处理逻辑
            List<String> globalIds = collect2.get(customerCode).stream().map(StandardsProjectInfoEntity::getGlobalId).distinct().collect(Collectors.toList());
            for (String globalId : globalIds) {
                //需要update的冲突项目信息
                List<StandardsProjectInfoEntity> updateInfoList = collect2.get(customerCode).stream().filter(entity -> globalId.equals(entity.getGlobalId())).collect(Collectors.toList());
                Set<String> nameSet = collect2.get(customerCode).stream().filter(entity -> globalId.equals(entity.getGlobalId())).map(StandardsProjectInfoEntity::getName).collect(Collectors.toSet());
                updateListAdd(projectInfoList, projectInfoNameMap, updateList2, updateInfoList);

                //todo 修改表名
                //需要insert的新的项目信息
                int ord2 = maxOrdOfSelf;
                insertListAddToSelf(projectInfoList, insertList2, customerCode, idsMap, globalId, nameSet, ord2);
            }
        }

        if (EmptyUtil.isNotEmpty(updateList1)) {
            log.info("zb_standards_project_info表中的update数量：{}", updateList1.size());
            projectInfoMapper.updateInfo(updateList1, TABLE_INFO);
        }
        if (EmptyUtil.isNotEmpty(updateList2)) {
            log.info("zb_standards_project_info_self表中的update数量：{}", updateList2.size());
            projectInfoMapper.updateInfo(updateList2, TABLE_INFO_SELF);
        }
        if (EmptyUtil.isNotEmpty(insertList1)) {
            log.info("zb_standards_project_info表中的insert数量：{}", insertList1.size());
            projectInfoMapper.projectInfoInsert(insertList1);
        }
        if (EmptyUtil.isNotEmpty(insertList2)) {
            log.info("zb_standards_project_info_self表中的insert数量：{}", insertList2.size());
            projectInfoMapper.projectInfoSelfInsert(insertList2);
        }
        log.info("冲突企业数据处理结束！！！！！！！！！！！");
    }

    private void insertListAddToSelf(ArrayList<StandardsProjectInfoEntity> projectInfoList, List<StandardsProjectInfoEntity> insertList2, String customerCode, Map<Integer, Long> idsMap, String globalId, Set<String> nameSet, int ord) {
        for (int i = 0; i < projectInfoList.size(); i++) {
            if (nameSet.contains(projectInfoList.get(i).getName())) {
                continue;
            }
            //雪花算法生成id
            Long id = SnowflakeIdUtils.getNextId();
            StandardsProjectInfoEntity entity = new StandardsProjectInfoEntity();
            BeanUtils.copyProperties(projectInfoList.get(i), entity);
            entity.setId(id);
            entity.setCustomerCode(customerCode);
            entity.setOrd(++ord);
            entity.setGlobalId(globalId);
            entity.setOriginId(idsMap.get(i));
            insertList2.add(entity);
        }
    }

    private void insertListAddToInfo(ArrayList<StandardsProjectInfoEntity> projectInfoList, List<StandardsProjectInfoEntity> insertList, String customerCode, Set<String> updateInfoNameSet, Map<Integer, Long> idsMap, int ord) {
        for (int i = 0; i < projectInfoList.size(); i++) {
            //有冲突的项目信息不插入
            if (updateInfoNameSet.contains(projectInfoList.get(i).getName())) {
                continue;
            }
            Long id = SnowflakeIdUtils.getNextId();
            idsMap.put(i, id);
            StandardsProjectInfoEntity entity = new StandardsProjectInfoEntity();
            BeanUtils.copyProperties(projectInfoList.get(i), entity);
            entity.setId(id);
            entity.setCustomerCode(customerCode);
            entity.setOrd(++ord);
            insertList.add(entity);
        }
    }

    private void updateListAdd(ArrayList<StandardsProjectInfoEntity> projectInfoList, Map<String, Integer> projectInfoNameMap, List<StandardsProjectInfoEntity> updateList, List<StandardsProjectInfoEntity> updateInfoList) {
        for (StandardsProjectInfoEntity entity : updateInfoList) {
            StandardsProjectInfoEntity referenceEntity = projectInfoList.get(projectInfoNameMap.get(entity.getName()));
            //类型重复
            if (referenceEntity.getTypeCode().equals( entity.getTypeCode())) {
                entity.setUnit(referenceEntity.getUnit());
                entity.setIsExpression(referenceEntity.getIsExpression());
                if (EmptyUtil.isEmpty(entity.getRemark())) {
                    entity.setRemark(referenceEntity.getRemark());
                } else if (!entity.getRemark().equals(referenceEntity.getRemark())){
                    entity.setRemark(entity.getRemark() + "|" + referenceEntity.getRemark());
                }
                updateList.add(entity);
            }
        }
    }

    private static ArrayList<StandardsProjectInfoEntity> getStandardsProjectInfoEntities() {
        StandardsProjectInfoEntity projectInfo1 = new StandardsProjectInfoEntity("建设用地面积","number",
                "m2", null, 1, 0, 1, "建筑占地面积+红线内市政道路面积+红线内景观园林面积",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo2 = new StandardsProjectInfoEntity("建筑占地面积","number",
                "m2", null, 1, 0, 1, "Σ各业态建筑占地面积",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo3 = new StandardsProjectInfoEntity("总建筑面积","number",
                "m2", null, 1, 0, 1, "地上建筑面积+地下建筑面积",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo4 = new StandardsProjectInfoEntity("精装修面积","number",
                "m2", null, 1, 0, 1, "Σ各业态精装修面积",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo5 = new StandardsProjectInfoEntity("可售比","number",
                "%", null, 1, 0, 0, "可售面积/总建筑面积（规划）",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo6 = new StandardsProjectInfoEntity("总计容面积","number",
                "m2", null, 1, 0, 1, "Σ各业态计容面积",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo7 = new StandardsProjectInfoEntity("规划容积率","number",
                null, null, 1, 0, 0, null,
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo8 = new StandardsProjectInfoEntity("建筑密度","number",
                "%", null, 1, 0, 0, "基底面积/建设用地面积",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo9 = new StandardsProjectInfoEntity("建筑限高","number",
                "m", null, 1, 0, 0, null,
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo10 = new StandardsProjectInfoEntity("总户数","number",
                "户", null, 1, 0, 1, "Σ各业态户数",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo11 = new StandardsProjectInfoEntity("抗震设防度数","select",
                null, "[{\"name\":\"6度\",\"isDeleted\":0},{\"name\":\"7度\",\"isDeleted\":0},{\"name\":\"8度\",\"isDeleted\":0}]", 1, 0, 0, null,
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo12 = new StandardsProjectInfoEntity("装配率","number",
                "%", null, 1, 0, 0, null,
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo13 = new StandardsProjectInfoEntity("PC单体建筑预制率","number",
                "%", null, 1, 0, 0, null,
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo14 = new StandardsProjectInfoEntity("绿色建筑等级","select",
                null, "[{\"name\":\"一星级\",\"isDeleted\":0},{\"name\":\"二星级\",\"isDeleted\":0},{\"name\":\"三星级\",\"isDeleted\":0}]", 1, 0, 0, null,
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo15 = new StandardsProjectInfoEntity("市政道路面积","number",
                "m2", null, 1, 0, 1, "红线内市政道路面积+红线外代建市政道路面积",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo16 = new StandardsProjectInfoEntity("景观园林面积","number",
                "m2", null, 1, 0, 1, "红线内景观园林面积+红线外代建景观园林面积",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo17 = new StandardsProjectInfoEntity("园区出入口数量","number",
                "个", null, 1, 0, 1, null,
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo18 = new StandardsProjectInfoEntity("绿地率","number",
                "%", null, 1, 0, 0, "景观园林面积/总用地面积",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo19 = new StandardsProjectInfoEntity("车位数","number",
                "辆", null, 1, 0, 1, "地上车位数+地下非人防车位数+地下人防车位数+机械车位数",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo20 = new StandardsProjectInfoEntity("车位配比","number",
                "辆/100m2", null, 1, 0, 0, "车位数/(总建筑面积/100)",
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo21 = new StandardsProjectInfoEntity("充电桩数量","number",
                "个", null, 1, 0, 1, null,
                null, 0, null, 1, null, null);
        StandardsProjectInfoEntity projectInfo22 = new StandardsProjectInfoEntity("耐火等级","select",
                null, "[{\"name\":\"一级\",\"isDeleted\":0},{\"name\":\"二级\",\"isDeleted\":0},{\"name\":\"三级\",\"isDeleted\":0},{\"name\":\"四级\",\"isDeleted\":0}]", 1, 0, 0, null,
                null, 0, null, 1, null, null);

        ArrayList<StandardsProjectInfoEntity> projectInfoList = new ArrayList<>();
        Collections.addAll(projectInfoList, projectInfo1, projectInfo2, projectInfo3, projectInfo4, projectInfo5,
                projectInfo6, projectInfo7, projectInfo8, projectInfo9, projectInfo10, projectInfo11, projectInfo12,
                projectInfo13, projectInfo14, projectInfo15, projectInfo16, projectInfo17, projectInfo18, projectInfo19,
                projectInfo20, projectInfo21, projectInfo22);
        return projectInfoList;
    }

    private void insertIntoProjectInfoSelf(ArrayList<StandardsProjectInfoEntity> projectInfoList, Map<String, List<Global>> customerCodeMap, int maxOrdOfSelf, List<StandardsProjectInfoEntity> projectInfoList2, String customerCode, List<Long> ids) {
        //zb_standards_project_info_self表插入数据
        List<String> globalIds = customerCodeMap.get(customerCode).stream().map(Global::getGlobalId).collect(Collectors.toList());
        for (String globalId : globalIds) {
            int ord2 = maxOrdOfSelf;
            for (int i = 0; i < projectInfoList.size(); i++) {
                //雪花算法生成id
                Long id2 = SnowflakeIdUtils.getNextId();
                StandardsProjectInfoEntity standardsProjectInfoEntity2 = new StandardsProjectInfoEntity();
                BeanUtils.copyProperties(projectInfoList.get(i), standardsProjectInfoEntity2);
                standardsProjectInfoEntity2.setId(id2);
                standardsProjectInfoEntity2.setCustomerCode(customerCode);
                standardsProjectInfoEntity2.setOrd(++ord2);
                standardsProjectInfoEntity2.setGlobalId(globalId);
                standardsProjectInfoEntity2.setOriginId(ids.get(i));
                projectInfoList2.add(standardsProjectInfoEntity2);
            }
            if (projectInfoList2.size() == 1000) {
                projectInfoMapper.projectInfoSelfInsert(projectInfoList2);
                projectInfoList2.clear();
            }
        }
    }

    public List<StandardsProjectInfoEntity> insertIntoProjectInfoV2(List<StandardsProjectInfoEntity> projectInfoList, Map<String, Prams> customerMap, List<StandardsProjectInfoEntity> projectInfoList1, String customerCode) {
        //zb_standards_project_info表插入数据
        Prams prams = customerMap.get(customerCode);
        int ord1 = prams.getOrd();
        List<StandardsProjectInfoEntity> insertList = new ArrayList<>();
        for (int i = 0; i < projectInfoList.size(); i++) {
            //雪花算法生成id
            Long id1 = SnowflakeIdUtils.getNextId();
            StandardsProjectInfoEntity standardsProjectInfoEntity1 = new StandardsProjectInfoEntity();
            BeanUtils.copyProperties(projectInfoList.get(i), standardsProjectInfoEntity1);
            standardsProjectInfoEntity1.setId(id1);
            standardsProjectInfoEntity1.setCustomerCode(customerCode);
            standardsProjectInfoEntity1.setOrd(++ord1);
            standardsProjectInfoEntity1.setQyCodeOld(prams.getQyCodeOld());
            standardsProjectInfoEntity1.setQyFlag(prams.getQyFlag());
            projectInfoList1.add(standardsProjectInfoEntity1);
            insertList.add(standardsProjectInfoEntity1);
            if (projectInfoList1.size() == 1000) {
                projectInfoMapper.projectInfoInsert(projectInfoList1);
                projectInfoList1.clear();
            }
        }
        return insertList;
    }

    public void insertIntoProjectInfoSelfV2(List<StandardsProjectInfoEntity> projectInfoList, Map<String, Prams> customerMap, List<StandardsProjectInfoEntity> projectInfoList2, String customerCode, List<Long> ids) {
        //zb_standards_project_info_self表插入数据
        Prams prams = customerMap.get(customerCode);
        int ord2 = prams.getOrd();
        for (int i = 0; i < projectInfoList.size(); i++) {
            //雪花算法生成id
            Long id2 = SnowflakeIdUtils.getNextId();
            StandardsProjectInfoEntity standardsProjectInfoEntity2 = new StandardsProjectInfoEntity();
            BeanUtils.copyProperties(projectInfoList.get(i), standardsProjectInfoEntity2);
            standardsProjectInfoEntity2.setId(id2);
            standardsProjectInfoEntity2.setCustomerCode(customerCode);
            standardsProjectInfoEntity2.setOrd(++ord2);
            standardsProjectInfoEntity2.setOriginId(ids.get(i));
            standardsProjectInfoEntity2.setQyCodeOld(prams.getQyCodeOld());
            standardsProjectInfoEntity2.setQyFlag(prams.getQyFlag());
            projectInfoList2.add(standardsProjectInfoEntity2);
        }
        if (projectInfoList2.size() == 1000) {
            projectInfoMapper.projectInfoSelfInsert(projectInfoList2);
            projectInfoList2.clear();
        }
    }

    public List<Long> insertIntoProjectInfo(List<StandardsProjectInfoEntity> projectInfoList, int maxOrdOfInfo, List<StandardsProjectInfoEntity> projectInfoList1, String customerCode) {
        //zb_standards_project_info表插入数据
        int ord1 = maxOrdOfInfo;
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < projectInfoList.size(); i++) {
            //雪花算法生成id
            Long id1 = SnowflakeIdUtils.getNextId();
            ids.add(id1);
            StandardsProjectInfoEntity standardsProjectInfoEntity1 = new StandardsProjectInfoEntity();
            BeanUtils.copyProperties(projectInfoList.get(i), standardsProjectInfoEntity1);
            standardsProjectInfoEntity1.setId(id1);
            standardsProjectInfoEntity1.setCustomerCode(customerCode);
            standardsProjectInfoEntity1.setOrd(++ord1);
            projectInfoList1.add(standardsProjectInfoEntity1);
            if (projectInfoList1.size() == 1000) {
                projectInfoMapper.projectInfoInsert(projectInfoList1);
                projectInfoList1.clear();
            }
        }
        return ids;
    }

    public void insertProjectInfoToOne(String customerCode) {
        ArrayList<StandardsProjectInfoEntity> projectInfoList = getStandardsProjectInfoEntities();
        List<String> globalIds  = projectInfoMapper.selectGlobalIdsByCustomerCode(customerCode);
        if ("gms_guoyn-a_1".equals(customerCode)) {
            handleConflictDataToOne(projectInfoList, customerCode,400, 400);
            return;
        }
        ArrayList<StandardsProjectInfoEntity> projectInfoList1 = new ArrayList<>();
        ArrayList<StandardsProjectInfoEntity> projectInfoList2 = new ArrayList<>();
        int ord1 = 400;
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < 22; i++) {
            //雪花算法生成id
            Long id1 = SnowflakeIdUtils.getNextId();
            ids.add(id1);
            StandardsProjectInfoEntity standardsProjectInfoEntity1 = new StandardsProjectInfoEntity();
            BeanUtils.copyProperties(projectInfoList.get(i), standardsProjectInfoEntity1);
            standardsProjectInfoEntity1.setId(id1);
            standardsProjectInfoEntity1.setCustomerCode(customerCode);
            standardsProjectInfoEntity1.setOrd(++ord1);
            projectInfoList1.add(standardsProjectInfoEntity1);

        }
        projectInfoMapper.projectInfoInsert(projectInfoList1);

        //zb_standards_project_info_self表插入数据
        //todo 根据customerCode查询globalId
        if (EmptyUtil.isEmpty(globalIds)) {
            return;
        }
        for (String globalId : globalIds) {
            int ord2 = 400;
            for (int i = 0; i < 22; i++) {
                //雪花算法生成id
                Long id2 = SnowflakeIdUtils.getNextId();
                StandardsProjectInfoEntity standardsProjectInfoEntity2 = new StandardsProjectInfoEntity();
                BeanUtils.copyProperties(projectInfoList.get(i), standardsProjectInfoEntity2);
                standardsProjectInfoEntity2.setId(id2);
                standardsProjectInfoEntity2.setCustomerCode(customerCode);
                standardsProjectInfoEntity2.setOrd(++ord2);
                standardsProjectInfoEntity2.setGlobalId(globalId);
                standardsProjectInfoEntity2.setOriginId(ids.get(i));
                projectInfoList2.add(standardsProjectInfoEntity2);
            }
        }
        projectInfoMapper.projectInfoSelfInsert(projectInfoList2);
    }

    private void handleConflictDataToOne(ArrayList<StandardsProjectInfoEntity> projectInfoList, String customerCode, int maxOrdOfInfo, int maxOrdOfSelf) {
        //内置的项目信息名与序号的对应关系
        Map<String, Integer> projectInfoNameMap = new HashMap<>();
        for (int i = 0; i < PROJECT_INFO_INSERT_NAME_LIST.size(); i++) {
            projectInfoNameMap.put(PROJECT_INFO_INSERT_NAME_LIST.get(i), i);
        }

        //不同customerCode下对应的冲突项目信息集合
        Map<String, List<StandardsProjectInfoEntity>> collect1 = projectInfoMapper.searchConflictInfo(TABLE_INFO).stream().collect(Collectors.groupingBy(StandardsProjectInfoEntity::getCustomerCode));
        Map<String, List<StandardsProjectInfoEntity>> collect2 = projectInfoMapper.searchConflictInfo(TABLE_INFO_SELF).stream().collect(Collectors.groupingBy(StandardsProjectInfoEntity::getCustomerCode));


        List<StandardsProjectInfoEntity> updateList1 = new ArrayList<>();
        List<StandardsProjectInfoEntity> updateList2 = new ArrayList<>();
        List<StandardsProjectInfoEntity> insertList1 = new ArrayList<>();
        List<StandardsProjectInfoEntity> insertList2 = new ArrayList<>();

        //遍历zb_standards_project_info表中的有冲突的项目信息，重名重类型的更新原有内置项，只重名的跳过，剩余的直接插入


        //zb_standards_project_info表处理逻辑
        //需要update的冲突项目信息
        List<StandardsProjectInfoEntity> updateInfoList1 = collect1.get(customerCode);
        Set<String> updateInfoNameSet = collect1.get(customerCode).stream().map(StandardsProjectInfoEntity::getName).collect(Collectors.toSet());
        updateListAdd(projectInfoList, projectInfoNameMap, updateList1, updateInfoList1);
        //需要insert的新的项目信息
        Map<Integer, Long> idsMap = new HashMap<>();
        int ord1 = maxOrdOfInfo;
        insertListAddToInfo(projectInfoList, insertList1, customerCode, updateInfoNameSet, idsMap, ord1);

        if (!collect2.containsKey(customerCode)) {
            return;
        }

        //zb_standards_project_info_self表处理逻辑
        List<String> globalIds = collect2.get(customerCode).stream().map(StandardsProjectInfoEntity::getGlobalId).distinct().collect(Collectors.toList());
        for (String globalId : globalIds) {
            //需要update的冲突项目信息
            List<StandardsProjectInfoEntity> updateInfoList = collect2.get(customerCode).stream().filter(entity -> globalId.equals(entity.getGlobalId())).collect(Collectors.toList());
            Set<String> nameSet = collect2.get(customerCode).stream().filter(entity -> globalId.equals(entity.getGlobalId())).map(StandardsProjectInfoEntity::getName).collect(Collectors.toSet());
            updateListAdd(projectInfoList, projectInfoNameMap, updateList2, updateInfoList);

            //todo 修改表名
            //需要insert的新的项目信息
            int ord2 = maxOrdOfSelf;
            insertListAddToSelf(projectInfoList, insertList2, customerCode, idsMap, globalId, nameSet, ord2);
        }


        if (EmptyUtil.isNotEmpty(updateList1)) {
            log.info("zb_standards_project_info表中的update数量：{}", updateList1.size());
            projectInfoMapper.updateInfo(updateList1, TABLE_INFO);
        }
        if (EmptyUtil.isNotEmpty(updateList2)) {
            log.info("zb_standards_project_info_self表中的update数量：{}", updateList2.size());
            projectInfoMapper.updateInfo(updateList2, TABLE_INFO_SELF);
        }
        if (EmptyUtil.isNotEmpty(insertList1)) {
            log.info("zb_standards_project_info表中的insert数量：{}", insertList1.size());
            projectInfoMapper.projectInfoInsert(insertList1);
        }
        if (EmptyUtil.isNotEmpty(insertList2)) {
            log.info("zb_standards_project_info_self表中的insert数量：{}", insertList2.size());
            projectInfoMapper.projectInfoSelfInsert(insertList2);
        }
        log.info("冲突企业数据处理结束！！！！！！！！！！！");
    }
}
