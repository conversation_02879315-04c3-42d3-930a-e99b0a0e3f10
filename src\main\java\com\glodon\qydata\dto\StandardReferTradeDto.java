package com.glodon.qydata.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
　　* @description: 标准统一--可被引用的专业数据载体
　　* <AUTHOR>
　　* @date 2021/10/21 17:13
　　*/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "可被引用的专业数据载体")
public class StandardReferTradeDto {

    @Schema(description = "专业名称")
    private String description;
    @Schema(description = "引用专业编码")
    private String tradeCode;
    @Schema(description = "专业下20个编码几个")
    private List<String> referCode;
}
