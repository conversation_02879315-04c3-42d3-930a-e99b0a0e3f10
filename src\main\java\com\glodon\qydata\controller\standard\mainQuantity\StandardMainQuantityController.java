package com.glodon.qydata.controller.standard.mainQuantity;


import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.standard.mainQuantity.IStandardsMainQuantityService;
import com.glodon.qydata.util.PublishLockerUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.mainQuantity.StandardsMainQuantityBO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
　　* @description: 标准打通--主要量指标控制层
　　* <AUTHOR>
　　* @date 2021/10/21 19:27
　　*/
@RestController
@RequestMapping("/basicInfo/standards")
@Tag(name = "主要量指标相关接口类", description = "主要量指标相关接口类")
public class StandardMainQuantityController extends BaseController {
    @Autowired
    private IStandardsMainQuantityService mainQuantityService;

    @Autowired
    private PublishLockerUtil publishLockerUtil;

    //------------------------------------------------------以下是企业级别-------------------------------------------------------------------
    /**
     　　* @description: 根据企业编码与专业id查看指定专业对应的主要量指标列表--企业级
     页面流程为：查询专业列表，再根据专业列表查询对应主要量指标列表
     　　* @param tradeId 专业id
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "根据企业编码与专业id查看指定专业对应的主要量指标列表--企业级")
    @GetMapping("/getMainQuantityList/{tradeId}")
    public ResponseVo<ZbStandardsMainQuantity> selectMainQuantityList(@PathVariable Long tradeId) throws BusinessException {
        String customerCode = getCustomerCode();
        List<ZbStandardsMainQuantity> returnList = mainQuantityService.getListByTradeId(customerCode, tradeId);
        return new ResponseVo(ResponseCode.SUCCESS, returnList);
    }

    /**
     　　* @description: excel导入数据，测试线执行一次--企业级 （非业务接口，可废弃）
     　　* @param tradeId 专业id
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "excel导入数据，测试线执行一次（非业务接口，可废弃）")
    @PostMapping("/excelInsert")
    public ResponseVo<ZbStandardsMainQuantity> initMainQuantityList(MultipartFile file){
//        Long globalId = getGlobalIdLong();
//        String customerCode = getCustomerCode();
//        mainQuantityService.insertFeatureDataFile(customerCode, globalId, file);
        return ResponseVo.success();
    }

    //------------------------------------------------------以下是个人级别-------------------------------------------------------------------
    /**
     　　* @description: 单个删除主要量指标--个人级
     　　* @param tradeId 专业id
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "单个删除主要量指标--个人级")
    @Permission
    @DeleteMapping("/deleteById/{id}")
    public ResponseVo deleteById(@PathVariable String id) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.MAIN_QUANTITY, globalId, customerCode);
        mainQuantityService.deleteById(customerCode, id);
        return ResponseVo.success();
    }

    /**
     　　* @description: 增加一条记录--个人级
     　　* @param tradeId 专业id
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "增加一条记录--个人级")
    @Permission
    @PostMapping("/insert")
    public ResponseVo<ZbStandardsMainQuantity> insertRecord(@RequestBody @Validated StandardsMainQuantityBO bo) throws BusinessException{
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.MAIN_QUANTITY, globalId, customerCode);
        return ResponseVo.success(mainQuantityService.insertRecord(customerCode, Long.parseLong(globalId), bo));
    }

    /**
     　　* @description: 更新一条记录--个人级
     　　* @param tradeId 专业id
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "更新一条记录--个人级")
    @Permission
    @PutMapping("/update")
    public ResponseVo<ZbStandardsMainQuantity> updateRecord(@RequestBody @Validated StandardsMainQuantityBO bo) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.MAIN_QUANTITY, globalId, customerCode);
        return ResponseVo.success(mainQuantityService.updateRecord(customerCode, Long.parseLong(globalId), bo));
    }

    /**
     * 上下移--个人级
     * @throws
     * @param flag 1：上移，2：下移
     * @param bo 主要量指标前端数据载体
     * <AUTHOR>
     * @return {@link com.glodon.qydata.vo.common.ResponseVo< Void>}
     * @date 2021/11/22 10:52
     */
    @Operation(summary = "上下移--个人级")
    @Permission
    @PutMapping("/moveUpDown/{flag}")
    public ResponseVo<Void> moveUpDown(@PathVariable Integer flag, @RequestBody @Validated StandardsMainQuantityBO bo) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.MAIN_QUANTITY, globalId, customerCode);
        mainQuantityService.moveUpDown(flag, bo);
        return ResponseVo.success();
    }

    /**
     　　* @description: 根据企业编码与专业id查看指定专业对应的主要量指标列表--个人级
     页面流程为：查询专业列表，再根据专业列表查询对应主要量指标列表
     　　* @param tradeId 专业id
     　　* <AUTHOR>
     　　* @date 2021/10/21 19:27
     　　*/
    @Operation(summary = "根据企业编码与专业id查看指定专业对应的主要量指标列表--个人级")
    @Permission
    @GetMapping("/getMainQuantityList/self/{tradeId}")
    public ResponseVo<ZbStandardsMainQuantity> selectSelfMainQuantityList(@PathVariable Long tradeId) throws BusinessException {
        String customerCode = getCustomerCode();
        Long globalId = getGlobalIdLong();
        publishLockerUtil.lock(OperateConstants.MAIN_QUANTITY, globalId.toString(), customerCode);
        List<ZbStandardsMainQuantity> returnList = mainQuantityService.getSelfListByTradeId(customerCode, tradeId);

        return new ResponseVo(ResponseCode.SUCCESS, returnList);
    }

    @Operation(summary = "发布")
    @Permission
    @PostMapping("/MainQuantity/publish")
    public ResponseVo publish() throws BusinessException{
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.MAIN_QUANTITY, globalId, customerCode);
        mainQuantityService.publish(customerCode, globalId);
        publishLockerUtil.unLock(OperateConstants.MAIN_QUANTITY, globalId, customerCode);
        return ResponseVo.success();
    }
}
