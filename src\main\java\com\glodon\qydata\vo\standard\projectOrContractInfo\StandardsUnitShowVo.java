package com.glodon.qydata.vo.standard.projectOrContractInfo;

import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsUnitEntity;

/**
 * <AUTHOR>
 * @ProjectName gcj_zb_company_site
 * @className StandardsUnitShowVo
 * @description
 * @date 2021/11/8
 **/
public class StandardsUnitShowVo extends StandardsUnitEntity {
    private static final long serialVersionUID = -4695342815248347443L;
    /**
     * 是否为内置单位
     */
    private Integer isBuiltIn;

    public Integer getIsBuiltIn() {
        return isBuiltIn;
    }

    public void setIsBuiltIn(Integer isBuiltIn) {
        this.isBuiltIn = isBuiltIn;
    }
}
