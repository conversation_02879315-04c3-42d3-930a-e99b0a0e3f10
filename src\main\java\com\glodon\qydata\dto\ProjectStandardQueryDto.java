package com.glodon.qydata.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ProjectStandardQueryDto extends PageForm {
    //名称
    private String name;
    //企业编码
    private String customerCode;
    //定位业态编码
    private String positionCategoryCode;
    //创建开始时间
    private Date createStartTime;
    //创建结束时间
    private Date createEndTime;
    //是否需要删除
    private Integer isShowDelete;
    //工程分类编码
    private String categoryCode;
    //是否启用
    private Integer isUsing;
    /** 任务委托新增字段-目标企业enterpriseId**/
    private String destEnterpriseId;
    /** 任务委托新增字段-产品来源，ysbz(预算编制)、gsbz(概算编制)、不传则取所有产品来源委托的项目**/
    private String trustProductSource;
}
