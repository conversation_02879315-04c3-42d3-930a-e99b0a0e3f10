package com.glodon.qydata.service.standard.feature;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.entity.standard.trade.TradeTagEnum;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.mapper.standard.trade.TradeTagEnumMapper;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.standard.expression.IExpressionService;
import com.glodon.qydata.vo.standard.expression.ExpressionResultVo;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureCategoryViewVO;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.glodon.qydata.common.constant.Constants.WHETHER_FALSE;
import static com.glodon.qydata.common.constant.Constants.WHETHER_TRUE;

@Service
public class FeatureCommonHandler {

    @Autowired
    private ZbStandardsTradeMapper tradeMapper;
    @Autowired
    private TradeTagEnumMapper tradeTagEnumMapper;
    @Autowired
    private IExpressionService expressionService;

    public List<ProjectFeatureResultVO> convertToResultVO(List<ProjectFeature> projectFeatures, Integer viewType, String categoryCode, Integer isSkipUserName) {
        List<ProjectFeatureResultVO> resultList = new ArrayList<>();
        if (CollectionUtils.isEmpty(projectFeatures)){
            return resultList;
        }

        //获取某个企业人员的globalId和姓名的映射关系
        Map<String, String> globalIdNameMap = new HashMap<>();
        if (WHETHER_FALSE.equals(isSkipUserName)){
            List<String> allUsers = projectFeatures.stream().map(feat -> String.valueOf(feat.getCreateGlobalId()))
                    .distinct().collect(Collectors.toList());
            allUsers.addAll(projectFeatures.stream().map(feat -> String.valueOf(feat.getUpdateGlobalId())).distinct()
                    .collect(Collectors.toList()));
            globalIdNameMap = RequestContent.getGlobalIdNameMap(allUsers);
        }

        for (ProjectFeature projectFeature : projectFeatures) {
            ProjectFeatureResultVO vo = new ProjectFeatureResultVO();
            BeanUtils.copyProperties(projectFeature, vo);

            if (Objects.nonNull(vo.getCreateGlobalId()) && globalIdNameMap.containsKey(vo.getCreateGlobalId().toString())) {
                vo.setCreateGlobalName(globalIdNameMap.get(vo.getCreateGlobalId().toString()));
            }
            if (Objects.nonNull(vo.getUpdateGlobalId()) && globalIdNameMap.containsKey(vo.getUpdateGlobalId().toString())) {
                vo.setUpdateGlobalName(globalIdNameMap.get(vo.getUpdateGlobalId().toString()));
            }

            // 分类视图的projectType处理
            if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(viewType)) {
                vo.setProjectType(getDealProjectType(vo.getProjectType(), categoryCode, Constants.ZbFeatureConstants.JSON_DEAL_1));
            }

            // 内置数据创建时间不展示
            if (Constants.DEFAULT.equals(vo.getIsDefault())){
                vo.setCreateTime(null);
            }
            resultList.add(vo);
        }
        return resultList;
    }

    /**
     * @description: 解析projectType，type=0剔除掉指定一级分类下的所有分类，还是type=1只要某一分类下的
     * @param projectType
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2021/10/27 11:06
     */
    public String getDealProjectType(String projectType, String baseCategoryCode, Integer type){
        List<JSONArray> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(projectType)){
            Map<String, Object> parse = JSONObject.parseObject(projectType, Map.class);
            if (Objects.nonNull(parse)){
                Object projectTypeJson = parse.get(BusinessConstants.PROJECT_TYPE);
                if(Objects.nonNull(projectTypeJson)){
                    JSONArray jsonArray = JSONArray.parseArray(projectTypeJson.toString());
                    for (Object o : jsonArray) {
                        String o1 = o.toString();
                        JSONArray objects = JSONArray.parseArray(o1);
                        String categoryCode = (String) objects.get(0);
                        if (type == Constants.ZbFeatureConstants.JSON_DEAL_1){
                            if (!baseCategoryCode.equals(categoryCode)){
                                continue;
                            }
                        }else {
                            if (baseCategoryCode.equals(categoryCode)){
                                continue;
                            }
                        }
                        list.add(objects);
                    }
                }
            }
        }
        Map<String, Object> objectMap = new HashMap<>(16);
        objectMap.put(BusinessConstants.PROJECT_TYPE, list);
        Object projectTypeJSON = JSONObject.toJSON(objectMap);
        return projectTypeJSON.toString();
    }

    /**
     * @description: 解析projectType，删除指定一级分类下的所有分类，然后新增一些分类
     * @param projectType
     * @param baseCategoryCode
     * @return java.lang.String
     * <AUTHOR>
     * @date 2021/11/4 15:44
     */
    public String dealProjectType(String oldProjectType, String baseCategoryCode, String projectType){
        List<JSONArray> oldList = new ArrayList<>();
        int start = -1;
        boolean flag = true;
        if (StringUtils.isNotEmpty(oldProjectType)){
            Map<String, Object> parse = JSONObject.parseObject(oldProjectType, Map.class);
            if (Objects.nonNull(parse)){
                Object projectTypeJson = parse.get(BusinessConstants.PROJECT_TYPE);
                if(Objects.nonNull(projectTypeJson)){
                    JSONArray jsonArray = JSONArray.parseArray(projectTypeJson.toString());
                    for (int i = 0; i < jsonArray.size(); i++) {
                        String o1 = jsonArray.get(i).toString();
                        JSONArray objects = JSONArray.parseArray(o1);
                        String categoryCode = (String) objects.get(0);
                        if (baseCategoryCode.equals(categoryCode)){
                            if (flag){
                                start = i;
                                flag = false;
                            }
                            continue;
                        }
                        oldList.add(objects);
                    }
                }
            }
        }

        List<JSONArray> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(projectType)){
            Map<String, Object> parse = JSONObject.parseObject(projectType, Map.class);
            if (Objects.nonNull(parse)){
                Object projectTypeJson = parse.get(BusinessConstants.PROJECT_TYPE);
                if(Objects.nonNull(projectTypeJson)){
                    JSONArray jsonArray = JSONArray.parseArray(projectTypeJson.toString());
                    jsonArray.forEach(json -> {
                        String o1 = json.toString();
                        JSONArray objects = JSONArray.parseArray(o1);
                        list.add(objects);
                    });
                }
            }
        }

        if (start == -1){
            oldList.addAll(list);
        }else {
            oldList.addAll(start, list);
        }

        Map<String, Object> objectMap = new HashMap<>(16);
        objectMap.put(BusinessConstants.PROJECT_TYPE, oldList);
        Object projectTypeJSON = JSONObject.toJSON(objectMap);
        return projectTypeJSON.toString();
    }

    /**
     * 快速插入时在当前特征的工程分类下追加新的特征的一级分类
     * @param projectType: 数据库中当前特征对应的工程分类
     * @param newCategoryCode
     * @return
     */
    public String addNewCategoryCode(String projectType, String newCategoryCode){
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(projectType)){
            Map<String, Object> parse = JSONObject.parseObject(projectType, Map.class);
            if (Objects.nonNull(parse)){
                Object projectTypeJson = parse.get(BusinessConstants.PROJECT_TYPE);
                if(Objects.nonNull(projectTypeJson)){
                    JSONArray jsonArray = JSONArray.parseArray(projectTypeJson.toString());
                    jsonArray.forEach(json -> {
                        String o1 = json.toString();
                        list.add(o1);
                    });
                }
            }
        }

        // 追加新的工程分类
        if (!list.contains(newCategoryCode)) {
            list.add(newCategoryCode);
        }

        Map<String, Object> objectMap = new HashMap<>(16);
        objectMap.put(BusinessConstants.PROJECT_TYPE, list);
        Object projectTypeJSON = JSONObject.toJSON(objectMap);
        return projectTypeJSON.toString();
    }

    /**
     * 按标签顺序调整专业顺序
     * @param customerCode
     */
    private Map<Long, ZbStandardsTrade> buildOrdedTradeMap(String customerCode) {
        // 获取当前企业的专业列表并排序
        List<ZbStandardsTrade> tradeList = tradeMapper.selectListByCustomerCode(customerCode);

        List<ZbStandardsTrade> orderedTradeList = new ArrayList<>();
        List<TradeTagEnum> tradeTagList = tradeTagEnumMapper.selectAllListByCustomerCode(customerCode);
        Map<Long, Integer> tagOrderMap = tradeTagList.stream().collect(Collectors.toMap(TradeTagEnum::getId, TradeTagEnum::getOrd, (v1, v2) -> v2));

        List<ZbStandardsTrade> tradeWithTagList = new ArrayList<>();
        List<ZbStandardsTrade> tradeNoTagList = new ArrayList<>();
        tradeList.forEach(item->{
            if (Objects.isNull(item.getTradeTagId())) {
                tradeNoTagList.add(item);
            } else {
                tradeWithTagList.add(item);
            }
        });

        // 按专业标签排序
        if (!CollUtil.isEmpty(tradeWithTagList)) {
            tradeWithTagList.sort(((o1, o2) -> {
                Integer tagOrder1 = tagOrderMap.get(o1.getTradeTagId());
                Integer tagOrder2 = tagOrderMap.get(o2.getTradeTagId());
                if (Objects.nonNull(tagOrder1) && Objects.nonNull(tagOrder2)){
                    // 按标签排序
                    return tagOrder1 - tagOrder2;
                }

                return 0;
            }));

            orderedTradeList.addAll(tradeWithTagList);
        }

        orderedTradeList.addAll(tradeNoTagList);

        // 按专业顺序生成map
        Map<Long, ZbStandardsTrade> orderedTradeMap = orderedTradeList.stream().collect(Collectors.toMap(ZbStandardsTrade::getId, Function.identity(), (v1, v2) -> v2, LinkedHashMap::new));
        return orderedTradeMap;
    }

    public List<ProjectFeatureCategoryViewVO> convertToCategoryViewVO(List<ProjectFeature> projectFeatures,
                                                                      List<ProjectFeatureCategoryView> categoryViews,
                                                                      String customerCode,
                                                                      Integer viewType,
                                                                      String categoryCode,
                                                                      Integer isSkipUserName) {
        if (CollectionUtils.isEmpty(projectFeatures) || CollectionUtils.isEmpty(categoryViews)){
            return new ArrayList<>();
        }

        Map<Long, Integer> ordCategoryMap = categoryViews.parallelStream()
                .collect(Collectors.toMap(ProjectFeatureCategoryView::getFeatureId, ProjectFeatureCategoryView::getOrdCategory, (v1, v2) -> v2));

        List<Long> featureIds = categoryViews.stream()
                .map(ProjectFeatureCategoryView::getFeatureId).collect(Collectors.toList());


        // in查询后，顺序会按主键排序，需要重新还原到分类视图下的排序
        projectFeatures.sort((o1, o2) -> {
            int io1 = featureIds.indexOf(o1.getId());
            int io2 = featureIds.indexOf(o2.getId());
            return io1 - io2;
        });

        // 填充创建人、更新人姓名
        List<ProjectFeatureResultVO> resultVOList = convertToResultVO(projectFeatures, viewType, categoryCode, isSkipUserName);

        resultVOList.forEach(item -> item.setOrdCategory(ordCategoryMap.get(item.getId())));

        // 按标签顺序调整专业顺序
        Map<Long, ZbStandardsTrade> tradeMap = buildOrdedTradeMap(customerCode);

        // 按专业分组,并调整顺序
        Map<Long, List<ProjectFeatureResultVO>> tradeGroup = resultVOList.stream().collect(Collectors.groupingBy(ProjectFeatureResultVO::getTradeId));
        LinkedHashMap<Long, List<ProjectFeatureResultVO>> orderTradeGroup = new LinkedHashMap<>();
        for (Long tradeId : tradeMap.keySet()) {
            if (tradeGroup.containsKey(tradeId)) {
                orderTradeGroup.put(tradeId, tradeGroup.get(tradeId));
            }
        }

        List<ProjectFeatureCategoryViewVO> result = new ArrayList<>();
        orderTradeGroup.forEach((tradeId, resultList) ->{
            ProjectFeatureCategoryViewVO categoryViewVO = new ProjectFeatureCategoryViewVO();
            categoryViewVO.setTradeId(tradeId);
            if (tradeMap.containsKey(tradeId)){
                categoryViewVO.setTradeName(tradeMap.get(tradeId).getDescription());
                categoryViewVO.setTradeOrd(tradeMap.get(tradeId).getOrd());
                categoryViewVO.setTradeCode(tradeMap.get(tradeId).getTradeCode());
                categoryViewVO.setEnabled(tradeMap.get(tradeId).getEnabled());
            }
            categoryViewVO.setFeatureList(resultList);

            IntStream.range(0, resultList.size()).forEach(i -> resultList.get(i).setOrdCategory(i + 1));

            result.add(categoryViewVO);
        });

        return result;
    }

    /**
     * 放入计算口径编码
     * @param featureList
     * @param customerCode
     * @param type
     */
    public void fillExpressionCode(List<ProjectFeature> featureList, String customerCode, Integer type) {
        if (CollectionUtils.isEmpty(featureList)){
            return;
        }

        List<ExpressionResultVo> expressionResultVos = expressionService.selectExpression(customerCode, type, WHETHER_TRUE);
        if (CollectionUtils.isEmpty(expressionResultVos)){
            return;
        }
        Map<String, String> nameAndCodeMap = expressionResultVos.parallelStream().filter(x -> x.getExpressionCode() != null)
                .collect(Collectors.toMap(ExpressionResultVo::getName, ExpressionResultVo::getExpressionCode, (v1, v2) -> v2));

        for (ProjectFeature feature : featureList) {
            if (Constants.WHETHER_TRUE.equals(feature.getIsExpression()) && nameAndCodeMap.containsKey(feature.getName())){
                feature.setExpressionCode(nameAndCodeMap.get(feature.getName()));
            }
        }
    }

    /**
     * 放入计算口径编码
     * @param feature
     */
    public void fillExpressionCode(ProjectFeature feature) {
        if (feature == null){
            return;
        }
        List<ProjectFeature> featureList = new ArrayList<>();
        featureList.add(feature);
        fillExpressionCode(featureList, feature.getCustomerCode(), feature.getType());
    }

    /**
     * 放入计算口径编码
     * @param resultVOList
     * @param customerCode
     * @param type
     */
    public void fillVoExpressionCode(List<ProjectFeatureResultVO> resultVOList, String customerCode, Integer type) {
        if (CollectionUtils.isEmpty(resultVOList)){
            return;
        }

        List<ExpressionResultVo> expressionResultVos = expressionService.selectExpression(customerCode, type, WHETHER_TRUE);
        Map<String, String> nameAndCodeMap = expressionResultVos.parallelStream()
                .collect(Collectors.toMap(ExpressionResultVo::getName, ExpressionResultVo::getExpressionCode, (v1, v2) -> v2));

        for (ProjectFeatureResultVO feature : resultVOList) {
            if (Constants.WHETHER_TRUE.equals(feature.getIsExpression()) && nameAndCodeMap.containsKey(feature.getName())){
                feature.setExpressionCode(nameAndCodeMap.get(feature.getName()));
            }
        }
    }
}
