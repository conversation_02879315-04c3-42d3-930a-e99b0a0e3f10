package com.glodon.qydata.service.cloud.middleGround;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
　　* @description: 建设项目总投数据载体
　　* <AUTHOR>
　　* @date 2022/6/21 9:50
　　*/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectInfo implements Serializable {

    private static final long serialVersionUID = -8282505823680235817L;
    /** 文件id */
    private String id;
    /** 文件名 */
    private String name;
    private String submitAuditDate;
    /** 地点 */
    private String provinceId;
    private String cityId;
    private String districtId;
    /** 模板id */
    private String templateId;
    private String buildStandardTemplateId;
    private String costBidNodeDataId;
    private String itemTypeBuildStandardJson;
}
