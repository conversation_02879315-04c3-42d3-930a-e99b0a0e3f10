<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.category.CategoryTagEnumRelMapper">

    <select id="selectByEnterpriseId" resultType="com.glodon.qydata.entity.standard.category.CategoryTagEnumRel">--
    SELECT *
    FROM category_tag_enum_rel
    WHERE enterprise_id = #{enterpriseId}
    </select>

    <select id="selectNameByEnterpriseId" resultType="com.glodon.qydata.entity.standard.category.CategoryTagEnumRel">--
    SELECT cter.*, ct.name AS tagName, cte.name AS tagEnumName
    FROM category_tag_enum_rel cter
             JOIN category_tag ct ON cter.tag_id = ct.id
             JOIN category_tag_enum cte ON cter.tag_enum_id = cte.id
    WHERE cter.enterprise_id = #{enterpriseId}
    ORDER BY ct.ord ASC, cte.ord ASC
    </select>

    <insert id="saveBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into category_tag_enum_rel (id,category_code,tag_id,tag_enum_id,enterprise_id,create_time) values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.id},
            #{item.categoryCode},
            #{item.tagId},
            #{item.tagEnumId},
            #{item.enterpriseId},
            #{item.createTime}
            )
        </foreach>
    </insert>

    <delete id="deleteByEnterpriseId">
        delete from category_tag_enum_rel where enterprise_id = #{enterpriseId};
    </delete>

</mapper>
