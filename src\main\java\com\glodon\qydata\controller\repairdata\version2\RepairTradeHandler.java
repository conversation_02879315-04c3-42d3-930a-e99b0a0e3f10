package com.glodon.qydata.controller.repairdata.version2;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.controller.repairdata.common.BaseRepairDataHandler;
import com.glodon.qydata.controller.repairdata.common.RepairConst;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.mapper.repairdata.TempTradeMapper;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;


/**
 * <AUTHOR> @description: 修复专业重复数据
 * @date 2023/4/21 14:14
 */
@Service
@Slf4j
public class RepairTradeHandler extends BaseRepairDataHandler<StandardsProjectInfoEntity> {
    @Resource
    private ZbStandardsTradeMapper standardsTradeMapper;
    @Resource
    TempTradeMapper tempTradeMapper;
    @Resource
    ProjectFeatureMapper projectFeatureMapper;
    @Resource
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;
    @Resource
    ProjectFeatureCategoryViewMapper projectFeatureCategoryViewMapper;
    @Resource
    private ProjectFeatureCategoryViewSelfMapper projectFeatureCategoryViewSelfMapper;

    @Autowired
    private CommonProjectCategoryUsedService projectCategoryUsedService;

    @Autowired
    private IProjectFeatureService projectFeatureService;
    /**
     * 检查数据是否需要修复
     * @param customerCode
     * @return 是否需要修复
     */
    @Override
    public boolean isNeedRepair(String customerCode){
        Integer repeatCount = tempTradeMapper.selectRepeatRecord(customerCode);
        if(repeatCount != null && repeatCount > 0) {
            log.error("专业修复后，专业仍然重复:" + customerCode);
            return true;
        }
        if(customerCode.equals(tempTradeMapper.selectValidFeature(customerCode))) {
            log.error("专业修复后，发现无效特征:" + customerCode);
            return true;
        }
        if(customerCode.equals(tempTradeMapper.selectValidFeatureCategoryView(customerCode))) {
            log.error("专业修复后，发现无效特征分类:" + customerCode);
            return true;
        }
        return false;
    }
    Map<Long,  Map<String, ProjectFeature>> buildTradeFeatureMap(List<ProjectFeature> featureList) {
        Map<Long,  Map<String, ProjectFeature>> tradeFeatureFlafMap = new HashMap<>();
        if(featureList == null) {
            return tradeFeatureFlafMap;
        }
        Map<Long,  List<ProjectFeature>> tradeFeatureMap = featureList.stream().collect(Collectors.groupingBy(ProjectFeature:: getTradeId));

        tradeFeatureMap.keySet().forEach(tradeId -> {
            if(!tradeFeatureFlafMap.containsKey(tradeId)) {
                tradeFeatureFlafMap.put(tradeId, new HashMap<>());
            }
            tradeFeatureMap.get(tradeId).forEach(projectFeature -> {
                String hashValue = buildDelimiter(projectFeature.getCustomerCode(), projectFeature.getName(),
                        projectFeature.getTypeCode(), String.valueOf(projectFeature.getType()), projectFeature.getOption(), projectFeature.getUnit());
                tradeFeatureFlafMap.get(tradeId).put(hashValue, projectFeature);
            });

        });
        return tradeFeatureFlafMap;
    }
    Map<Long,  Map<Long,  Map<String, ProjectFeature>>> buildSelfTradeFeatureMap(String customerCode, Integer useType, List<Long> globalIds) {
        Map<Long,  Map<Long,  Map<String, ProjectFeature>>> selfTradeFeatureFlagMap = new HashMap<>();
        globalIds.forEach(globalId -> {
            List<ProjectFeature> featureList = projectFeatureSelfMapper.selectAll(customerCode, useType);
            Map<Long,  Map<String, ProjectFeature>> tradeFeatureFlafMap = buildTradeFeatureMap(featureList);
            selfTradeFeatureFlagMap.put(globalId, tradeFeatureFlafMap);
        });
        return selfTradeFeatureFlagMap;
    }
    boolean hasSameFeature(Map<Long,  Map<String, ProjectFeature>> tradeTradeIdFeatureMap, Long masterTradeId,  Long compareTradeId) {
        AtomicBoolean hasSameFeature = new AtomicBoolean(true);
        Map<String, ProjectFeature> hashValueList = tradeTradeIdFeatureMap.get(compareTradeId);
        if(hashValueList == null) {
            return hasSameFeature.get();
        }
        hashValueList.keySet().forEach(key -> {
            if(!tradeTradeIdFeatureMap.get(masterTradeId).containsKey(key)) {
                hasSameFeature.set(false);
            }
        });
        return hasSameFeature.get();
    }
    List<ProjectFeature> buildInsertFeatures(Map<Long,  Map<String, ProjectFeature>> tradeTradeIdFeatureMap, Map<String, List<ProjectFeatureCategoryView>> groupMapCategoryView,
                                             Long masterTradeId,  Long compareTradeId) {
        Map<String, ProjectFeature> masterFeatureMap = tradeTradeIdFeatureMap.get(masterTradeId);
        Map<String, ProjectFeature> compareFeatureMap = tradeTradeIdFeatureMap.get(compareTradeId);
        List<ProjectFeature> insertFeatureList = new ArrayList<>();

        if (org.springframework.util.CollectionUtils.isEmpty(masterFeatureMap)) {
            return insertFeatureList;
        }

        compareFeatureMap.keySet().forEach(key -> {
            if(masterFeatureMap.containsKey(key)) {
                boolean needRepairCategoryView = groupMapCategoryView.values().stream().filter(list ->
                        list.stream().filter(item ->
                                item.getFeatureId().equals(masterFeatureMap.get(key).getId())
                        ).findFirst().orElse(null) != null).findFirst().orElse(null) == null;
                if(needRepairCategoryView) {
                    List<ProjectFeatureCategoryView> categoryViewList = projectFeatureService.initCategoryView(masterFeatureMap.get(key), groupMapCategoryView);
                    if (CollectionUtils.isNotEmpty(categoryViewList)) {
                        projectFeatureCategoryViewMapper.saveBatch(categoryViewList);
                    }
                }
                return;
            } else {
                // 如果找到关键要素一致则尽心修复
                ProjectFeature newFeature = new ProjectFeature();
                BeanUtils.copyProperties(compareFeatureMap.get(key), newFeature);
                newFeature.setTradeId(masterTradeId);
                newFeature.setOrdTrade(masterFeatureMap.size() + insertFeatureList.size() + 1);
                Date logData = new Date(2023-1900, 07, 07, 0,0,0);
                newFeature.setCreateTime(logData);
                newFeature.setUpdateTime(logData);
                insertFeatureList.add(newFeature);
            }

            if(masterFeatureMap.values().stream().filter(item -> item.getName().equals(compareFeatureMap.get(key).getName())).findFirst().orElse(null)!= null) {
                throw new BusinessException("修复专业下工程特征有重名项");
            }

        });
        return insertFeatureList;
    }
    List<ProjectFeatureCategoryView> buildInsertCategoryView(Long masterTradeId,  Long compareTradeId,
                                                             Map<Long, List<ProjectFeatureCategoryView>> categoryViewMap, Map<String, List<ProjectFeatureCategoryView>> codeCategoryViewMap, List<ProjectFeature> featureList) {
        List<Long> featureIds = featureList.stream().map(ProjectFeature::getId).collect(Collectors.toList());
        List<ProjectFeatureCategoryView> categoryViewList = categoryViewMap.get(compareTradeId).stream().filter(item -> featureIds.contains(item.getFeatureId())).collect(Collectors.toList());
        for (int i = 0; i < categoryViewList.size(); i++) {
            ProjectFeatureCategoryView categoryView = categoryViewList.get(i);
            categoryView.setTradeId(masterTradeId);
            int ord = getMaxtCategoryViewOrd(masterTradeId, categoryView.getCategoryCode() ,codeCategoryViewMap);
            categoryView.setOrdCategory(ord + categoryView.getOrdCategory());
        }
        return categoryViewList;
    }

    /**
     * 获取一专业下分类下 分类关系顺需序号
     * @param masterTradeId 专业id
     * @param categoryCode 分类code
     * @param codeCategoryViewMap 分类map
     * @return
     */
    int getMaxtCategoryViewOrd(Long masterTradeId, String categoryCode, Map<String, List<ProjectFeatureCategoryView>> codeCategoryViewMap) {
        List<ProjectFeatureCategoryView> projectFeatures = Optional.ofNullable(codeCategoryViewMap.get(categoryCode)).orElse(new ArrayList<>());
        // 插入到最后
        Integer max = projectFeatures.stream().filter(x -> x.getTradeId().equals(masterTradeId)).map(ProjectFeatureCategoryView::getOrdCategory).filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
        return max + 1;
    }

    /**
     * 构造专业下分类mao
     * @return
     */
    Map<Long, List<ProjectFeatureCategoryView>> buildCategoryViewMap(Integer type, List<ProjectFeatureCategoryView> categoryViewList) {
        if(categoryViewList == null) {
            return new HashMap<>();
        }
        return categoryViewList.stream()
                .filter(x -> type.equals(x.getType()))
                .collect(Collectors.groupingBy(ProjectFeatureCategoryView::getTradeId));
    }

    Map<Long, Map<Long, List<ProjectFeatureCategoryView>>> buildSelfCategoryViewMap(String customerCode, Integer type, List<Long> globalIds) {
        Map<Long, Map<Long, List<ProjectFeatureCategoryView>>> selfCategoryViewMap = new HashMap<>();
        globalIds.forEach(globalId -> {
            List<ProjectFeatureCategoryView> categoryViewList = projectFeatureCategoryViewSelfMapper.selectBySelfCategoryAndCustomerCode(null, customerCode, null);
            selfCategoryViewMap.put(globalId, buildCategoryViewMap(type, categoryViewList));
        });

        return selfCategoryViewMap;
    }
    /**
     * 修复专业下的特征及特征分类关系表
     */
    void repairFeature(List<ProjectFeature> featureList,
                       List<ProjectFeatureCategoryView> featureCategoryList) {

        Map<Long, List<ProjectFeatureCategoryView>> groupMap = featureCategoryList.stream().collect(Collectors.groupingBy(ProjectFeatureCategoryView::getFeatureId));
        featureList.forEach(feature -> {
            if(!groupMap.containsKey(feature.getId())) {
                return;
            }
            Long newFeatureId = SnowflakeIdUtils.getNextId();
            // 工程特征对应的分类关系表 特征id维护正确
            groupMap.get(feature.getId()).forEach(featureCategory -> {
                featureCategory.setFeatureId(newFeatureId);
            });
            // 更新特征id
            feature.setId(newFeatureId);

        });
        if(!featureList.isEmpty()) {
            projectFeatureMapper.insertBatch(featureList);
        }
        if(!featureCategoryList.isEmpty()) {
            projectFeatureCategoryViewMapper.saveBatch(featureCategoryList);
        }
    }

    @Override
    public boolean repairData(String customerCode) {
        List<ZbStandardsTrade> list = standardsTradeMapper.selectAllListByCustomerCode(customerCode);

        Map<String, List<ZbStandardsTrade>> groupMap = list.stream().collect(Collectors.groupingBy(entity -> buildDelimiter(entity.getCustomerCode(), String.valueOf(entity.getType()), entity.getTradeCode(),
                String.valueOf(entity.getDescription()))));
        Integer useType = projectCategoryUsedService.getUsedCategoryType(customerCode);
        List<ProjectFeature> featureList = projectFeatureMapper.selectAll(customerCode, useType);
        List<ProjectFeatureCategoryView> categoryViewList = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(null, customerCode, useType);

        Map<String, List<ProjectFeatureCategoryView>> groupMapCategoryView = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(categoryViewList)){
            groupMapCategoryView = categoryViewList.stream().collect(Collectors.groupingBy(ProjectFeatureCategoryView::getCategoryCode));
        }
        Map<String, List<ProjectFeatureCategoryView>> codeCategoryViewMap = groupMapCategoryView;
        // 专业id工程特征map
        Map<Long,  Map<String, ProjectFeature>> tradeTradeIdFeatureMap = buildTradeFeatureMap(featureList);
        // 专业id工程特征分类表map
        Map<Long, List<ProjectFeatureCategoryView>> categoryViewMap = buildCategoryViewMap(useType, categoryViewList);

        List<Long> globalIds = tempTradeMapper.selectGlobalIds(customerCode);
        Map<Long,  Map<Long,  Map<String, ProjectFeature>>> selfTradeTradeIdFeatureMap = buildSelfTradeFeatureMap(customerCode, useType, globalIds);
        Map<Long, Map<Long, List<ProjectFeatureCategoryView>>> selfCategoryViewMap = buildSelfCategoryViewMap(customerCode, useType, globalIds);
        groupMap.keySet().forEach(key -> {
            List<ZbStandardsTrade> repeatData = groupMap.get(key);
            if(repeatData.size() < 2) {
                return;
            }
            selfTradeTradeIdFeatureMap.values().forEach(item -> {
                if(item.size() > 0) {
                    throw new BusinessException("专业下工程特征有暂存数据不修复");
                }
            });
            selfCategoryViewMap.values().forEach(item -> {
                if(item.size() > 0) {
                    throw new BusinessException("专业下工程特征分类关系有暂存数据不修复");
                }
            });

            List<Long> repeatTradeIds = repeatData.stream().map(ZbStandardsTrade::getId).collect(Collectors.toList());
            Long masterTradeId = tempTradeMapper.selectValidTradeId(customerCode, repeatTradeIds);
            List<ZbStandardsTrade> sortList = repeatData.stream().sorted(Comparator.comparing(ZbStandardsTrade::getIsDeleted).reversed()).collect(Collectors.toList());
            // 重复数据标记为待删除
            for(int i = 0; i < sortList.size(); ++ i) {
                if(sortList.stream().filter(item -> !RepairConst.data_status_invalid.equals(item.getInvalid())).count() <= 1) {
                    break;
                }
                // 保留被工程特征引用次数最多的专业
                if(sortList.get(i).getId().equals(masterTradeId)) {
                    continue;
                }
                // 如果两个专业的工程特征不一致则，不修复
                if(!hasSameFeature(tradeTradeIdFeatureMap, masterTradeId, sortList.get(i).getId())) {
                    Long tradeId = sortList.get(i).getId();
                    // 构造要迁移的工程特征
                    List<ProjectFeature> listFeature = buildInsertFeatures(tradeTradeIdFeatureMap, codeCategoryViewMap, masterTradeId, tradeId);
                    if(!listFeature.isEmpty()) {
                        // 构造要迁移的工程特征分类关系
                        List<ProjectFeatureCategoryView> listCategoryView = buildInsertCategoryView(masterTradeId, tradeId, categoryViewMap, codeCategoryViewMap, listFeature);
                        // 迁移被删除专业下的特征及分类关系
                        repairFeature(listFeature, listCategoryView);
                    }
                }
                sortList.get(i).setInvalid(RepairConst.data_status_invalid);
            }
        });
        // 获取有效专业id集合
        List<Long> validTradeIds = list.stream().filter(item ->
                !RepairConst.data_status_invalid.equals(item.getInvalid()) && Constants.ZbStandardsTradeConstants.DeletedStatus.NO_DELETED.equals(item.getIsDeleted()))
                .map(ZbStandardsTrade::getId).collect(Collectors.toList());
        if(!validTradeIds.isEmpty()) {
            // 无效数据专业下的特征和特征分类关系表清理掉
            tempTradeMapper.setFeatureInvalid(customerCode, validTradeIds);
            tempTradeMapper.setCategoryViewInvalid(customerCode, validTradeIds);
            tempTradeMapper.setSelfFeatureInvalid(customerCode, validTradeIds);
            tempTradeMapper.setSelfCategoryViewInvalid(customerCode, validTradeIds);
        }

        List<Long> repairTradeIds = list.stream().filter(item -> RepairConst.data_status_invalid.equals(item.getInvalid())).map(ZbStandardsTrade::getId).collect(Collectors.toList());
        if(repairTradeIds.isEmpty()) {
            return false;
        }
        tempTradeMapper.setTradeInvalId(customerCode, repairTradeIds);
        return true;
    }

    @Override
    public boolean checkDataAfterRepair(String customerCode) {
        if(isNeedRepair(customerCode)) {
           throw new BusinessException("工程特征修复后仍重复");
        }
        return true;
    }
}
