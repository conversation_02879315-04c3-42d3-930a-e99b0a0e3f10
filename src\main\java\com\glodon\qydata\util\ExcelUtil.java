package com.glodon.qydata.util;

import com.glodon.qydata.common.annotation.ExcelParam;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.excel.ExcelParseStrategyContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 导入导出Excel工具类
 * <AUTHOR>
 * @date 2020/4/29 19:23
 */

@Slf4j
public class ExcelUtil {


    /**
     * @Title: readExcel
     * @Description: 导入excel表格.返回的是List<Object> 的数据结构,使用反射去赋值.
     * @throws
     * @param:
     * @param file   导入的文件
     * @param clazz 存储数据的实体
     * @return: java.util.List<T>
     * @auther: zhaohy-c
     * @date: 2021/5/31 15:39
     */
    public static <T> List<T> readExcel(MultipartFile file,String sheetName, Class<T> clazz) {
        Workbook workbook = null;
        try {
            //最终返回数据
            List<T> resultList = new ArrayList<>();

            InputStream is = file.getInputStream();
            String name = Objects.requireNonNull(file.getOriginalFilename()).toLowerCase();
            // 创建excel操作对象
            if (!name.contains(".xlsx") && !name.contains(".xls")) {
                throw new BusinessException(ResponseCode.ERROR, "创建Excel异常");
            }
            //使用工厂方法创建.
            workbook = WorkbookFactory.create(is);
            //得到一个工作表
            Sheet sheet = null;
            if(StringUtils.isNotBlank(sheetName)){
                sheet = workbook.getSheet(sheetName);
            }
            if(sheet == null){
                sheet = workbook.getSheetAt(0);
            }
            //获得数据的总行数
            int totalRowNum = sheet.getLastRowNum();
            //获得总列数
            int cellLength = sheet.getRow(0).getPhysicalNumberOfCells();
            //获取反射类的所有字段
            Field [] fields =  clazz.getDeclaredFields();
            //创建一个字段数组,用于和excel行顺序一致.
            Field [] newFields = new Field[cellLength];
            //将excel列下标映射为实体类字段.
            for (Field field : fields) {
                ExcelParam excelParam = field.getAnnotation(ExcelParam.class);
                if (null == excelParam) {
                    continue;
                }
                int index = excelParam.index();
                if (EmptyUtil.isEmpty(index)) {
                    continue;
                }
                newFields[index] = field;
            }

            //从第x行开始获取
            for(int x = 1 ; x <= totalRowNum ; x++){
                T object = clazz.newInstance();
                //获得第i行对象
                Row row = sheet.getRow(x);
                //如果一行里的所有单元格都为空则不放进list里面
                int a = 0;
                for(int y=0;y<cellLength;y++){
                    if (row != null) {
                        Cell cell = row.getCell(y);
                        if (cell == null) {
                            a++;
                        } else {
                            Field field = newFields[y];
                            String value =  getXCellVal(cell);
                            if (field != null && value != null && !value.equals("")){
                                //给字段设置值.
                                setValue(field,value,object);
                            }
                        }
                    }
                }//for
                if(a!=cellLength && row!=null){
                    resultList.add(object);
                }
            }
            return  resultList;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResponseCode.ERROR, "数据转化异常");
        } finally {
            try {
                assert workbook != null;
                workbook.close();
            } catch (IOException e) {
                log.info("流关闭失败");
            }
        }

    }

    public static List<List<String>> readExcel(MultipartFile file){
        try{
            //最终返回数据
            List<List<String>> resultList = new ArrayList<>();

            Workbook workbook = null;
            InputStream is = file.getInputStream();
            String name = file.getOriginalFilename().toLowerCase();
            // 创建excel操作对象
            if (name.contains(".xlsx") || name.contains(".xls")) {
                //使用工厂方法创建.
                workbook = WorkbookFactory.create(is);
            } else {
                throw new BusinessException(ResponseCode.ERROR, "创建Excel异常");
            }
            //得到一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            //获得数据的总行数
            int totalRowNum = sheet.getLastRowNum();
            //获得总列数
            int cellLength = sheet.getRow(0).getPhysicalNumberOfCells();
            //获取表头

            //从第x行开始获取
            for(int x = 1 ; x <= totalRowNum ; x++){
                //获得第i行对象
                Row row = sheet.getRow(x);
                List<String> rowObject = new ArrayList();
                //如果一行里的所有单元格都为空则不放进list里面
                int a = 0;
                for(int y=0;y<cellLength;y++){
                    if (row != null) {
                        Cell cell = row.getCell(y);
                        if (cell == null) {
                            a++;
                            rowObject.add("");
                        } else {
                            String value =  getXCellVal(cell);
                            rowObject.add(value);
                        }
                    }
                }//for
                if(a!=cellLength && row!=null){
                    resultList.add(rowObject);
                }
            }
            return  resultList;
        }catch (Exception e){
            e.printStackTrace();
            throw new BusinessException(ResponseCode.ERROR, "数据转化异常");
        }

    }
    public static List<List<String>> readExcel(MultipartFile file, String type){
        try{
            //最终返回数据
            List<List<String>> resultList = new ArrayList<>();

            Workbook workbook = null;
            InputStream is = file.getInputStream();
            String name = file.getOriginalFilename().toLowerCase();
            // 创建excel操作对象
            if (name.contains(".xlsx") || name.contains(".xls")) {
                //使用工厂方法创建.
                workbook = WorkbookFactory.create(is);
            } else {
                throw new BusinessException(ResponseCode.ERROR, "创建Excel异常");
            }
            //得到一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            //获得数据的总行数
            int totalRowNum = sheet.getLastRowNum();
            //获得总列数
            int cellLength = sheet.getRow(0).getPhysicalNumberOfCells();
            //获取表头
            Row headerRow = sheet.getRow(0);
            validateHeader(headerRow, type);

            //从第x行开始获取
            for(int x = 1 ; x <= totalRowNum ; x++){
                //获得第i行对象
                Row row = sheet.getRow(x);
                List<String> rowObject = new ArrayList();
                //如果一行里的所有单元格都为空则不放进list里面
                int a = 0;
                for(int y=0;y<cellLength;y++){
                    if (row != null) {
                        Cell cell = row.getCell(y);
                        if (cell == null) {
                            a++;
                            rowObject.add("");
                        } else {
                            String value =  getXCellVal(cell);
                            rowObject.add(value);
                        }
                    }
                }//for
                if(a!=cellLength && row!=null){
                    resultList.add(rowObject);
                }
            }
            return  resultList;
        } catch (BusinessException businessException) {
            throw businessException;
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(ResponseCode.ERROR, "数据转化异常");
        }
    }

    /**
     * 校验Excel表头
     *
     * @param headerRow 表头行
     * @param type 表头类型
     * @throws BusinessException 如果表头不符合预期
     */
    public static void validateHeader(Row headerRow, String type) throws BusinessException {
        if (headerRow == null) {
            throw new BusinessException(ResponseCode.EXCEL_HEADER_INVALID_ERROR);
        }

        // 获取期望的表头
        String[] expectedHeaders = ExcelParseStrategyContext.HEADER_MAP.get(type);
        if (expectedHeaders == null) {
            throw new BusinessException(ResponseCode.EXCEL_HEADER_INVALID_ERROR);
        }

        // 校验表头内容
        for (int i = 0; i < expectedHeaders.length; i++) {
            Cell cell = headerRow.getCell(i);
            String headerName = (cell != null) ? cell.getStringCellValue().trim() : "";

            if (!headerName.equals(expectedHeaders[i])) {
                throw new BusinessException(ResponseCode.EXCEL_HEADER_INVALID_ERROR);
            }
        }
    }

    /**
     * @Title: setValue
     * @Description: 给字段赋值,判断值的类型,然后转化成实体需要的类型值.
     * @throws
     * @param:
     * @param field 字段
     * @param value 值
     * @param object 对象
     * @return: void
     * @auther: zhaohy-c
     * @date: 2021/5/31 15:40
     */
    private static void setValue(Field field, String value, Object object) {
        try {
            field.setAccessible(true);
            DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
            if (field.getGenericType().toString().contains("Integer")){
                field.set(object,Integer.valueOf(value));
            }else if(field.getGenericType().toString().contains("String")){
                field.set(object,value);
            }else
            if (field.getGenericType().toString().contains("Date")){
                field.set(object,fmt.parse(value));
            }
            field.setAccessible(false);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * @Title: getXCellVal
     * @Description: 获取单元格中的值
     * @throws
     * @param:
     * @param cell 单元格
     * @return: java.lang.String
     * @auther: zhaohy-c
     * @date: 2021/5/31 15:41
     */
    private static String getXCellVal(Cell cell) {
        DateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
        DecimalFormat df = new DecimalFormat("0.0000");
        String val = "";
        switch (cell.getCellType()) {
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    val = fmt.format(cell.getDateCellValue()); //日期型
                } else {
                    val = df.format(cell.getNumericCellValue()); //数字型
                    // 去掉多余的0，如最后一位是.则去掉
                    val = val.replaceAll("0+?$", "").replaceAll("[.]$","");
                }
                break;
            case STRING: //文本类型
                val = cell.getStringCellValue();
                break;
            case BOOLEAN: //布尔型
                val = String.valueOf(cell.getBooleanCellValue());
                break;
            case BLANK: //空白
                val = cell.getStringCellValue();
                break;
            case ERROR: //错误
                val = "";
                break;
            case FORMULA: //公式
                try {
                    val = String.valueOf(cell.getStringCellValue());
                } catch (IllegalStateException e) {
                    val = String.valueOf(cell.getNumericCellValue());
                }
                break;
            default:
                val = cell.getRichStringCellValue() == null ? null : cell.getRichStringCellValue().toString();
        }
        return val;
    }



    /**
     * @Title: dropDownListXSSF
     * @Description: 给sheet页设置下拉框样式 (切记下拉框数据量少时可用)
     * @throws
     * @param:
     * @param sheet 目标sheet
     * @param datas 下拉框列表
     * @param firstRowIndex 开始行下标
     * @param lastRowIndex 结束行下标
     * @param firstColIndex 开始列下标
     * @param lastColIndex 结束列下标
     * @return: void
     * @auther: zhaohy-c
     * @date: 2021/5/27 16:53
     */
    public static XSSFSheet dropDownListXSSF(XSSFSheet sheet,  String[] datas, int firstRowIndex, int lastRowIndex, int firstColIndex, int lastColIndex){
        XSSFDataValidationHelper dvHelper = new XSSFDataValidationHelper(sheet);
        XSSFDataValidation validation = null;
        validation = (XSSFDataValidation) dvHelper.createValidation(dvHelper.createExplicitListConstraint(datas), new CellRangeAddressList(firstRowIndex, lastRowIndex, firstColIndex, lastColIndex));
        sheet.addValidationData(validation);
        return sheet;
    }

    /**
     * @Title: setLongHSSFValidation
     * @Description: 下拉框数据处理，下拉框数据量大
     * @throws
     * @param:
     * @param workbook
     * @param deptList 下拉数据数组
     * @param sheet 目标sheet页
     * @param firstRow 开始行
     * @param endRow 结束行
     * @param cellNum 下拉框所在的列
     * @param sheetIndex 隐藏sheet页下标
     * @return: void
     * @auther: zhaohy-c
     * @date: 2021/5/27 18:08
     */
    public static void setLongHSSFValidation(XSSFWorkbook workbook, String[] deptList , XSSFSheet sheet , int firstRow, int endRow, int cellNum, int sheetIndex) {
        String hiddenName = "hidden"+cellNum;
        //1.创建隐藏的sheet页。        起个名字吧！叫"hidden"！
        XSSFSheet hidden = workbook.createSheet(hiddenName);
        //2.循环赋值（为了防止下拉框的行数与隐藏域的行数相对应，将隐藏域加到结束行之后）
        for (int i = 0, length = deptList.length; i < length; i++) {
            hidden.createRow(endRow + i).createCell(cellNum).setCellValue(deptList[i]);
        }
        Name category1Name = workbook.createName();
        category1Name.setNameName(hiddenName);
        //3 A1:A代表隐藏域创建第N列createCell(N)时。以A1列开始A行数据获取下拉数组
        category1Name.setRefersToFormula(hiddenName + "!A1:A" + (deptList.length + endRow));
        //
        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createFormulaListConstraint(hiddenName);
        CellRangeAddressList addressList = new CellRangeAddressList(1, endRow, cellNum, cellNum);
        DataValidation dataValidation = helper.createValidation(constraint, addressList);
        if (dataValidation instanceof XSSFDataValidation) {
            // 数据校验
            dataValidation.setSuppressDropDownArrow(true);
            dataValidation.setShowErrorBox(true);
        } else {
            dataValidation.setSuppressDropDownArrow(false);
        }
        // 作用在目标sheet上
        sheet.addValidationData(dataValidation);
        // 设置hiddenSheet隐藏
        workbook.setSheetHidden(sheetIndex, true);
    }

    /**
     * 根据工作簿以及待导出的标题列创建sheet页的表头行
     * @param workbook 工作簿
     * @param titles 待导出的标题列
     */
    public static XSSFSheet createTitle(XSSFWorkbook workbook,String[] titles){
        XSSFSheet sheet = workbook.createSheet();
        //创建表头
        XSSFRow rowTitle = sheet.createRow(0);
        //设置行高
        rowTitle.setHeightInPoints(25);
        //设置为居中加粗
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        XSSFCell cell;
        for(int i=0;i<titles.length;i++){
            //设置列宽度
            sheet.setColumnWidth(i,30*180);
            cell = rowTitle.createCell(i);
            cell.setCellValue(titles[i]);
            cell.setCellStyle(style);
        }
        return sheet;
    }
    /**
     * 根据工作簿以及待导出的标题列创建sheet页的表头行
     * @param workbook 工作簿
     * @param titles 待导出的标题列
     */
    public static XSSFSheet createTitle(String sheetName, XSSFWorkbook workbook,String[] titles){
        XSSFSheet sheet = workbook.createSheet(sheetName);
        //创建表头
        XSSFRow rowTitle = sheet.createRow(0);
        //设置行高
        rowTitle.setHeightInPoints(25);
        //设置为居中加粗
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        XSSFCell cell;
        for(int i=0;i<titles.length;i++){
            //设置列宽度
            sheet.setColumnWidth(i,30*180);
            cell = rowTitle.createCell(i);
            cell.setCellValue(titles[i]);
            cell.setCellStyle(style);
        }
        return sheet;
    }
    /**
     * 根据工作簿以及待导出的标题列创建sheet页的表头行
     * @param workbook 工作簿
     * @param titles 待导出的标题列
     */
    public static synchronized Sheet createTitle(String sheetName, SXSSFWorkbook workbook, String[] titles){
        Sheet sheet = workbook.createSheet(sheetName);
        //创建表头
        Row rowTitle = sheet.createRow(0);
        //设置行高
        rowTitle.setHeightInPoints(25);
        //设置为居中加粗
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        Cell cell;
        for(int i=0;i<titles.length;i++){
            //设置列宽度
            sheet.setColumnWidth(i,30*180);
            cell = rowTitle.createCell(i);
            cell.setCellValue(titles[i]);
            cell.setCellStyle(style);
        }
        return sheet;
    }

    /**
     * 读取Excel值
     * @param row 行
     * @param index 一行的列号(列号是从0开始)
     * @return
     */
    public static String getCellValue(Row row, int index){
        String value=null;
        if(row!=null && index>=0){
            Cell cell=row.getCell(index);
            if(cell!=null){
                org.apache.poi.ss.usermodel.CellType type = cell.getCellType();
                if(org.apache.poi.ss.usermodel.CellType.NUMERIC == type){
                    Double doubleVal = cell.getNumericCellValue();
                    if(doubleVal != null){
                        value = doubleVal.toString();
                    }
                }
                switch (type){
                    case NUMERIC:
                        Double doubleVal = cell.getNumericCellValue();
                        if(doubleVal != null){
                            value = doubleVal.toString();
                        }
                        break;
                    case STRING:
                        value = cell.getStringCellValue();
                        break;
                    default:
                        value = cell.getStringCellValue();
                }
                if(value!=null){
                    value=value.trim();
                    if("".equals(value)){
                        value=null;
                    }
                }
            }
        }
        return value;
    }

    /**
     * 以splitLength为大小对待导出集合进行分割
     * @param list 待导出list集合
     * @param splitLength 每次导出list的长度大小
     * @param <T>
     * @return
     * @throws Exception
     */
    public static <T> List<List<T>> subList(List<T> list, int splitLength) {
        List<List<T>> lists = new ArrayList<>();
        int size;
        if(list!=null&&(size=list.size())>splitLength){
            int cycle = size/splitLength;
            for(int i=0;i<cycle;i++){
                lists.add(list.subList(i*splitLength,splitLength+(i*splitLength)));
            }
            lists.add(list.subList(cycle*splitLength,size));
        }else {
            lists.add(list);
        }
        return lists;
    }

    /**
     * 生成excel文件
     * @param filename
     * @param workbook
     * @throws Exception
     */
    public static void buildExcelFile(String filename,XSSFWorkbook workbook) throws Exception{
        FileOutputStream fos = new FileOutputStream(filename);
        workbook.write(fos);
        fos.flush();
        fos.close();
    }

    /**
     * 将工作簿内容写入到流并供浏览器下载excel
     * @param filename
     * @param workbook
     * @param response
     * @throws Exception
     */
    public static void buildExcelDocument(String filename, Workbook workbook, HttpServletResponse response) throws Exception{
        response.setContentType("application/x-download");
        response.setHeader("Content-Disposition", "attachment;filename="+ URLEncoder.encode(filename, "utf-8"));
        OutputStream outputStream = response.getOutputStream();
        workbook.write(outputStream);
        outputStream.flush();
        outputStream.close();
    }

    /**
     * @description: 生成表头样式
     * @param: * @param workbook
     * @return: org.apache.poi.xssf.usermodel.XSSFCellStyle
     * <AUTHOR>
     * @date: 2021/8/2 10:52
     */
    public static XSSFCellStyle generateTitleStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        XSSFFont font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        java.awt.Color color = new java.awt.Color(156, 195, 230);
        byte[] rgb = new byte[]{(byte)color.getRed(), (byte)color.getGreen(), (byte)color.getBlue()};
        style.setFillForegroundColor(new XSSFColor(rgb, null));
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return style;
    }

    /**
     * @description: 生成内容样式
     * @param: * @param workbook
     * @return: org.apache.poi.xssf.usermodel.XSSFCellStyle
     * <AUTHOR>
     * @date: 2021/8/2 10:48
     */
    public static XSSFCellStyle generateContentStyle(XSSFWorkbook workbook) {
        XSSFCellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        style.setFont(font);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setVerticalAlignment(VerticalAlignment.CENTER);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setBorderTop(BorderStyle.THIN);
        return style;
    }
    public static <T> void downloadExcel(List<T> list, String fileName, HttpServletResponse response, Class<T> t) {
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet();
        XSSFRow titleRow = sheet.createRow(0);
        titleRow.setHeightInPoints(25);
        XSSFCellStyle titleStyle = generateTitleStyle(workbook);
        XSSFCellStyle contentStyle = generateContentStyle(workbook);
        Map<Integer,Integer> widthMap = new HashMap<>();
        for (Field declaredField : t.getDeclaredFields()) {
            declaredField.setAccessible(true);
            ExcelParam excelParam = declaredField.getAnnotation(ExcelParam.class);
            if (excelParam != null) {
                int index = excelParam.index();
                String title = excelParam.value();
                if (StringUtils.isBlank(title)) {
                    title = declaredField.getName();
                }
                XSSFCell cell = titleRow.createCell(index);
                cell.setCellValue(title);
                cell.setCellStyle(titleStyle);
                widthMap.put(index,title.getBytes().length);
            }
        }
        if (CollectionUtils.isNotEmpty(list)) {
            for (int i = 0; i < list.size(); i++) {
                T t1 = list.get(i);
                Row row = sheet.createRow(i+1);
                row.setHeightInPoints(20);
                for (Field declaredField : t1.getClass().getDeclaredFields()) {
                    declaredField.setAccessible(true);
                    ExcelParam excelParam = declaredField.getAnnotation(ExcelParam.class);
                    if (null == excelParam) {
                        continue;
                    }
                    int index = excelParam.index();
                    Cell cell = row.createCell(index);
                    cell.setCellStyle(contentStyle);
                    try {
                        Object result = declaredField.get(t1);
                        if (null == result) {
                            continue;
                        }
                        if (widthMap.get(index) < result.toString().length()) {
                            widthMap.put(index,result.toString().getBytes().length);
                        }
                        if (result instanceof Number) {
                            cell.setCellValue(((Number)result).doubleValue());
                        } else if (result instanceof Date) {
                            cell.setCellValue((Date)result);
                        } else if (result instanceof Calendar) {
                            cell.setCellValue((Calendar)result);
                        } else if (result instanceof RichTextString) {
                            cell.setCellValue((RichTextString)result);
                        } else {
                            cell.setCellValue(String.valueOf(result));
                        }
                    } catch (IllegalAccessException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        for (Map.Entry<Integer, Integer> integerIntegerEntry : widthMap.entrySet()) {
            sheet.setColumnWidth(integerIntegerEntry.getKey(),Math.min(255*256,integerIntegerEntry.getValue() * 256));
        }
        try {
            buildExcelDocument(fileName + ".xlsx",workbook,response);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @Title: createCellRangeAddress
     * @Description: 创建合并单元格并赋值
     * @throws
     * @param:
     * @param workbook
     * @param sheet
     * @param firstRow
     * @param lastRow
     * @param firstCol
     * @param lastCol
     * @param cellValue
     * @return: void
     * @auther: zhaohy-c
     * @date: 2021/6/16 10:36
     */
    public static void createCellRangeAddress(XSSFWorkbook workbook, XSSFSheet sheet, int firstRow, int lastRow, int firstCol, int lastCol, XSSFRichTextString cellValue) {
        CellRangeAddress cra = new CellRangeAddress(firstRow, lastRow, firstCol, lastCol);
        sheet.addMergedRegion(cra);
        Row row = sheet.createRow(firstRow);
        Cell cell = row.createCell(firstCol);
        XSSFCellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setWrapText(true);
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cell.setCellValue(cellValue);
        cell.setCellStyle(cellStyle);
    }

    public static <T> List<T> readExcel(InputStream is, String sheetName, Class<T> clazz){
        try{
            //最终返回数据
            List<T> resultList = new ArrayList<>();

            Workbook workbook = WorkbookFactory.create(is);
            //得到一个工作表
            Sheet sheet = null;
            if(StringUtils.isNotBlank(sheetName)){
                sheet = workbook.getSheet(sheetName);
            }
            if(sheet == null){
                sheet = workbook.getSheetAt(0);
            }
            //获得数据的总行数
            int totalRowNum = sheet.getLastRowNum();
            //获得总列数
            int cellLength = sheet.getRow(0).getPhysicalNumberOfCells();
            //获取反射类的所有字段
            Field [] fields =  clazz.getDeclaredFields();
            //创建一个字段数组,用于和excel行顺序一致.
            Field [] newFields = new Field[cellLength];
            //将excel列下标映射为实体类字段.
            for (Field field : fields) {
                ExcelParam excelParam = field.getAnnotation(ExcelParam.class);
                if (null == excelParam) {
                    continue;
                }
                int index = excelParam.index();
                if (EmptyUtil.isEmpty(index)) {
                    continue;
                }
                newFields[index] = field;
            }

            //从第x行开始获取
            for(int x = 1 ; x <= totalRowNum ; x++){
                T object = clazz.newInstance();
                //获得第i行对象
                Row row = sheet.getRow(x);
                //如果一行里的所有单元格都为空则不放进list里面
                int a = 0;
                for(int y=0;y<cellLength;y++){
                    if (row != null) {
                        Cell cell = row.getCell(y);
                        if (cell == null) {
                            a++;
                        } else {
                            Field field = newFields[y];
                            String value =  getXCellVal(cell);
                            if (field != null && value != null && !value.equals("")){
                                //给字段设置值.
                                setValue(field,value,object);
                            }
                        }
                    }
                }//for
                if(a!=cellLength && row!=null){
                    resultList.add(object);
                }
            }
            return  resultList;
        }catch (Exception e){
            e.printStackTrace();
            throw new BusinessException(ResponseCode.ERROR, "数据转化异常");
        }

    }

    /**
     * 获取文件后缀名
     *
     * @param file
     * @return
     */
    public static String getFileSuffix(File file) {
        if (file == null) {
            return null;
        }
        String suffix = null;
        String fileName = file.getName();
        if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
            suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
        }
        return suffix;
    }

}
