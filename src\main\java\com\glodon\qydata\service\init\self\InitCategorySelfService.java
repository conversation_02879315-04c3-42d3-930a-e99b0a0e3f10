package com.glodon.qydata.service.init.self;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.mapper.standard.category.CommonProjCategorySelfMapper;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 工程分类暂存数据初始化
 * @date 2022/7/8 17:37
 */
@Service
public class InitCategorySelfService extends InitSelfService {
    @Autowired
    private CommonProjCategorySelfMapper commonProjCategorySelfMapper;

    @Autowired
    private CommonProjCategoryService commonProjCategoryService;

    public void initData(String customerCode, Integer type){
        super.initData(customerCode, type, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        return CollectionUtils.isEmpty(commonProjCategorySelfMapper.selectSelfAll(customerCode, type, Constants.CategoryConstants.WHETHER_TRUE));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
        // 暂存数据没有，但是有发布数据
        List<CommonProjCategory> publishCategories = commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_TRUE);

        if (CollectionUtils.isEmpty(publishCategories)){
            return;
        }

        for (CommonProjCategory commonProjCategory : publishCategories) {
            commonProjCategory.setOriginId(commonProjCategory.getId());
            commonProjCategory.setId(null);
        }

        commonProjCategorySelfMapper.saveBatchSelfProjCategory(publishCategories);
    }
}
