apiAuth:
  appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
  appSecret: EVmzN0G5ebSGh44q9NNhVlwDywvBxSXd
  g-signature: F864378123E9BA92E14E6C7862257FCC
  url: https://api-auth.glodon.com
basic_auth:
  server:
    secret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC
  service:
    key: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
config:
  depend:
    digitalCostApiUrl: ${ditital.cost.domain}
    glodonApiAuthUrl: https://api-auth.glodon.com
    glodonEntUrl: ${account.ent.domain}
    glodonUrl: ${account.domain}
    trustUrl: ${dcost.sub.domain}
    zbsqItemUrl: ${dcost.sub.domain}/item
  gcw_cloud:
    APPId: dcost_zblib
    secret: 0L4M99Jl
    secretKey: 1deb69b1411600c5df6f3832b7b96a6b
glodon:
  gcw_cloud_url: http://cloud-api.gldjc.com
  getUserinfoByIdentityHost: ${account.domain}
  middlegroundhost: http://server-gcdp-mbcb.gcost-platform-pd
ip2region:
  path: /opt/project/data/ip2region.db
log:
  level: INFO
privateDeployment: true
sendIoLog: false
sendLogDebug: false
sequence:
  defaultCacheSize: 1000
  defaultIntegerIdStartValue: *********
server:
  port: 8083
sharding_table:
  current_table_name: '{0:''zb_project_feature_category_view_standards'', 1:''tb_commonprojcategory_standards''}'
spring:
  jmx:
    enable: false
  application:
    name: qydata-basic-info
  banner:
    location: banner.txt
  datasource:
    master:
      driverClassName: com.mysql.cj.jdbc.Driver
      password: ${mysql.password}
      type: com.alibaba.druid.pool.DruidDataSource
      url: jdbc:mysql://${mysql.url}/db_cost_data_platform_pro?rewriteBatchedStatements=true&useUnicode=true&amp&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&allowMultiQueries=true&zeroDateTimeBehavior=CONVERT_TO_NULL&sessionVariables=group_concat_max_len=204800&scrollTolerantForwardOnly=true
      username: ${mysql.username}
  main:
    allow-bean-definition-overriding: true
  redis:
    host: ${redis.host}
    jedis:
      pool:
        max-active: 800
        max-idle: 400
        max-wait: 18000
        min-idle: 1
    maxTotal: 300
    password: ${redis.password}
    port: ${redis.port}
    timeout: 10000ms
  servlet:
    multipart:
      max-file-size: 80MB
      max-request-size: 100MB
  shardingsphere:
    datasource:
      ds1:
        driver-class-name: com.mysql.cj.jdbc.Driver
        password: ${mysql.password}
        type: com.alibaba.druid.pool.DruidDataSource
        url: jdbc:mysql://${mysql.url}/basic_info?rewriteBatchedStatements=true&useUnicode=true&amp&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&allowMultiQueries=true&zeroDateTimeBehavior=CONVERT_TO_NULL&sessionVariables=group_concat_max_len=204800&scrollTolerantForwardOnly=true
        username: ${mysql.username}
      names: ds1
    props:
      sql:
        show: false
    sharding:
      tables:
        tb_commonprojcategory_standards:
          actual-data-nodes: ds1.tb_commonprojcategory_standards
          table-strategy:
            standard:
              precise-algorithm-class-name: com.glodon.qydata.config.CustomShardingAlgorithm
              sharding-column: qy_code
        zb_project_feature_category_view_standards:
          actual-data-nodes: ds1.zb_project_feature_category_view_standards
          key-generator:
            column: id
            props:
              worker-id: 123
            type: SNOWFLAKE
          table-strategy:
            standard:
              precise-algorithm-class-name: com.glodon.qydata.config.CustomShardingAlgorithm
              sharding-column: customer_code