package com.glodon.qydata.common.enums;

import com.glodon.qydata.dto.StandardReferTradeDto;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @title: TradeReferEnum
 * @projectName gcj_zb_company_site
 * @description: 标准打通-参考专业枚举类
 * @date 2021/10/21 15:45
 */
public enum TradeReferEnum {
    TU_JIAN("1001","土建工程"),
    AN_ZHAUNG("1008","安装工程"),
    TONG_FENG_KONG_TIAO("1009","通风空调工程"),
    XIAO_FANG("1011","消防工程"),
    JING_ZHUANG_XIU("1012","精装修工程"),
    YUAN_LIN("1013","园建工程"),
    LV_HUA("1014","绿化工程"),

    /**
     * 以下为2023 sprint11新增参考专业
     */
    JI_KANG_ZHI_HU("1003","基坑支护工程"),
    ZHUANG_JI("1004","桩基工程"),
    MEN_CHUANG("1005","门窗工程"),
    ZHI_NENG_HUA("1010","智能化工程"),
    MU_QIANG("1015","幕墙工程"),
    CAI_GUANG_DING("1016","采光顶工程"),
    FAN_GUANG_ZHAO_MING("1017","泛光照明工程"),
    DAO_HANG_BIAO_SHI("1018","导航标识工程"),
    SHI_ZHENG("1019","市政工程"),
    QI_TA("1100","其他工程");


    private String code;
    private String name;
    private List<String> referCode;
    TradeReferEnum(String code, String name){
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<String> getReferCode() {
        return referCode;
    }

    public void setReferCode(List<String> referCode) {
        this.referCode = referCode;
    }

    /**
     * 查询列表
     */
    public static List<StandardReferTradeDto> toList(){
        List<StandardReferTradeDto> returnList = new ArrayList<>();
        TradeReferEnum[] enums = TradeReferEnum.values();
        for (TradeReferEnum anEnum : enums) {
            StandardReferTradeDto bo = new StandardReferTradeDto();
            bo.setDescription(anEnum.getName());
            bo.setTradeCode(anEnum.getCode());
            bo.setReferCode(anEnum.getReferCode());
            returnList.add(bo);
        }
        return returnList;
    }

    /**
     * 根据专业编码查询枚举类型
     */
    public static TradeReferEnum getEnumByTradeCode(String tradeCode){
        TradeReferEnum[] enums = TradeReferEnum.values();
        for (TradeReferEnum anEnum : enums) {
            List<String> referCodeList = anEnum.getReferCode();
            if(referCodeList.contains(tradeCode)){
                return anEnum;
            }
        }
        return null;
    }


    /**
     * 根据引用编码查询枚举类型
     */
    public static TradeReferEnum getEnumByReferTradeCode(String referTradeCode){
        TradeReferEnum[] enums = TradeReferEnum.values();
        for (TradeReferEnum anEnum : enums) {
          String tradeCode = anEnum.getCode();
          if(tradeCode.equals(referTradeCode)){
              return anEnum;
          }
        }
        return null;
    }

    /**
    　　* @description: 系统内置专业与预定20编码对应
    　　* <AUTHOR>
    　　* @date 2021/10/21 18:20
    　　*/
    static {
        /**
         * 土建工程 1001
         */
        String[] tradeCodeArr1 = {"20101", "20102", "20103", "20104", "20105", "20106", "20107", "20108", "20109", "20110",
                "20111", "20112", "20113", "20114", "20115", "20116", "20117", "20118", "20119", "20120"
        };
        List<String> tradeCode1 = Arrays.asList(tradeCodeArr1);
        TU_JIAN.setReferCode(tradeCode1);

        /**
         * 安装工程 1008
         */
        String[] tradeCodeArr2 = {"20801", "20802", "20803", "20804", "20805", "20806", "20807", "20808", "20809", "20810",
                "20811", "20812", "20813", "20814", "20815", "20816", "20817", "20818", "20819", "20820"
        };
        List<String> tradeCode2 = Arrays.asList(tradeCodeArr2);
        AN_ZHAUNG.setReferCode(tradeCode2);

        /**
         * 通风空调工程 1009
         */
        String[] tradeCodeArr3 = {"20901", "20902", "20903", "20904", "20905", "20906", "20907", "20908", "20909", "20910",
                "20911", "20912", "20913", "20914", "20915", "20916", "20917", "20918", "20919", "20920"
        };
        List<String> tradeCode3 = Arrays.asList(tradeCodeArr3);
        TONG_FENG_KONG_TIAO.setReferCode(tradeCode3);

        /**
         * 消防工程 1011
         */
        String[] tradeCodeArr4 = {"21101", "21102", "21103", "21104", "21105", "21106", "21107", "21108", "21109", "21110",
                "21111", "21112", "21113", "21114", "21115", "21116", "21117", "21118", "21119", "21120"
        };
        List<String> tradeCode4 = Arrays.asList(tradeCodeArr4);
        XIAO_FANG.setReferCode(tradeCode4);

        /**
         * 精装修工程 1012
         */
        String[] tradeCodeArr5 = {"21201", "21202", "21203", "21204", "21205", "21206", "21207", "21208", "21209", "21210",
                "21211", "21212", "21213", "21214", "21215", "21216", "21217", "21218", "21219", "21220"
        };
        List<String> tradeCode5 = Arrays.asList(tradeCodeArr5);
        JING_ZHUANG_XIU.setReferCode(tradeCode5);

        /**
         * 园建工程 1013
         */
        String[] tradeCodeArr6 = {"21301", "21302", "21303", "21304", "21305", "21306", "21307", "21308", "21309", "21310",
                "21311", "21312", "21313", "21314", "21315", "21316", "21317", "21318", "21319", "21320"
        };
        List<String> tradeCode6 = Arrays.asList(tradeCodeArr6);
        YUAN_LIN.setReferCode(tradeCode6);

        /**
         * 绿化工程 1014
         */
        String[] tradeCodeArr7 = {"21401", "21402", "21403", "21404", "21405", "21406", "21407", "21408", "21409", "21410",
                "21411", "21412", "21413", "21414", "21415", "21416", "21417", "21418", "21419", "21420"};
        List<String> tradeCode7 = Arrays.asList(tradeCodeArr7);
        LV_HUA.setReferCode(tradeCode7);

        /**
         * 基坑支护工程 1003
         */
        String[] tradeCodeArr8 = {"20301", "20302", "20303", "20304", "20305", "20306", "20307", "20308",
                "20309", "20310", "20311", "20312", "203013", "20314", "20315", "20316", "20317", "20318", "20319", "20320"};
        List<String> tradeCode8 = Arrays.asList(tradeCodeArr8);
        JI_KANG_ZHI_HU.setReferCode(tradeCode8);

        /**
         * 桩基工程 1004
         */
        String[] tradeCodeArr9 = {"20401", "20402", "20403", "20404", "20405", "20406", "20407", "20408",
                "20409", "20410", "20411", "20412", "204013", "20414", "20415", "20416", "20417", "20418", "20419", "20420"};
        List<String> tradeCode9 = Arrays.asList(tradeCodeArr9);
        ZHUANG_JI.setReferCode(tradeCode9);

        /**
         * 门窗工程 1005
         */
        String[] tradeCodeArr10 = {"20501", "20502", "20503", "20504", "20505", "20506", "20507", "20508",
                "20509", "20510", "20511", "20512", "205013", "20514", "20515", "20516", "20517", "20518", "20519", "20520"};
        List<String> tradeCode10 = Arrays.asList(tradeCodeArr10);
        MEN_CHUANG.setReferCode(tradeCode10);

        /**
         * 智能化工程 1010
         */
        String[] tradeCodeArr11 = {"21001", "21002", "21003", "21004", "21005", "21006", "21007", "21008",
                "21009", "21010", "21011", "21012", "210013", "21014", "21015", "21016", "21017", "21018", "21019", "21020"};
        List<String> tradeCode11 = Arrays.asList(tradeCodeArr11);
        ZHI_NENG_HUA.setReferCode(tradeCode11);

        /**
         * 幕墙工程 1015
         */
        String[] tradeCodeArr12 = {"21501", "21502", "21503", "21504", "21505", "21506", "21507", "21508",
                "21509", "21510", "21511", "21512", "215013", "21514", "21515", "21516", "21517", "21518", "21519", "21520"};
        List<String> tradeCode12 = Arrays.asList(tradeCodeArr12);
        MU_QIANG.setReferCode(tradeCode12);

        /**
         * 采光顶工程 1016
         */
        String[] tradeCodeArr13 = {"21601", "21602", "21603", "21604", "21605", "21606", "21607", "21608",
                "21609", "21610", "21611", "21612", "216013", "21614", "21615", "21616", "21617", "21618", "21619", "21620"};
        List<String> tradeCode13 = Arrays.asList(tradeCodeArr13);
        CAI_GUANG_DING.setReferCode(tradeCode13);

        /**
         * 泛光照明工程 1017
         */
        String[] tradeCodeArr14 = {"21701", "21702", "21703", "21704", "21705", "21706", "21707", "21708",
                "21709", "21710", "21711", "21712", "217013", "21714", "21715", "21716", "21717", "21718", "21719", "21720"};
        List<String> tradeCode14 = Arrays.asList(tradeCodeArr14);
        FAN_GUANG_ZHAO_MING.setReferCode(tradeCode14);

        /**
         * 导航标识工程 1018
         */
        String[] tradeCodeArr15 = {"21801", "21802", "21803", "21804", "21805", "21806", "21807", "21808",
                "21809", "21810", "21811", "21812", "218013", "21814", "21815", "21816", "21817", "21818", "21819", "21820"};
        List<String> tradeCode15 = Arrays.asList(tradeCodeArr15);
        DAO_HANG_BIAO_SHI.setReferCode(tradeCode15);

        /**
         * 市政工程 1019
         */
        String[] tradeCodeArr16 = {"21901", "21902", "21903", "21904", "21905", "21906", "21907", "21908",
                "21909", "21910", "21911", "21912", "219013", "21914", "21915", "21916", "21917", "21918", "21919", "21920"};
        List<String> tradeCode16 = Arrays.asList(tradeCodeArr16);
        SHI_ZHENG.setReferCode(tradeCode16);

        /**
         * 其他工程 1100
         */
        String[] tradeCodeArr17 = {"210001", "210002", "210003", "210004", "210005", "210006", "210007",
                "210008", "210009", "210010", "210011", "210012", "2100013", "210014", "210015", "210016", "210017", "210018", "210019", "210020"};
        List<String> tradeCode17= Arrays.asList(tradeCodeArr17);
        QI_TA.setReferCode(tradeCode17);
    }
}
