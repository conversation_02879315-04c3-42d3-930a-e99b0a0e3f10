package com.glodon.qydata.controller.repairdata.version3;

import com.glodon.qydata.controller.repairdata.common.BaseRepairDataHandler;
import com.glodon.qydata.controller.repairdata.common.BaseRepairManagerImpl;
import com.glodon.qydata.controller.repairdata.common.RepairVersionConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 计算口径修复管理者
 */
@Service
@Slf4j
public class RepairErroCategoryManagerImpl extends BaseRepairManagerImpl {

    @Autowired
    RepairProjectCategoryErroHandler repairProjectCategoryErroHandler;

    @Override
    protected List<BaseRepairDataHandler> getHandlerList() {
        return Arrays.asList(repairProjectCategoryErroHandler);
    }

    @Override
    protected Integer getRepairVersion() {
        return RepairVersionConst.repair_version_3;
    }
    @Override
    protected List<String> getCustomCodeList () {
        return repairProjectCategoryErroHandler.selectAllCustomerCode();
    }
}
