package com.glodon.qydata.dto;

import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: luoml-b
 * @date: 2024/8/30 15:03
 * @description: 升降级入参
 */
@Data
public class ProjectInfoPromotionOrDemotionDTO implements Serializable {

    private static final long serialVersionUID = -7924454475534019651L;

    /**
     * 需要升降级的分组
     */
    private StandardsProjectInfoEntity group;

    /**
     * type: 1：升级，2：降级
     */
    private Integer type;

    private String customerCode;

}
