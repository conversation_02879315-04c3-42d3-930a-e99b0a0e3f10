<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.trade.TradeTagEnumMapper">
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.trade.TradeTagEnum">
    <!--@mbg.generated-->
    <!--@Table trade_tag_enum-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="enterprise_id" jdbcType="VARCHAR" property="enterpriseId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="modify_time" jdbcType="TIMESTAMP" property="modifyTime" />
    <result column="ord" jdbcType="INTEGER" property="ord" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, `name`, enterprise_id, create_time, modify_time, ord
  </sql>
  <select id="selectSystemTradeTags" resultType="com.glodon.qydata.entity.standard.trade.TradeTagEnum">
    select
    <include refid="Base_Column_List" />
    from trade_tag_enum
    where enterprise_id=-100
  </select>
  <!--按企业查询当前企业的专业分类-->
  <select id="selectAllListByCustomerCode" resultMap="BaseResultMap" parameterType="java.lang.String" useCache="false" flushCache="true">
    select <include refid="Base_Column_List"/>
    FROM trade_tag_enum
    where enterprise_id = #{customerCode} order by ord ASC
  </select>
  <!--保存专业标签数据-->
  <insert id="saveBatch" parameterType="java.util.List" useGeneratedKeys="false">
    insert into trade_tag_enum (id,name,enterprise_id,create_time,modify_time, ord) values
    <foreach collection="list" item="item" index="index" separator="," >
      (
      #{item.id},
      #{item.name},
      #{item.enterpriseId},
      #{item.createTime},
      #{item.modifyTime},
      #{item.ord}
      )
    </foreach>
  </insert>
  <insert id="saveOrUpdate" parameterType="java.util.List" useGeneratedKeys="false">
    <foreach collection="list" item="item" index="index" separator=";" >
      insert into trade_tag_enum (id, name, enterprise_id, create_time, modify_time, ord) values
      (
      #{item.id},
      #{item.name},
      #{item.enterpriseId},
      #{item.createTime},
      #{item.modifyTime},
      #{item.ord}
      )
      ON DUPLICATE KEY UPDATE
      name = VALUES(item.name),
      create_time = VALUES(item.createTime),
      modify_time = VALUES(item.modifyTime),
      ord = VALUES(item.ord)
    </foreach>
  </insert>
  <!--删除当前企业的专业分类-->
  <delete id="removeAllByCustomCode" parameterType="java.lang.String">
    delete from trade_tag_enum
    where  enterprise_id = #{customerCode}
  </delete>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from trade_tag_enum
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from trade_tag_enum
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" parameterType="com.glodon.qydata.entity.standard.trade.TradeTagEnum">
    <!--@mbg.generated-->
    insert into trade_tag_enum (id, `name`, enterprise_id, 
      create_time, modify_time, ord
      )
    values (#{id,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{enterpriseId,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{modifyTime,jdbcType=TIMESTAMP}, #{ord,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.glodon.qydata.entity.standard.trade.TradeTagEnum">
    <!--@mbg.generated-->
    insert into trade_tag_enum
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="enterpriseId != null">
        enterprise_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="modifyTime != null">
        modify_time,
      </if>
      <if test="ord != null">
        ord,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseId != null">
        #{enterpriseId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ord != null">
        #{ord,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.glodon.qydata.entity.standard.trade.TradeTagEnum">
    <!--@mbg.generated-->
    update trade_tag_enum
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="enterpriseId != null">
        enterprise_id = #{enterpriseId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="modifyTime != null">
        modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="ord != null">
        ord = #{ord,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.glodon.qydata.entity.standard.trade.TradeTagEnum">
    <!--@mbg.generated-->
    update trade_tag_enum
    set `name` = #{name,jdbcType=VARCHAR},
      enterprise_id = #{enterpriseId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      modify_time = #{modifyTime,jdbcType=TIMESTAMP},
      ord = #{ord,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>