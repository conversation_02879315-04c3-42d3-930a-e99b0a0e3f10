/**
 *    Copyright 2009-2015 the original author or authors.
 *
 *    Licensed under the Apache License, Version 2.0 (the "License");
 *    you may not use this file except in compliance with the License.
 *    You may obtain a copy of the License at
 *
 *       http://www.apache.org/licenses/LICENSE-2.0
 *
 *    Unless required by applicable law or agreed to in writing, software
 *    distributed under the License is distributed on an "AS IS" BASIS,
 *    WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *    See the License for the specific language governing permissions and
 *    limitations under the License.
 */
package com.glodon.qydata.config.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * <AUTHOR>
 */
public class BigDecimalTypeHandler extends BaseTypeHandler<BigDecimal> {

  @Override
  public void setNonNullParameter(PreparedStatement ps, int i, BigDecimal parameter, JdbcType jdbcType)
      throws SQLException {
    ps.setBigDecimal(i, parameter);
  }

  @Override
  public BigDecimal getNullableResult(ResultSet rs, String columnName)
      throws SQLException {
    BigDecimal bigDecimal = rs.getBigDecimal(columnName);
    if (null != bigDecimal) {
      bigDecimal = bigDecimal.setScale(3,BigDecimal.ROUND_HALF_UP);
    }
    return bigDecimal;
  }

  @Override
  public BigDecimal getNullableResult(ResultSet rs, int columnIndex)
      throws SQLException {
    BigDecimal bigDecimal = rs.getBigDecimal(columnIndex);
    if (null != bigDecimal) {
      bigDecimal = bigDecimal.setScale(3,BigDecimal.ROUND_HALF_UP);
    }
    return bigDecimal;
  }

  @Override
  public BigDecimal getNullableResult(CallableStatement cs, int columnIndex)
      throws SQLException {
    BigDecimal bigDecimal = cs.getBigDecimal(columnIndex);
    if (null != bigDecimal) {
      bigDecimal = bigDecimal.setScale(3,BigDecimal.ROUND_HALF_UP);
    }
    return bigDecimal;
  }
}
