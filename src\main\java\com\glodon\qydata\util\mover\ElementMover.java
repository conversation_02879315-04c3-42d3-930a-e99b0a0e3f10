package com.glodon.qydata.util.mover;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.exception.BusinessException;
import org.apache.commons.collections.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * sortList传入前必须排好序
 * uniqueIdentity是唯一标识，不一定是id，注意重写父类方法时使用的标识字段
 */
public class ElementMover {

    /**
     * 移动元素，并根据需要保存到数据库
     *
     * @param sortList      排序过的元素列表
     * @param uniqueIdentity 唯一标识
     * @param moveType      移动类型，up表示向上移动，down表示向下移动
     * @param databaseSaver 数据库保存器，为空就不入库
     * @param <T>           可移动元素的类型，需要实现Movable接口
     */
    public static <T extends Movable> void move(List<T> sortList, Long uniqueIdentity, String moveType, DatabaseSaver<T> databaseSaver) {
        if (CollectionUtils.isEmpty(sortList)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "列表记录为空，请刷新重试");
        }

        Set<Long> ordChangeSet = new HashSet<>();

        // 先判断 ord 字段是否有重复
        boolean hasDuplicateOrd = checkForDuplicateOrd(sortList);
        if (hasDuplicateOrd) {
            handleDuplicateOrd(sortList, ordChangeSet);
        }

        // 根据 ID 进行移动操作
        int currentIndex = findIndexById(sortList, uniqueIdentity);
        if (isValidIndex(sortList, currentIndex, moveType)) {
            int targetIndex = Objects.equals(moveType, Constants.MOVE_TYPE_UP) ? currentIndex - 1 : currentIndex + 1;
            updateSwappedOrd(sortList, currentIndex, targetIndex, ordChangeSet);
        }

        if (databaseSaver == null || ordChangeSet.isEmpty()){
            return;
        }
        // 筛选出 ord 变化的元素，保存到数据库
        List<T> updateList = sortList.stream().filter(x -> ordChangeSet.contains(x.getUniqueIdentity())).collect(Collectors.toList());
        saveToDatabase(updateList, databaseSaver);
    }

    /**
     * 检查是否存在重复的 ord 字段
     *
     * @param list 元素列表
     * @param <T>  可移动元素的类型，需要实现Movable接口
     * @return 是否存在重复的 ord 字段
     */
    private static <T extends Movable> boolean checkForDuplicateOrd(List<T> list) {
        Map<Integer, Long> ordCounts = list.stream()
                .collect(Collectors.groupingBy(Movable::getOrdValue, Collectors.counting()));
        return ordCounts.values().stream().anyMatch(count -> count > 1);
    }

    /**
     * 处理重复的 ord 字段，为每个元素重新分配唯一的 ord 值
     *
     * @param list          元素列表
     * @param ordChangeSet  用于记录 ord 发生变化的元素的ID集合
     * @param <T>           可移动元素的类型，需要实现Movable接口
     */
    private static <T extends Movable> void handleDuplicateOrd(List<T> list, Set<Long> ordChangeSet) {
        int nextOrd = 1;
        for (T item : list) {
            if (item.getOrdValue() != nextOrd) {
                ordChangeSet.add(item.getUniqueIdentity());
            }
            item.setOrdValue(nextOrd++);
        }
    }

    /**
     * 根据 ID 查找元素在列表中的索引
     *
     * @param list 元素列表
     * @param uniqueIdentity 唯一标识
     * @param <T>  可移动元素的类型，需要实现Movable接口
     * @return 元素在列表中的索引，若未找到则返回-1
     */
    private static <T extends Movable> int findIndexById(List<T> list, Long uniqueIdentity) {
        for (int i = 0; i < list.size(); i++) {
            if (Objects.equals(list.get(i).getUniqueIdentity(), uniqueIdentity)) {
                return i;
            }
        }
        throw new BusinessException(ResponseCode.PARAMETER_ERROR, "操作的记录不存在，请刷新重试");
    }

    /**
     * 检查索引是否有效
     *
     * @param list  元素列表
     * @param index 索引值
     * @param <T>   元素的类型
     * @return 索引是否有效
     */
    private static <T> boolean isValidIndex(List<T> list, int index, String moveType) {
        if (list.size() == 1){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "只有一条记录，无法移动");
        }

        if (Objects.equals(moveType, Constants.MOVE_TYPE_UP) && index == 0){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "最上面的记录不能上移");
        }

        if (Objects.equals(moveType, Constants.MOVE_TYPE_DOWN) && index == list.size() - 1){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "最下面的记录不能下移");
        }

        return true;
    }

    /**
     * 交换元素的 ord 值并记录发生变化的元素
     *
     * @param list          元素列表
     * @param index1        第一个元素的索引
     * @param index2        第二个元素的索引
     * @param ordChangeSet  用于记录 ord 发生变化的元素的ID集合
     * @param <T>           可移动元素的类型，需要实现Movable接口
     */
    private static <T extends Movable> void updateSwappedOrd(List<T> list, int index1, int index2, Set<Long> ordChangeSet) {
        T item1 = list.get(index1);
        T item2 = list.get(index2);

        // 交换 ord 值
        Integer ord1 = item1.getOrdValue();
        Integer ord2 = item2.getOrdValue();
        item1.setOrdValue(ord2);
        item2.setOrdValue(ord1);

        // 记录发生变化的元素
        ordChangeSet.add(item1.getUniqueIdentity());
        ordChangeSet.add(item2.getUniqueIdentity());
    }


    /**
     * 将元素保存到数据库
     *
     * @param list          元素列表
     * @param databaseSaver 数据库保存器
     * @param <T>           元素的类型
     */
    public static <T extends Movable> void saveToDatabase(List<T> list, DatabaseSaver<T> databaseSaver) {
        databaseSaver.batchUpdateOrd(list);
    }

    /**
     * 数据库保存器接口
     *
     * @param <T> 元素的类型
     */
    public interface DatabaseSaver<T extends Movable> {
        void batchUpdateOrd(List<T> list);
    }
}

