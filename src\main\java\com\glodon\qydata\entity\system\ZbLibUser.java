package com.glodon.qydata.entity.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @className: ZbLibUser
 * @description: 用户实体
 * @date: 2020/7/21
 **/
@Data
@Schema(name = "用户实体")
public class ZbLibUser {

    @Schema(name = "用户ID")
    private Long id;
    @Schema(name = "广联云globalId")
    private String globalId;
    @Schema(name = "用户类型，vip体系，云账号体系：")
    private Integer uType;
    @Schema(name = "用户状态：1-有效，2-过期")
    private Integer status;
    @Schema(name = "账号")
    private String accountName;
    @Schema(name = "用户名")
    private String userName;
    @Schema(name = "是否为主账号")
    private Integer isMain;
    @Schema(name = "是否为管理员账号")
    private Integer isManager;
    @Schema(name = "关联主账号id")
    private String joinMainGlobalId;
    @Schema(name = "关联管理员账号id")
    private String joinManagerGlobalId;
    @Schema(name = "企业id")
    private Long companyId;
    @Schema(name = "客户编码")
    private String customerCode;
    @Schema(name = "入库时间")
    private LocalDateTime createTime;
    @Schema(name = "vip体系账号名称")
    private String vipAccountName;
    /**
     * 真实姓名
     */
    private String fullName;
    @Schema(name = "已进行新手指引次数-最多3次")
    private Integer guideNum;
    @Schema(name = "是否完成新手指引：1-完成，0-未完成")
    private Integer guideFlag;
    @Schema(name = "授权平台获取到的企业id")
    private Long authEnterpriseId;
}
