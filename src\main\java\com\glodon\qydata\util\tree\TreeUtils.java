package com.glodon.qydata.util.tree;

import com.google.common.collect.Lists;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 树形工具类
 * <AUTHOR>
 *
 */
public class TreeUtils {
	/**
	 * 每层层次码的长度
	 */
	private static  final int LEVEL_CODE_LENGTH_OF_ONE = 3;

	public static <T extends DataNode & TreeDto> List<T> toTreeListNew(List<T> nodes, Boolean hasOrderBy) {
		if (nodes == null) {
			return null;
		}
		Map<String,T> map = nodes.parallelStream().collect(Collectors.toMap(DataNode::getLevelcode, v ->v, (x, y) ->x));
		for (T node : nodes) {
			String parentcode = node.getLevelcode().substring(0,node.getLevelcode().length() - LEVEL_CODE_LENGTH_OF_ONE);
			T parent;
			while((parent = map.get(parentcode))==null && parentcode.length()>=3) {
				parentcode = parentcode.substring(0,parentcode.length() - LEVEL_CODE_LENGTH_OF_ONE);
			}
			if (parent != null) {
				T finalParent = parent;
				List<T> children = Optional.ofNullable(parent.getChildren()).orElseGet(() -> {
					List<T> list = new ArrayList<>();
					finalParent.setChildren(list);
					return list;
				});
				if (null == node.getOrd()) {
					node.setOrd(0);
				}
				children.add(node);
				if (Boolean.TRUE.equals(hasOrderBy)) {
					children.sort(Comparator.comparing(DataNode::getOrd));
				}
			}
		}
		if (Boolean.TRUE.equals(hasOrderBy)) {
			return map.values().parallelStream().filter(t -> t.getLevelcode().length() == LEVEL_CODE_LENGTH_OF_ONE).sorted(Comparator.comparing(DataNode::getOrd)).collect(Collectors.toList());
		} else {
			return map.values().parallelStream().filter(t -> t.getLevelcode().length() == LEVEL_CODE_LENGTH_OF_ONE).collect(Collectors.toList());
		}
	}
	/**
	 * 将复杂结构树，转换为普通的对象嵌套对象的结构
	 * @return
	 */
	public static <T extends TreeDto & DataNode> List<T> treeNodesToObj(List<TreeNode<T>> treeNodes) {
		List<T> vos = new ArrayList<>();
		for (TreeNode<T> treeNode : treeNodes) {
			T data = treeNode.getData();
			vos.add(data);
			if (treeNode.getParent() != null) {
				if (treeNode.getParent().getData().getChildren() != null) {
					treeNode.getParent().getData().getChildren().add(data);
				} else {
					List<T> childVos = new ArrayList<>();
					treeNode.getParent().getData().setChildren(childVos);
					treeNode.getParent().getData().getChildren().add(data);
				}
			}
			if (treeNode.getChildren() != null && treeNode.getChildren().size() > 0) {
				treeNodesToObj(treeNode.getChildren());
			}
		}
		return vos;
	}
	public static <T extends DataNode & TreeDto> List<T> toTreeList(List<T> nodes, Boolean hasOrderBy) {
		List<TreeNode<T>> nodes1 = toTreeNodes2(nodes,hasOrderBy);
		return treeNodesToObj(nodes1);
	}
	/**
	 * 将数据集合转成树形节点集合
	 * @param nodes 数据集合
	 * @param hasOrderBy 是否已经排序
	 * @return 树形节点集合
	 */
	public static <T extends DataNode>  List<TreeNode<T>> toTreeNodes2(List<T> nodes, Boolean hasOrderBy){
		List<TreeNode<T>> result= Lists.newArrayList();
		List<T> fixNodes = nodes;
		if (Boolean.FALSE.equals(hasOrderBy)){
			fixNodes =  Lists.newArrayList(nodes);
			TreeUtils.orderBy2(fixNodes, true);
		}
		TreeNode<T> preNode = null;
		for (T data : fixNodes) {
			TreeNodeDefault<T> currNode = new TreeNodeDefault<>(data);
			TreeNode<T> parent = null;
			String levelcode = currNode.getLevelcode();
			while(null != preNode && levelcode != null){
				String preLevelCode = preNode.getLevelcode();
				String parentLevelCode = levelcode.substring(0, levelcode.length() - LEVEL_CODE_LENGTH_OF_ONE);
				while(parentLevelCode.length() >= LEVEL_CODE_LENGTH_OF_ONE){
					if (preLevelCode.compareTo(parentLevelCode) == 0){
						parent = preNode;
						break;
					}
					parentLevelCode = parentLevelCode.substring(0, parentLevelCode.length() - LEVEL_CODE_LENGTH_OF_ONE);
				}
				if (null != parent) {
                    break;
                }
				preNode = preNode.getParent();
			}
			preNode = currNode;
			if (null != parent){
				currNode.setParent(parent);
				parent.getChildren().add(currNode);
				orderBy3(parent.getChildren());
			}else{
				result.add(currNode);
			}
		}
		return orderBy3(result);
	}





	/**
	 * @Description 排序根据ord 升序
	 * @param nodes
	 **/
	@SuppressWarnings("rawtypes")
	public static <T extends TreeNode> List<T> orderBy3(List<T> nodes){
		nodes.sort((Comparator<TreeNode>) (o1, o2) -> {
			if (o1.getData().getOrd() != null && o2.getData().getOrd() == null) {
				return 1;
			}
			if (o1.getData().getOrd() == null && o2.getData().getOrd() != null) {
				return -1;
			}
			if (o1.getData().getOrd() == null && o2.getData().getOrd() == null) {
				return 0;
			}
			if (o1.getData().getOrd().compareTo(o2.getData().getOrd()) == 0) {
				return o1.getLevelcode().compareTo(o2.getLevelcode());
			} else {
				return o1.getData().getOrd().compareTo(o2.getData().getOrd());
			}
		});
		return nodes;
	}





	/**
	 * 获取叶子节点
	 * @param treeNodes
	 * @param output
	 * @return
	 */
	public static  <T extends DataNode>  List<T> toLeafData(List<TreeNode<T>> treeNodes, List<T> output){
		for(TreeNode<T> treeNode : treeNodes){
			List<TreeNode<T>> clist=treeNode.getChildren();
			if(clist==null || clist.isEmpty()){
				output.add(treeNode.getData());
			}else{
				toLeafData(treeNode.getChildren(),output);
			}
		}
		return output;
	}


	/**
	 * 将树形节点转成列表，只能处理实现DataNode接口的数据
	 * @param treeNodes 树形节点
	 * @param output 数据列表
	 * @return 数据列表
	 */
	public static  <T extends DataNode>  List<T> toArrayList2(List<TreeNode<T>> treeNodes, List<T> output){
		for (TreeNode<T> treeNode : treeNodes) {
			output.add(treeNode.getData());
			if (!treeNode.getChildren().isEmpty()){
				toArrayList2(treeNode.getChildren(), output);
			}
		}
		return output;
	}
	/**
	 * 将树形节点转成列表，只能处理实现DataNode接口的数据
	 * @param treeNodes 树形节点
	 * @return 数据列表
	 */
	public static  <T extends DataNode>  List<T> toArrayList2(List<TreeNode<T>> treeNodes){
		List<T> result= Lists.newArrayList();
		return toArrayList2(treeNodes, result);
	}
	/**
	 * 排序 根据levecode
	 * @param nodes
	 * @param asc
	 * @return
	 */
	public static <T extends DataNode> List<T> orderBy2(List<T> nodes, Boolean asc){
		if (Boolean.FALSE.equals(asc)){
			nodes.sort((Comparator<DataNode>) (o1, o2) -> {
				if (o1.getLevelcode() != null && o2.getLevelcode() == null) {
					return 1;
				}
				if (o1.getLevelcode() == null && o2.getLevelcode() != null) {
					return -1;
				}
				if (o1.getLevelcode() == null && o2.getLevelcode() == null) {
					return 0;
				}
				return o2.getLevelcode().compareTo(o1.getLevelcode());
			});
		}else{
			nodes.sort((Comparator<DataNode>) (o1, o2) -> {
				if (o1.getLevelcode() != null && o2.getLevelcode() == null) {
					return 1;
				}
				if (o1.getLevelcode() == null && o2.getLevelcode() != null) {
					return -1;
				}
				if (o1.getLevelcode() == null && o2.getLevelcode() == null) {
					return 0;
				}
				return o1.getLevelcode().compareTo(o2.getLevelcode());
			});
		}
		return nodes;
	}
	/**
	 * @Description 重置节点类型
	 * @param treeNodes
	 * @param level
	 **/
	private static <T extends DataNode> void	resetNodeType2(List<TreeNode<T>> treeNodes, int level){
		for (TreeNode<T> child : treeNodes) {
			Boolean isLeaf = child.getChildren().isEmpty();
			child.setIsLeaf(isLeaf);
			if (level == 0){
				child.setNodeType(NodeType.ROOT);
			}
			else if(level == 1){
				child.setNodeType(NodeType.TRUNK);
			}
			else if(Boolean.TRUE.equals(isLeaf)){
				child.setNodeType(NodeType.LEAF);
			}
			else{
				child.setNodeType(NodeType.BRANCH);
			}

			if (!child.getChildren().isEmpty()){
				resetNodeType2(child.getChildren(), level + 1);
			}
		}
	}
	/**
	 * @Description 重置序号
	 * @return void
	 **/
	private static <T extends DataNode> void	resetSerialNo2(List<TreeNode<T>> treeNodes,
			SerialNoGenerator<T> generator){
		for (TreeNode<T> child : treeNodes) {
			generator.generateSerialNo(child, treeNodes.indexOf(child));
			if (!child.getChildren().isEmpty()){
				resetSerialNo2(child.getChildren(), generator);
			}
		}
	}

	/**
	 * 批量调整，包括排序、重置节点类型、重置序号
	 * @param nodes 待处理的数据集合
	 * @param operations 操作集
	 * @param generator 序号产生器
	 * @return 处理后的集合
	 */
	public static  <T extends DataNode> List<T> batchAdjust2(List<T> nodes,
			EnumSet<OperateType> operations, Boolean ascIfOrderBy, SerialNoGenerator<T> generator){
		if (operations.contains(OperateType.RESET_SERIAL_NO)
				&& !operations.contains(OperateType.RESET_NODE_TYPE)){
			operations.add(OperateType.RESET_NODE_TYPE);
		}
		if (operations.contains(OperateType.RESET_NODE_TYPE)
				&& !operations.contains(OperateType.ORDER_BY)){
			operations.add(OperateType.ORDER_BY);
		}
		if(operations.contains(OperateType.ORDER_BY)){
			TreeUtils.orderBy2(nodes, ascIfOrderBy);
		}
		List<TreeNode<T>> treeNodes = toTreeNodes2(nodes, true);
		if(operations.contains(OperateType.RESET_NODE_TYPE)){
			TreeUtils.resetNodeType2(treeNodes, 0);
		}
		if(operations.contains(OperateType.RESET_SERIAL_NO)){
			TreeUtils.resetSerialNo2(treeNodes, generator);
		}
		return toArrayList2(treeNodes);
	}

	public  static  <T extends DataNode> void traverseBottomUp2(List<TreeNode<T>> treeNodes,
			TreeNodeHandler<T> handler) throws Exception {
		for (TreeNode<T> treeNode : treeNodes) {
			if (!treeNode.getChildren().isEmpty()){
				traverseBottomUp2(treeNode.getChildren(), handler);
			}
			handler.process(treeNode, treeNodes);
		}
	}
	/**
	 * 从底自上遍历数据节点
	 * @param nodes 数据节点集合
	 * @param handler 处理器
	 * @return 加工后的数据节点
	 */
	public  static  <T extends DataNode> List<T> traverseBottomUp2(List<T> nodes, boolean hasOrderBy,
			TreeNodeHandler<T> handler) throws Exception {
		List<TreeNode<T>> treeNodes = toTreeNodes2(nodes, hasOrderBy);
		traverseBottomUp2(treeNodes, handler);
		return nodes;
	}

	/**
	 * 自上而下
	 * @param treeNodes
	 * @param handler
	 * @param <T>
	 * @throws Exception
	 */
	private  static  <T extends DataNode> void traverseTopDown2(List<TreeNode<T>> treeNodes,
			TreeNodeHandler<T> handler) throws Exception {
		for (TreeNode<T> treeNode : treeNodes) {
			handler.process(treeNode, treeNodes);
			if (!treeNode.getChildren().isEmpty()){
				traverseTopDown2(treeNode.getChildren(), handler);
			}
		}
	}
	/**
	 * 自上而下遍历数据节点
	 * @param nodes 数据节点集合
	 * @param handler 处理器
	 * @return 加工后的数据节点集合
	 */
	public  static  <T extends DataNode> List<T> traverseTopDown2(List<T> nodes, boolean hasOrderBy,
			TreeNodeHandler<T> handler) throws Exception {
		List<TreeNode<T>> treeNodes = toTreeNodes2(nodes, hasOrderBy);
		traverseTopDown2(treeNodes, handler);
		return nodes;
	}



}
