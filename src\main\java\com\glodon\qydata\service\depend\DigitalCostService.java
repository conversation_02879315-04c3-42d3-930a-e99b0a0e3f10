package com.glodon.qydata.service.depend;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.constant.DigitalCostConstants;
import com.glodon.qydata.config.CommonConfig;
import com.glodon.qydata.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;

/**
 * 数字成本平台
 * Created by weijf on 2022/1/26.
 */
@Slf4j
@Service
public class DigitalCostService {
    @Autowired
    private CommonConfig commonConfig;

    @Autowired
    private RestTemplate restTemplate;


    /**
     * @description: 根据gldtoken获取登录信息
     * @param: * @param gldToken
     * @return:
     * {
     * 		"authEnterpriseId": "",
     * 		"geipToken": "",
     * 		"gldToken": "",
     * 		"gldUserId": ""
     *        }
     * <AUTHOR>
     * @date: 2022/2/14 13:39
     */
    public JSONObject getSessionByGldToken(String gldToken){
        String url = commonConfig.getDigitalCostApiUrl() + "/auth/getSession?gldToken=" + gldToken;
        try {
            HttpEntity entity = new HttpEntity<>(null);
            ResponseEntity<JSONObject> exchange = restTemplate.exchange(url, HttpMethod.GET, entity, JSONObject.class);
            JSONObject resultBody = exchange.getBody();
            disposeCostResult(resultBody, url);
            return resultBody.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA);
        } catch (BusinessException e) {
            return null;
        } catch (Exception e) {
            log.error("访问成本平台接口 url={}, e={}",url, e);
            throw new BusinessException("调用成本平台查询登录信息, 成本平台响应异常");
        }
    }

    /**
     * @description:  用户重新登录(gldToken)
     * @param: * @param gldToken
     * @return:
     * {
     * 		"authEnterpriseId": "",
     * 		"companyName": "",
     * 		"email": "",
     * 		"geipToken": "",
     * 		"gldToken": "",
     * 		"gldUserId": "",
     * 		"passwordMobile": "",
     * 		"userName": ""
     *        }
     * <AUTHOR>
     * @date: 2022/2/14 14:03
     */
    public JSONObject reLoginByGloToken(String gldToken){
        String url = commonConfig.getDigitalCostApiUrl() + "/auth/login/gldToken";
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> request = new HttpEntity<>(gldToken, headers);
            ResponseEntity<JSONObject> responseEntity = restTemplate.postForEntity(url, request, JSONObject.class);
            JSONObject resultBody = responseEntity.getBody();
            disposeCostResult(resultBody, url);
            return resultBody.getJSONObject(DigitalCostConstants.RESULT_KEY_DATA);
        } catch (Exception e) {
            log.error("访问成本平台用户重新登录(gldToken)接口 url={}, e={}",url, e);
            throw new BusinessException("调用成本平台用户重新登录(gldToken)接口, 成本平台响应异常");
        }
    }

    /**
     * @description:  统一处理成本平台返回数据
     * @param: * @param resultBody
     * @param url
     * @return: void
     * <AUTHOR>
     * @date: 2022/2/14 11:25
     */
    private void disposeCostResult(JSONObject resultBody, String url) {
        Integer code = resultBody.getInteger(DigitalCostConstants.RESULT_KEY_CODE);
        if (DigitalCostConstants.RESULT_CODE_200.equals(code)){
            return;
        }
        if (DigitalCostConstants.RESULT_CODE_200008.equals(code)){
            throw new BusinessException("账号没有登录");
        }
        log.error("调用成本平台接口：{}，成本平台响应异常：{}", url, resultBody.toJSONString());
        throw new BusinessException("调用成本平台, 成本平台响应异常");
    }

    /**
     * 获取授权
     * 授权接口文档：https://docs.qq.com/doc/DWHdOcUVYcnNYVVpO
     * subSystemCodes 文档 https://ycg3d2h0q0.feishu.cn/docs/doccnsYjV962CPy3V24tHNVzMxd#3auOHm
     * @param sgToken
     * @param subSystemCodes
     * @return
     */
    public JSONArray getAccessRight(String sgToken,List<String> subSystemCodes) throws BusinessException {
        String url = commonConfig.getDigitalCostApiUrl() + "/permission/license/list";
        try {

            HttpHeaders headers = new HttpHeaders();

            MediaType type = MediaType.parseMediaType("application/json; charset=UTF-8");
            headers.setContentType(type);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            headers.add("token", sgToken);
            headers.add("referCode", DigitalCostConstants.REFER_CODE);

            HashMap reqBody = new HashMap<>();
            reqBody.put("subSystemCodes", subSystemCodes==null?new ArrayList<>():subSystemCodes);

            String reqJson = JSONObject.toJSONString(reqBody);

            HttpEntity<String> entity = new HttpEntity<>(reqJson, headers);
            JSONObject result = restTemplate.postForObject(url, entity, JSONObject.class);

            String code = result.getString("code");

            if(!"0".equals(code)){
                log.error("调用数字成本平台授权接口异常：result={}",JSONObject.toJSONString(result));
                throw new BusinessException(result.getString("message"));
            }
            return result.getJSONArray("data");

        }catch (BusinessException ex) {
            throw ex;
        }catch (Exception e) {
            log.error("调用数字成本平台授权接口 DigitalCostService.getAccessRight error,url={},sgToken={},subSystemCodes={},e={}",url,sgToken,subSystemCodes,e.getMessage());
            throw new BusinessException("调用数字成本平台授权接口异常");
        }
    }

    /**
     * 获取权限
     * 接口文档 ：https://docs.qq.com/doc/DTnVIVFpTVGJXQWZx
     * 编码权限对应：https://docs.qq.com/sheet/DTnFWaGJPTVJUWHNk?tab=7yf7h8
     * @param sgToken
     * @param enterpriseId 非必填
     * @return
     */
    public JSONArray getEditRight(String sgToken, String enterpriseId) throws BusinessException {
        String url = new StringBuilder(commonConfig.getDigitalCostApiUrl()).append("/permission/list")
//                .append("?orgId=").append(enterpriseId)
                .append("?productCode=").append(DigitalCostConstants.PRODUCT_CODE)
                .toString();

        try {

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
            headers.add("token", sgToken);
            headers.add("referCode", DigitalCostConstants.REFER_CODE);

            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(null, headers);

            ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.GET, request, JSONObject.class);

            JSONObject result = response.getBody();

            String code = result.getString("code");
            if(!"0".equals(code)){
                log.error("调用数字成本平台权限接口异常：result={}",JSONObject.toJSONString(result));
                throw new BusinessException(result.getString("message"));
            }

            return result.getJSONArray("data");

        }catch (BusinessException ex) {
            throw ex;
        }catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException("调用数字成本平台权限接口异常");
        }
    }

    public String getAccountNameById(String userId, String sgToken) {
        String url = commonConfig.getDigitalCostApiUrl() + "/integrate/subAccount" +
                "?account=" + userId;
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.add("geipToken", sgToken);
            HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(null, headers);
            ResponseEntity<JSONObject> response = restTemplate.exchange(url, HttpMethod.GET, request, JSONObject.class);
            Integer code = Objects.requireNonNull(response.getBody()).getInteger("code");

            if(code != 200){
                log.error("调用获取主账号用户信息接口失败：result={}", JSON.toJSONString(response.getBody()));
                return null;
            }
            return response.getBody().getJSONObject("data").getString("accountName");
        }catch (BusinessException ex) {
            throw new BusinessException("调用获取主账号用户信息接口失败");
        }
    }
}
