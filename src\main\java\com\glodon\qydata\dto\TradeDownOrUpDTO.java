package com.glodon.qydata.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 专业工程上移下移DTO
 * @date 2023-03-31 18:56
 * @email <EMAIL>
 */
@Data
@Schema(description = "专业工程上移下移DTO")
public class TradeDownOrUpDTO implements Serializable {

    @Schema(description = "当前专业工程id")
    @NotNull(message = "专业工程id不能为空")
    private Long tradeId;

    @Schema(description = "操作类型, UP or DOWN")
    @NotBlank(message = "操作类型不能为空")
    private String operation;
}
