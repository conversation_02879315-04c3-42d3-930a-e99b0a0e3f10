
package com.glodon.qydata.interceptor;

import com.glodon.qydata.common.annotation.PassAuth;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

@Slf4j
public class LoginAuthInterceptor implements HandlerInterceptor {


	@Autowired
	RedisUtil redisUtil;

	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object o) throws Exception {
		//方法上有PassAuth注解则跳过校验
		if (o instanceof HandlerMethod) {
			HandlerMethod handlerMethod = (HandlerMethod) o;
			PassAuth methodAnnotation = handlerMethod.getMethodAnnotation(PassAuth.class);
			if (methodAnnotation != null){
				return true;
			}
		}

		String token = request.getHeader(Constants.HEADER_TOKEN);
		String userId = request.getHeader(Constants.HEADER_USER_ID);
//		String fingerPrint = request.getHeader(Constants.HEADER_FINGER_PRINT); //浏览器指纹
		String path = request.getRequestURI();

//		//未登录、uid为空
//		if(StringUtils.isEmpty(userId) || StringUtils.isEmpty(token)){
//			throw new BaseException(ResponseCode.NOT_LOGIN);
//		}


		/*JSONObject loginInfo = redisUtil.getObject(RedisKeyEnum.USER_SESSION, JSONObject.class,userId,token);

		if(loginInfo == null || loginInfo.isEmpty()){ //有帐号及旧的登录信息，但缓存中没有：请重新登录
			throw new BaseException(ResponseCode.LOGIN_AGAIN);
		}else if (loginInfo.get("otherLogin")!=null){  //其它地方登录
			throw new BaseException(ResponseCode.OTHER_LOGIN);
		}*/

		//更新登录缓存，重置缓存
//		redisUtil.setObject(RedisKeyEnum.USER_SESSION,loginInfo,userId,token);

		//未登录
		if(StringUtils.isEmpty(userId)){
			throw new BusinessException(ResponseCode.NOT_LOGIN);
		}

		return true;
	}

	@Override
	public void postHandle(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, ModelAndView modelAndView) throws Exception {
		// 空实现
	}

	@Override
	public void afterCompletion(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse, Object o, Exception e) throws Exception {
		// 空实现
	}
}
