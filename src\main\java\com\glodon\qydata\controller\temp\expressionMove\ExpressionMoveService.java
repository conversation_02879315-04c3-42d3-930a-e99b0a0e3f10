package com.glodon.qydata.controller.temp.expressionMove;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.glodon.qydata.common.constant.CategoryTypeConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.ExpressionTypeEnum;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.common.enums.TradeReferEnum;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionMapper;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.util.CategoryUtil;
import com.glodon.qydata.util.ExcelUtil;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.SpringUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.init.FeatureExcelVo;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.CategoryTypeConstants.TYPE_DEFAULT;
import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.WHETHER_FALSE;
import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.WHETHER_TRUE;
import static com.glodon.qydata.common.constant.Constants.ZbStandardsTradeConstants.SYSTEM_CUSTOMER_CODE;
import static com.glodon.qydata.controller.temp.TempConstant.*;

@Service
@Slf4j
public class ExpressionMoveService {

    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;
    @Autowired
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;
    @Autowired
    private ZbStandardsExpressionMapper standardsExpressionMapper;
    @Autowired
    private ZbStandardsExpressionSelfMapper standardsExpressionSelfMapper;
    @Autowired
    private ProjectFeatureCategoryViewMapper projectFeatureCategoryViewMapper;
    @Autowired
    private ProjectFeatureCategoryViewSelfMapper projectFeatureCategoryViewSelfMapper;
    @Autowired
    private ZbStandardsTradeMapper standardsTradeMapper;
    @Autowired
    private CommonProjCategoryService commonProjCategoryService;
    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;
    @Autowired
    private FeatureRecordService featureRecordService;
    @Autowired
    private FeatureRecordSelfService featureRecordSelfService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private IProjectFeatureService projectFeatureService;

    private static AtomicInteger count = new AtomicInteger(0);

    final ThreadPoolExecutor syncExecutor = new ThreadPoolExecutor(5, 10, 3000, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());

    private static List<FeatureExcelVo> excelDataList;


//    @PostConstruct
    public void readExcelDataList(){
        try {
            InputStream in = this.getClass().getResourceAsStream("/excel/工程特征口径.xlsx");
            excelDataList = ExcelUtil.readExcel(in, "附表2-工程特征中标准指标项", FeatureExcelVo.class);
            log.info("excel读取成功，共{}条记录...", excelDataList.size());
        } catch (Exception e) {
            log.error("excel读取失败...", e);
        }
    }

    public ResponseVo initCustomerFeature(List<FeatureExcelVo> excelDataList, String initCustomerCode){
        try {
            LocalDateTime start = LocalDateTime.now();
            //查询所有的企业
            List<String> customerCodes;
            if("ALL".equals(initCustomerCode)){
                LambdaQueryWrapper<FeatureRecord> queryWrapper = new LambdaQueryWrapper<FeatureRecord>()
                        .eq(FeatureRecord::getToFeatureStatus, 1)
                        .eq(FeatureRecord::getAddStatus, 0);
                List<FeatureRecord> recordList = featureRecordService.list(queryWrapper);

                customerCodes = recordList.parallelStream().map(FeatureRecord::getCustomerCode).collect(Collectors.toList());
            }else{
                customerCodes = new ArrayList<>();
                customerCodes.add(initCustomerCode);
            }

            log.info("需要处理的企业数：{}", customerCodes.size());

            int limit = customerCodes.size() <= 5 ? 1 : customerCodes.size() / 5;
            List<List<String>> threadCustomerCodes = Lists.partition(customerCodes, limit);
            final CountDownLatch latch = new CountDownLatch(threadCustomerCodes.size());

            for (int i = 0; i < threadCustomerCodes.size(); i++) {
                List<String> threadCustomerCode = threadCustomerCodes.get(i);

                syncExecutor.execute(() -> {
                    for (int j = 0; j < threadCustomerCode.size(); j++) {
                        String customerCode = threadCustomerCode.get(j);
                        ExpressionMoveService expressionMoveService = SpringUtil.getBean(Constants.BeanName.EXPRESSION_MOVE_SERVICE);
                        FeatureRecord featureRecord = new FeatureRecord();
                        try{
                            Integer status = expressionMoveService.handler(customerCode, excelDataList);
                            featureRecord.setAddStatus(status);
                        }catch (Exception e){
                            log.error("customerCode：{}", customerCode, e);
                            featureRecord.setAddStatus(2);
                        }
                        featureRecordService.update(featureRecord, new LambdaQueryWrapper<FeatureRecord>().eq(FeatureRecord::getCustomerCode, customerCode));
                    }
                    latch.countDown();
                });
            }

            latch.await();

            LocalDateTime end5 = LocalDateTime.now();
            String desc = String.format("所有企业处理结束，共 %d 个企业，总耗时：%d ms", customerCodes.size(), Duration.between(start, end5).toMillis());
            log.info(desc);
            return ResponseVo.success(desc);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseVo.error();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer handler(String customerCode, List<FeatureExcelVo> excelDataList) {
        // expression的内置数据
        List<ZbStandardsExpression> systemExpressionList = standardsExpressionMapper.selectListByCustomCode(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, null);
        // expression的数据
        List<ZbStandardsExpression> expressionList = standardsExpressionMapper.selectListByCustomCode(customerCode, null);
        // feature的数据
        List<ProjectFeature> featureList = projectFeatureMapper.selectNoBlobByCustomerCode(customerCode, null, WHETHER_TRUE);
        // categoryView的数据
        List<ProjectFeatureCategoryView> categoryViewList = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(null, customerCode, null);
        // trade的数据
        List<ZbStandardsTrade> tradeList = standardsTradeMapper.selectAllListByCustomerCode(customerCode);

        if (CollectionUtils.isEmpty(systemExpressionList) || CollectionUtils.isEmpty(expressionList)
                || CollectionUtils.isEmpty(featureList) || CollectionUtils.isEmpty(categoryViewList) || CollectionUtils.isEmpty(tradeList)){
            throw new BusinessException("原始数据错误...");
        }

        Map<String, Long> tradeNameAndIdMap = tradeList.parallelStream().collect(Collectors.toMap(ZbStandardsTrade::getDescription, ZbStandardsTrade::getId, (v1, v2) -> v2));
        List<Integer> typeList = featureList.parallelStream().map(ProjectFeature::getType).distinct().collect(Collectors.toList());
        List<ProjectFeature> insertList = new ArrayList<>();
        List<ProjectFeature> updateList = new ArrayList<>();
        List<ProjectFeatureCategoryView> categoryViewInsertList = new ArrayList<>();
        int removeFlagCount = 0;
        // 区分type处理
        for (Integer type : typeList) {
            Map<String, ProjectFeature> numberFeatureMap = featureList.parallelStream()
                    .filter(x -> type.equals(x.getType()))
                    .filter(x -> ExpressionTypeEnum.TYPE_NUMBER.getCode().equals(x.getTypeCode()))
                    .collect(Collectors.toMap(x -> x.getTradeId() + "-" + x.getName(), Function.identity(), (v1, v2) -> v2));

            Map<Long, List<ProjectFeatureCategoryView>> tradeCategoryView = categoryViewList.stream()
                    .filter(x -> type.equals(x.getType()))
                    .collect(Collectors.groupingBy(ProjectFeatureCategoryView::getTradeId));

            Map<String, ZbStandardsExpression> expressionMap = expressionList.parallelStream()
                    .filter(x -> type.equals(x.getType()))
                    .filter(x -> ExpressionTypeEnum.TYPE_NUMBER.getCode().equals(x.getTypeCode()))
                    .collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(), (v1, v2)-> v2));

            // 工程分类
            List<CommonProjCategory> categoryList = commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE);
            boolean removeFlag = CategoryUtil.deDuplication(categoryList);
            if (removeFlag){
                removeFlagCount++;
            }

            // 根据excel导入数据
            Map<Long, List<ProjectFeature>> tradeFeatureMap = this.insertOrUpdate(excelDataList, tradeNameAndIdMap, numberFeatureMap, expressionMap, type, categoryList, customerCode, updateList, false);

            for (Map.Entry<Long, List<ProjectFeature>> entry : tradeFeatureMap.entrySet()) {
                Long tradeId = entry.getKey();
                List<ProjectFeature> projectFeatures = entry.getValue();

                // 将需要新增的数据放入集合，后续统一入库
                insertList.addAll(projectFeatures);

                // 处理分类视图的数据
                List<ProjectFeatureCategoryView> featureCategoryViewList;
                if (tradeCategoryView.containsKey(tradeId)){
                    featureCategoryViewList = tradeCategoryView.get(tradeId);
                }else {
                    featureCategoryViewList = new ArrayList<>();
                    tradeCategoryView.put(tradeId, featureCategoryViewList);
                }

                for (ProjectFeature projectFeature : projectFeatures) {
                    List<ProjectFeatureCategoryView> projectFeatureCategoryViews = this.addCategoryView(projectFeature, featureCategoryViewList);
                    categoryViewInsertList.addAll(projectFeatureCategoryViews);
                    featureCategoryViewList.addAll(projectFeatureCategoryViews);
                }
            }
        }

        this.batchUpdate(updateList,false);

        if (CollectionUtils.isNotEmpty(insertList)){
            projectFeatureMapper.insertBatch(insertList);
        }

        if (CollectionUtils.isNotEmpty(categoryViewInsertList)) {
            projectFeatureCategoryViewMapper.saveBatch(categoryViewInsertList);
        }

        return removeFlagCount == 0 ? STATUS_SUCCESS : STATUS_CATEGORY_REPEAT;
    }



    private Map<Long, List<ProjectFeature>> insertOrUpdate(List<FeatureExcelVo> excelDataList,
                                                           Map<String, Long> tradeNameAndIdMap,
                                                           Map<String, ProjectFeature> numberFeatureMap,
                                                           Map<String, ZbStandardsExpression> expressionMap,
                                                           Integer type,
                                                           List<CommonProjCategory> categoryList,
                                                           String customerCode,
                                                           List<ProjectFeature> updateList,
                                                           boolean isSystem){
        Map<Long, List<ProjectFeature>> tradeFeatureMap = new HashMap<>();

        excelDataList.forEach(item -> {
            String tradeStr = item.getTrade();

            String[] tradeArr = tradeStr.split("\\|");
            for (String tradeName : tradeArr) {
                if (!tradeNameAndIdMap.containsKey(tradeName)){
                    throw new BusinessException("customerCode:" + customerCode + "，tradeName:" + tradeName);
                }
                Long tradeId = tradeNameAndIdMap.get(tradeName);

                if (numberFeatureMap.containsKey(tradeId + "-" + item.getName())) {
                    ProjectFeature projectFeature = numberFeatureMap.get(tradeId + "-" + item.getName());
                    if (isSystem){
                        projectFeature.setUnit(item.getUnit());
                        projectFeature.setProjectType(this.buildProjectType(item.getCategory(), type, categoryList));
                        projectFeature.setIsUsing("☑".equals(item.getIsEnable()) ? 1 : 0);
                        projectFeature.setIsRequired("☑".equals(item.getIsRequired()) ? 1 : 0);
                        projectFeature.setIsExpression("☑".equals(item.getIsExpression()) ? 1 : 0);
                        projectFeature.setRemark(this.setRemark(projectFeature.getRemark(), item.getRemark()));
                        projectFeature.setIsExpressionDefault(WHETHER_TRUE);
                    }else {
                        projectFeature.setUnit(item.getUnit());
                        projectFeature.setIsExpression(WHETHER_TRUE);
                        projectFeature.setRemark(this.setRemark(projectFeature.getRemark(), item.getRemark()));
                        projectFeature.setIsExpressionDefault(WHETHER_TRUE);
                    }

                    updateList.add(projectFeature);
                }else {
                    ProjectFeature excelFeature = this.buildBasicFeature(expressionMap, item);
                    excelFeature.setType(type);
                    excelFeature.setTradeId(tradeId);
                    excelFeature.setTradeName(tradeName);
                    excelFeature.setCustomerCode(customerCode);
                    excelFeature.setProjectType(this.buildProjectType(item.getCategory(), type, categoryList));

                    List<ProjectFeature> projectFeatureList;
                    if (tradeFeatureMap.containsKey(tradeId)){
                        projectFeatureList = tradeFeatureMap.get(tradeId);
                    }else {
                        projectFeatureList = new ArrayList<>();
                        tradeFeatureMap.put(tradeId, projectFeatureList);
                    }

                    excelFeature.setOrdTrade(projectFeatureList.size() + 1);
                    projectFeatureList.add(excelFeature);
                }
            }
        });
        return tradeFeatureMap;
    }

    private List<ProjectFeatureCategoryView> addCategoryView(ProjectFeature projectFeature, List<ProjectFeatureCategoryView> featureCategoryViewList){
        // 工程分类
        String projectType = projectFeature.getProjectType();
        Set<String> categoryCodeSet = CategoryUtil.getCategoryCode(projectType);
        List<ProjectFeatureCategoryView> projectFeatureCategoryViewList = new ArrayList<>();

        Map<String, List<ProjectFeatureCategoryView>> groupMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(featureCategoryViewList)){
            groupMap = featureCategoryViewList.stream().collect(Collectors.groupingBy(ProjectFeatureCategoryView::getCategoryCode));
        }

        for (String categoryCode : categoryCodeSet) {
            ProjectFeatureCategoryView projectFeatureCategoryView = new ProjectFeatureCategoryView();
            projectFeatureCategoryView.setTradeId(projectFeature.getTradeId());
            projectFeatureCategoryView.setCategoryCode(categoryCode);
            projectFeatureCategoryView.setFeatureId(projectFeature.getId());
            projectFeatureCategoryView.setType(projectFeature.getType());
            projectFeatureCategoryView.setCustomerCode(projectFeature.getCustomerCode());
            // 排序字段
            List<ProjectFeatureCategoryView> projectFeatures = Optional.ofNullable(groupMap.get(categoryCode)).orElse(new ArrayList<>());

            // 插入到最后
            Integer max = projectFeatures.stream().map(ProjectFeatureCategoryView::getOrdCategory)
                    .filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
            projectFeatureCategoryView.setOrdCategory(max + 1);

            projectFeatureCategoryViewList.add(projectFeatureCategoryView);
        }
       return projectFeatureCategoryViewList;
    }

    private void dealCustomerExpression(List<ZbStandardsExpression> expressionList, List<ZbStandardsExpression> systemExpressionList, String customerCode){
        Map<String, String> nameMap = systemExpressionList.parallelStream()
                .collect(Collectors.toMap(ZbStandardsExpression::getOldName, ZbStandardsExpression::getName, (v1, v2) -> v2));

        // 需要更新的数据
        List<ZbStandardsExpression> expressionUpdateList = new ArrayList<>();
        // 记录企业有几种type及最大ord
        Map<Integer, Integer> typeAndMaxOrd = new HashMap<>();

        for (ZbStandardsExpression expression : expressionList) {
            Integer type = expression.getType();
            String name = expression.getName();
            Integer expressionOrd = expression.getExpressionOrd() == null ? 0 : expression.getExpressionOrd();

            if (typeAndMaxOrd.containsKey(type)) {
                Integer ord = typeAndMaxOrd.get(type);
                if (expressionOrd > ord){
                    typeAndMaxOrd.put(type, expressionOrd);
                }
            }else {
                typeAndMaxOrd.put(type, expressionOrd);
            }

            if (!nameMap.containsKey(name)){
                continue;
            }

            String newName = nameMap.get(name);
            // 数据后面还需要使用，将更新的数据也维护完整
            expression.setName(newName);
            expression.setOldName(name);

            // 更新实体的构建
            ZbStandardsExpression updateExpression = new ZbStandardsExpression();
            updateExpression.setId(expression.getId());
            updateExpression.setName(newName);
            updateExpression.setOldName(name);
            expressionUpdateList.add(updateExpression);
        }

        if (CollectionUtils.isNotEmpty(expressionUpdateList)){
            standardsExpressionMapper.batchUpdateSelective(expressionUpdateList);
        }

        // 口径字典表原先没有的数据需要内置进去，如：总计容面积，地下车库层高
        List<ZbStandardsExpression> insertList = new ArrayList<>();
        for (Integer type : typeAndMaxOrd.keySet()) {
            Integer maxOrd = typeAndMaxOrd.get(type);
            insertList.add(buildExpression(SnowflakeIdUtils.getNextId(), "K_4776729036038602798", "地下车库层高", "m", null, maxOrd  + 1, type, customerCode));
            insertList.add(buildExpression(SnowflakeIdUtils.getNextId(), "K_4776729036038603054", "总计容面积", "m2", "Σ各业态计容面积", maxOrd + 2, type, customerCode));
        }

        if (CollectionUtils.isNotEmpty(insertList)){
            standardsExpressionMapper.batchInsert(insertList);
        }

        expressionList.addAll(insertList);
    }

    private void dealSelfExpression(List<ZbStandardsExpression> selfExpressionList, List<ZbStandardsExpression> expressionList, Long globalId, String customerCode){
        Map<String, String> nameMap = expressionList.parallelStream()
                .collect(Collectors.toMap(ZbStandardsExpression::getOldName, ZbStandardsExpression::getName, (v1, v2) -> v2));

        // 需要更新的数据
        List<ZbStandardsExpression> expressionUpdateList = new ArrayList<>();
        // 记录企业有几种type及最大ord
        Map<Integer, Integer> typeAndMaxOrd = new HashMap<>();

        for (ZbStandardsExpression expression : selfExpressionList) {
            Integer type = expression.getType();
            String name = expression.getName();
            Integer expressionOrd = expression.getExpressionOrd() == null ? 0 : expression.getExpressionOrd();

            if (typeAndMaxOrd.containsKey(type)) {
                Integer ord = typeAndMaxOrd.get(type);
                if (expressionOrd > ord){
                    typeAndMaxOrd.put(type, expressionOrd);
                }
            }else {
                typeAndMaxOrd.put(type, expressionOrd);
            }

            if (!nameMap.containsKey(name)){
                continue;
            }

            String newName = nameMap.get(name);
            // 数据后面还需要使用，将更新的数据也维护完整
            expression.setName(newName);
            expression.setOldName(name);

            // 更新实体的构建
            ZbStandardsExpression updateExpression = new ZbStandardsExpression();
            updateExpression.setId(expression.getId());
            updateExpression.setName(newName);
            updateExpression.setOldName(name);
            expressionUpdateList.add(updateExpression);
        }

        if (CollectionUtils.isNotEmpty(expressionUpdateList)){
            standardsExpressionSelfMapper.batchUpdateSelective(expressionUpdateList);
        }

        // 口径字典表原先没有的数据需要内置进去，如：总计容面积，地下车库层高
        List<ZbStandardsExpression> insertList = new ArrayList<>();
        for (Integer type : typeAndMaxOrd.keySet()) {
            Integer maxOrd = typeAndMaxOrd.get(type);

            Long originId = expressionList.parallelStream()
                    .filter(x -> type.equals(x.getType()))
                    .filter(x -> "地下车库层高".equals(x.getName()))
                    .map(ZbStandardsExpression::getId).findFirst().orElse(null);
            ZbStandardsExpression expression = buildExpression(SnowflakeIdUtils.getNextId(), "K_4776729036038602798", "地下车库层高", "m", null, maxOrd + 1, type, customerCode);
            expression.setOriginId(originId);
            expression.setSelfGlobalId(globalId);
            insertList.add(expression);

            Long originId1 = expressionList.parallelStream()
                    .filter(x -> type.equals(x.getType()))
                    .filter(x -> "总计容面积".equals(x.getName()))
                    .map(ZbStandardsExpression::getId).findFirst().orElse(null);
            ZbStandardsExpression expression1 = buildExpression(SnowflakeIdUtils.getNextId(), "K_4776729036038603054", "总计容面积", "m2", "Σ各业态计容面积", maxOrd + 2, type, customerCode);
            expression1.setOriginId(originId1);
            expression1.setSelfGlobalId(globalId);
            insertList.add(expression1);
        }

        if (CollectionUtils.isNotEmpty(insertList)){
            standardsExpressionSelfMapper.batchInsert(insertList);
        }

        selfExpressionList.addAll(insertList);
    }

    /**
     * 去掉名称中的单位
     * @param name
     * @return
     */
    private String nameRemoveUnit(String name){
        String pattern = ".*\\(.+\\)$";

        if(StringUtils.isNotEmpty(name) && name.matches(pattern)){
            return name.split("\\(")[0];
        }

        return name;
    }

    private List<ProjectFeature> convertFeature(List<ZbStandardsExpression> expressionList, List<ProjectFeature> projectFeatureList){
        if (CollectionUtils.isEmpty(expressionList) || CollectionUtils.isEmpty(projectFeatureList)){
            return new ArrayList<>();
        }

        Map<Long, ZbStandardsExpression> expressionMap = expressionList.parallelStream().collect(Collectors.toMap(ZbStandardsExpression::getId, Function.identity()));

        List<ProjectFeature> featureUpdateList = new ArrayList<>(projectFeatureList.size());

        projectFeatureList.forEach(item -> {
            ZbStandardsExpression zbStandardsExpression = expressionMap.get(item.getExpressionId());

            if (zbStandardsExpression != null){
                ProjectFeature projectFeature = new ProjectFeature();

                projectFeature.setId(item.getId());
                projectFeature.setName(zbStandardsExpression.getName());
                projectFeature.setTypeCode(zbStandardsExpression.getTypeCode());
                projectFeature.setOption(zbStandardsExpression.getOption());
                projectFeature.setUnit(zbStandardsExpression.getUnit());
                projectFeature.setExpressionCode(zbStandardsExpression.getExpressionCode());

                featureUpdateList.add(projectFeature);
            }

        });

        return featureUpdateList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void initSystemFeature(List<FeatureExcelVo> excelDataList, String initCustomerCode) {
        LocalDateTime begin = LocalDateTime.now();

        if(StringUtils.isEmpty(initCustomerCode) || !Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE.equals(initCustomerCode)) {
            throw new BusinessException("参数initCustomerCode为空或内置编码错误...");
        }

        log.info("----初始化工程特征内置数据中，开始...");

        List<ZbStandardsExpression> systemExpressionList = standardsExpressionMapper.selectListByCustomCode(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, null);
        List<ProjectFeature> systemFeatureList = projectFeatureMapper.selectNoBlobByCustomerCode(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, null, WHETHER_TRUE);
        List<ZbStandardsTrade> systemTradeList = standardsTradeMapper.selectAllListByCustomerCode(SYSTEM_CUSTOMER_CODE);

        if (CollectionUtils.isEmpty(systemExpressionList) || CollectionUtils.isEmpty(systemFeatureList) || CollectionUtils.isEmpty(systemTradeList)){
            throw new BusinessException("原始内置数据错误...");
        }

        // 对expression数据的处理：1.去掉名称中的单位；2.新增2条内置数据
        this.dealSystemExpression(systemExpressionList);

        // 给feature放入expression的信息
        this.fillName(systemFeatureList, systemExpressionList, true);

        Map<String, Long> tradeNameAndIdMap = systemTradeList.parallelStream().collect(Collectors.toMap(ZbStandardsTrade::getDescription, ZbStandardsTrade::getId));
        List<ProjectFeature> insertList = new ArrayList<>();
        List<ProjectFeature> updateList = new ArrayList<>();

        // 区分type处理
        for (Integer type : CategoryTypeConstants.categoryTypeList) {
            List<CommonProjCategory> categoryList = commonProjCategoryService.getCategoryListTree(SYSTEM_CUSTOMER_CODE, type, Constants.CategoryConstants.WHETHER_FALSE);

            Map<String, ProjectFeature> numberFeatureMap = systemFeatureList.parallelStream()
                    .filter(x -> type.equals(x.getType()))
                    .filter(x -> ExpressionTypeEnum.TYPE_NUMBER.getCode().equals(x.getTypeCode()))
                    .collect(Collectors.toMap(x -> x.getTradeId() + "-" + x.getName(), Function.identity()));

            Map<String, ZbStandardsExpression> expressionMap = systemExpressionList.parallelStream()
                    .filter(x -> type.equals(x.getType()))
                    .filter(x -> ExpressionTypeEnum.TYPE_NUMBER.getCode().equals(x.getTypeCode()))
                    .collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(), (v1, v2)-> v2));

            // 根据excel导入数据
            Map<Long, List<ProjectFeature>> tradeFeatureMap = this.insertOrUpdate(excelDataList, tradeNameAndIdMap, numberFeatureMap, expressionMap, type, categoryList, SYSTEM_CUSTOMER_CODE, updateList, true);

            // 调整原先数据的顺序
            systemFeatureList.parallelStream().filter(x -> type.equals(x.getType())).forEach(item -> {
                if (tradeFeatureMap.containsKey(item.getTradeId())){
                    List<ProjectFeature> projectFeatures = tradeFeatureMap.get(item.getTradeId());
                    item.setOrdTrade(item.getOrdTrade() + projectFeatures.size());
                }
            });

            // 将需要新增的数据放入集合，后续统一入库
            for (List<ProjectFeature> value : tradeFeatureMap.values()) {
                insertList.addAll(value);
            }
        }

        this.batchUpdate(systemFeatureList, false);

        if (CollectionUtils.isNotEmpty(insertList)){
            projectFeatureMapper.insertBatch(insertList);
        }

        LocalDateTime end = LocalDateTime.now();
        log.info("----初始化工程特征内置数据完成，耗时={}ms ---", Duration.between(begin, end).toMillis());
    }

    private void batchUpdate(List<ProjectFeature> featureList, boolean isSelf){
        if (CollectionUtils.isEmpty(featureList)){
            return;
        }

        List<ProjectFeature> featureUpdateList = featureList.parallelStream().map(x -> {
            ProjectFeature projectFeature = new ProjectFeature();
            projectFeature.setId(x.getId());
            projectFeature.setName(x.getName());
            projectFeature.setTypeCode(x.getTypeCode());
            projectFeature.setOption(x.getOption());
            projectFeature.setUnit(x.getUnit());
            projectFeature.setExpressionCode(x.getExpressionCode());
            projectFeature.setOrdTrade(x.getOrdTrade());
            projectFeature.setUnit(x.getUnit());
            projectFeature.setIsExpression(x.getIsExpression());
            projectFeature.setRemark(x.getRemark());
            projectFeature.setIsExpressionDefault(x.getIsExpressionDefault());
            return projectFeature;
        }).collect(Collectors.toList());

        if (isSelf){
            projectFeatureSelfMapper.batchUpdateSelective(featureUpdateList);
        }else {
            projectFeatureMapper.batchUpdateSelective(featureUpdateList);
        }

    }

    private String buildProjectType(String category, Integer type, List<CommonProjCategory> categoryList){
        List<String> topCategoryNameList = null;
        if (TYPE_DEFAULT.equals(type)){
            topCategoryNameList = new ArrayList<>(Arrays.asList(category.split("、")));
        }
        return CategoryUtil.buildProjectType(categoryList, topCategoryNameList);
    }

    private ProjectFeature buildBasicFeature(Map<String, ZbStandardsExpression> expressionMap, FeatureExcelVo item){
        ProjectFeature excelFeature = new ProjectFeature();

        excelFeature.setId(SnowflakeIdUtils.getNextId());
        if (expressionMap.containsKey(item.getName())){
            ZbStandardsExpression expression = expressionMap.get(item.getName());
            excelFeature.setExpressionId(expression.getId());
            excelFeature.setExpressionCode(expression.getExpressionCode());
        }
        excelFeature.setName(item.getName());
        excelFeature.setTypeCode(ExpressionTypeEnum.TYPE_NUMBER.getCode());
        excelFeature.setUnit(item.getUnit());
        excelFeature.setIsUsing("☑".equals(item.getIsEnable()) ? 1 : 0);
        excelFeature.setIsRequired("☑".equals(item.getIsRequired()) ? 1 : 0);
        excelFeature.setIsExpression("☑".equals(item.getIsExpression()) ? 1 : 0);
        excelFeature.setRemark(item.getRemark());
        excelFeature.setIsExpressionDefault(WHETHER_TRUE);

        excelFeature.setIsSync(WHETHER_FALSE);
        excelFeature.setIsDefault(WHETHER_TRUE);
        excelFeature.setIsFromSystem(WHETHER_TRUE);
        excelFeature.setIsDeleted(WHETHER_FALSE);
        excelFeature.setCreateGlobalId(-100L);
        excelFeature.setCreateTime(new Date());
        excelFeature.setIsSearchCondition(WHETHER_FALSE);

        return excelFeature;
    }

    private void fillName(List<ProjectFeature> featureList, List<ZbStandardsExpression> expressionList, boolean isSystem){
        Map<Long, ZbStandardsExpression> allExpressionMap = expressionList.parallelStream().collect(Collectors.toMap(ZbStandardsExpression::getId, Function.identity()));

        featureList.forEach(item -> {
            ZbStandardsExpression zbStandardsExpression = allExpressionMap.get(item.getExpressionId());

            if (zbStandardsExpression != null){
                item.setName(zbStandardsExpression.getName());
                item.setTypeCode(zbStandardsExpression.getTypeCode());
                item.setOption(zbStandardsExpression.getOption());
                item.setUnit(zbStandardsExpression.getUnit());
                item.setExpressionCode(zbStandardsExpression.getExpressionCode());
                item.setIsExpression(zbStandardsExpression.getIsExpression());
                if (!isSystem && item.getOrdTrade() != null) {
                    item.setOrdTrade(item.getOrdTrade() + 30);
                }
            }
        });
    }

    private String setRemark(String oldRemark, String newRemark){
        if (StringUtils.isNotEmpty(oldRemark) && StringUtils.isNotEmpty(newRemark)){
            return oldRemark + "|" + newRemark;
        }

        if (StringUtils.isNotEmpty(oldRemark)){
            return oldRemark;
        }

        if (StringUtils.isNotEmpty(newRemark)){
            return newRemark;
        }

        return null;
    }

    private void dealSystemExpression(List<ZbStandardsExpression> expressionList){
        List<ZbStandardsExpression> expressionUpdateList = new ArrayList<>();
        expressionList.forEach(item ->{
            String oldName = item.getName();
            String newName = this.nameRemoveUnit(oldName);
            item.setName(newName);
            item.setOldName(oldName);

            ZbStandardsExpression expression = new ZbStandardsExpression();
            expression.setId(item.getId());
            expression.setName(newName);
            expression.setOldName(oldName);
            expressionUpdateList.add(expression);
        });

        standardsExpressionMapper.batchUpdateSelective(expressionUpdateList);

        // 口径字典表原先没有的数据需要内置进去，如：总计容面积，地下车库层高
        List<ZbStandardsExpression> insertList = new ArrayList<>();
        for (Integer type : CategoryTypeConstants.categoryTypeList) {
            insertList.add(buildExpression(SnowflakeIdUtils.getNextId(), "K_4776729036038602798", "地下车库层高", "m", null, 122, type, SYSTEM_CUSTOMER_CODE));
            insertList.add(buildExpression(SnowflakeIdUtils.getNextId(), "K_4776729036038603054", "总计容面积", "m2", "Σ各业态计容面积", 123, type, SYSTEM_CUSTOMER_CODE));
        }

        standardsExpressionMapper.batchInsert(insertList);

        expressionList.addAll(insertList);
    }

    private ZbStandardsExpression buildExpression(Long id, String code, String name, String unit, String remark, Integer ord, Integer type, String customerCode){
        ZbStandardsExpression expression = new ZbStandardsExpression();
        expression.setId(id);
        expression.setExpressionCode(code);
        expression.setQyCode(customerCode);
        expression.setIsUsable(WHETHER_FALSE);
        expression.setIsDeleted(WHETHER_FALSE);
        expression.setName(name);
        expression.setTypeCode(ExpressionTypeEnum.TYPE_NUMBER.getCode());
        expression.setType(type);
        expression.setCreateGlobalId(-100L);
        expression.setIsExpression(WHETHER_TRUE);
        expression.setUnit(unit);
        expression.setExpressionIsFromSystem(WHETHER_TRUE);
        expression.setExpressionIsUsing(WHETHER_TRUE);
        expression.setExpressionOrd(ord);
        expression.setExpressionCreateGlobalId(-100L);
        expression.setOldName(name);
        expression.setExpressionRemark(remark);
        return expression;
    }

    @Transactional(rollbackFor = Exception.class)
    public void expressionToFeatureHandler(String customerCode, List<ZbStandardsExpression> systemExpressionList) {
        // expression的数据
        List<ZbStandardsExpression> expressionList = standardsExpressionMapper.selectListByCustomCode(customerCode, null);
        // feature的数据
        List<ProjectFeature> featureList = projectFeatureMapper.selectNoBlobByCustomerCode(customerCode, null, WHETHER_TRUE);

        if (CollectionUtils.isEmpty(expressionList) || CollectionUtils.isEmpty(featureList)){
            throw new BusinessException("企业数据数据错误...");
        }

        // 对expression数据的处理：1.去掉名称中的单位；2.新增2条内置数据
        this.dealCustomerExpression(expressionList, systemExpressionList, customerCode);

        // 给feature放入expression的信息
        this.fillName(featureList, expressionList, false);

        this.batchUpdate(featureList,false);

    }

    public ResponseVo expressionToFeature(String initCustomerCode){
        try {
            LocalDateTime start = LocalDateTime.now();

            //查询所有的企业，排队默认企业
            List<String> customerCodes;
            if("ALL".equals(initCustomerCode)){
                LambdaQueryWrapper<FeatureRecord> queryWrapper = new LambdaQueryWrapper<FeatureRecord>()
                        .eq(FeatureRecord::getToFeatureStatus, 0);
                List<FeatureRecord> recordList = featureRecordService.list(queryWrapper);

                customerCodes = recordList.parallelStream().map(FeatureRecord::getCustomerCode).collect(Collectors.toList());
            }else{
                customerCodes = new ArrayList<>();
                customerCodes.add(initCustomerCode);
            }

            log.info("需要处理的企业数：{}", customerCodes.size());

            // expression的内置数据
            List<ZbStandardsExpression> systemExpressionList = standardsExpressionMapper.selectListByCustomCode(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, null);
            if (CollectionUtils.isEmpty(systemExpressionList)){
                throw new BusinessException("expression的内置数据错误...");
            }

            int limit = customerCodes.size() <= 5 ? 1 : customerCodes.size() / 5;
            List<List<String>> threadCustomerCodes = Lists.partition(customerCodes, limit);
            final CountDownLatch latch = new CountDownLatch(threadCustomerCodes.size());

            for (int i = 0; i < threadCustomerCodes.size(); i++) {
                List<String> threadCustomerCode = threadCustomerCodes.get(i);

                syncExecutor.execute(() -> {
                    for (int j = 0; j < threadCustomerCode.size(); j++) {
                        String customerCode = threadCustomerCode.get(j);
                        ExpressionMoveService expressionMoveService = SpringUtil.getBean(Constants.BeanName.EXPRESSION_MOVE_SERVICE);
                        FeatureRecord featureRecord = new FeatureRecord();
                        try{
                            expressionMoveService.expressionToFeatureHandler(customerCode, systemExpressionList);
                            featureRecord.setToFeatureStatus(1);
                        }catch (Exception e){
                            log.error("customerCode：{}", customerCode, e);
                            featureRecord.setToFeatureStatus(2);
                        }
                        featureRecordService.update(featureRecord, new LambdaQueryWrapper<FeatureRecord>().eq(FeatureRecord::getCustomerCode, customerCode));
                    }
                    latch.countDown();
                });
            }

            latch.await();

            LocalDateTime end5 = LocalDateTime.now();
            String desc = String.format("所有企业处理结束，共 %d 个企业，总耗时：%d ms", customerCodes.size(), Duration.between(start, end5).toMillis());
            log.info(desc);
            return ResponseVo.success(desc);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseVo.error();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void initRecord() {
        LocalDateTime begin = LocalDateTime.now();
        long countTemp = featureRecordService.count();

        if (countTemp > 0){
            throw new BusinessException("记录表已经初识化过...");
        }

        Set<String> customerCodes = projectFeatureMapper.selectAllCustomerCodes();
        log.info("需要处理的企业数：{}", customerCodes.size());

        Set<String> filterSet = new HashSet<>(Arrays.asList("-100", "null","undefined"));

        List<FeatureRecord> featureRecordList = customerCodes.parallelStream()
                .filter(x -> !filterSet.contains(x))
                .map(FeatureRecord::new).collect(Collectors.toList());

        featureRecordService.saveBatch(featureRecordList);

        // 暂存
        Set<String> customerCodesSet = projectFeatureSelfMapper.selectAllCustomerCodes();

        List<FeatureRecordSelf> featureRecordSelfList = new ArrayList<>();

        for (String customerCode : customerCodesSet) {
            if (StringUtils.isEmpty(customerCode)){
                continue;
            }
            FeatureRecordSelf featureRecordSelf = new FeatureRecordSelf();
            featureRecordSelf.setCustomerCode(customerCode);
            featureRecordSelfList.add(featureRecordSelf);
        }

        featureRecordSelfService.saveBatch(featureRecordSelfList);

        LocalDateTime end = LocalDateTime.now();
        log.info("----初始化记录表完成，耗时={}ms ---", Duration.between(begin, end).toMillis());
    }

    public ResponseVo expressionToFeatureSelf(Long globalId, String customerCode){
        try {
            LocalDateTime start = LocalDateTime.now();

            //查询所有的企业，排队默认企业
            List<FeatureRecordSelf> selfList;
            if(globalId == null){
                LambdaQueryWrapper<FeatureRecordSelf> queryWrapper = new LambdaQueryWrapper<FeatureRecordSelf>()
                        .eq(FeatureRecordSelf::getToFeatureStatus, 0);
                selfList = featureRecordSelfService.list(queryWrapper);
            }else{
                selfList = new ArrayList<>();
                FeatureRecordSelf featureRecordSelf = new FeatureRecordSelf();
                featureRecordSelf.setGlobalId(globalId);
                featureRecordSelf.setCustomerCode(customerCode);
                selfList.add(featureRecordSelf);
            }

            if (CollectionUtils.isEmpty(selfList)){
                return ResponseVo.error("没有需要处理...");
            }

            log.info("需要处理的用户数：{}", selfList.size());

            int limit = selfList.size() <= 5 ? 1 : selfList.size() / 5;
            List<List<FeatureRecordSelf>> threadCustomerCodes = Lists.partition(selfList, limit);
            final CountDownLatch latch = new CountDownLatch(threadCustomerCodes.size());

            for (int i = 0; i < threadCustomerCodes.size(); i++) {
                List<FeatureRecordSelf> threadCustomerCode = threadCustomerCodes.get(i);

                syncExecutor.execute(() -> {
                    for (int j = 0; j < threadCustomerCode.size(); j++) {
                        FeatureRecordSelf recordSelf = threadCustomerCode.get(j);
                        ExpressionMoveService expressionMoveService = SpringUtil.getBean(Constants.BeanName.EXPRESSION_MOVE_SERVICE);
                        FeatureRecordSelf featureRecordSelf = new FeatureRecordSelf();
                        try{
                            expressionMoveService.expressionToFeatureSelfHandler(recordSelf.getGlobalId(), recordSelf.getCustomerCode());
                            featureRecordSelf.setToFeatureStatus(1);
                        }catch (Exception e){
                            log.error("globalId：{}", recordSelf.getGlobalId(), e);
                            featureRecordSelf.setToFeatureStatus(2);
                        }
                        featureRecordSelfService.update(featureRecordSelf, new LambdaQueryWrapper<FeatureRecordSelf>()
                                .eq(FeatureRecordSelf::getCustomerCode, recordSelf.getCustomerCode()).eq(FeatureRecordSelf::getGlobalId, recordSelf.getGlobalId()));
                    }
                    latch.countDown();
                });
            }

            latch.await();

            LocalDateTime end5 = LocalDateTime.now();
            String desc = String.format("所有用户处理结束，共 %d 个用户，总耗时：%d ms", selfList.size(), Duration.between(start, end5).toMillis());
            log.info(desc);
            return ResponseVo.success(desc);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseVo.error();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void expressionToFeatureSelfHandler(Long globalId, String customerCode) {
        LambdaQueryWrapper<FeatureRecord> queryWrapper = new LambdaQueryWrapper<FeatureRecord>()
                .eq(FeatureRecord::getCustomerCode, customerCode)
                .eq(FeatureRecord::getToFeatureStatus, 1);
        FeatureRecord customerStatus = featureRecordService.getOne(queryWrapper);
        if (customerStatus == null){
            throw new BusinessException("企业的数据还没处理成功...");
        }

        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        // expression的数据
        List<ZbStandardsExpression> selfExpressionList = standardsExpressionSelfMapper.selectAllByCustomCode(customerCode, type, globalId);
        // feature的数据
        List<ProjectFeature> selfFeatureList = projectFeatureSelfMapper.selectAllNoBlobByCustomCode(customerCode, type);
        // feature的数据
        List<ZbStandardsExpression> expressionList = standardsExpressionMapper.selectListByCustomCode(customerCode, type);

        if (CollectionUtils.isEmpty(selfExpressionList) || CollectionUtils.isEmpty(selfFeatureList)){
            throw new BusinessException("暂存数据错误...");
        }

        // 对expression数据的处理：1.去掉名称中的单位；2.新增2条内置数据
        this.dealSelfExpression(selfExpressionList, expressionList, globalId, customerCode);

        // 给feature放入expression的信息
        this.fillName(selfFeatureList, selfExpressionList, false);

        this.batchUpdate(selfFeatureList,true);
    }

    @Transactional(rollbackFor = Exception.class)
    public Integer handlerSelf(List<FeatureExcelVo> excelDataList, Long globalId, String customerCode) {
//        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
//        // 工程分类
//        List<CommonProjCategory> categoryList = commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE);
//        boolean removeFlag = CategoryUtil.deDuplication(categoryList);
//
//        List<ZbStandardsExpression> expressionList = standardsExpressionMapper.selectListByCustomCode(customerCode, type);
//
//        List<ProjectFeature> featureList = projectFeatureMapper.selectNoBlobByCustomerCode(customerCode, type, WHETHER_TRUE);
//
//        List<ProjectFeature> expressionDefault = featureList.parallelStream().filter(x -> Constants.DEFAULT.equals(x.getIsExpressionDefault())).collect(Collectors.toList());
//
//        Map<String, Long> nameAndIdMap = expressionDefault.parallelStream().collect(Collectors.toMap((x -> x.getTradeId() + "-" + x.getName()), ProjectFeature::getId));
//
//        Map<Long, String> idAndNameMap = expressionDefault.parallelStream().collect(Collectors.toMap(ProjectFeature::getId, (x -> x.getTradeId() + "-" + x.getName())));
//
//        List<Long> featureIds = expressionDefault.parallelStream().map(ProjectFeature::getId).collect(Collectors.toList());
//
//        List<ProjectFeatureCategoryView> newCategoryViewList = projectFeatureCategoryViewMapper.selectByFeatureIds(customerCode, featureIds);
//
//        Map<String, Long> categoryNameAndIdMap = newCategoryViewList.parallelStream()
//                .collect(Collectors.toMap((x -> idAndNameMap.get(x.getFeatureId()) + "-" + x.getTradeId() + "-" + x.getCategoryCode()), ProjectFeatureCategoryView::getId));
//
//
//        // expression的数据
//        List<ZbStandardsExpression> selfExpressionList = standardsExpressionSelfMapper.selectAllByCustomCode(customerCode, type, globalId);
//        // feature的数据
//        List<ProjectFeature> selfFeatureList = projectFeatureSelfMapper.selectAllNoBlobByCustomCode(customerCode, type, globalId);
//        // categoryView的数据
//        List<ProjectFeatureCategoryView> selfCategoryViewList = projectFeatureCategoryViewSelfMapper.selectAllByCustomer(customerCode, globalId, type);
//        // trade的数据
//        List<ZbStandardsTrade> tradeList = standardsTradeMapper.selectAllListByCustomerCode(customerCode);
//        Map<String, Long> tradeNameAndIdMap = tradeList.parallelStream().collect(Collectors.toMap(ZbStandardsTrade::getDescription, ZbStandardsTrade::getId, (v1, v2) -> v2));
//
//        if (CollectionUtils.isEmpty(expressionList) || CollectionUtils.isEmpty(selfExpressionList)
//                || CollectionUtils.isEmpty(selfFeatureList) || CollectionUtils.isEmpty(selfCategoryViewList)){
//            throw new BusinessException("原始数据错误...");
//        }
//
//        List<Long> globalIdList = selfFeatureList.parallelStream().map(ProjectFeature::getSelfGlobalId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
//
//        if (CollectionUtils.isNotEmpty(globalIdList)){
//            List<ProjectFeature> insertList = new ArrayList<>();
//            List<ProjectFeature> updateList = new ArrayList<>();
//            List<ProjectFeatureCategoryView> categoryViewInsertList = new ArrayList<>();
//
//            // 区分globalId处理
//            Map<String, ProjectFeature> numberFeatureMap = selfFeatureList.parallelStream()
//                    .filter(x -> ExpressionTypeEnum.TYPE_NUMBER.getCode().equals(x.getTypeCode()))
//                    .collect(Collectors.toMap(x -> x.getTradeId() + "-" + x.getName(), Function.identity()));
//
//            Map<Long, List<ProjectFeatureCategoryView>> tradeCategoryView = selfCategoryViewList.stream()
//                    .collect(Collectors.groupingBy(ProjectFeatureCategoryView::getTradeId));
//
//            Map<String, ZbStandardsExpression> expressionMap = selfExpressionList.parallelStream()
//                    .filter(x -> ExpressionTypeEnum.TYPE_NUMBER.getCode().equals(x.getTypeCode()))
//                    .collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(), (v1, v2)-> v2));
//
//            // 根据excel导入数据
//            Map<Long, List<ProjectFeature>> tradeFeatureMap = this.insertOrUpdate(excelDataList, tradeNameAndIdMap, numberFeatureMap, expressionMap, type, categoryList, customerCode, updateList, false);
//
//            for (Map.Entry<Long, List<ProjectFeature>> entry : tradeFeatureMap.entrySet()) {
//                Long tradeId = entry.getKey();
//                List<ProjectFeature> projectFeatures = entry.getValue();
//
//                projectFeatures.forEach(item -> {
//                    item.setOriginId(nameAndIdMap.get(item.getTradeId() + "-" + item.getName()));
//                });
//
//                // 将需要新增的数据放入集合，后续统一入库
//                insertList.addAll(projectFeatures);
//
//                // 处理分类视图的数据
//                List<ProjectFeatureCategoryView> featureCategoryViewList;
//                if (tradeCategoryView.containsKey(tradeId)){
//                    featureCategoryViewList = tradeCategoryView.get(tradeId);
//                }else {
//                    featureCategoryViewList = new ArrayList<>();
//                    tradeCategoryView.put(tradeId, featureCategoryViewList);
//                }
//
//                for (ProjectFeature projectFeature : projectFeatures) {
//                    List<ProjectFeatureCategoryView> projectFeatureCategoryViews = this.addCategoryView(projectFeature, featureCategoryViewList);
//                    projectFeatureCategoryViews.forEach(item -> {
//                        item.setOriginId(categoryNameAndIdMap.get(projectFeature.getTradeId() + "-" + projectFeature.getName() + "-" + item.getTradeId() + "-" + item.getCategoryCode()));
//                    });
//
//                    categoryViewInsertList.addAll(projectFeatureCategoryViews);
//                    featureCategoryViewList.addAll(projectFeatureCategoryViews);
//                }
//            }
//
//            this.batchUpdate(updateList, true);
//
//            if (CollectionUtils.isNotEmpty(insertList)){
//                projectFeatureSelfMapper.insertSelectiveBatch(insertList);
//            }
//
//            if (CollectionUtils.isNotEmpty(categoryViewInsertList)) {
//                projectFeatureCategoryViewSelfMapper.saveBatch(categoryViewInsertList);
//            }
//        }
//
//        return removeFlag ? STATUS_CATEGORY_REPEAT : STATUS_SUCCESS;
        return null;
    }

    public ResponseVo initSelfFeature(List<FeatureExcelVo> excelDataList, Long globalId, String customerCode){
        try {
            LocalDateTime start = LocalDateTime.now();
            //查询所有的企业
            List<FeatureRecordSelf> selfList;
            if(globalId == null){
                LambdaQueryWrapper<FeatureRecordSelf> queryWrapper = new LambdaQueryWrapper<FeatureRecordSelf>()
                        .eq(FeatureRecordSelf::getToFeatureStatus, 1)
                        .eq(FeatureRecordSelf::getAddStatus, 0);
                selfList = featureRecordSelfService.list(queryWrapper);
            }else{
                selfList = new ArrayList<>();
                FeatureRecordSelf featureRecordSelf = new FeatureRecordSelf();
                featureRecordSelf.setGlobalId(globalId);
                featureRecordSelf.setCustomerCode(customerCode);
                selfList.add(featureRecordSelf);
            }

            log.info("需要处理的用户数：{}", selfList.size());

            int limit = selfList.size() <= 5 ? 1 : selfList.size() / 5;
            List<List<FeatureRecordSelf>> threadCustomerCodes = Lists.partition(selfList, limit);
            final CountDownLatch latch = new CountDownLatch(threadCustomerCodes.size());

            for (int i = 0; i < threadCustomerCodes.size(); i++) {
                List<FeatureRecordSelf> threadCustomerCode = threadCustomerCodes.get(i);

                syncExecutor.execute(() -> {
                    for (int j = 0; j < threadCustomerCode.size(); j++) {
                        FeatureRecordSelf recordSelf = threadCustomerCode.get(j);
                        ExpressionMoveService expressionMoveService = SpringUtil.getBean(Constants.BeanName.EXPRESSION_MOVE_SERVICE);
                        FeatureRecordSelf featureRecordSelf = new FeatureRecordSelf();
                        try{
                            Integer status = expressionMoveService.handlerSelf(excelDataList, recordSelf.getGlobalId(), recordSelf.getCustomerCode());
                            featureRecordSelf.setAddStatus(status);
                        }catch (Exception e){
                            log.error("globalId：{}", recordSelf.getGlobalId(), e);
                            featureRecordSelf.setAddStatus(2);
                        }
                        featureRecordSelfService.update(featureRecordSelf,
                                new LambdaQueryWrapper<FeatureRecordSelf>().eq(FeatureRecordSelf::getCustomerCode, recordSelf.getCustomerCode())
                                        .eq(FeatureRecordSelf::getGlobalId, recordSelf.getGlobalId()));
                    }
                    latch.countDown();
                });
            }

            latch.await();

            LocalDateTime end5 = LocalDateTime.now();
            String desc = String.format("所有用户处理结束，共 %d 个用户，总耗时：%d ms", selfList.size(), Duration.between(start, end5).toMillis());
            log.info(desc);
            return ResponseVo.success(desc);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseVo.error();
        }
    }

    protected boolean isNeedInit(String customerCode) {
        // 根据企业编码查询记录表
        LambdaQueryWrapper<FeatureRecord> queryWrapper = new LambdaQueryWrapper<FeatureRecord>()
                .eq(FeatureRecord::getCustomerCode, customerCode);
        FeatureRecord recordTemp = featureRecordService.getOne(queryWrapper);

        // 有记录，并且toFeatureStatus的状态是0，说明是需要处理的，并且还没处理过的
        return recordTemp != null && STATUS_DEFAULT.equals(recordTemp.getToFeatureStatus());
    }

    public boolean refreshData(String customerCode, boolean isLogTime) {
        if (!isNeedInit(customerCode)){
            return false;
        }

        String lockValue = redisUtil.generateLockValue();
        if (redisUtil.tryLockRetry(lockValue, RedisKeyEnum.LOCK_REFRESH_FEATURE, customerCode)) {
            try {
                if (!isNeedInit(customerCode)){
                    return false;
                }
                LocalDateTime start = LocalDateTime.now();
                executeInit(customerCode);
                LocalDateTime end = LocalDateTime.now();
                if (isLogTime){
                    log.info("企业【{}】执行refreshData完毕，总耗时：{} ms", customerCode, Duration.between(start, end).toMillis());
                }
            } finally {
                redisUtil.unlock(lockValue, RedisKeyEnum.LOCK_REFRESH_FEATURE, customerCode);
            }
        }

        return true;
    }

    private void executeInit(String customerCode) {
        FeatureRecord customerRecord = new FeatureRecord();
        // 企业-ExpressionToFeature
        customerExpressionToFeature(customerCode, customerRecord);

        if (STATUS_SUCCESS.equals(customerRecord.getToFeatureStatus())){
            // 企业-新增数据
            customerAddData(customerCode, customerRecord);
        }

        featureRecordService.update(customerRecord, new LambdaQueryWrapper<FeatureRecord>().eq(FeatureRecord::getCustomerCode, customerCode));

        executeSelfInit(customerCode, customerRecord.getToFeatureStatus(), customerRecord.getAddStatus());
    }

    private void executeSelfInit(String customerCode, Integer customerToFeatureStatus, Integer customerAddStatus) {
        if (STATUS_FAIL.equals(customerToFeatureStatus)){
            return;
        }

        LambdaQueryWrapper<FeatureRecordSelf> selfQueryWrapper = new LambdaQueryWrapper<FeatureRecordSelf>()
                    .eq(FeatureRecordSelf::getCustomerCode, customerCode);
        List<FeatureRecordSelf> selfList = featureRecordSelfService.list(selfQueryWrapper);

        if (CollectionUtils.isEmpty(selfList)){
            return;
        }

        for (FeatureRecordSelf recordSelf : selfList) {
            Long globalId = recordSelf.getGlobalId();
            // self-ExpressionToFeature
            Integer selfToFeatureStatus = selfExpressionToFeature(globalId, customerCode);
            Integer selfAddStatus = null;

            if (STATUS_SUCCESS.equals(customerAddStatus) && STATUS_SUCCESS.equals(selfToFeatureStatus)){
                // self-新增数据
                selfAddStatus = selfAddData(globalId, customerCode);
            }

            FeatureRecordSelf selfRecord = new FeatureRecordSelf(selfToFeatureStatus, selfAddStatus);
            featureRecordSelfService.update(selfRecord, new LambdaQueryWrapper<FeatureRecordSelf>()
                    .eq(FeatureRecordSelf::getCustomerCode, customerCode).eq(FeatureRecordSelf::getGlobalId, globalId));
        }
    }

    private void customerExpressionToFeature(String customerCode, FeatureRecord customerRecord){
        try{
            // expression的内置数据
            List<ZbStandardsExpression> systemExpressionList = standardsExpressionMapper.selectListByCustomCode(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, null);
            if (CollectionUtils.isEmpty(systemExpressionList)){
                throw new BusinessException("expression的内置数据错误...");
            }

            ExpressionMoveService expressionMoveService = SpringUtil.getBean(Constants.BeanName.EXPRESSION_MOVE_SERVICE);
            expressionMoveService.expressionToFeatureHandler(customerCode, systemExpressionList);
            customerRecord.setToFeatureStatus(STATUS_SUCCESS);
        }catch (Exception e){
            log.error("customerExpressionToFeature-error：{}", customerCode, e);
            customerRecord.setToFeatureStatus(STATUS_FAIL);
            customerRecord.setErrorMsg(e.getMessage());
        }

    }

    private void customerAddData(String customerCode, FeatureRecord customerRecord){
        try{
            ExpressionMoveService expressionMoveService = SpringUtil.getBean(Constants.BeanName.EXPRESSION_MOVE_SERVICE);
            Integer status = expressionMoveService.handler(customerCode, excelDataList);
            customerRecord.setAddStatus(status);
        }catch (Exception e){
            log.error("customerAddData-error：{}", customerCode, e);
            customerRecord.setAddStatus(STATUS_FAIL);
            customerRecord.setErrorMsg(e.getMessage());
        }
    }

    private Integer selfExpressionToFeature(Long globalId, String customerCode){
        Integer status;

        try{
            ExpressionMoveService expressionMoveService = SpringUtil.getBean(Constants.BeanName.EXPRESSION_MOVE_SERVICE);
            expressionMoveService.expressionToFeatureSelfHandler(globalId, customerCode);
            status = STATUS_SUCCESS;
        }catch (Exception e){
            log.error("selfExpressionToFeature-error：{}", globalId, e);
            status = STATUS_FAIL;
        }
        return status;
    }

    private Integer selfAddData(Long globalId, String customerCode){
        Integer status;

        try{
            ExpressionMoveService expressionMoveService = SpringUtil.getBean(Constants.BeanName.EXPRESSION_MOVE_SERVICE);
            status = expressionMoveService.handlerSelf(excelDataList, globalId, customerCode);
        }catch (Exception e){
            log.error("selfAddData-error：{}", globalId, e);
            status = STATUS_FAIL;
        }
        return status;
    }


    public ResponseVo refreshDataByCustomer(String initCustomerCode, Integer limit, boolean isLogTime){
        try {
            LocalDateTime start = LocalDateTime.now();

            //查询所有的企业，排队默认企业
            List<String> customerCodes;
            if("ALL".equals(initCustomerCode)){
                LambdaQueryWrapper<FeatureRecord> queryWrapper = new LambdaQueryWrapper<FeatureRecord>()
                        .eq(FeatureRecord::getToFeatureStatus, 0).last(" limit " + limit);
                List<FeatureRecord> recordList = featureRecordService.list(queryWrapper);

                customerCodes = recordList.parallelStream().map(FeatureRecord::getCustomerCode).collect(Collectors.toList());
            }else{
                customerCodes = new ArrayList<>();
                customerCodes.add(initCustomerCode);
            }

            log.info("需要处理的企业数：{}", customerCodes.size());

            int size = customerCodes.size() <= 5 ? 1 : customerCodes.size() / 5;
            List<List<String>> threadCustomerCodes = Lists.partition(customerCodes, size);
            final CountDownLatch latch = new CountDownLatch(threadCustomerCodes.size());

            for (int i = 0; i < threadCustomerCodes.size(); i++) {
                List<String> threadCustomerCode = threadCustomerCodes.get(i);

                syncExecutor.execute(() -> {
                    for (int j = 0; j < threadCustomerCode.size(); j++) {
                        String customerCode = threadCustomerCode.get(j);
                        refreshData(customerCode, isLogTime);

                        // 100家打一次日志
                        count.getAndIncrement();
                        if (count.get() % 100 == 0) {
                            log.info("已处理：{}", count.get());
                        }
                    }
                    latch.countDown();
                });
            }

            latch.await();

            LocalDateTime end5 = LocalDateTime.now();
            String desc = String.format("耗时：%d ms", Duration.between(start, end5).toMillis());
            log.info(desc);
            return ResponseVo.success(desc);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseVo.error();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void dataStatistics() {
        LocalDateTime begin = LocalDateTime.now();

        List<FeatureRecord> recordList = featureRecordService.list();
        log.info("需要处理的企业数：{}", recordList.size());

        List<FeatureRecord> updateList = new ArrayList<>();

        for (FeatureRecord featureRecord : recordList) {
            FeatureRecord updateFeatureRecord = dataStatisticsByCustomer(featureRecord.getCustomerCode(), featureRecord.getId());
            updateList.add(updateFeatureRecord);
        }

        featureRecordService.updateBatchById(updateList);

        LocalDateTime end = LocalDateTime.now();
        log.info("----数据统计完成，耗时={}ms ---", Duration.between(begin, end).toMillis());
    }

    private FeatureRecord dataStatisticsByCustomer(String customerCode, Long id){
        FeatureRecord updateFeatureRecord = new FeatureRecord();
        updateFeatureRecord.setId(id);

        List<ZbStandardsTrade> tradeList = standardsTradeMapper.selectListByCustomerCodeAndType(customerCode, Constants.ZbStandardsTradeConstants.TradeType.SYSTEM_TRADE);

        long countTemp = tradeList.stream().filter(x -> TradeReferEnum.TU_JIAN.getCode().equals(x.getTradeCode())).count();
        updateFeatureRecord.setInitCount((int) countTemp);

        if (countTemp == 1){
            List<ProjectFeature> projectFeatures = projectFeatureMapper.selectRepeat(customerCode);
            if (CollectionUtils.isNotEmpty(projectFeatures)){
                updateFeatureRecord.setIsRepeat(1);
                projectFeatures = projectFeatures.subList(0, Math.min(projectFeatures.size(), 10));
                updateFeatureRecord.setRepeatInfo(JSON.toJSONString(projectFeatures));
            }
        }

        return updateFeatureRecord;
    }

    public void repairCategoryView(String customerCode){
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<ProjectFeatureCategoryView> categoryViewList = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(null, customerCode, type);

        if (CollectionUtils.isNotEmpty(categoryViewList)){
            log.error("分类视图有数据，该修复方法不适用...");
            return;
        }

        log.info("开始修复企业【{}】的分类视图...", customerCode);

        List<ProjectFeature> featureList = projectFeatureMapper.selectAll(customerCode, type);

        Map<String, List<ProjectFeatureCategoryView>> groupMap = new HashMap<>(16);
        List<ProjectFeatureCategoryView> projectFeatureCategoryViewList = new ArrayList<>();

        for (ProjectFeature feature : featureList) {
            // todo 这块代码 groupMap没有初始化可能导致数据 顺序不正确
            List<ProjectFeatureCategoryView> projectFeatureCategoryViews = projectFeatureService.initCategoryView(feature, groupMap);
            projectFeatureCategoryViewList.addAll(projectFeatureCategoryViews);
        }

        // 插入分类视图
        if (CollectionUtils.isNotEmpty(projectFeatureCategoryViewList)) {
            projectFeatureCategoryViewMapper.saveBatch(projectFeatureCategoryViewList);
        }

        log.info("修复企业【{}】的分类视图完成，专业视图条数：{}，分类视图条数：{}...", customerCode, featureList.size(), projectFeatureCategoryViewList.size());
    }

}
