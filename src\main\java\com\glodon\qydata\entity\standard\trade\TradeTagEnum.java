package com.glodon.qydata.entity.standard.trade;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: dongxp
 * @date: 2024/10/22 14:31
 * @description: 专业标签分类表
 */
@Data
public class TradeTagEnum implements Serializable {
    private static final long serialVersionUID = 2268907735095701191L;

    private Long id;

    private String name;

    private String enterpriseId;

    private Date createTime;

    private Date modifyTime;

    private Integer ord;
}
