<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardDetailMapper">
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="standard_id" jdbcType="BIGINT" property="standardId" />
    <result column="level_code" jdbcType="VARCHAR" property="levelcode" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="desc" jdbcType="VARCHAR" property="desc" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ord" jdbcType="INTEGER" property="ord" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="trade_id" jdbcType="BIGINT" property="tradeId" />
    <result column="trade_name" jdbcType="VARCHAR" property="tradeName" />
    <result column="item_division_subject_id" jdbcType="VARCHAR" property="itemDivisionSubjectId" />
    <result column="item_division_subject_name" jdbcType="LONGVARCHAR" property="itemDivisionSubjectName" />
  </resultMap>

  <sql id="Base_Column_List">
    id,standard_id,level_code,`name`,`desc`,`level`,remark,ord,create_time,update_time,trade_id, trade_name, item_division_subject_id, item_division_subject_name
  </sql>

  <insert id="insert" parameterType="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail">
    insert into zb_standards_build_standard_detail_self (id, standard_id, level_code,
      name, `desc`, level,
      remark, ord, create_time,
      update_time, trade_id, trade_name)
    values (#{id,jdbcType=BIGINT}, #{standardId,jdbcType=BIGINT}, #{levelcode,jdbcType=VARCHAR},
      #{name,jdbcType=VARCHAR}, #{desc,jdbcType=VARCHAR}, #{level,jdbcType=INTEGER},
      #{remark,jdbcType=BIGINT}, #{ord,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP},
      #{updateTime,jdbcType=TIMESTAMP}, #{tradeId,jdbcType=BIGINT}, #{tradeName,jdbcType=VARCHAR})

  </insert>
  <select id="selectBrothersById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from zb_standards_build_standard_detail_self where standard_id = #{standardId,jdbcType=BIGINT}
    and length(level_code) = length(#{fatherLevelCode,jdbcType=VARCHAR}) + 3
    <if test="fatherLevelCode != null and fatherLevelCode != ''">
      and level_code > #{fatherLevelCode,jdbcType=VARCHAR}
      and instr(level_code, #{fatherLevelCode,jdbcType=VARCHAR}) = 1
    </if>
  </select>

  <select id="selectById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from zb_standards_build_standard_detail_self where id = #{id,jdbcType=BIGINT}
  </select>

  <update id="updateOnePlusOrdByStandardIdAndOrd">
    update zb_standards_build_standard_detail_self set ord = ord + 1 where standard_id = #{standardId,jdbcType=BIGINT} and ord >= #{ord,jdbcType=INTEGER}
  </update>

  <select id="selectByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from zb_standards_build_standard_detail where id in
    <foreach collection="idList" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>

  <select id="selectByStandardId" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from zb_standards_build_standard_detail where standard_id = #{standardId,jdbcType=BIGINT} order by ord asc
  </select>

  <select id="selectByStandardIdList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from zb_standards_build_standard_detail where standard_id in
    <foreach collection="standardIdList" open="(" close=")" separator="," item="item" index="index">
      #{item}
    </foreach>
  </select>

  <select id="selectSelfByStandardIdList" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" />, origin_id from zb_standards_build_standard_detail_self where standard_id in
    <foreach collection="standardIdList" open="(" close=")" separator="," item="item" index="index">
      #{item}
    </foreach>
  </select>

  <delete id="deleteByStandardIds">
    delete from zb_standards_build_standard_detail where standard_id in
    <foreach collection="ids" item="item" separator="," close=")" open="(">
      #{item}
    </foreach>
  </delete>
  <delete id="deleteSelfByIds">
    delete from zb_standards_build_standard_detail_self where id in
    <foreach collection="ids" item="item" separator="," close=")" open="(">
      #{item}
    </foreach>
  </delete>
  <delete id="updateDescBlankByLevelCodeAndStandardId">
    update zb_standards_build_standard_detail_self
    set `desc` = null
    where standard_id = #{standardId,jdbcType=BIGINT}
      and level_code = #{levelcode,jdbcType=VARCHAR}
  </delete>

  <select id="selectSons" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from zb_standards_build_standard_detail_self where standard_id = #{standardId,jdbcType=BIGINT}
    and level_code > #{levelcode,jdbcType=VARCHAR}
    and instr(level_code, #{levelcode,jdbcType=VARCHAR}) = 1
    and length(level_code) = length(#{levelcode,jdbcType=VARCHAR}) + 3
    </select>

  <update id="updateSonsTrade">
    update zb_standards_build_standard_detail_self
    set trade_id = #{detail.tradeId,jdbcType=BIGINT}, trade_name = #{detail.tradeName,jdbcType=VARCHAR}
    where standard_id = #{standardId,jdbcType=BIGINT}
      and level_code > #{detail.levelcode,jdbcType=VARCHAR}
      and instr(level_code, #{detail.levelcode,jdbcType=VARCHAR}) = 1
  </update>

  <select id="selectAllSons" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from zb_standards_build_standard_detail_self where standard_id = #{standardId,jdbcType=BIGINT}
    and level_code > #{levelcode,jdbcType=VARCHAR}
    and instr(level_code, #{levelcode,jdbcType=VARCHAR}) = 1
    order by ord asc
  </select>

  <select id="selectSameLevelByOrd" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from zb_standards_build_standard_detail_self where standard_id = #{standardId,jdbcType=BIGINT}
    and level = #{level,jdbcType=INTEGER}
    order by ord asc
  </select>

  <update id="updateOnePlusOrdByIds">
    update zb_standards_build_standard_detail_self set ord = ord + #{size,jdbcType=INTEGER} where id in
    <foreach collection="ids" close=")" open="(" separator="," item="item">
      #{item}
    </foreach>
  </update>

  <update id="updateOneMinusOrdByIds">
    update zb_standards_build_standard_detail_self set ord = ord - #{size,jdbcType=INTEGER} where id in
    <foreach collection="ids" close=")" open="(" separator="," item="item">
      #{item}
    </foreach>
  </update>

  <insert id="saveBatch" parameterType="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail">
    insert into zb_standards_build_standard_detail (id, standard_id, level_code,
                                                    name, `desc`, level,
                                                    remark, ord, create_time,
                                                    update_time, trade_id, trade_name,item_division_subject_id, item_division_subject_name)
    values
      <foreach collection="details" item="item" separator=",">
        (#{item.id,jdbcType=BIGINT}, #{item.standardId,jdbcType=BIGINT}, #{item.levelcode,jdbcType=VARCHAR},
        #{item.name,jdbcType=VARCHAR}, #{item.desc,jdbcType=VARCHAR}, #{item.level,jdbcType=INTEGER},
        #{item.remark,jdbcType=BIGINT}, #{item.ord,jdbcType=INTEGER}, now(),
        #{item.updateTime,jdbcType=TIMESTAMP}, #{item.tradeId,jdbcType=BIGINT}, #{item.tradeName,jdbcType=VARCHAR},
        #{item.itemDivisionSubjectId,jdbcType=VARCHAR}, #{item.itemDivisionSubjectName,jdbcType=LONGVARCHAR})
      </foreach>
  </insert>

  <update id="updateOrdByList">
    <foreach collection="updateOrdList" separator=";" item="item">
      update zb_standards_build_standard_detail_self set ord = #{item.ord} where id = #{item.id}
    </foreach>
  </update>

  <select id="selectSelfByIds" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from zb_standards_build_standard_detail_self where id in
    <foreach collection="idList" open="(" close=")" separator="," item="item">
      #{item}
    </foreach>
  </select>
  <insert id="saveSelfBatch" parameterType="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail">
    insert into zb_standards_build_standard_detail_self (id, standard_id, level_code,
    name, `desc`, level,
    remark, ord, create_time,
    update_time, trade_id, trade_name, origin_id, item_division_subject_id, item_division_subject_name)
    values
    <foreach collection="details" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.standardId,jdbcType=BIGINT}, #{item.levelcode,jdbcType=VARCHAR},
      #{item.name,jdbcType=VARCHAR}, #{item.desc,jdbcType=VARCHAR}, #{item.level,jdbcType=INTEGER},
      #{item.remark,jdbcType=BIGINT}, #{item.ord,jdbcType=INTEGER}, now(),
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.tradeId,jdbcType=BIGINT}, #{item.tradeName,jdbcType=VARCHAR},
      #{item.originId,jdbcType=BIGINT}, #{item.itemDivisionSubjectId,jdbcType=VARCHAR}, #{item.itemDivisionSubjectName,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <select id="selectSelfByStandardId" resultMap="BaseResultMap" useCache="false" flushCache="true">
    select <include refid="Base_Column_List" /> from zb_standards_build_standard_detail_self
    where standard_id = #{standardId,jdbcType=BIGINT}
    order by ord asc
  </select>
  <insert id="publishInsert" parameterType="com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail">
    insert into zb_standards_build_standard_detail (id, standard_id, level_code,
    name, `desc`, level,
    remark, ord, create_time,
    update_time, trade_id, trade_name, item_division_subject_id, item_division_subject_name)
    values
    <foreach collection="details" item="item" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.standardId,jdbcType=BIGINT}, #{item.levelcode,jdbcType=VARCHAR},
      #{item.name,jdbcType=VARCHAR}, #{item.desc,jdbcType=VARCHAR}, #{item.level,jdbcType=INTEGER},
      #{item.remark,jdbcType=BIGINT}, #{item.ord,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.tradeId,jdbcType=BIGINT}, #{item.tradeName,jdbcType=VARCHAR},
      #{item.itemDivisionSubjectId,jdbcType=VARCHAR}, #{item.itemDivisionSubjectName,jdbcType=LONGVARCHAR})
    </foreach>
  </insert>
  <delete id="deleteSelfByStandardIds">
    delete from zb_standards_build_standard_detail_self where standard_id in
    <foreach collection="ids" item="item" separator="," close=")" open="(">
      #{item}
    </foreach>
  </delete>
  <update id="updateSelfStandardDetailColData">
    update zb_standards_build_standard_detail_self
    set
    `name` = #{detail.name,jdbcType=VARCHAR},
    `desc` = #{detail.desc,jdbcType=VARCHAR},
    remark = #{detail.remark,jdbcType=VARCHAR},
    trade_id = #{detail.tradeId,jdbcType=BIGINT},
    trade_name = #{detail.tradeName,jdbcType=VARCHAR},
    update_time = now(),
    item_division_subject_id = #{detail.itemDivisionSubjectId,jdbcType=VARCHAR},
    item_division_subject_name = #{detail.itemDivisionSubjectName,jdbcType=LONGVARCHAR}
    where
    standard_id = #{standardId,jdbcType=BIGINT}
    and
    id = #{detail.id,jdbcType=BIGINT}
  </update>

  <update id="batchUpdateOrd" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_standards_build_standard_detail_self set ord = #{item.ord, jdbcType=INTEGER}
      where id = #{item.id, jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="batchSubjectDivisionName" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_standards_build_standard_detail_self
      set
          item_division_subject_id = #{item.itemDivisionSubjectId,jdbcType=VARCHAR},
          item_division_subject_name = #{item.itemDivisionSubjectName,jdbcType=LONGVARCHAR}
      where id = #{item.id, jdbcType=BIGINT}
    </foreach>
  </update>

  <select id="selectSubjectDivision" resultMap="BaseResultMap">
    select <include refid="Base_Column_List" /> from zb_standards_build_standard_detail_self
    where standard_id = #{standardId,jdbcType=BIGINT}
    and item_division_subject_id = #{itemDivisionSubjectId,jdbcType=VARCHAR}
    and id != #{id,jdbcType=BIGINT}
  </select>
</mapper>
