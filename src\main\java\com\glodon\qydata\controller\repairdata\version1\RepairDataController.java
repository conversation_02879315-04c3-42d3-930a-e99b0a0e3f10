package com.glodon.qydata.controller.repairdata.version1;

import com.glodon.qydata.controller.repairdata.common.BaseRepairDataHandler;
import com.glodon.qydata.controller.temp.expression.TempExpressionDataDealHandler;
import com.glodon.qydata.vo.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> @description: 修复错误数据
 * @date 2023/4/21 14:14
 */
@RestController
@RequestMapping("/basicInfo/repair/expression")
@Slf4j
public class RepairDataController {

    @Autowired
    private BaseRepairDataHandler repairProjectInfoHandler;
    @Autowired
    private TempExpressionDataDealHandler tempExpressionDataDealHandler;
    @Autowired
    private RepairExpressionDataManagerImpl repairExpressionDataManagerImpl;

    /**
     * 修复系统默认数据
     * @return
     */
    @GetMapping("/system")
    public ResponseVo<Boolean> repairSystemExpression(){
        log.info("开始修复系统默认数据");
        repairExpressionDataManagerImpl.repairSystemData();
        log.info("结束修复系统默认数据");
        return ResponseVo.success();
    }

    @GetMapping("")
    public ResponseVo<Boolean> repairExpression(){
        log.info("开始修复");
        List<String> customerCodeList = repairExpressionDataManagerImpl.selectErroDataCustomerCode();
        customerCodeList.forEach(customerCode -> {
            try {
                repairOneExpression(customerCode);
            } catch (Exception e) {
                e.printStackTrace();
                log.info("修复发生异常，企业编码:{}", customerCode);
            }
        });
        log.info("修复完成");
        return ResponseVo.success();
    }
    @GetMapping("/one")
    public ResponseVo<Boolean> repairExpressionByCustomerCode(@RequestParam(value = "customerCode") String customerCode){
        log.info("开始修复{}", customerCode);
        repairOneExpression(customerCode);
        log.info("修复完成{}", customerCode);
        return ResponseVo.success();
    }
    private void repairOneExpression(String customerCode) {
        boolean isRepairSuccess = false;
        try {
            isRepairSuccess = repairExpressionDataManagerImpl.repair(customerCode);
        } catch (Exception e) {
            e.printStackTrace();
        }
        repairExpressionDataManagerImpl.logRepairStatus(customerCode, isRepairSuccess);
    }
    @GetMapping("/logErro")
    public ResponseVo<Boolean> checkExpression(){
        repairExpressionDataManagerImpl.logErroDataCustomerCode();
        return ResponseVo.success();
    }
}
