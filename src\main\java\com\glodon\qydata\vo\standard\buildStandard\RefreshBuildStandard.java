package com.glodon.qydata.vo.standard.buildStandard;

import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import lombok.Data;

import java.util.List;

@Data
public class RefreshBuildStandard {
    private String name;
    private Long standardId;
    private String categoryCode;
    private String categoryName;
    private ZbProjectStandard projectStandard;
    private List<ZbProjectStandardDetail> detailList;
    private List<ZbStandardsBuildStandardDetailDesc> descList;
}
