package com.glodon.qydata.service.init.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.CategoryTypeConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.TableNameConstants;
import com.glodon.qydata.config.idsequence.IDGenerator;
import com.glodon.qydata.entity.repairdata.RepairLog;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard;
import com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandardDetail;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.repairdata.TempCommonRepairMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardDetailMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbProjectStandardMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildStandardDetailDescMapper;
import com.glodon.qydata.mapper.standard.category.CommonProjCategoryMapper;
import com.glodon.qydata.mapper.standard.category.CommonProjCategorySelfMapper;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionMapper;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.excel.ExcelParseUtil;
import com.glodon.qydata.service.init.ImportBuiltInDataService;
import com.glodon.qydata.service.standard.buildStandard.ZbStandardsBuildStandardDetailDescService;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.util.DataDealUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.init.BuildStandardExcelVo;
import com.glodon.qydata.vo.init.CommonCategoryExcelVo;
import com.glodon.qydata.vo.init.ExpressionExcelVo;
import com.glodon.qydata.vo.standard.buildStandard.RefreshBuildStandard;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.CategoryTypeConstants.TYPE_DEFAULT;
import static com.glodon.qydata.common.constant.Constants.CategoryConstants.*;
import static com.glodon.qydata.controller.repairdata.common.RepairVersionConst.repair_version_4;

/**
 * @author: weijf
 * @date: 2022-10-10
 */
@Slf4j
@Service
public class ImportBuiltInDataServiceImpl implements ImportBuiltInDataService {

    @Autowired
    private ZbProjectStandardMapper projectStandardMapper;

    @Autowired
    private ZbProjectStandardDetailMapper projectStandardDetailMapper;
    @Autowired
    private ZbStandardsExpressionMapper standardsExpressionMapper;
    @Autowired
    private ZbStandardsExpressionSelfMapper standardsExpressionSelfMapper;
    @Autowired
    private ZbStandardsBuildStandardDetailDescService standardsBuildStandardDetailDescService;
    @Autowired
    private IProjectFeatureService projectFeatureService;
    @Autowired
    private ZbStandardsTradeMapper standardsTradeMapper;
    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;
    @Autowired
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;
    @Autowired
    private ProjectFeatureCategoryViewMapper projectFeatureCategoryViewMapper;
    @Autowired
    private ProjectFeatureCategoryViewSelfMapper projectFeatureCategoryViewSelfMapper;
    @Autowired
    private CommonProjCategoryMapper commonProjCategoryMapper;
    @Autowired
    private CommonProjCategorySelfMapper commonProjCategorySelfMapper;
    @Autowired
    private ZbStandardsBuildStandardDetailDescMapper zbStandardsBuildStandardDetailDescMapper;
    @Autowired
    private CommonProjCategoryService commonProjCategoryService;
    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;
    @Autowired
    private TempCommonRepairMapper tempCommonRepairMapper;

   String defaultCustomerCode = Constants.CategoryConstants.SYSTEM_CUSTOMER_CODE;
    Long defaultGlobalId = Long.parseLong(SYSTEM_CUSTOMER_CODE);
    final ThreadPoolExecutor syncExectutor = new ThreadPoolExecutor(5, 10, 3000, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());

    //-------------------- 内置建造标准 begin ----------------

    /**
     * 导入内置建造标准
     * @param excelDataList
     * @param categoryCode 一级工程分类编码，参考工程分类
     * @param buildStandardName 待导入的建造标准名称，若没有，则新建，若有，则覆盖原有内置数据
     * @param defaultBuildStandardId 新建时，默认建造标准ID，若不传，则随机
     * <AUTHOR>
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseVo importBuildStandardData(List<BuildStandardExcelVo> excelDataList,String categoryCode, String categoryName, String buildStandardName,Long defaultBuildStandardId) {
        try{

            if(CollectionUtils.isEmpty(excelDataList)){
                return ResponseVo.error("待导入Excel数据为空");
            }

            // 2、查询建造标准名称是否存在 ，若存在，则把原数据（建造标准细则表、标准说明表）删除；若不存在，则新建
            ZbProjectStandard standard = initBuiltInProjectStandard(buildStandardName,categoryCode,categoryName,defaultBuildStandardId);

            // 3、处理并存储 建造标准细则表、标准说明表
            handleBuiltInProjectStandardDetailData(standard,excelDataList);

            return ResponseVo.success();
        }catch (Exception e){
            e.printStackTrace();
            log.error("导入内置建造标准失败，e={}",e.getMessage());
            return ResponseVo.error("导入内置建造标准失败");
        }

    }

    /**
     * 初始化内置建造标准数据
     * @param buildStandardName
     * @return
     * <AUTHOR>
     */
    private ZbProjectStandard initBuiltInProjectStandard(String buildStandardName,String categoryCode,String categoryName,Long defaultBuildStandardId) {

        List<ZbProjectStandard> standardList = projectStandardMapper.selectByCustomerCode(defaultCustomerCode, 0);

        ZbProjectStandard standard = null;

        if(CollectionUtils.isNotEmpty(standardList)){
            for (ZbProjectStandard s : standardList) {
                String name = s.getName();
                if(name.equals(buildStandardName)){ //如果存在，则跳过
                    standard = s;
                    break;
                }
            }
        }

        if(standard != null){
            // 删除旧数据
            List<Long> standardIds = new ArrayList<>();
            standardIds.add(standard.getId());
            projectStandardDetailMapper.deleteByStandardIds(standardIds);
            standardsBuildStandardDetailDescService.delByStandardIds(standardIds);
        }else{
            // 创建一条新的建造标准数据
            standard = new ZbProjectStandard();
            if(defaultBuildStandardId != null){
                standard.setId(defaultBuildStandardId);
            }else {
                standard.setId(SnowflakeIdUtils.getNextId());
            }
            standard.setCategoryCode(categoryCode);
            standard.setCategoryName(categoryName);
            standard.setName(buildStandardName);
            standard.setCustomerCode(defaultCustomerCode);
            standard.setIsExample(Constants.ZbExpressionConstants.WHETHER_TRUE);
            standard.setIsDeleted(false);
            standard.setCategoryDeleted(false);
            standard.setIsUsed(false);
            standard.setIsUsing(Constants.ZbExpressionConstants.WHETHER_TRUE);
            standard.setVersion(Constants.ZbExpressionConstants.WHETHER_FALSE);

            projectStandardMapper.insertSelective(standard);
        }

        return standard;
    }


    /**
     * 处理Excel数据并存储
     * @param standard
     * @param excelDataList
     * <AUTHOR>
     */
    private void handleBuiltInProjectStandardDetailData(ZbProjectStandard standard, List<BuildStandardExcelVo> excelDataList) throws BusinessException {
        String parentLevelCode = null; //记录上一次的科目编码，用于合并单元格获取不到下一级数据的情况
        Long parentDetailId = null;  //上一次的细则ID
        String parentName = null;  //上一次的名称
        String parentTradeName = null;  //上一次的专业名称
        String parentSubjectName = null;  //上一次的项目划分科目名称
        int detailOrd = 1;
        int descIndex = 1;
        int descCode = 0;
        int descOrd = 0;
        Date now = new Date();

        List<ZbProjectStandardDetail>  detailList = new ArrayList<>(); //待保存的 建造标准细则 数据
        List<ZbStandardsBuildStandardDetailDesc>  detailDescList = new ArrayList<>(); //待保存的 标准说明表 数据

        for (BuildStandardExcelVo excelVo : excelDataList) {
            String levelCode = excelVo.getLevelCode();
            String name = excelVo.getName();
            String tradeName = excelVo.getTradeName();
            String subjectName = excelVo.getSubjectName();
            Boolean isNewDetail = true; // 新数据则保存，合并单元格的为旧数据，第二条以下不保存

            if(StringUtils.isBlank(name)){
                if(parentName == null){
                    throw new BusinessException("科目名称 不能为空");
                }else{
                    name = parentName;
                    isNewDetail = false;
                    tradeName = parentTradeName;
                    subjectName = parentSubjectName;
                }
            }
            if(StringUtils.isBlank(levelCode)){
                if(parentLevelCode == null){
                    throw new BusinessException("科目编码 不能为空");
                }else{
                    levelCode = parentLevelCode;
                }
            }else{
                levelCode = convertToStandardLevelCode(levelCode);
            }

            if(Boolean.TRUE.equals(isNewDetail)) { // 处理 建造标准细则
                ZbProjectStandardDetail detail = new ZbProjectStandardDetail();
                Long detailId = SnowflakeIdUtils.getNextId();

                detail.setId(detailId);
                detail.setStandardId(standard.getId());
                detail.setLevelcode(levelCode);
                detail.setName(name);
                detail.setTradeName(tradeName);
                detail.setItemDivisionSubjectName(subjectName);
                detail.setLevel(levelCode.length()/3);
                detail.setOrd(detailOrd);
                detail.setCreateTime(now);
                detail.setUpdateTime(now);

                detailList.add(detail); //添加进待保存列表

                detailOrd ++;
                parentLevelCode = levelCode;
                parentDetailId = detailId;
                parentName = name;
                parentTradeName = tradeName;
                parentSubjectName = subjectName;
                descCode = 0;
                descOrd = 0;
                descIndex = 1;
            }
            //处理标准说明
            String detailDesc = excelVo.getDetailDesc();
            String typeCode = excelVo.getTypeCode();
            String selectList = excelVo.getSelectList();

            if(StringUtils.isEmpty(detailDesc) || "/".equals(detailDesc)) continue;
            if("/".equals(typeCode)) typeCode = null;

            ZbStandardsBuildStandardDetailDesc desc = new ZbStandardsBuildStandardDetailDesc();
            desc.setId(SnowflakeIdUtils.getNextId());
            desc.setName(Constants.DESC_PREFIX+descIndex);
            desc.setDetailDesc(detailDesc);
            desc.setStandardId(standard.getId());
            desc.setStandardDetailId(parentDetailId);
            descCode ++;
            descOrd ++;
            descIndex++;
            desc.setCode(setNewCategoryCode(descCode));
            desc.setOrd(descOrd);
            desc.setRemark(levelCode);
            desc.setCreateTime(now);
            desc.setUpdateTime(now);
            desc.setTypeCode(ExcelParseUtil.getTypeCode(typeCode));
            String newSelectList = convertToSelectListJson(selectList);
            desc.setSelectList(newSelectList);

            detailDescList.add(desc);
        }

        //批量新增
        projectStandardDetailMapper.saveBatch(detailList);
        standardsBuildStandardDetailDescService.saveBatch(detailDescList);

    }

    /**
     * 转换成标准LevelCode，如 1.1.11.2 转成 001001011002
     * @param levelCode
     * @return
     * <AUTHOR>
     */
    private String convertToStandardLevelCode(String levelCode) throws BusinessException{
        String[] codes = levelCode.split("\\.");
        StringBuffer sb = new StringBuffer();

        for (String code : codes) {
            try{
                String newCategoryCode = setNewCategoryCode(Integer.parseInt(code));
                sb.append(newCategoryCode);
            }catch (Exception e){
                throw new BusinessException("科目编码 格式错误，只能是1.1.1或者001001之类");
            }
        }
        return sb.toString();
    }
    /**
     * 设置新的编码
     * @param categoryCode
     * @return
     */
    private String setNewCategoryCode(int categoryCode) {
        if(categoryCode<10){
            return "00"+categoryCode;
        }else if(categoryCode<100){
            return "0"+categoryCode;
        }else{
            return ""+categoryCode;
        }
    }

    /**
     * 由【灰土换填|混凝土换填】 转成
     * [{"name":"灰土换填","isDeleted":0},{"name":"混凝土换填","isDeleted":0}]
     *  形式
     * @param selectList
     * @return
     */
    private String convertToSelectListJson(String selectList) {
        if(StringUtils.isEmpty(selectList) || "/".equals(selectList)) return null;
        String[] selects = selectList.split("\\|");

        List<Map> result = new ArrayList<>();

        for (String select : selects) {
            Map map = new LinkedHashMap();
            map.put("name",select);
            map.put("isDeleted",0);

            result.add(map);
        }

        return JSONObject.toJSONString(result);
    }

    //-------------------- 内置建造标准 end ----------------

    //-------------------- 内置计算口径 begin ----------------

    /**
     * 导入内置计算口径
     * @param excelDataList
     * @param standardTradeName 【产品设计指标】的数据，同步到某个工程特征下，若名称为空，则不同步
     * <AUTHOR>
     * @return
     */
    @Override
    public ResponseVo importExpressionData(List<ExpressionExcelVo> excelDataList,String standardTradeName) {
        try{

            // 2、对比及存储内置计算口径
            //查询计算口径时，需要查询所有数值类型，若有重复并且不是计算口径，则需要调整为计算口径
            List<ZbStandardsExpression> expressionList = standardsExpressionMapper.selectListByCustomCode(defaultCustomerCode,null);
            Map<Integer,List<ZbStandardsExpression>> designIndexExpressionMap = compareAndSaveBuiltInExpression(expressionList,excelDataList);

            //3、初始化内置工程特征
            if(StringUtils.isBlank(standardTradeName) || designIndexExpressionMap.isEmpty()) return ResponseVo.success();
            //查询专业
            ZbStandardsTrade trade = standardsTradeMapper.searchRecordsByCusCodeAndName(standardTradeName,defaultCustomerCode);
            if(trade == null) return ResponseVo.success();
            //初始化
            initBuiltInFeature(trade,designIndexExpressionMap);

            return ResponseVo.success();
        }catch (Exception e){
            e.printStackTrace();
            log.error("导入内置计算口径失败，e={}",e.getMessage());
            return ResponseVo.error("导入内置计算口径失败");
        }
    }


    /**
     * 与内置计算口径比较，并存储新的数据
     * @param expressionList
     * @param excelDataList
     * <AUTHOR>
     * @return 需要初始化到工程特征中的数据
     */
    private Map<Integer,List<ZbStandardsExpression>> compareAndSaveBuiltInExpression(List<ZbStandardsExpression> expressionList, List<ExpressionExcelVo> excelDataList) {
        Map<Integer,List<ZbStandardsExpression>> expressionMap = expressionList.stream()
                .filter(e->Constants.ZbExpressionConstants.TYPE_NUMBER.equals(e.getTypeCode())).collect(Collectors.groupingBy(ZbStandardsExpression::getType));

        Date now = new Date();
        int maxOrder = expressionList.stream().filter(e->Constants.ZbFeatureConstants.WHETHER_TRUE == e.getIsExpression()
                        && Constants.ZbFeatureConstants.WHETHER_TRUE == e.getExpressionIsFromSystem() && e.getExpressionOrd()!=null)
                .map(ZbStandardsExpression::getExpressionOrd).distinct().max(Integer::compareTo).orElse(0);

        List<ZbStandardsExpression> needUpdateExpressionList = new ArrayList<>(); //若数据已存在，并且不是计算口径的，需要改为计算口径
        List<ZbStandardsExpression> newExpressionList = new ArrayList<>(); // 待新增的数据
        Map<String,String> expressionCodeMap = new HashMap<>(); //三套对应的expressionCode 均一样,key：名称，value：expressionCode
        Map<Integer,List<ZbStandardsExpression>> designIndexExpressionMap = new HashMap<>(); // 待返回的，需要初始化工程特征的数据

        //内置的需要存储3套
        CategoryTypeConstants.categoryTypeList.forEach(type -> {
            List<ZbStandardsExpression> typeExpressionList = expressionMap.get(type);
            Map<String,ZbStandardsExpression> expressionNameMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(typeExpressionList)){
                expressionNameMap = typeExpressionList.stream().collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity()));
            }
            int currentMaxOrder = maxOrder +1;
            for (ExpressionExcelVo excelVo : excelDataList) {
                String name = excelVo.getName();
                String unit = excelVo.getUnit();
                String dataType = excelVo.getDataType();

                if(StringUtils.isBlank(name)) continue;

                String nameAndUnit;
                if(StringUtils.isNotBlank(unit)){
                    if("㎡".equals(unit)){
                        unit = "m2";
                    }
                    nameAndUnit = name+"("+unit+")";
                }else{
                    nameAndUnit = name;
                }

                Long id = SnowflakeIdUtils.getNextId();

                String expressionCode = expressionCodeMap.get(nameAndUnit);
                if(StringUtils.isBlank(expressionCode)){
                    expressionCode = "K_"+id;
                    expressionCodeMap.put(nameAndUnit,expressionCode);
                }


                Boolean needUpdate = false;
                if(expressionNameMap.containsKey(nameAndUnit)) { //若库中已存在
                    ZbStandardsExpression expression = expressionNameMap.get(nameAndUnit);
                    //若有重复并且不是计算口径，则需要调整为计算口径
                    if(Constants.ZbExpressionConstants.WHETHER_FALSE != expression.getIsExpression()){
                        continue;
                    }
                    needUpdate = true;
                    id = expression.getId();
                    expressionCode = expression.getExpressionCode();
                    expressionCodeMap.put(nameAndUnit,expressionCode);
                }

                ZbStandardsExpression expression = new ZbStandardsExpression();
                expression.setId(id);
                expression.setExpressionCode(expressionCode);
                expression.setQyCode(defaultCustomerCode);
                expression.setCreateGlobalId(defaultGlobalId);
                expression.setExpressionCreateGlobalId(defaultGlobalId);
                expression.setIsUsable(Constants.ZbExpressionConstants.WHETHER_FALSE);
                expression.setIsDeleted(Constants.ZbExpressionConstants.WHETHER_FALSE);
                expression.setName(nameAndUnit);
                expression.setUnit(unit);
                expression.setTypeCode(Constants.ZbExpressionConstants.TYPE_NUMBER);
                expression.setType(type);
                expression.setCreateTime(now);
                expression.setIsExpression(Constants.ZbExpressionConstants.WHETHER_TRUE);
                expression.setExpressionIsUsing(Constants.ZbExpressionConstants.WHETHER_TRUE);
                expression.setExpressionIsFromSystem(Constants.ZbExpressionConstants.WHETHER_TRUE);
                expression.setExpressionRemark("只读");
                expression.setExpressionOrd(currentMaxOrder);
                currentMaxOrder ++;

                if(needUpdate){
                    needUpdateExpressionList.add(expression);
                }else {
                    newExpressionList.add(expression);
                }

                // 产品设计指标 同时添加进专业工程【精装修工程】中，默认启用，默认是计算口径，是否必填不勾选，备注为空。
                if(!"产品设计指标".equals(dataType)){
                    continue;
                }

                List<ZbStandardsExpression> typeList = designIndexExpressionMap.get(type);
                if(typeList == null){
                    typeList = new ArrayList<>();
                }

                typeList.add(expression);

                designIndexExpressionMap.put(type,typeList);
            }
        });

        //批量保存
        if(CollectionUtils.isNotEmpty(newExpressionList)){
              standardsExpressionMapper.batchInsert(newExpressionList);
        }
        if(CollectionUtils.isNotEmpty(needUpdateExpressionList)){
             standardsExpressionMapper.batchUpdateSelective(newExpressionList);
        }

        return designIndexExpressionMap;
    }

    //-------------------- 内置计算口径 end ----------------

    //-------------------- 内置及刷新工程分类 begin --------

    /**
     * 初始化内置的工程分类数据
     * @param trade
     * @param designIndexExpressionMap
     */
    private void initBuiltInFeature(ZbStandardsTrade trade, Map<Integer, List<ZbStandardsExpression>> designIndexExpressionMap) {

        // 查询用户的类型
        List<Map> typeOrders = projectFeatureMapper.selectUserTypesAndOrderByCustomerCode(defaultCustomerCode);
        List<ProjectFeature> featureList = new ArrayList<>();

        for (Map typeOrder : typeOrders) {
            Integer type = (Integer) typeOrder.get("type");
            int order = typeOrder.get(BusinessConstants.ORDER_TRADE) == null ? 1 : (Integer) typeOrder.get(BusinessConstants.ORDER_TRADE) + 1; //从最大的排

            List<ZbStandardsExpression> expressionList = designIndexExpressionMap.get(type);

            if(CollectionUtils.isEmpty(expressionList)) continue;

            String projectType = projectFeatureService.categoryAllCode(defaultCustomerCode,type,null);

            for (ZbStandardsExpression expression : expressionList) {
                ProjectFeature feature = new ProjectFeature();
                feature.setId(SnowflakeIdUtils.getNextId());
                feature.setTradeId(trade.getId());
                feature.setExpressionId(expression.getId());
                feature.setCustomerCode(defaultCustomerCode);
                feature.setCreateGlobalId(defaultGlobalId);
                feature.setIsSync(Constants.ZbFeatureConstants.WHETHER_FALSE);
                feature.setType(type);
                feature.setProjectType(projectType);
                feature.setIsUsing(Constants.ZbFeatureConstants.WHETHER_TRUE);
                feature.setIsDefault(Constants.ZbFeatureConstants.WHETHER_TRUE);
                feature.setIsRequired(Constants.ZbFeatureConstants.WHETHER_TRUE);
                feature.setIsFromSystem(Constants.ZbFeatureConstants.WHETHER_TRUE);
                feature.setIsExpression(Constants.ZbFeatureConstants.WHETHER_TRUE);
                feature.setIsDeleted(Constants.ZbFeatureConstants.WHETHER_FALSE);
                feature.setIsSearchCondition(Constants.ZbFeatureConstants.WHETHER_FALSE);
                feature.setCreateTime(expression.getCreateTime());
                feature.setOrdTrade(order);
                order ++;

                featureList.add(feature);
            }
        }

        // 保存
        if(!featureList.isEmpty()){
             projectFeatureMapper.batchInsert(featureList);
        }

    }

    /**
     * 处理某个工程特征的数据，把某专业下的数据与内置计算口径的数据刷新成一致的
     * @param standardTradeName
     * @return
     */
    @Override
    public ResponseVo refreshUserFeatureData(List<ExpressionExcelVo> excelDataList,String standardTradeName,String defaultInitCustomerCode) {
        try {
            LocalDateTime start = LocalDateTime.now();

            List<String> expressionNames = convertToFeatureNames(excelDataList);
            if (CollectionUtils.isEmpty(expressionNames)) {
                return ResponseVo.error("待导入Excel数据为空");
            }

            LocalDateTime end1 = LocalDateTime.now();
            log.info("解析Excel成功，耗时={}ms", Duration.between(start, end1).toMillis());
            // 2、初始化已发布数据的工程特征
            //查询所有的企业，排队默认企业
            List<String> customerCodes;
            if(StringUtils.isNotBlank(defaultInitCustomerCode)){
                customerCodes = new ArrayList<>();
                customerCodes.add(defaultInitCustomerCode);
            }else{
                customerCodes = projectFeatureMapper.selectAllCustomerCode();
            }
            customerCodes.remove(defaultCustomerCode);
            customerCodes.remove(BusinessConstants.NULL_STR);
            customerCodes.remove(BusinessConstants.UNDEFINED);

            int limit = customerCodes.size() <= 5 ? 1 : customerCodes.size() / 5;
            List<List<String>> threadCustomerCodes = DataDealUtil.averageAssign(customerCodes, limit);
            final CountDownLatch latch = new CountDownLatch(threadCustomerCodes.size());

            LocalDateTime end2 = LocalDateTime.now();
            log.info("企业查询成功，共查询{}个企业，耗时={}ms", customerCodes.size() - 1, Duration.between(end1, end2).toMillis());
            for (int i = 0; i < threadCustomerCodes.size(); i++) {
                List<String> threadCustomerCode = threadCustomerCodes.get(i);
                syncExectutor.execute(() -> {
                    int allNewFeatureCount = 0;
                    int allNewSelfFeatureCount = 0;
                    LocalDateTime end3 = LocalDateTime.now();
                    for (int j = 0; j < threadCustomerCode.size(); j++) {
                        String customerCode = threadCustomerCode.get(j);
                        Map<String, Integer> result = handleFeatureData(j, customerCode, standardTradeName, expressionNames);
                        allNewFeatureCount += result.get("allNewFeatureCount");
                        allNewSelfFeatureCount += result.get("allNewSelfFeatureCount");

                    }
                    LocalDateTime end4 = LocalDateTime.now();
                    String desc = String.format("企业处理结束，共 %d 个企业，耗时：%d ms，已发布工程特征数量：%d用户暂存工程特征数量：%d",
                            threadCustomerCode.size(), Duration.between(end3, end4).toMillis(), allNewFeatureCount, allNewSelfFeatureCount);
                    log.info(desc);
                    latch.countDown();
                });
            }

            latch.await();

            LocalDateTime end5 = LocalDateTime.now();
            String desc = String.format("所有企业处理结束，共 %d 个企业，总耗时：%d ms", customerCodes.size(), Duration.between(start, end5).toMillis());
            log.info(desc);
            return ResponseVo.success(desc);
        }catch (Exception e){
            e.printStackTrace();
            return ResponseVo.error();
        }
    }

    /**
     * 处理工程特征数据，包括已发布+暂存+分类视图数据
     * @param index
     * @param customerCode
     * @param standardTradeName
     * @param expressionNames
     * @return
     */
    private Map<String,Integer> handleFeatureData(int index,String customerCode,String standardTradeName,List<String> expressionNames) {

        int allNewFeatureCount = 0;
        int allNewSelfFeatureCount = 0;
        Map<String, Integer> result = new HashMap();
        try {
            LocalDateTime begin = LocalDateTime.now();

            index++;
            log.info("正在初始化第{}个企业工程特征数据中...，customerCode={}", index, customerCode);
            //查询专业
            ZbStandardsTrade trade = standardsTradeMapper.searchRecordsByCusCodeAndName(standardTradeName, customerCode);
            if (trade == null) {
                result.put("allNewFeatureCount", 0);
                result.put("allNewSelfFeatureCount", 0);
                return result;
            }

            //查询该企业下暂存的用户数据
            List<Long> allGlobalIds = projectFeatureSelfMapper.selectAllGlobalIds(customerCode);
            if (CollectionUtils.isNotEmpty(allGlobalIds)) {
                allGlobalIds.remove(defaultGlobalId);
            }
            LocalDateTime end3 = LocalDateTime.now();
            log.info("查询企业 {} 中的用户数量为：{}，耗时={}ms", index, customerCode, allGlobalIds.size(), Duration.between(begin, end3).toMillis());
            List<ProjectFeature> allNewFeatureList = new ArrayList<>(); //已发布工程特征
            List<ProjectFeatureCategoryView> allNewFeatureViewList = new ArrayList<>(); //已发布分类视图
            List<ProjectFeature> allNewSelfFeatureList = new ArrayList<>(); //用户暂存工程特征
            List<ProjectFeatureCategoryView> allNewSelfFeatureViewList = new ArrayList<>(); //用户暂存分类视图

            for (Integer type : CategoryTypeConstants.categoryTypeList) { //三套工程分类
                //查询该企业的计算口径
                List<ZbStandardsExpression> expressionList = standardsExpressionMapper.selectExpression(customerCode, type);
                if (CollectionUtils.isEmpty(expressionList)) continue; //若该分类下无计算口径，直接不处理
                //查询该专业下的工程特征
                List<ProjectFeature> featureList = projectFeatureMapper.selectByTradeIdAndType(trade.getId(), type);
                if (CollectionUtils.isEmpty(featureList)) continue;
                LocalDateTime end4 = LocalDateTime.now();
                log.info("查询第{}个企业 {} 中的计算口径、工程特征数据结束，耗时={}ms", index, customerCode, Duration.between(end3, end4).toMillis());
                String projectType = projectFeatureService.categoryAllCode(customerCode, type, null);
                LocalDateTime end5 = LocalDateTime.now();
                log.info("查询第{}个企业 {} 中的projectType数据结束，耗时={}ms", index, customerCode, Duration.between(end4, end5).toMillis());
                //1、初始化已发布工程特征，返回 newFeatureList、expressionNameIds
                Map<String, Long> expressionNameIds = new HashMap<>();
                List<ProjectFeature> newFeatureList = compareAndRefreshUserFeature(customerCode, expressionList, featureList, expressionNames, trade.getId(), type, projectType, expressionNameIds, null, null);
                LocalDateTime end6 = LocalDateTime.now();
                log.info("对比并初始化第{}个企业 {} 已发布的 工程特征数据结束，耗时={}ms", index, customerCode, Duration.between(end5, end6).toMillis());
                if (CollectionUtils.isNotEmpty(newFeatureList)) {
                    allNewFeatureList.addAll(newFeatureList);

                    //2、初始化用户的暂存工程特征，需要用到 expressionNameIds，返回 selfGlobalFeatureIdMap
                    Map<Long, Map<Long, Long>> selfGlobalFeatureIdMap = new HashMap<>(); //存储每个用户，所有 已发布的工程特征ID对应的暂存的工程特征ID，用于暂存视图表
                    if (CollectionUtils.isNotEmpty(allGlobalIds)) {
                        for (Long globalId : allGlobalIds) {
                            if (globalId == null) continue;
                            List<ProjectFeature> selfFeatureList = projectFeatureSelfMapper.selectBySelfTradeId(customerCode, trade.getId(), type);
                            List<ZbStandardsExpression> selfExpressionList = standardsExpressionSelfMapper.selectSelfExpression(customerCode, globalId, type);
                            List<ProjectFeature> newSelfFeatureList = compareAndRefreshUserFeature(customerCode, selfExpressionList, selfFeatureList, expressionNames, trade.getId(), type, projectType, expressionNameIds, globalId, selfGlobalFeatureIdMap);
                            if (CollectionUtils.isNotEmpty(newSelfFeatureList)) {
                                allNewSelfFeatureList.addAll(newSelfFeatureList);
                            }
                        }
                    }
                    LocalDateTime end7 = LocalDateTime.now();
                    log.info("初始化第{}个企业下{} 所有用户暂存数据的工程特征数据结束，耗时={}ms", index, customerCode, Duration.between(end6, end7).toMillis());

                }
            }
            LocalDateTime end10 = LocalDateTime.now();
            log.info("第{}个企业{}处理结束，共耗时={}ms，已发布工程特征数量：{}，已发布分类视图数量：{}，用户暂存工程特征数量：{}，用户暂存分类视图数量：{}",
                    index, customerCode, Duration.between(begin, end10).toMillis(), allNewFeatureList.size(), allNewFeatureViewList.size(), allNewSelfFeatureList.size(), allNewSelfFeatureViewList.size());


            // 初始化用户已发布工程特征数据
            if (CollectionUtils.isNotEmpty(allNewFeatureList)) {
                projectFeatureMapper.batchInsert(allNewFeatureList);
                allNewFeatureCount += allNewFeatureList.size();
            }

            //初始化用户暂存数据
            if (CollectionUtils.isNotEmpty(allNewSelfFeatureList)) {
                projectFeatureSelfMapper.insertSelfBatch(allNewSelfFeatureList);
                allNewSelfFeatureCount += allNewSelfFeatureList.size();
            }

            LocalDateTime end11 = LocalDateTime.now();
            log.info("第{}个企业{}处理保存结束，耗时={}ms", index, customerCode, Duration.between(end10, end11).toMillis());

        }catch (Exception e){
            e.printStackTrace();
        }
        result.put("allNewFeatureCount", allNewFeatureCount);
        result.put("allNewSelfFeatureCount", allNewSelfFeatureCount);
        return result;
    }

    /**
     * 转换成待导入的工程特征的名称，分类为产品设计指标
     * @param excelDataList
     * @return
     */
    private List<String> convertToFeatureNames(List<ExpressionExcelVo> excelDataList) {
        List<String> names = new ArrayList<>();
        if(CollectionUtils.isEmpty(excelDataList)) return names;

        for (ExpressionExcelVo excelVo : excelDataList) {
            if(!"产品设计指标".equals(excelVo.getDataType())) continue;
            String unit = excelVo.getUnit();
            String name = excelVo.getName();
            String nameAndUnit;
            if(StringUtils.isNotBlank(unit)){
                nameAndUnit = name+"("+unit+")";
            }else{
                nameAndUnit = name;
            }
            names.add(nameAndUnit);
        }

        return names;
    }

    /**
     * 与导入的expressionNames 名称做对比，找到对应 的计算口径编码，并初始化到计算口径列表中
     * @param customerCode
     * @param expressionList
     * @param featureList
     * @param expressionNames
     * @param tradeId
     * @param type
     * @param projectType
     */
    private List<ProjectFeature> compareAndRefreshUserFeature(String customerCode, List<ZbStandardsExpression> expressionList, List<ProjectFeature> featureList,List<String> expressionNames,
                                                              Long tradeId, Integer type, String projectType,Map<String,Long> expressionNameIds,Long globalId,Map<Long,Map<Long,Long>> selfGlobalFeatureIdMap) {
        Map<String,ZbStandardsExpression> expressionMap = expressionList.parallelStream().collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(),(e1, e2) -> e1));
        List<Long> expressionIds = featureList.stream().map(f -> f.getExpressionId()).collect(Collectors.toList());
        int maxOrder = featureList.stream().filter(f -> f.getOrdTrade()!=null).map(ProjectFeature::getOrdTrade).distinct().max(Integer::compareTo).orElse(0)+1;
        Date now = new Date();

        List<ProjectFeature> retList = new ArrayList<>();
        Map<Long,Long> selfFeatureIdMap = new HashMap<>();

        for (String expressionName : expressionNames) {
            ZbStandardsExpression expression = expressionMap.get(expressionName);
            if(expression == null)continue;
            if(expressionIds.contains(expression.getId())) continue; //待导入的数据，在现有的工程分类中已存在，则不处理

            ProjectFeature feature = new ProjectFeature();
            Long id = SnowflakeIdUtils.getNextId();

            feature.setId(id);
            feature.setExpressionId(expression.getId());
            feature.setCustomerCode(customerCode);
            feature.setType(type);
            feature.setProjectType(projectType);
            feature.setIsUsing(Constants.ZbFeatureConstants.WHETHER_TRUE);
            feature.setIsDefault(Constants.ZbFeatureConstants.WHETHER_TRUE);
            feature.setIsFromSystem(Constants.ZbFeatureConstants.WHETHER_TRUE);
            feature.setIsDeleted(Constants.ZbFeatureConstants.WHETHER_FALSE);
            feature.setIsRequired(Constants.ZbFeatureConstants.WHETHER_FALSE); // 是否必填不勾选
            feature.setIsExpression(Constants.ZbFeatureConstants.WHETHER_TRUE);
            feature.setIsSearchCondition(Constants.ZbFeatureConstants.WHETHER_FALSE);
            feature.setCreateGlobalId(defaultGlobalId);
            feature.setCreateTime(now);
            feature.setOrdTrade(maxOrder);
            feature.setTradeId(tradeId);
            Long originId = expressionNameIds.get(expressionName);
            if(originId!=null){
                feature.setOriginId(originId);
                selfFeatureIdMap.put(originId,id);
            }
            maxOrder ++;

            retList.add(feature);

            if(globalId == null) { //企业已发布数据
                expressionNameIds.put(expressionName, id);
            }
        }
        if(selfGlobalFeatureIdMap!=null){
            selfGlobalFeatureIdMap.put(globalId,selfFeatureIdMap);
        }
        return retList;
    }

    /**
     * 删除分类视图中多余的计算口径数据
     * （包括暂存，只删除该分类下只有这几个计算口径的数据）
     * @return
     * <AUTHOR>
     */
    @Override
    public void deleteFeatureCategoryViewUnuseData(List<ExpressionExcelVo> excelDataList,String standardTradeName) {

        List<String> expressionNames = convertToFeatureNames(excelDataList);
        if(CollectionUtils.isEmpty(expressionNames)) return;

        //2、查询所有分类视图的企业
        List<String> customerCodes = projectFeatureCategoryViewMapper.selectAllCustomerCode();
        customerCodes.remove(defaultCustomerCode);
        customerCodes.remove(BusinessConstants.NULL_STR);
        customerCodes.remove(BusinessConstants.UNDEFINED);
        if(customerCodes.isEmpty()) return;

        for (Integer type : CategoryTypeConstants.categoryTypeList) { //三套
            for (String customerCode : customerCodes) { //遍历每个企业
                //3、查询专业
                ZbStandardsTrade trade = standardsTradeMapper.searchRecordsByCusCodeAndName(standardTradeName, customerCode);
                String projectType = projectFeatureService.categoryAllCode(customerCode, type, null);
                List<String> firstCategoryCodes = projectFeatureService.projectTypeConvertToFirstCategoryCode(projectType);
                if(trade == null || CollectionUtils.isEmpty(firstCategoryCodes)) continue;

                //处理分类视图
                deleteFeatureCategoryView(expressionNames,firstCategoryCodes, type, customerCode);
                //处理分类视图：暂存
                deleteFeatureCategoryViewSelf(expressionNames,firstCategoryCodes, type, customerCode);
            }
        }

    }

    /**
     * 执行删除：发布视图
     * @param expressionNames
     * @param type
     * @param customerCode
     */
    private void deleteFeatureCategoryView(List<String> expressionNames, List<String> firstCategoryCodes,Integer type, String customerCode) {
        List<ZbStandardsExpression> expressionList = standardsExpressionMapper.selectExpression(customerCode,type);

        for (String firstCategoryCode : firstCategoryCodes) { //遍历每个企业下的每一个专业
            //查询当前分类视图数据
            List<ProjectFeatureCategoryView> dbCategoryViewList = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(firstCategoryCode, customerCode, type);
            if(CollectionUtils.isEmpty(dbCategoryViewList) || dbCategoryViewList.size() != expressionNames.size()) continue; //若为空，或者数量不等，则不处理

            //查询所有的工程分类
            List<Long> featureIds = dbCategoryViewList.stream().map(c -> c.getFeatureId()).collect(Collectors.toList());
            List<ProjectFeature> featureList = projectFeatureMapper.selectByFeatureIdList(featureIds,customerCode,Constants.ZbExpressionConstants.WHETHER_FALSE,type);
            if(CollectionUtils.isEmpty(featureList))continue;
            // 匹配对应的计算口径
            List<Long> expressionIds = featureList.stream().map(f -> f.getExpressionId()).collect(Collectors.toList());
            List<ZbStandardsExpression> currentCategoryExpressions = expressionList.stream().filter(e ->expressionIds.contains(e.getId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(currentCategoryExpressions))continue;

            boolean needDelete = true;
            for (ZbStandardsExpression expression : currentCategoryExpressions) {
                if(!expressionNames.contains(expression.getName())){ //若当前视图下，有不同的，则不删除
                    needDelete = false;
                    break;
                }
            }

            if(needDelete){
                List<Long> ids = dbCategoryViewList.stream().map(c -> c.getId()).collect(Collectors.toList());
                 projectFeatureCategoryViewMapper.batchDeleteByIds(ids);
                log.info("清除企业{} {}分类视图数据【{}】成功",customerCode,firstCategoryCode,expressionNames);
            }

        }
    }

    /**
     * 执行删除：暂存视图
     * @param expressionNames
     * @param type
     * @param customerCode
     */
    private void deleteFeatureCategoryViewSelf(List<String> expressionNames,List<String> firstCategoryCodes, Integer type, String customerCode) {
        List<Long> allGlobalIds = projectFeatureCategoryViewSelfMapper.selectAllGlobalIds(customerCode);
        if(CollectionUtils.isEmpty(allGlobalIds)) return;

        for (Long selfGlobalId : allGlobalIds) { // 遍历所有帐号
            if(selfGlobalId == null) continue;
            List<ZbStandardsExpression> expressionList = standardsExpressionSelfMapper.selectSelfExpression(customerCode,selfGlobalId, type);

            for (String firstCategoryCode : firstCategoryCodes) { //遍历每个企业下的每一个专业
                //查询当前分类视图数据
                List<ProjectFeatureCategoryView> dbCategoryList = projectFeatureCategoryViewSelfMapper.selectBySelfCategoryAndCustomerCode(firstCategoryCode, customerCode, type);
                if (CollectionUtils.isEmpty(dbCategoryList) || dbCategoryList.size() != expressionNames.size())
                    continue; //若为空，或者数量不等，则不处理

                //查询所有的工程分类
                List<Long> featureIds = dbCategoryList.stream().map(c -> c.getFeatureId()).collect(Collectors.toList());
                List<ProjectFeature> featureList = projectFeatureSelfMapper.selectBySelfFeatureIdList(featureIds, customerCode, Constants.ZbExpressionConstants.WHETHER_FALSE, type);
                if (CollectionUtils.isEmpty(featureList)) continue;
                // 匹配对应的计算口径
                List<Long> expressionIds = featureList.stream().map(f -> f.getExpressionId()).collect(Collectors.toList());
                List<ZbStandardsExpression> currentCategoryExpressions = expressionList.stream().filter(e -> expressionIds.contains(e.getId())).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(currentCategoryExpressions)) continue;

                boolean needDelete = true;
                for (ZbStandardsExpression expression : currentCategoryExpressions) {
                    if (!expressionNames.contains(expression.getName())) { //若当前视图下，有不同的，则不删除
                        needDelete = false;
                        break;
                    }
                }

                if (needDelete) {
                    List<Long> ids = dbCategoryList.stream().map(c -> c.getId()).collect(Collectors.toList());
                      projectFeatureCategoryViewSelfMapper.batchDeleteByIds(ids);
                    log.info("清除企业{} {}分类视图暂存数据【{}】成功", customerCode, firstCategoryCode, expressionNames);
                }

            }
        }
    }

    /**
     * 根据Excel模板，只初始化工程特征暂存数据，若已存在，则不处理
     * @param excelDataList
     * @param standardTradeName
     * @param defaultInitCustomerCode
     */
    @Override
    public void initSelfFeatureOnly(List<ExpressionExcelVo> excelDataList, String standardTradeName, String defaultInitCustomerCode) {
        LocalDateTime begin = LocalDateTime.now();
        List<String> expressionNames = convertToFeatureNames(excelDataList);
        if(CollectionUtils.isEmpty(expressionNames)) return;
        log.info("----初始化工程特征暂存数据中，初始化：{}",expressionNames);
        //1、查询所有工程特征的企业
        List<String> customerCodes;
        if(StringUtils.isNotBlank(defaultInitCustomerCode)){
            customerCodes = new ArrayList<>();
            customerCodes.add(defaultInitCustomerCode);
        }else {
            customerCodes = projectFeatureMapper.selectAllCustomerCode(); //此处查询会比较慢，线上大约10分钟
        }
        customerCodes.remove(defaultCustomerCode);
        customerCodes.remove(BusinessConstants.NULL_STR);
        customerCodes.remove(BusinessConstants.UNDEFINED);
        log.info("----初始化工程特征暂存数据中，查询所有企业编码成功，企业数量为：{}",customerCodes.size());
        if(customerCodes.isEmpty()) return;

        for (Integer type : CategoryTypeConstants.categoryTypeList) { //三套
            for (String customerCode : customerCodes) { //遍历每个企业
                log.info("----初始化工程特征暂存数据，处理企业{} 中type={}的数据",customerCode,type);
                try{
                    List<Long> allGlobalIds = projectFeatureSelfMapper.selectAllGlobalIds(customerCode);
                    if (CollectionUtils.isNotEmpty(allGlobalIds)) {
                        allGlobalIds.remove(defaultGlobalId);
                    }
                    //2、查询计算口径，筛选出待初始化的计算口径
                    List<ZbStandardsExpression> expressionList = standardsExpressionMapper.selectExpression(customerCode,type);
                    List<ZbStandardsExpression> releasedExpressionList = expressionList.stream().filter(e -> expressionNames.contains(e.getName())).collect(Collectors.toList());//只保留待插入的计算口径数据
                    if(CollectionUtils.isEmpty(releasedExpressionList)) continue;

                    List<Long> expressionIds = releasedExpressionList.stream().map(e -> e.getId()).collect(Collectors.toList());
                    Map<String,ZbStandardsExpression> releasedExpressionMap = releasedExpressionList.parallelStream().filter(e ->expressionNames.contains(e.getName())).collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(),(e1, e2) -> e1));
                    //2、查询专业
                    ZbStandardsTrade trade = standardsTradeMapper.searchRecordsByCusCodeAndName(standardTradeName, customerCode);
                    if(trade == null) continue;
                    //3、查询工程特征，只保留要处理的工程特征
                    List<ProjectFeature> featureList = projectFeatureMapper.selectByTradeIdAndType(trade.getId(),type);

                    List<ProjectFeature> releasedFeatureList = featureList.stream().filter(f ->expressionIds.contains(f.getExpressionId())).collect(Collectors.toList());
                    if(CollectionUtils.isEmpty(releasedFeatureList)) continue;

                    //4、查询暂存的工程特征
                    handleSelfFeature(customerCode,type,expressionNames,trade.getId(),releasedFeatureList,releasedExpressionMap,allGlobalIds);
                    log.info("----初始化工程特征暂存数据，处理企业{} 中type={}的数据 完成---",customerCode,type);
                }catch (Exception e){
                    e.printStackTrace();
                }
            }
        }
        LocalDateTime end = LocalDateTime.now();
        log.info("----初始化工程特征暂存数据，所有企业初始化完成，耗时={}ms ---", Duration.between(begin, end).toMillis());
    }

    /**
     * 只初始化工程特征暂存数据
     * @param customerCode
     * @param type
     * @param expressionNames
     * @param tradeId
     * @param releasedFeatureList
     * @param releasedExpressionMap
     * @param allGlobalIds
     */
    private void handleSelfFeature(String customerCode, Integer type,List<String> expressionNames, Long tradeId, List<ProjectFeature> releasedFeatureList,Map<String,ZbStandardsExpression> releasedExpressionMap, List<Long> allGlobalIds) {
        List<ProjectFeature> newProjectFeatureList = new ArrayList<>();
        for (Long selfGlobalId : allGlobalIds) {
            if(selfGlobalId == null) continue;
            Map<Long,ProjectFeature> releasedExpressionFeatureMap = releasedFeatureList.stream().collect(Collectors.toMap(x->x.getExpressionId(), x->x,(k1, k2)-> k1));

            //查找当前暂存的工程特征，用于对比及拿到最大排序
            List<ProjectFeature> selfFeatureList = projectFeatureSelfMapper.selectBySelfTradeId(customerCode,tradeId,type);
            Map<Long,ProjectFeature> selfExpressionFeatureMap = CollectionUtils.isEmpty(selfFeatureList)?new HashMap<>():selfFeatureList.stream().collect(Collectors.toMap(x->x.getExpressionId(), x->x,(k1, k2)-> k1));
            int maxOrder = CollectionUtils.isEmpty(selfFeatureList)?1:selfFeatureList.stream().filter(e-> e.getOrdTrade()!=null).map(ProjectFeature::getOrdTrade).distinct().max(Integer::compareTo).orElse(0)+1;

            List<ZbStandardsExpression> selfExpressionList = standardsExpressionSelfMapper.selectSelfExpression(customerCode, selfGlobalId, type);
            Map<String,ZbStandardsExpression> selfExpressionMap = selfExpressionList.parallelStream().filter(e ->expressionNames.contains(e.getName())).collect(Collectors.toMap(ZbStandardsExpression::getName, Function.identity(),(e1, e2) -> e1));
            if(selfExpressionMap == null || selfExpressionMap.isEmpty()) continue;

            for (String expressionName : expressionNames) {
                ZbStandardsExpression selfExpression = selfExpressionMap.get(expressionName);
                ZbStandardsExpression releasedExpression = releasedExpressionMap.get(expressionName);
                if(selfExpression == null || releasedExpression == null) continue; //待导入的数据在计算口径中没有，则不处理

                ProjectFeature selfFeature = selfExpressionFeatureMap.get(selfExpression.getId());
                if(selfFeature!= null) continue; //待导入的在计算口径中已存在，则不处理

                ProjectFeature releasedFeature = releasedExpressionFeatureMap.get(releasedExpression.getId());

                ProjectFeature newFeature = new ProjectFeature();
                BeanUtils.copyProperties(releasedFeature,newFeature);
                newFeature.setOriginId(releasedFeature.getId());
                newFeature.setId(SnowflakeIdUtils.getNextId());
                newFeature.setExpressionId(selfExpression.getId());
                newFeature.setOrdTrade(maxOrder);

                maxOrder++;

                newProjectFeatureList.add(newFeature);
            }
        }
        if(CollectionUtils.isNotEmpty(newProjectFeatureList)){
            projectFeatureSelfMapper.insertSelfBatch(newProjectFeatureList);
        }

    }

    /**
     * 根据Excel模板，只初始化分类视图数据
     * @param excelDataList
     * @param standardTradeName
     * <AUTHOR>
     */
    @Override
    public void initCategoryViewOnly(List<ExpressionExcelVo> excelDataList, String standardTradeName,String defaultInitCustomerCode) {
        LocalDateTime begin = LocalDateTime.now();
        List<String> expressionNames = convertToFeatureNames(excelDataList);
        log.info("----初始化分类视图数据中，初始化：{}",expressionNames);
        if(CollectionUtils.isEmpty(expressionNames)) return;

        //2、查询所有分类视图的企业
        List<String> customerCodes;
        if(StringUtils.isNotBlank(defaultInitCustomerCode)){
            customerCodes = new ArrayList<>();
            customerCodes.add(defaultInitCustomerCode);
        }else {
            customerCodes = projectFeatureCategoryViewMapper.selectAllCustomerCode();
        }
        customerCodes.remove(defaultCustomerCode);
        customerCodes.remove(BusinessConstants.NULL_STR);
        customerCodes.remove(BusinessConstants.UNDEFINED);
        log.info("----初始化分类视图数据中，查询所有企业编码成功，企业数量为：{}",customerCodes.size());
        if(customerCodes.isEmpty()) return;

        for (Integer type : CategoryTypeConstants.categoryTypeList) { //三套
            for (String customerCode : customerCodes) { //遍历每个企业
                log.info("----初始化分类视图数据，处理企业{} 中type={}的数据",customerCode,type);
                //3、查询专业
                ZbStandardsTrade trade = standardsTradeMapper.searchRecordsByCusCodeAndName(standardTradeName, customerCode);
                String projectType = projectFeatureService.categoryAllCode(customerCode, type, null);
                List<String> firstCategoryCodes = projectFeatureService.projectTypeConvertToFirstCategoryCode(projectType);
                List<Long> allGlobalIds = projectFeatureCategoryViewSelfMapper.selectAllGlobalIds(customerCode);
                if(trade == null || CollectionUtils.isEmpty(firstCategoryCodes)) continue;

                //处理分类视图
                List<ProjectFeature> releasedFeatureList = initFeatureCategoryViewOnly(expressionNames,firstCategoryCodes, type, customerCode, trade.getId());
                if(CollectionUtils.isNotEmpty(releasedFeatureList) && CollectionUtils.isNotEmpty(allGlobalIds)) {
                    allGlobalIds.remove(defaultGlobalId);
                    //处理分类视图：暂存
                    initSelfFeatureCategoryViewOnly(expressionNames,firstCategoryCodes, type, customerCode, trade.getId(),releasedFeatureList,allGlobalIds);
                }
                log.info("----初始化分类视图数据，处理企业{} 中type={}的数据 完成---",customerCode,type);
            }
        }
        LocalDateTime end = LocalDateTime.now();
        log.info("----初始化分类视图数据，所有企业初始化完成，耗时={}ms ---", Duration.between(begin, end).toMillis());
    }


    /**
     * 初始化分类视图数据
     * @param expressionNames
     * @param firstCategoryCodes
     * @param type
     * @param customerCode
     * @param tradeId
     */
    private List<ProjectFeature> initFeatureCategoryViewOnly(List<String> expressionNames, List<String> firstCategoryCodes, Integer type, String customerCode, Long tradeId) {
        List<ZbStandardsExpression> expressionList = standardsExpressionMapper.selectExpression(customerCode,type);
        List<ZbStandardsExpression> currentExpressionList = expressionList.stream().filter(e -> expressionNames.contains(e.getName())).collect(Collectors.toList());//只保留待插入的计算口径数据
        if(CollectionUtils.isEmpty(currentExpressionList)) return null;
        List<Long> expressionIds = currentExpressionList.stream().map(e -> e.getId()).collect(Collectors.toList());

        //查询工程特征
        List<ProjectFeature> featureList = projectFeatureMapper.selectByTradeIdAndType(tradeId,type);
        //只保留要处理的工程特征
        List<ProjectFeature> releasedFeatureList = featureList.stream().filter(f ->expressionIds.contains(f.getExpressionId())).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(releasedFeatureList)) return null;


        List<ProjectFeatureCategoryView> newCategoryViewList = new ArrayList<>();
        for (String firstCategoryCode : firstCategoryCodes) { //遍历每个企业下的每一个专业
            //查询当前分类视图数据
            List<ProjectFeatureCategoryView> dbCategoryList = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(firstCategoryCode, customerCode, type);
            if(CollectionUtils.isEmpty(dbCategoryList)) continue;//若为空，则不处理

            List<Long> categoryViewIds = dbCategoryList.stream().map(e -> e.getFeatureId()).collect(Collectors.toList());
            //拿到专业下的最大排序
            List<ProjectFeatureCategoryView> dbOrderCategoryList = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCodeAndTradeId(firstCategoryCode, customerCode,tradeId,type);
            int maxOrder = dbOrderCategoryList.stream().filter(e-> e.getOrdCategory()!=null).map(ProjectFeatureCategoryView::getOrdCategory).distinct().max(Integer::compareTo).orElse(0);

            for (ProjectFeature feature : releasedFeatureList) {
                if(categoryViewIds.contains(feature.getId())) continue; //若已存在，则不处理

                ProjectFeatureCategoryView newView = new ProjectFeatureCategoryView();
                newView.setId(SnowflakeIdUtils.getNextId());
                newView.setTradeId(tradeId);
                newView.setCategoryCode(firstCategoryCode);
                newView.setType(type);
                newView.setFeatureId(feature.getId());
                newView.setOrdCategory(maxOrder);
                newView.setCustomerCode(customerCode);
                newView.setTradeName(null);

                maxOrder ++;
                newCategoryViewList.add(newView);
            }
        }
        if(!newCategoryViewList.isEmpty()){
            projectFeatureCategoryViewMapper.saveBatch(newCategoryViewList);
        }
        return releasedFeatureList;
    }

    /**
     * 初始化暂存的分类视图数据
     * @param expressionNames
     * @param firstCategoryCodes
     * @param type
     * @param customerCode
     * @param tradeId
     */
    private void initSelfFeatureCategoryViewOnly(List<String> expressionNames, List<String> firstCategoryCodes, Integer type, String customerCode, Long tradeId,List<ProjectFeature> releasedFeatureList,List<Long> allGlobalIds) {

        List<Long> currentFeaturIds = releasedFeatureList.stream().map(ProjectFeature::getId).collect(Collectors.toList());
        List<ProjectFeatureCategoryView> newSelfCategoryViewList = new ArrayList<>();

        for (Long selfGlobalId : allGlobalIds) { // 遍历所有帐号
            if(selfGlobalId == null) continue;
            List<ZbStandardsExpression> selfExpressionList = standardsExpressionSelfMapper.selectSelfExpression(customerCode,selfGlobalId, type);
            List<ZbStandardsExpression> currentSelfExpressionList = selfExpressionList.stream().filter(e -> expressionNames.contains(e.getName())).collect(Collectors.toList());//只保留待插入的计算口径数据
            if(CollectionUtils.isEmpty(currentSelfExpressionList)) continue;
            List<Long> expressionIds = currentSelfExpressionList.stream().map(e -> e.getId()).collect(Collectors.toList());

            //查询工程特征
            List<ProjectFeature> selfFeatureList = projectFeatureSelfMapper.selectBySelfTradeId(customerCode,tradeId,type);
            //只保留要处理的工程特征
            List<ProjectFeature> currentSelfFeatureList = selfFeatureList.stream().filter(f ->expressionIds.contains(f.getExpressionId())).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(currentSelfFeatureList)) continue;

            for (String firstCategoryCode : firstCategoryCodes) { //遍历每个企业下的每一个工程分类
                //查询当前已发布的分类视图数据
                List<ProjectFeatureCategoryView> dbCategoryList = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(firstCategoryCode, customerCode, type);
                if(CollectionUtils.isEmpty(dbCategoryList)) continue;
                List<ProjectFeatureCategoryView> currentCategoryViewList = dbCategoryList.stream().filter(c -> currentFeaturIds.contains(c.getFeatureId())).collect(Collectors.toList()); //筛选后已发布的分类视图数据
                if(CollectionUtils.isEmpty(currentCategoryViewList)) continue;

                //查询当前分类视图暂存的数据
                List<ProjectFeatureCategoryView> dbSelfCategoryList = projectFeatureCategoryViewSelfMapper.selectBySelfCategoryAndCustomerCode(firstCategoryCode,customerCode,type);
                List<Long> selfViewOriginIds = CollectionUtils.isEmpty(dbSelfCategoryList)?new ArrayList<>():dbSelfCategoryList.stream().map(e -> e.getOriginId()).collect(Collectors.toList());

                List<ProjectFeatureCategoryView> dbSelfOrderCategoryList = projectFeatureCategoryViewSelfMapper.selectByCategoryAndCustomerCodeAndTradeId(firstCategoryCode,customerCode,tradeId,type);
                int maxOrder = dbSelfOrderCategoryList.stream().filter(e-> e.getOrdCategory()!=null).map(ProjectFeatureCategoryView::getOrdCategory).distinct().max(Integer::compareTo).orElse(0);

                Map<Long,ProjectFeature> selfOriginFeature = currentSelfFeatureList.stream().collect(Collectors.toMap(x->x.getOriginId(), x->x,(k1, k2)-> k1));

                for (ProjectFeatureCategoryView categoryView : currentCategoryViewList) {
                    if(selfViewOriginIds.contains(categoryView.getId())) continue; //若该条数据在暂存表中有，则不处理
                    ProjectFeature feature = selfOriginFeature.get(categoryView.getFeatureId());
                    if(feature == null) continue;
                    ProjectFeatureCategoryView newView = new ProjectFeatureCategoryView();
                    newView.setId(SnowflakeIdUtils.getNextId());
                    newView.setTradeId(tradeId);
                    newView.setCategoryCode(firstCategoryCode);
                    newView.setType(type);
                    newView.setFeatureId(feature.getId());
                    newView.setOrdCategory(maxOrder);
                    newView.setCustomerCode(customerCode);
                    newView.setTradeName(null);
                    newView.setOriginId(categoryView.getId());

                    maxOrder ++;
                    newSelfCategoryViewList.add(newView);
                }
            }
        }
        if(CollectionUtils.isNotEmpty(newSelfCategoryViewList)){
             projectFeatureCategoryViewSelfMapper.saveBatch(newSelfCategoryViewList);
        }
    }


    /**
     * 导入内置工程分类
     *  注意：导入完成后，需要初始化用户工程分类
     * @param parentCategoryName 上级分类名称，多级用|隔开
     * @param categoryType 需要导入到的分类，多个用英文逗号隔开，数据取自：CategoryTypeConstants
     * @param categoryTypeCode 如果导入到一级分类的话，此参数为导入到的模板Code，如果非一级，则此参数不生效，默认取父级模板Code
     * @param categoryTypeName 如果导入到一级分类的话，此参数为导入到的模板名称，如果非一级，则此参数不生效，默认取父级模板名称
     * @return
     * <AUTHOR>
     */
    public ResponseVo importCommonprojCategoryData(List<CommonCategoryExcelVo> excelDataList, String parentCategoryName, String categoryType,String categoryTypeCode,String categoryTypeName){
        try{
            LocalDateTime begin = LocalDateTime.now();
            List<Integer> categoryTypeList = getCategoryTypeList(categoryType);
            List<String> allImportCategoryNames = getImportCategoryNames(excelDataList);
            log.info("导入内置工程分类开始----，待初始化数据为{}条",allImportCategoryNames.size());
            if(CollectionUtils.isEmpty(allImportCategoryNames)) throw new BusinessException("待初始化的工程分类数据为空");

            // 查询所有的内置工程分类
            for (Integer type : categoryTypeList) {
                LocalDateTime end1 = LocalDateTime.now();
                log.info("导入内置工程分类，type：{}", type);
                List<CommonProjCategory> categoryList = commonProjCategoryMapper.selectAll(defaultCustomerCode,type, WHETHER_FALSE);
                if(CollectionUtils.isEmpty(categoryList)) throw new BusinessException("该类型"+type+"对应的内置工程分类为空");
                LocalDateTime end2 = LocalDateTime.now();
                log.info("查询系统内置工程分类成功，type：{}，数量为：{}，耗时={}ms", type,categoryList.size(), Duration.between(end1, end2).toMillis());
                List<CommonProjCategory> subCategories = new ArrayList<>();
                int maxCode = 0;
                int maxOrd = 0;

                CommonProjCategory parentCategory = getParentCategoryAndSubCategories(categoryList, parentCategoryName, subCategories);
                LocalDateTime end3 = LocalDateTime.now();
                log.info("查询系统内置工程分类{} 成功，type：{}，数量为：{},子级数量为{}，耗时={}ms", parentCategoryName,type,categoryList.size(),subCategories.size(), Duration.between(end2, end3).toMillis());
                if(parentCategory != null){ //若上级不为空
                    Map<String,Integer> maxOrderAndCode = getMaxOrderAndCode(subCategories);
                    maxOrd = maxOrderAndCode.get(BusinessConstants.MAX_ORDER);
                    maxCode = maxOrderAndCode.get(BusinessConstants.MAX_CODE);
                }else{ // 若上级为空，找到一级的最大的一个编码+1
                    for (CommonProjCategory category : categoryList) {
                        if(category.getLevel() != 1L)continue;
                        Integer code = Integer.parseInt(category.getCommonprojcategoryid());
                        Integer order = category.getOrd();
                        if(code.intValue()>maxCode) maxCode = code;
                        if(order.intValue()>maxOrd) maxOrd = order;
                    }
                }

                // 转化为待新增数据
                List<CommonProjCategory> insertCategoryList = convertToBuiltInCategoryData(allImportCategoryNames, subCategories, parentCategory,categoryTypeCode,categoryTypeName,type, ++maxCode, ++maxOrd);
                LocalDateTime end4 = LocalDateTime.now();
                log.info("转化为待新增数据成功，type：{}，待新增数量为{}，耗时={}ms", type,insertCategoryList.size(), Duration.between(end3, end4).toMillis());
                if(CollectionUtils.isNotEmpty(insertCategoryList)){
                     commonProjCategoryMapper.saveBatchCommonProjCategory(insertCategoryList);
                }
            }
            LocalDateTime end = LocalDateTime.now();
            log.info("导入内置工程分类成功，耗时={}ms", Duration.between(begin, end).toMillis());
            return ResponseVo.success();
        }catch (Exception e){
            e.printStackTrace();
            return ResponseVo.error("导入内置工程分类出错：e="+e.getMessage());
        }
    }

    /**
     * 获取待导入父级的分类，如果parentCategoryName为空，则直接返回空值
     * @param categoryList
     * @param parentCategoryName
     * @param subCategories
     * @return
     * <AUTHOR>
     */
    private CommonProjCategory getParentCategoryAndSubCategories(List<CommonProjCategory> categoryList, String parentCategoryName, List<CommonProjCategory> subCategories) throws BusinessException{
        if(StringUtils.isBlank(parentCategoryName)){ //若导入未指定上级，则取一级的所有名称(用于判重)
            List<CommonProjCategory> firstCategories = categoryList.parallelStream().filter(c ->c.getLevel() == 1L).collect(Collectors.toList());
            subCategories.addAll(firstCategories);
            return null;
        }

        CommonProjCategory parentCategory = null;

        Map<String,CommonProjCategory> categoryMap = categoryList.parallelStream().collect(Collectors.toMap(x->x.getCommonprojcategoryid(), x->x,(k1, k2)-> k1));
        for (CommonProjCategory category : categoryList) {
            String allCategoryNames = getAllCategoryNames(categoryMap, category); //工业建筑|厂房

            if(allCategoryNames.equals(parentCategoryName)){
                parentCategory = category;
                break;
            }
        }
        if(parentCategory == null){ //没匹配到，直接抛出
            throw new BusinessException("未匹配到对应的工程分类数据");
        }

        // 查询该级别下的所有子级
        for (CommonProjCategory category : categoryList) {
            Integer categoryId = category.getId();
            String categorycode1 = category.getCategorycode1();
            String categorycode2 = category.getCategorycode2();
            String categorycode3 = category.getCategorycode3();
            Integer parentCategoryId = parentCategory.getId();
            String parentCategorycode1 = parentCategory.getCategorycode1();
            String parentCategorycode2 = parentCategory.getCategorycode2();
            String parentCategorycode3 = parentCategory.getCategorycode3();

            if(categoryId.intValue() == parentCategoryId.intValue()) continue;
            if(!parentCategorycode1.equals(categorycode1)) continue;
            if(StringUtils.isNotBlank(parentCategorycode2) && !parentCategorycode2.equals(categorycode2)) continue;
            if(StringUtils.isNotBlank(parentCategorycode3) && !parentCategorycode3.equals(categorycode3)) continue;

            subCategories.add(category);
        }

        return parentCategory;
    }

    /**
     * 根据code拼装名称，用|隔开，如：工业建筑|物流仓库
     * @param categoryMap
     * @param category
     * @return
     */
    private String getAllCategoryNames(Map<String, CommonProjCategory> categoryMap, CommonProjCategory category) {
        String categorycode1 = category.getCategorycode1();
        String categorycode2 = category.getCategorycode2();
        String categorycode3 = category.getCategorycode3();
        String categorycode4 = category.getCategorycode4();

        StringBuffer allCategoryNames =  new StringBuffer();
        String categoryName = categoryMap.containsKey(categorycode1)?categoryMap.get(categorycode1).getCategoryname():"";
        allCategoryNames.append(categoryName);
        if(StringUtils.isNotBlank(categorycode2) && categoryMap.containsKey(categorycode2)){
            allCategoryNames.append("|").append(categoryMap.get(categorycode2).getCategoryname());
        }
        if(StringUtils.isNotBlank(categorycode3) && categoryMap.containsKey(categorycode3)){
            allCategoryNames.append("|").append(categoryMap.get(categorycode3).getCategoryname());
        }
        if(StringUtils.isNotBlank(categorycode4) && categoryMap.containsKey(categorycode4)){
            allCategoryNames.append("|").append(categoryMap.get(categorycode4).getCategoryname());
        }
        return allCategoryNames.toString();
    }

    /**
     * 待导入的工程分类数据转成待插入的数据
     * @param allImportCategoryNames
     * @param subCategories
     * @param maxCode
     * @return
     * <AUTHOR>
     */
    private List<CommonProjCategory> convertToBuiltInCategoryData(List<String> allImportCategoryNames,List<CommonProjCategory> subCategories,
                                                                  CommonProjCategory parentCategory,String categoryTypeCode,String categoryTypeName,int type,int maxCode,int maxOrd) {
        List<CommonProjCategory> result = new ArrayList<>();
         Date now = new Date();
        List<String> subCategoryNames = subCategories.stream().map(c -> c.getCategoryname()).collect(Collectors.toList());

        for (String categoryName : allImportCategoryNames) {
            //名称已存在，则不处理
            if(StringUtils.isBlank(categoryName) || subCategoryNames.contains(categoryName)) continue;

            String newCategorySubCode = setNewCategoryCode(maxCode);
            CommonProjCategory category = new CommonProjCategory();
            category.setId(IDGenerator.getNextIntId(TableNameConstants.TB_COMMONPROJCATEGORY_STANDARDS)); // 自动递增
            category.setCategoryname(categoryName);
            category.setType(type);
            category.setOrd(maxOrd);
            category.setGlobalId(defaultGlobalId.toString());
            category.setQyCode(defaultCustomerCode);
            category.setIsDeleted(false);
            category.setIsUsing(WHETHER_TRUE);
             category.setCreateTime(now);

            if(parentCategory != null){
                String parentLevel = parentCategory.getLevel().toString();
                String currentCode = parentCategory.getCommonprojcategoryid() + newCategorySubCode;
                category.setCommonprojcategoryid(currentCode);
                category.setCategorycode1(parentCategory.getCategorycode1());
                category.setCategoryTypeCode(parentCategory.getCategoryTypeCode());
                category.setCategoryTypeName(parentCategory.getCategoryTypeName());
                switch (parentLevel){
                    case "1":
                        category.setCategorycode2(currentCode);
                        category.setLevel(2L);
                        break;
                    case "2":
                        category.setCategorycode2(parentCategory.getCategorycode2());
                        category.setCategorycode3(currentCode);
                        category.setLevel(3L);
                        break;
                    case "3":
                    default:
                        category.setCategorycode2(parentCategory.getCategorycode2());
                        category.setCategorycode3(parentCategory.getCategorycode3());
                        category.setCategorycode4(currentCode);
                        category.setLevel(4L);
                        break;
                }

            }else{ //父级为空，该数据则为1级
                category.setCommonprojcategoryid(newCategorySubCode);
                category.setCategorycode1(newCategorySubCode);
                category.setLevel(1L);
                category.setCategoryTypeCode(categoryTypeCode);
                category.setCategoryTypeName(categoryTypeName);
            }

            maxOrd ++;
            maxCode ++;
            result.add(category);
        }

        return result;
    }

    /**
     * 获取待导入的分类名称列表
     * @param excelDataList
     * @return
     */
    private List<String> getImportCategoryNames(List<CommonCategoryExcelVo> excelDataList) {
        List<String> result = new ArrayList<>();
        for (CommonCategoryExcelVo excelVo : excelDataList) {
            if(!"新增".equals(excelVo.getDesc())) continue;

            String categoryName = excelVo.getCategoryName();
            //名称为空则不处理
            if(StringUtils.isBlank(categoryName) ) continue;
            result.add(categoryName);
        }
        return result;
    }

    /**
     * 初始化用户工程分类（发布、暂存）
     * @param excelDataList
     * @param parentCategoryName 上级分类名称，多级用|隔开
     * @param categoryType 需要导入到的分类，多个用英文逗号隔开，数据取自：CategoryTypeConstants
     * @return
     * <AUTHOR>
     */
    public ResponseVo initUserCommonprojCategoryData(List<CommonCategoryExcelVo> excelDataList, String parentCategoryName, String categoryType,String defaultInitCustomerCode){
        try{
            LocalDateTime begin = LocalDateTime.now();
            List<Integer> categoryTypeList = getCategoryTypeList(categoryType);
            List<String> allImportCategoryNames = getImportCategoryNames(excelDataList);
            log.info("初始化用户工程分类开始----，待初始化数据为{}条",allImportCategoryNames.size());
            if(CollectionUtils.isEmpty(allImportCategoryNames)) throw new BusinessException("待初始化的工程分类数据为空");

            //查询所有的企业
            List<String> customerCodes;
            if(StringUtils.isNotBlank(defaultInitCustomerCode)){
                customerCodes = new ArrayList<>();
                customerCodes.add(defaultInitCustomerCode);
            }else{
                customerCodes = commonProjCategoryMapper.selectAllCustomerCode();
            }
            customerCodes.remove(defaultCustomerCode);

            LocalDateTime end1 = LocalDateTime.now();
            log.info("导入内置工程分类-查询所有的企业 成功，企业数量：{}，耗时={}ms", customerCodes.size(), Duration.between(begin, end1).toMillis());

            // 1、查询所有的内置工程分类
            for (Integer type : categoryTypeList) {
                LocalDateTime end2 = LocalDateTime.now();
                List<CommonProjCategory> categoryList = commonProjCategoryMapper.selectAll(defaultCustomerCode,type, WHETHER_FALSE);
                LocalDateTime end3 = LocalDateTime.now();
                log.info("导入内置工程分类-查询类别为{}的工程分类数据成功，工程分类数量：{}，耗时={}ms",type, categoryList.size(), Duration.between(end2, end3).toMillis());
                if(CollectionUtils.isEmpty(categoryList)) continue;

                List<CommonProjCategory> builtInCategoryList = getBuiltInCategoryList(categoryList, allImportCategoryNames,parentCategoryName);
                LocalDateTime end4 = LocalDateTime.now();
                log.info("导入内置工程分类-获取类别为{}的待导入的工程分类数据成功，工程分类数量：{}，耗时={}ms",type, builtInCategoryList.size(), Duration.between(end3, end4).toMillis());
                if(CollectionUtils.isEmpty(builtInCategoryList)) continue;

                customerCodeHandle(customerCodes, type, parentCategoryName, builtInCategoryList);

            }
            LocalDateTime end = LocalDateTime.now();
            log.info("初始化用户工程分类成功，耗时={}ms", Duration.between(begin, end).toMillis());
            return ResponseVo.success();
        }catch (Exception e){
            e.printStackTrace();
            return ResponseVo.error("初始化用户工程分类出错：e="+e.getMessage());
        }
    }

    /**
     * @Description 方法抽提
     * @return void
     **/
    private void customerCodeHandle(List<String> customerCodes, Integer type, String parentCategoryName, List<CommonProjCategory> builtInCategoryList) {
        for (String customerCode : customerCodes) {
            if(StringUtils.isBlank(customerCode)) continue;
            LocalDateTime end5 = LocalDateTime.now();
            try {
                List<CommonProjCategory> userCategoryList = commonProjCategoryMapper.selectAll(customerCode, type, WHETHER_FALSE);
                LocalDateTime end6 = LocalDateTime.now();
                log.info("导入内置工程分类-查询企业：{} 已发布的工程分类数据成功，工程分类数量：{}，耗时={}ms",customerCode, userCategoryList.size(), Duration.between(end5, end6).toMillis());

                List<CommonProjCategory> subCategories = new ArrayList<>();
                CommonProjCategory parentCategory = getParentCategoryAndSubCategories(userCategoryList, parentCategoryName, subCategories);

                if (Objects.isNull(parentCategory)) {
                    continue;
                }

                LocalDateTime end7 = LocalDateTime.now();
                log.info("导入内置工程分类-查询企业：{} 父级工程分类及子级数据成功，子级分类数量：{}，耗时={}ms",customerCode, subCategories.size(), Duration.between(end6, end7).toMillis());

                List<CommonProjCategory> userCategories = convertToUserBuiltInCategories(customerCode,builtInCategoryList,parentCategory,subCategories);
                LocalDateTime end8 = LocalDateTime.now();
                log.info("导入内置工程分类- 初始化企业：{} 待导入的已发布工程数据成功，数量：{}，耗时={}ms",customerCode, userCategories.size(), Duration.between(end7, end8).toMillis());

                if(CollectionUtils.isNotEmpty(userCategories)){
                    commonProjCategoryMapper.saveBatchCommonProjCategory(userCategories);

                    //因暂存表有数据的用户比较少，所以直接同步初始化暂存表
                    saveUserSelfCategories(customerCode,type,userCategories,parentCategory);
                    LocalDateTime end9 = LocalDateTime.now();
                    log.info("导入内置工程分类- 初始化企业：{} 待导入的暂存工程数据成功，耗时={}ms",customerCode,Duration.between(end8, end9).toMillis());
                }

            } catch (BusinessException e) {
                //用户无此分类，直接跳过
                log.info("用户无此分类");
            }
        }
    }

    /**
     * 将categoryType 转成List<Integer>形式，用英文逗号隔开，若为空，则返回所有
     * @param categoryType
     * @return
     */
    private List<Integer> getCategoryTypeList(String categoryType) {
        List<Integer> categoryTypeList;

        if(StringUtils.isNotBlank(categoryType)){
            categoryTypeList = new ArrayList<>();
            String[] types = categoryType.split(",");
            for (String type : types) {
                categoryTypeList.add(Integer.parseInt(type));
            }
        }else{ // 若为空，则返回所有
            categoryTypeList = CategoryTypeConstants.categoryTypeList;
        }
        return categoryTypeList;
    }

    /**
     * 根据Excel导入的数据以及内置的工程分类数据，筛选出内置的待初始化数据
     * @param categoryList
     * @param parentCategoryName
     * @return
     */
    private List<CommonProjCategory> getBuiltInCategoryList(List<CommonProjCategory> categoryList, List<String> allImportCategoryNames, String parentCategoryName) {
        List<CommonProjCategory> result = new ArrayList<>();
        List<String> allNewImportCategoryNames = new ArrayList<>(); //如：工业建筑|厂房|物流仓储厂房
        if(StringUtils.isNotBlank(parentCategoryName)){
            for (String categoryName : allImportCategoryNames) {
                categoryName = parentCategoryName+"|"+categoryName;
                allNewImportCategoryNames.add(categoryName);
            }
        }else{
            allNewImportCategoryNames = allImportCategoryNames;
        }

        Map<String,CommonProjCategory> categoryMap = categoryList.parallelStream().collect(Collectors.toMap(x->x.getCommonprojcategoryid(), x->x,(k1, k2)-> k1));
        for (CommonProjCategory category : categoryList) {
            String allCategoryNames = getAllCategoryNames(categoryMap, category);
            //若内置的工程分类名称与待导入的名称一致，则为待初始化数据
            if(allNewImportCategoryNames.contains(allCategoryNames)){
                result.add(category);
            }
        }

        return result;
    }

    /**
     * 将新增的内置数据转换成用户的内置数据
     * @param customerCode
     * @param builtInCategoryList
     * @param subCategories
     * @return
     */
    private List<CommonProjCategory> convertToUserBuiltInCategories(String customerCode, List<CommonProjCategory> builtInCategoryList,CommonProjCategory parentCategory, List<CommonProjCategory> subCategories) {
        List<String> subCategoryNames = subCategories.parallelStream().map(CommonProjCategory::getCategoryname).collect(Collectors.toList());
        List<CommonProjCategory> insertCategories = new ArrayList<>();
        Map<String,Integer> maxOrderAndCode = getMaxOrderAndCode(subCategories);
        int maxOrder = maxOrderAndCode.get(BusinessConstants.MAX_ORDER) + 1;
        int maxCode = maxOrderAndCode.get(BusinessConstants.MAX_CODE) + 1; //重新排，避免与subCategories重合

        for (CommonProjCategory category : builtInCategoryList) {
            if(subCategoryNames.contains(category.getCategoryname()))continue; //若该分类在用户工程分类中已存在，则不处理

            CommonProjCategory newCategory = new CommonProjCategory();
            BeanUtils.copyProperties(category,newCategory);

            newCategory.setId(IDGenerator.getNextIntId(TableNameConstants.TB_COMMONPROJCATEGORY_STANDARDS));
            newCategory.setQyCode(customerCode);
            newCategory.setOrd(maxOrder);
            newCategory.setCreateTime(null);
            if(parentCategory!=null) {
                newCategory.setCommonprojcategoryid(parentCategory.getCommonprojcategoryid()+setNewCategoryCode(maxCode));
            }else{
                newCategory.setCommonprojcategoryid(setNewCategoryCode(maxCode));
            }

            maxOrder ++;
            maxCode ++;
            insertCategories.add(newCategory);
        }
        return insertCategories;
    }

    /**
     * 插入用户内置数据
     * @param customerCode
     * @param userCategories
     * @param parentCategory
     */
    private void saveUserSelfCategories(String customerCode, Integer type, List<CommonProjCategory> userCategories, CommonProjCategory parentCategory) {
        //查询该用户暂存表下的所有用户
        List<Long> allGlobalIds = commonProjCategorySelfMapper.selectAllGlobalIds(customerCode);
        if(CollectionUtils.isEmpty(allGlobalIds)) return;

        for (Long selfGlobalId : allGlobalIds) { // 遍历所有帐号
            if(selfGlobalId == null) continue;

            // 查询当前帐号下的暂存数据
            CommonProjCategory category = commonProjCategorySelfMapper.getByCategoryCode(parentCategory.getCommonprojcategoryid(),customerCode,type);
            if(category == null) continue; // 未找到父级，则直接跳过

            //查询该父级下的所有子级
            List<CommonProjCategory> subCategories = commonProjCategorySelfMapper.listDirectChildrenByCode(parentCategory.getCommonprojcategoryid(),customerCode,type);

            //转化
            List<CommonProjCategory> newSelfCategories = convertToSelfCategory(selfGlobalId,userCategories,category,subCategories);

            if(CollectionUtils.isNotEmpty(newSelfCategories)){
                commonProjCategorySelfMapper.saveBatchSelfProjCategory(newSelfCategories);
            }
        }


    }

    /**
     * 根据用户的工程分类与用户的当前暂存的子分类数据，拼装成新的暂存数据
     * @param selfGlobalId
     * @param userCategories
     * @param subCategories
     * @return
     */
    private List<CommonProjCategory> convertToSelfCategory(Long selfGlobalId, List<CommonProjCategory> userCategories, CommonProjCategory parentCategory,List<CommonProjCategory> subCategories) {
        //取名称，判断是否有重复
        List<String> subCategoryNames = subCategories.parallelStream().map(CommonProjCategory::getCategoryname).collect(Collectors.toList());
        Map<String,Integer> maxOrderAndCode = getMaxOrderAndCode(subCategories);
        int maxOrder = maxOrderAndCode.get(BusinessConstants.MAX_ORDER) + 1;
        int maxCode = maxOrderAndCode.get(BusinessConstants.MAX_CODE) + 1;// 取新的最大的编码，避免和subCategories冲突

        List<CommonProjCategory> newSelfCategories = new ArrayList<>();

        for (CommonProjCategory userCategory : userCategories) {
            if(subCategoryNames.contains(userCategory.getCategoryname())) continue; //若已存在，则不处理

            CommonProjCategory newCategory = new CommonProjCategory();
            BeanUtils.copyProperties(userCategory,newCategory);

            newCategory.setId(null);
            newCategory.setOriginId(userCategory.getId());
            newCategory.setOrd(maxOrder);
            if(parentCategory!=null) {
                newCategory.setCommonprojcategoryid(parentCategory.getCommonprojcategoryid()+setNewCategoryCode(maxCode));
            }else{
                newCategory.setCommonprojcategoryid(setNewCategoryCode(maxCode));
            }

            maxOrder++;
            maxCode++;
            newSelfCategories.add(newCategory);
        }

        return newSelfCategories;
    }

    /**
     * 获取最大的排序和Code
     * @param subCategories
     * @return
     */
    private Map getMaxOrderAndCode(List<CommonProjCategory> subCategories) {
        int maxOrder = 0;
        int maxCode = 0;
        if(CollectionUtils.isNotEmpty(subCategories)){ //若还有子级，找到最大
            for (CommonProjCategory subCategory : subCategories) {
                String commonprojcategoryid = subCategory.getCommonprojcategoryid();
                Integer code = Integer.parseInt(commonprojcategoryid.substring(commonprojcategoryid.length()-3));
                Integer order = subCategory.getOrd();
                if(code >maxCode) maxCode = code;
                if(order >maxOrder) maxOrder = order;
            }
        }
        Map<String,Integer> result = new HashMap<>();
        result.put(BusinessConstants.MAX_ORDER,maxOrder);
        result.put(BusinessConstants.MAX_CODE,maxCode);
        return result;
    }

    @Override
    public Set<String> buildStandardCustomerCodeList() {
        Set<String> customerCodeList = projectStandardMapper.selectAllCustomerCodes();
        return customerCodeList;
    }

    @Override
    public void refreshBuildStandard(Set<String> customerCodeList) {
        log.info("开始刷新标准数据");
        // 查询内置的标准数据
        List<RefreshBuildStandard> defaultDataList = this.initDefaultData();

        // 逐个企业处理
        for (String customerCode : customerCodeList) {
            try {
                if(isNext(customerCode)){
                    this.singleCustomerRefresh(defaultDataList, customerCode);
                }
            }catch (Exception e){
                errorLog(customerCode, e.getMessage());
                log.error("刷新企业：{}的标准数据出错：{}", customerCode, e.getMessage(), e);
            }
        }
        log.info("刷新标准数据完成");
    }

    public boolean isNext(String customerCode) {
        RepairLog entity = tempCommonRepairMapper.selectOne(
                new LambdaQueryWrapper<RepairLog>()
                        .eq(RepairLog::getCustomerCode, customerCode)
                        .eq(RepairLog::getVersion, repair_version_4));

        if(Objects.nonNull(entity) && Objects.nonNull(entity.getRepairStatus())) {
            return false;
        }
        if(Objects.isNull(entity)) {
            RepairLog repairLog = new RepairLog();
            repairLog.setCustomerCode(customerCode);
            repairLog.setVersion(repair_version_4);
            tempCommonRepairMapper.insert(repairLog);
        }
        return true;
    }

    public void successLog(String customerCode, List<ZbProjectStandard> copyStandardList) {
        List<String> categoryNames = copyStandardList.stream().map(ZbProjectStandard::getCategoryName).collect(Collectors.toList());
        RepairLog repairLog = new RepairLog();
        repairLog.setRepairStatus(1);
        repairLog.setErroType(categoryNames.toString());

        tempCommonRepairMapper.update(repairLog, new LambdaQueryWrapper<RepairLog>()
                .eq(RepairLog::getCustomerCode, customerCode)
                .eq(RepairLog::getVersion, repair_version_4));
    }

    public void errorLog(String customerCode, String errorMsg) {
        RepairLog repairLog = new RepairLog();
        repairLog.setRepairStatus(2);
        repairLog.setErroType(errorMsg);

        tempCommonRepairMapper.update(repairLog, new LambdaQueryWrapper<RepairLog>()
                .eq(RepairLog::getCustomerCode, customerCode)
                .eq(RepairLog::getVersion, repair_version_4));
    }

    private void singleCustomerRefresh(List<RefreshBuildStandard> defaultDataList, String customerCode) {
        log.info("开始刷新企业：{}的标准数据", customerCode);
        // 工程分类类型
        Integer categoryType = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        if (!Objects.equals(categoryType, TYPE_DEFAULT)) {
            throw new BusinessException("企业："+customerCode+"工程分类类型不为默认，跳过");
        }

        // 查询该企业的工程分类
        List<CommonProjCategory> allCategoryList = commonProjCategoryService.getAllCategoryList(customerCode, categoryType, Constants.CategoryConstants.WHETHER_TRUE);

        // 工程分类名称和id的映射
        List<String> importCategoryNames = Arrays.asList("居住建筑", "办公建筑", "工业建筑");
        Map<String, String> topCategoryNameAndCodeMap = allCategoryList.stream()
                .filter(x -> x.getLevel() == 1 && importCategoryNames.contains(x.getCategoryname()))
                .sorted(Comparator.comparing(CommonProjCategory::getIsDeleted))
                .collect(Collectors.toMap(CommonProjCategory::getCategoryname, CommonProjCategory::getCommonprojcategoryid, (k1, k2) -> k1));

        // 查询企业现有的标准数据
        Set<String> standardNameSet = new HashSet<>();
        List<ZbProjectStandard> customerStandardList = projectStandardMapper.selectByCustomerCode(customerCode, WHETHER_TRUE);
        Integer qyFlag = null;
        String qyCodeOld = null;
        if (CollectionUtils.isNotEmpty(customerStandardList)) {
            standardNameSet = customerStandardList.stream().map(ZbProjectStandard::getName).collect(Collectors.toSet());
            ZbProjectStandard first = CollUtil.getFirst(customerStandardList);
            qyFlag = first.getQyFlag();
            qyCodeOld = first.getQyCodeOld();
        }

        // 处理非一级工程分类
        List<ZbProjectStandard> updateStandardList = dealWarnCategory(allCategoryList, customerStandardList);

        // 定义待入库的标准数据
        List<ZbProjectStandard> copyStandardList = Lists.newArrayList();
        List<ZbProjectStandardDetail> copyDetailList = Lists.newArrayList();
        List<ZbStandardsBuildStandardDetailDesc> copyDescList = Lists.newArrayList();
        List<ZbProjectStandard> copyStandardSelfList = Lists.newArrayList();

        // 逐个内置标准复制
        for (RefreshBuildStandard defaultData : defaultDataList) {
            if (standardNameSet.contains(defaultData.getName())) {
                log.info("企业：{}已配置标准：{}，跳过", customerCode, defaultData.getName());
                continue;
            }
            if (!topCategoryNameAndCodeMap.containsKey(defaultData.getCategoryName())){
                log.info("企业：{}未配置工程分类：{}，跳过", customerCode, defaultData.getCategoryName());
                continue;
            }
            String categoryCode = topCategoryNameAndCodeMap.get(defaultData.getCategoryName());
            RefreshBuildStandard refreshBuildStandard = this.executeCopy(defaultData, customerCode, categoryCode);
            copyStandardList.add(refreshBuildStandard.getProjectStandard());
            copyDetailList.addAll(refreshBuildStandard.getDetailList());
            copyDescList.addAll(refreshBuildStandard.getDescList());
        }

        // 复制暂存数据。只需要复制主表，其余表的数据会走增量的暂存初始化逻辑
        this.copeSelfData(customerCode, copyStandardList, copyStandardSelfList);

        // 处理启用状态
        this.dealWithEnable(customerStandardList, copyStandardList, updateStandardList);

        // 插入数据库
        ((ImportBuiltInDataServiceImpl)AopContext.currentProxy())
                .saveToDB(customerCode, copyStandardList, copyDetailList, copyDescList, copyStandardSelfList, updateStandardList, qyFlag, qyCodeOld);
    }

    private List<ZbProjectStandard> dealWarnCategory(List<CommonProjCategory> allCategoryList, List<ZbProjectStandard> customerStandardList) {
        Map<String, String> codeCategoryMap = allCategoryList.stream()
                .collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, CommonProjCategory::getCategoryname, (v1, v2) -> v1));

        List<ZbProjectStandard> updateStandardList = new ArrayList<>();
        customerStandardList.stream()
                .filter(x -> StringUtils.isNotEmpty(x.getCategoryCode()) && x.getCategoryCode().length() > LEVEL_1_LEN)
                .forEach(x -> {
                    String topCategoryCode = x.getCategoryCode().substring(0, LEVEL_1_LEN);
                    String topCategoryName = codeCategoryMap.get(topCategoryCode);
                    ZbProjectStandard updateStandard = new ZbProjectStandard();
                    updateStandard.setId(x.getId());
                    updateStandard.setCategoryCode(topCategoryCode);
                    updateStandard.setCategoryName(topCategoryName);
                    updateStandardList.add(updateStandard);
                });

        return updateStandardList;
    }

    private void dealWithEnable(List<ZbProjectStandard> customerStandardList,
                                List<ZbProjectStandard> copyStandardList,
                                List<ZbProjectStandard> updateStandardList) {
        Map<Long, ZbProjectStandard> idAndStandardMap = updateStandardList.stream().collect(Collectors.toMap(ZbProjectStandard::getId, Function.identity()));

        List<ZbProjectStandard> allStandardList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(customerStandardList)){
            allStandardList.addAll(customerStandardList);
        }
        if (CollectionUtils.isNotEmpty(copyStandardList)){
            allStandardList.addAll(copyStandardList);
        }

        Map<String, String> defaultCategoryCodeAndStandardNameMap = copyStandardList.stream()
                .filter(x -> !"建造标准-居住建筑".equals(x.getName()))
                .collect(Collectors.toMap(ZbProjectStandard::getCategoryCode, ZbProjectStandard::getName));

        Map<String, List<ZbProjectStandard>> categoryCodeMap = allStandardList.stream()
                .filter(x -> StringUtils.isNotEmpty(x.getCategoryCode())).collect(Collectors.groupingBy(ZbProjectStandard::getCategoryCode));

        categoryCodeMap.forEach((k, v) -> {
            Optional<ZbProjectStandard> newestStandard = v.stream()
                    .filter(x -> Objects.equals(x.getIsUsing(), Constants.WHETHER_TRUE))
                    .max(Comparator.comparing(ZbProjectStandard::getUpdateTime, Comparator.nullsLast(Comparator.naturalOrder())));

            if (newestStandard.isPresent()){
                // 将其他的设置为未启用
                v.stream().filter(x -> !Objects.equals(x.getId(), newestStandard.get().getId()) && Objects.equals(x.getIsUsing(), WHETHER_TRUE))
                        .forEach(x -> {
                            if (idAndStandardMap.containsKey(x.getId())){
                                idAndStandardMap.get(x.getId()).setIsUsing(WHETHER_FALSE);
                            }else {
                                ZbProjectStandard updateUsingStandard = new ZbProjectStandard();
                                updateUsingStandard.setId(x.getId());
                                updateUsingStandard.setIsUsing(WHETHER_FALSE);
                                updateStandardList.add(updateUsingStandard);
                            }
                        });
            }else{
                // 将内置的标准设置为启用
                if (defaultCategoryCodeAndStandardNameMap.containsKey(k)) {
                    String standardName = defaultCategoryCodeAndStandardNameMap.get(k);
                    v.stream().filter(x -> Objects.equals(x.getName(), standardName))
                            .forEach(x -> x.setIsUsing(Constants.ZbExpressionConstants.WHETHER_TRUE));
                }
            }
        });
    }

    private void copeSelfData(String customerCode, List<ZbProjectStandard> copyStandardList, List<ZbProjectStandard> copyStandardSelfList) {
        // 查询该企业的是否有暂存数据
        List<ZbProjectStandard> selfStandards = projectStandardMapper.selectSelfByCustomerCode(customerCode, WHETHER_TRUE);

        if (CollectionUtils.isEmpty(selfStandards)) {
            return;
        }

        List<Long> selfNewIdList = SnowflakeIdUtils.getNextId(copyStandardList.size());
        for (int i = 0; i < copyStandardList.size(); i++) {
            ZbProjectStandard copyStandard = copyStandardList.get(i);
            ZbProjectStandard copyStandardSelf = new ZbProjectStandard();
            BeanUtils.copyProperties(copyStandard, copyStandardSelf);
            copyStandardSelf.setOriginId(copyStandard.getId());
            copyStandardSelf.setId(selfNewIdList.get(i));
            copyStandardSelfList.add(copyStandardSelf);
        }
    }

    private List<RefreshBuildStandard> initDefaultData() {
        List<ZbProjectStandard> systemCustomerStandardList = projectStandardMapper.selectByCustomerCode(SYSTEM_CUSTOMER_CODE, WHETHER_TRUE);
        if (CollectionUtils.isEmpty(systemCustomerStandardList)){
            throw new BusinessException("系统内置数据为空，请检查");
        }
        List<String> oldStandardNames = Arrays.asList("建造标准-居住建筑", "建造标准-办公建筑", "建造标准-工业建筑");
        List<ZbProjectStandard> defaultStandardList = systemCustomerStandardList.stream().filter(x -> oldStandardNames.contains(x.getName())).collect(Collectors.toList());
         if (CollectionUtils.isEmpty(defaultStandardList) || defaultStandardList.size() != 3){
             throw new BusinessException("3套内置数据不全，请检查");
         }

        List<RefreshBuildStandard> defaultDataList = new ArrayList<>();
        for (ZbProjectStandard zbProjectStandard : defaultStandardList) {
            RefreshBuildStandard defaultData = new RefreshBuildStandard();
            defaultData.setName(zbProjectStandard.getName());
            defaultData.setStandardId(zbProjectStandard.getId());
            defaultData.setProjectStandard(zbProjectStandard);
            defaultData.setCategoryCode(zbProjectStandard.getCategoryCode());
            defaultData.setCategoryName(zbProjectStandard.getCategoryName());
            defaultData.setDetailList(projectStandardDetailMapper.selectByStandardId(zbProjectStandard.getId()));
            defaultData.setDescList(zbStandardsBuildStandardDetailDescMapper.selectByStandardId(zbProjectStandard.getId()));
            if(CollectionUtils.isEmpty(defaultData.getDetailList()) || CollectionUtils.isEmpty(defaultData.getDescList())){
                throw new BusinessException("系统标准数据异常standardName：[" + zbProjectStandard.getName() + "]，请检查");
            }
            defaultDataList.add(defaultData);
        }
        return defaultDataList;
    }

    private RefreshBuildStandard executeCopy(RefreshBuildStandard defaultData, String customerCode, String categoryCode) {
        ZbProjectStandard defaultStandard = defaultData.getProjectStandard();
        List<ZbProjectStandardDetail> defaultDetailList = defaultData.getDetailList();
        List<ZbStandardsBuildStandardDetailDesc> defaultDescList = defaultData.getDescList();

        // 1.复制建造标准主表
        ZbProjectStandard copyStandard = new ZbProjectStandard();
        BeanUtils.copyProperties(defaultStandard, copyStandard);
        // 重新生成ID
        Long newStandardId = SnowflakeIdUtils.getNextId();
        // 重设部分字段
        copyStandard.setId(newStandardId);
        copyStandard.setCustomerCode(customerCode);
        copyStandard.setUpdateTime(null);
        copyStandard.setIsUsing(Constants.ZbExpressionConstants.WHETHER_FALSE);
        copyStandard.setIsExample(Constants.ZbExpressionConstants.WHETHER_FALSE);
        copyStandard.setIsDeleted(false);
        copyStandard.setCategoryCode(categoryCode);

        // 2.复制建造标准详情
        List<ZbProjectStandardDetail> copyDetailList = Lists.newArrayList();
        Map<Long, Long> detailDefaultIdAndNewIdMap = new HashMap<>();
        List<Long> detailNewIdList = SnowflakeIdUtils.getNextId(defaultDetailList.size());
        for (int i = 0; i < defaultDetailList.size(); i++) {
            ZbProjectStandardDetail defaultDetail = defaultDetailList.get(i);
            ZbProjectStandardDetail copyDetail = new ZbProjectStandardDetail();
            BeanUtils.copyProperties(defaultDetail, copyDetail);
            copyDetail.setId(detailNewIdList.get(i));
            copyDetail.setStandardId(newStandardId);
            copyDetail.setUpdateTime(null);
            detailDefaultIdAndNewIdMap.put(defaultDetail.getId(), copyDetail.getId());
            copyDetailList.add(copyDetail);
        }

        // 3.复制建造标准标准说明
        List<ZbStandardsBuildStandardDetailDesc> copyDescList = Lists.newArrayList();
        List<Long> descNewIdList = SnowflakeIdUtils.getNextId(defaultDescList.size());
        for (int i = 0; i < defaultDescList.size(); i++) {
            ZbStandardsBuildStandardDetailDesc defaultDesc = defaultDescList.get(i);
            ZbStandardsBuildStandardDetailDesc copyDesc = new ZbStandardsBuildStandardDetailDesc();
            BeanUtils.copyProperties(defaultDesc, copyDesc);
            if (!detailDefaultIdAndNewIdMap.containsKey(defaultDesc.getStandardDetailId())){
                throw new BusinessException("系统标准数据异常detailId：[" + defaultDesc.getStandardDetailId() + "]，请检查");
            }
            copyDesc.setStandardId(newStandardId);
            copyDesc.setStandardDetailId(detailDefaultIdAndNewIdMap.get(defaultDesc.getStandardDetailId()));
            copyDesc.setId(descNewIdList.get(i));
            copyDescList.add(copyDesc);
        }

        RefreshBuildStandard copyData = new RefreshBuildStandard();
        copyData.setName(copyStandard.getName());
        copyData.setStandardId(copyStandard.getId());
        copyData.setProjectStandard(copyStandard);
        copyData.setDetailList(copyDetailList);
        copyData.setDescList(copyDescList);
        return copyData;
    }

    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void saveToDB(String customerCode,
                         List<ZbProjectStandard> copyStandardList,
                         List<ZbProjectStandardDetail> copyDetailList,
                         List<ZbStandardsBuildStandardDetailDesc> copyDescList,
                         List<ZbProjectStandard> copyStandardSelfList,
                         List<ZbProjectStandard> updateStandardList,
                         Integer qyFlag,
                         String qyCodeOld) {
        if (CollectionUtils.isNotEmpty(copyStandardList)){
            if (Objects.nonNull(qyFlag)){
                copyStandardList.forEach(x -> {x.setQyFlag(qyFlag);x.setQyCodeOld(qyCodeOld);});
            }
            projectStandardMapper.batchInsert(copyStandardList);
        }
        if (CollectionUtils.isNotEmpty(copyDetailList)){
            projectStandardDetailMapper.saveBatch(copyDetailList);
        }
        if (CollectionUtils.isNotEmpty(copyDescList)){
            zbStandardsBuildStandardDetailDescMapper.batchSave(copyDescList);
        }
        if (CollectionUtils.isNotEmpty(copyStandardSelfList)) {
            projectStandardMapper.selfBatchInsert(copyStandardSelfList);
        }
        if (CollectionUtils.isNotEmpty(updateStandardList)) {
            projectStandardMapper.batchUpdateUsingAndCategory(customerCode, updateStandardList);
            projectStandardMapper.batchUpdateUsingAndCategorySelf(customerCode, updateStandardList);
        }
        successLog(customerCode, copyStandardList);
    }

}
