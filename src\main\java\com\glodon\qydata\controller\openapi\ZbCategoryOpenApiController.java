package com.glodon.qydata.controller.openapi;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.TrustConstants;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.util.CategoryUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.WHETHER_FALSE;


/**
 * 标准统一专项-工程分类  -对外接口
 * <AUTHOR>
 * @date 2021/10/15 17:18
 */
@RestController
@RequestMapping("/basicInfo/openApi/standards/zbCategory")
@Slf4j
@Tag(name = "工程分类-对外接口", description = "工程分类-对外接口")
public class ZbCategoryOpenApiController extends BaseController {

    @Autowired
    private CommonProjCategoryService commonProjCategoryService;
    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;

    /**
     * 对外提供-获取工程分类集合
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * @param dataType 返回数据的结构（0：原始结构；1：树结构）
     * @param isUsing 是否只返回启用的数据 1：只返回启用 else：不限
     * @param categoryType 慧果共存在两套标准，加上系统的1套标准，共3套标准 categoryType
     *                      1：系统内置
     *                      2 慧果《建设工程分类标准》GB/T 50841-2013
     *                      3 慧果工程造价指标分类及编制指南(中价协2021)
     * @param isSkipUserName 是否跳过用户名称赋值（0：不跳过；1：跳过；默认为跳过）
     * @param destEnterpriseId 基于委托-需要查询的企业id
     * @param trustProductSource 基于委托-委托来源 ysbz,gsbz
     * @param levelLimit 层级限制，不传返回全量，1：返回1级；2：返回1、2级；3：返回1、2、3级。。。。
     * @param categoryCode: 一级工程分类
     * @param needTag: 是否需要附加标签信息到工程分类列表
     * <AUTHOR>
     * @date 2021/10/21 9:25
     */
    @Operation(summary = "对外提供-获取工程分类集合")
    @GetMapping(value = "other/categoryList")
    public ResponseVo categoryList(Integer isShowDelete,
                                   Integer dataType,
                                   Integer isUsing,
                                   Integer categoryType,
                                   Integer isSkipUserName,
                                   String destEnterpriseId,
                                   String trustProductSource,
                                   Integer levelLimit,
                                   @RequestParam(value = "categoryCode1", required = false) String categoryCode1,
                                   @RequestParam(value = "needTag", required = false, defaultValue = "false") Boolean needTag){
        try {
            String customerCode = getCustomerCode(TrustConstants.TYPE_CATEGORY, destEnterpriseId, trustProductSource);
            // 工程分类未传值则查询企业最新确定的类别
            if(categoryType == null){
                categoryType = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
            }
            log.info("对外提供-获取工程分类集合-isShowDelete：{}，dataType：{}，customerCode：{}", isShowDelete, dataType, customerCode);
            return ResponseVo.success(commonProjCategoryService.getCategoryList(isShowDelete, dataType, isUsing, customerCode, categoryType, isSkipUserName, levelLimit, categoryCode1, needTag));
        } catch (Exception e) {
            log.error("获取工程分类集合数据错误", e);
        }
        return null;
    }

    /**
     * 对外提供-根据工程分类ID获取工程分类
     * @param categoryId 工程分类ID
     * @param isSkipUserName 是否跳过用户名称赋值（0：不跳过；1：跳过；默认为跳过）
     * <AUTHOR>
     * @date 2021/11/12 16:03
     */
    @Operation(summary = "对外提供-根据工程分类ID获取工程分类")
    @GetMapping(value = "other/category")
    public ResponseVo category(Integer categoryId, Integer isSkipUserName){
        try {
            CommonProjCategory commonProjCategory = commonProjCategoryService.getCategoryById(categoryId);
            if (WHETHER_FALSE.equals(isSkipUserName)){
                Map<String, String> globalIdNameMap = getGlobalIdNameMap(commonProjCategoryService.getAllGlobalIds(Collections.singletonList(commonProjCategory)));
                CategoryUtil.fillName(commonProjCategory, globalIdNameMap);
            }
            CategoryUtil.setUpdateTimeByUpdateGlobalId(commonProjCategory);
            return ResponseVo.success(commonProjCategory);
        } catch (Exception e) {
            log.error("根据工程分类ID获取工程分类数据错误", e);
        }
        return null;
    }

    /**
     * 对外提供-根据工程分类code获取工程分类
     * @param categoryCode 工程分类编码
     * @param isSkipUserName 是否跳过用户名称赋值（0：不跳过；1：跳过；默认为跳过）
     * @param destEnterpriseId 基于委托-需要查询的企业id
     * @param trustProductSource 基于委托-委托来源 ysbz,gsbz
     * <AUTHOR>
     * @Date 2022/2/16 17:48
     */
    @Operation(summary = "对外提供-根据工程分类code获取工程分类")
    @GetMapping(value = "other/categoryByCode")
    public ResponseVo categoryByCode(String categoryCode,
                                     Integer isSkipUserName,
                                     String destEnterpriseId,
                                     String trustProductSource){
        try {
            String customerCode = getCustomerCode(TrustConstants.TYPE_CATEGORY, destEnterpriseId, trustProductSource);
            CommonProjCategory commonProjCategory = commonProjCategoryService.getCategoryByCode(categoryCode, customerCode);
            if (WHETHER_FALSE.equals(isSkipUserName)){
                Map<String, String> globalIdNameMap = getGlobalIdNameMap(commonProjCategoryService.getAllGlobalIds(Collections.singletonList(commonProjCategory)));
                CategoryUtil.fillName(commonProjCategory, globalIdNameMap);
            }
            CategoryUtil.setUpdateTimeByUpdateGlobalId(commonProjCategory);
            return ResponseVo.success(commonProjCategory);
        } catch (Exception e) {
            log.error("根据工程分类ID获取工程分类数据错误", e);
        }
        return null;
    }

    /**
     * @description: 对外提供 - 获取指定企业的所有分类code和分类的关系（包括已删除的）
     * @param categoryType 慧果共存在两套标准，加上系统的1套标准，共3套标准 categoryType
     *                     1：系统内置 无值默认
     *                     2 慧果《建设工程分类标准》GB/T 50841-2013
     *                     3 慧果工程造价指标分类及编制指南(中价协2021)
     * @return java.util.Map<java.lang.String, com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/11/30 14:23
     *
     * 兼容性处理接口路径
     */
    @Operation(summary = "获取指定企业的所有分类code和分类的关系（包括已删除的）")
    @GetMapping({"/getCodeCategoryMap", "/getCodeCategoryMap."})
    public Map<String, CommonProjCategory> getCodeCategoryMap(Integer categoryType,
                                                              String destEnterpriseId,
                                                              String trustProductSource){
        String customerCode = getCustomerCode(TrustConstants.TYPE_CATEGORY, destEnterpriseId, trustProductSource);
        // 工程分类未传值则查询企业最新确定的类别
        if(categoryType == null){
            categoryType = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        }
        Map<String, CommonProjCategory> codeCategoryMap = new HashMap<>(16);
        List<CommonProjCategory> allCategoryList = commonProjCategoryService.getAllCategoryList(customerCode, categoryType, Constants.CategoryConstants.WHETHER_TRUE);
        CategoryUtil.setUpdateTimeByUpdateGlobalId(allCategoryList);
        if (CollectionUtils.isNotEmpty(allCategoryList)){
            codeCategoryMap = allCategoryList.stream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, Function.identity(), (v1, v2) -> v2));
        }
        return codeCategoryMap;
    }

    /**
    　　* @description: 获取企业下最新的工程分类类型
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2022/3/22 10:58
    　　*/
    @Operation(summary = "对外提供-获取企业下最新的工程分类类型")
    @GetMapping(value = "other/getCategoryType")
    public ResponseVo getCategoryType(String destEnterpriseId, String trustProductSource){
        try {
            String customerCode = getCustomerCode(TrustConstants.TYPE_CATEGORY, destEnterpriseId, trustProductSource);
            Integer categoryType = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
            return ResponseVo.success(categoryType);
        } catch (Exception e) {
            log.error("获取企业下最新的工程分类类型错误", e);
        }
        return null;
    }
}
