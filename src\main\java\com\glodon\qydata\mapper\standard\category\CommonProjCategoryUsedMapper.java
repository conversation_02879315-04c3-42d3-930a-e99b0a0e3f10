package com.glodon.qydata.mapper.standard.category;

import com.glodon.qydata.entity.standard.category.CommonProjectCategoryUsed;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CommonProjCategoryUsedMapper {
    /**
     * 查询企业当前使用的工程分类类别
     * @return java.util.List<CommonProjCategoryUsed>
     * <AUTHOR> @date
     */
    List<CommonProjectCategoryUsed> selectAll();

    /**
     * 批量插入企业当前使用的工程分类
     * @return java.util.List<CommonProjectCategoryUsed>
     * <AUTHOR> @date
     */
    int batchInsert(@Param("list") List<CommonProjectCategoryUsed> list);
}
