package com.glodon.qydata.controller.standard.projectOrContractInfo;


import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.constant.DigitalCostConstants;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.system.ISysRightInfoService;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.system.RightInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 *  机械字段设置
 * <AUTHOR>
 * @date 2023/5/31 14:59
 */
@RestController
@RequestMapping("/basicInfo/largeMachinery")
@Tag(name = "机械字段设置相关接口类", description = "机械字段设置相关接口类")
public class LargeMachineryController extends BaseController {
    @Autowired
    private ISysRightInfoService sysRightInfoService;

    /**
     * 是否显示机械字段设置
     * <AUTHOR>
     * @return
     * @date 2023/5/31 14:59
     */
    @Operation(summary = "是否显示机械字段设置")
    @GetMapping("/getRights")
    public ResponseVo getRights() throws BusinessException {
        String globalId = getGlobalId();
        String sgToken = getSgToken();
        if(StringUtils.isEmpty(globalId) || StringUtils.isBlank(sgToken)){
            throw new BusinessException("无效请求,"+"globalId："+globalId+"，sgToken："+sgToken);
        }
        try {
            RightInfoVo rightInfo = sysRightInfoService.getRightInfoBySubSystemCodes(globalId, sgToken, true, DigitalCostConstants.largeMachinerySubSystemCodes);
            return ResponseVo.success(rightInfo);
        }catch (BusinessException be){
            return ResponseVo.error(be.getMessage());
        }
    }

}
