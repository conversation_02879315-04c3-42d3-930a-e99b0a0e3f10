package com.glodon.qydata.entity.deleteself;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: luoml-b
 * @date: 2024/4/24 08:59
 * @description: 删除self表实体
 */
@Data
public class SelfEntity implements Serializable {

    private static final long serialVersionUID = -6709508849962043398L;
    private String selfGlobalId;

    private Date createTime;

    private Date updateTime;

    private String customerCode;
}
