package com.glodon.qydata.mapper.repairdata;

import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工程特征修复mapper（非业务使用）
 */
public interface TempRepairFeatureMapper {
    int updateBatchById(@Param("customerCode") String customerCode, @Param("list") List<ProjectFeature> list);
    Integer selectRepeatRecord(@Param("customerCode") String customerCode);
    void setFeatureInvalid(@Param("customerCode") String customerCode, @Param("ids") List<Long> ids);
    void synsSelfFeature(@Param("customerCode") String customerCode, @Param("originIds") List<Long> originIds);
    void setCategoryViewInvalid(@Param("customerCode") String customerCode, @Param("ids") List<Long> ids);
    void syncSelfCategoryView(@Param("customerCode") String customerCode, @Param("originIds") List<Long> originIds);

}
