package com.glodon.qydata.config.feign;

import com.glodon.qydata.common.constant.HeaderConstants;
import com.glodon.qydata.util.HeaderValueUtil;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.extern.slf4j.Slf4j;

/**
 * 调用中台feign配置
 *
 * <AUTHOR>
 */
@Slf4j
public class SubjectDivisionFeignConfiguration implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate requestTemplate) {
        // header中填充globalId信息
        HeaderValueUtil.addDataToHeader(requestTemplate, HeaderConstants.GLOBAL_ID);
        // header中填充enterpriseId信息
        HeaderValueUtil.addDataToHeader(requestTemplate, HeaderConstants.ENTERPRISE_ID);
        // header中填充sgToken信息
        HeaderValueUtil.addDataToHeader(requestTemplate, HeaderConstants.SG_TOKEN);
    }
}
