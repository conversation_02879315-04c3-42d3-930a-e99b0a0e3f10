<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.temp.TempStandardsProjectInfoMapper">
  <select id="selectRepeatRecord" resultType="java.lang.Integer">
     select count(1) as repeateCount, customer_code from zb_standards_project_info where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
      group by customer_code ,standard_data_type ,type_code ,name  having repeateCount > 1 limit 1
   </select>
  <update id="setProjectInfoInvalId" parameterType="java.lang.Long">
    update zb_standards_project_info set invalid = 1 where customer_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
    <foreach collection="ids" index="index" item="item" separator="," open="id IN (" close=")">
      #{item}
    </foreach>
  </update>
  <update id="synsSelfProjectInfo" parameterType="java.lang.Long">
    update zb_standards_project_info_self  set invalid = 1 where customer_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
    <foreach collection="originIds" index="index" item="item" separator="," open="origin_id IN (" close=")">
      #{item}
    </foreach>
  </update>
</mapper>
