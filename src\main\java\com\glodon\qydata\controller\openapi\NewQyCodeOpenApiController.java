package com.glodon.qydata.controller.openapi;

import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @description: 新企业标识-对外接口(中台调用)
 * <AUTHOR>
 * @date 2023/08/23 17:19
 */
@RestController
@RequestMapping("/basicInfo/openApi/common")
@Slf4j
@Tag(name = "新企业标识-对外接口(中台调用)", description = "新企业标识-对外接口(中台调用)")
public class NewQyCodeOpenApiController {

    @Autowired
    private IGlodonUserService glodonUserService;

    /**
     * 获取基础信息库的新企业标识
     * @param globalId
     * <AUTHOR>
     * @date 2023/08/23 17:19
     */
    @Operation(summary = "获取基础信息库的新企业标识")
    @GetMapping("/getNewQyCode")
    public ResponseVo<String> getNewQyCode(@RequestParam String globalId) {
        return ResponseVo.success(glodonUserService.getCustomerCode(globalId));
    }

}
