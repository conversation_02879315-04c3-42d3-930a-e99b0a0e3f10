package com.glodon.qydata.mapper.standard.trade;

import com.glodon.qydata.entity.standard.category.CategoryTagEnum;
import com.glodon.qydata.entity.standard.trade.TradeTagEnum;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface TradeTagEnumMapper {
    List<TradeTagEnum> selectSystemTradeTags();

    /**
     * 查询当前企业的专业分类
     * @param customerCode
     * @return
     */
    List<TradeTagEnum> selectAllListByCustomerCode(@Param("customerCode") String customerCode);

    /**
     * 保存标签数据
     * @param list
     */
    void saveBatch(@Param("list") List<TradeTagEnum> list);

    /**
     * 更新专业分类标签表,如果存在则更新,不存在则插入
     * @param list
     */
    void saveOrUpdate(@Param("list") List<TradeTagEnum> list);

    /**
     * 删除当前企业的专业标签分类
     * @param customerCode
     */
    void removeAllByCustomCode(@Param("customerCode") String customerCode);

    int deleteByPrimaryKey(Long id);

    int insert(TradeTagEnum record);

    int insertSelective(TradeTagEnum record);

    TradeTagEnum selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(TradeTagEnum record);

    int updateByPrimaryKey(TradeTagEnum record);
}