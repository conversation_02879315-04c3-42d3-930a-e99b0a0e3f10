package com.glodon.qydata.util;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;

/**
 * Basic的头信息
 *
 *
 * <AUTHOR> 2014-3-26
 */
@Component
public class BasicAuthBuilder {

    public static String SERVICE_KEY;

    public static String SERVER_SECRET;

    @Value("${basic_auth.service.key}")
    public void setServiceKey(String serviceKey) {
        BasicAuthBuilder.SERVICE_KEY = serviceKey;
    }

    @Value("${basic_auth.server.secret}")
    public void setServerSecret(String serverSecret) {
        BasicAuthBuilder.SERVER_SECRET = serverSecret;
    }


    /**
     * 创建Basic认证头信息中的Key
     *
     * @return
     */
    public static String headerKey() {
        return "Authorization";
    }

    /**
     * 创建Basic认证头信息中的Value
     *
     * @return
     */
    public static String headerValue() {
        try {
            String creds = String.format("%s:%s", SERVICE_KEY, SERVER_SECRET);
            String credential = Base64.encodeToString(creds.getBytes(StandardCharsets.UTF_8));
            return "Basic " + credential;
        }catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }
}
