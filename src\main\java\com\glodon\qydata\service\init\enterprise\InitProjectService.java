package com.glodon.qydata.service.init.enterprise;

import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper;
import com.glodon.qydata.service.init.InitService;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsUnitService;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 项目/合同信息初始化
 * @date 2022/7/8 15:52
 */
@Service
public class InitProjectService extends InitService {
    @Autowired
    private StandardsProjectInfoMapper standardsProjectInfoMapper;

    @Autowired
    private IStandardsUnitService standardsUnitService;

    @Autowired
    private RedisUtil redisUtil;

    public void initData(String customerCode, Integer type){
        super.initData(customerCode, type, null);
    }

    @Override
    protected Boolean isNeedInit(String customerCode, Integer type, Long id) {
        return CollectionUtils.isEmpty(standardsProjectInfoMapper.selectAllPublishData(customerCode, Constants.CategoryConstants.WHETHER_FALSE, type));
    }

    @Override
    protected void executeInit(String customerCode, Integer type, Long id) {
        List<StandardsProjectInfoEntity> systemData = standardsProjectInfoMapper.selectAllPublishData(
                Constants.StandardsProjectOrContractInfoConstants.BUILT_IN_DATA_CUSTOMER_CODE, 1, type);

        if (CollectionUtils.isEmpty(systemData)){
            return;
        }

        for (StandardsProjectInfoEntity entity : systemData) {
            entity.setCustomerCode(customerCode);
            entity.setId(SnowflakeIdUtils.getNextId());
            entity.setCreatorId(null);
            entity.setCreateTime(null);

            if (Constants.StandardsProjectOrContractInfoConstants.CONTRACT_INFO.equals(type)) {
                boolean contains = Constants.StandardsProjectOrContractInfoConstants.CONTRACT_INFO_BUILT_IN_NON_EDITABLE_NAME_LIST.contains(entity.getName());
                if (!contains) {
                    //合同信息的开工时间、竣工时间，默认不必填、不启用
                    entity.setIsRequired(0);
                    entity.setIsUsing(0);
                }
            }

            if ("项目规模".equals(entity.getName()) || "工程规模".equals(entity.getName())) {
                // 默认选中单位为m2
                standardsUnitService.setDefaultCheck(entity.getId(), "m2", customerCode);
            }
        }
        standardsProjectInfoMapper.insertBatch(systemData);
    }

    @Override
    protected Boolean tryLockRetry(String customerCode, String lockValue) {
        return redisUtil.tryLockRetry(lockValue, RedisKeyEnum.LOCK_INIT_PROJECT, customerCode);
    }

    @Override
    protected void unlock(String customerCode, String lockValue) {
        redisUtil.unlock(lockValue, RedisKeyEnum.LOCK_INIT_PROJECT, customerCode);
    }
}
