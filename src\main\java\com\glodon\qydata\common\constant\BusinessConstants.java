package com.glodon.qydata.common.constant;

/**
 * <AUTHOR>
 * @program ebq
 * @description 业务常量类
 * @create 2023/10/18 14:53
 */
@SuppressWarnings("squid:S1192")
public class BusinessConstants {

    public static final String PRODUCT_CODE = "productCode";
    public static final String ORDER_TRADE = "orderTrade";
    public static final String UNDEFINED = "undefined";
    public static final String NULL_STR = "null";
    public static final String MAX_ORDER = "maxOrder";
    public static final String MAX_CODE = "maxCode";
    public static final String PROJECT_TYPE = "projectType";
    public static final String TRADE_ID = "tradeId";
    public static final String TRADE_NAME = "tradeName";
    public static final String CUSTOMER_CODE_LIST = "customerCodeList";
    public static final String UTF_8 = "UTF-8";
    public static final String UNKNOWN = "unknown";
    public static final String PG_SQL = "Pgsql";
    public static final String STANDARD_ID = "standardId";

    /**
     * 工程特征中的工程分类json转换，老转新
     */
    public static final String PROJECT_TYPE_CONVERT_NEW = "2";

    /**
     * 工程特征中的工程分类json转换，新转老
     */
    public static final String PROJECT_TYPE_CONVERT_OLD = "1";

    /**
     * 枚举值存储格式转换，转成|分隔的字符串
     */
    public static final String SELECT_LIST_TO_STR = "2";

    /**
     * 枚举值存储格式转换，转成json
     */
    public static final String SELECT_LIST_TO_JSON = "1";
}
