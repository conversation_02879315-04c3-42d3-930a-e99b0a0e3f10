package com.glodon.qydata.vo.standard.feature;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 特征标准数据同步
 * <AUTHOR>
 * @date 2021/11/19 8:44
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class ProjectFeatureDsVO implements Serializable {
    private static final long serialVersionUID = -277300502389356058L;

    /**
     * 工程特征名称
     */
    private String name;

    /**
     * 数据类型:值为dictionary表中type_code字段等于data_type所对应的code字段的值
     */
    private String typeCode;

    /**
     * 工程特征对应的选项，与数据类型连用,数据格式为json字符串：{id:xxx，name：xxx,sort: xxx}
     */
    private String option;

    /**
     * 工程分类 工程分类:json字符串 {"projectType":"[["001","001001","001001002"],["001","001001","001001003"],["001","001001","001001004"],["001","001001","001001005"],["001","001002","001002001"],["001","001002","001002002"],["001","001002","001002003"],["001","001003","001003002"],["001","001003","001003003"],["001","001003","001003004"],["001","001004","001004001"],["001","001004","001004002"],["001","001004","001004003"],["001","001004","001004004"],["001","001005"],["001","001006","001006001"],["001","001006","001006002"],["001","001006","001006003"],["001","001006","001006004"],["001","001006","001006005"],["001","001006","001006006"],["001","001006","001006007"],["001","001007"],["002","002001","002001001"],["002","002001","002001002"],["002","002001","002001003"],["002","002002"],["002","002003"],["002","002004"],["002","002005"],["002","002006","002006001"],["002","002006","002006002"],["002","002006","002006003"],["002","002007"]]"}
     */
    private String projectType;

    /**
     * 是否必填：1 必填；0 不必填
     */
    private Integer isRequired;

    /**
     * 注释，最大300个字符
     */
    private String remark;

    /**
     * 企业编码
     */
    private String customerCode;

    /**
     * 创建人ID（广联云用户体系）：-100为系统内置
     */
    private Long createGlobalId;

    /**
     * 更新人ID（广联达用户体系）
     */
    private Long updateGlobalId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 专业视图排序
     */
    private Integer ordTrade;

    /**
     * 工程专业id
     */
    private Long tradeId;

    /**
     * 工程专业名称
     */
    private String tradeName;

    /**
     * 是否为筛选项，0：不是，1：是
     */
    private Integer isSearchCondition;

    /**
     * 是否删除：1已删除；0未删除
     */
    private Integer isDeleted;

    /**
     * 计算口径单位
     */
    private String unit;

}