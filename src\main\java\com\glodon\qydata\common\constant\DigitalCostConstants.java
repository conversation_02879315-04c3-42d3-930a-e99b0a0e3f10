package com.glodon.qydata.common.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * 数字成本平台 相关常量
 * Created by weijf on 2022/1/27.
 */
public class DigitalCostConstants {

    /**
     * 新平台益类型 0：正常 1：过期 2：禁用 3：未生效
     */
    public static final Integer RIGHT_STATUS_0 = 0;
    public static final Integer RIGHT_STATUS_1 = 1;
    public static final Integer RIGHT_STATUS_2 = 2;
    public static final Integer RIGHT_STATUS_3 = 3;

    /** 数字成本平台-权限接口 productCode 固定**/
    public static final String PRODUCT_CODE = "dcost";

    /**
     * 数字成本平台-授权接口 referCode
     * 参考：https://docs.qq.com/sheet/DTnFWaGJPTVJUWHNk?tab=BB08J2
     **/
    public static final String REFER_CODE = "jcxxk";
//    大型机械库rediskey
    public static final String LARGEMACHINERY_REFER_CODE = "dxjxk";

    /**
     * 编码权限
     * https://docs.qq.com/sheet/DTnFWaGJPTVJUWHNk?tab=7yf7h8
     */
    public static final String EDIT_RIGHT_MANAGE = "dcost.module.jcxxk.manage";
//    大型机械库权限编码
    public static final String LARGEMACHINERY_EDIT_RIGHT = "dcost.module.dxjxk";
    public static final String LARGEMACHINERY_EDIT_RIGHT_MANAGER = "dcost.module.dxjxk.manage";// TODO 编码待定
    public static final String LARGEMACHINERY_EDIT_RIGHT_DOWNLOAD = "dcost.module.dxjxk.download";// TODO 编码待定

    /**
     * 数字成本平台-授权接口 subSystemCode
     * 参考子系统编码：https://ycg3d2h0q0.feishu.cn/docs/doccnsYjV962CPy3V24tHNVzMxd#3auOHm
     **/
    public static final List<String> subSystemCodes = new ArrayList<>();
//    大型机械库资产
    public static final List<String> largeMachinerySubSystemCodes = new ArrayList<>();
    public static final List<String> subGmsPids = new ArrayList<>();
    public static final List<String> hgGmsPids = new ArrayList<>();
    static {
        subSystemCodes.add("mubiaochengben"); // 目标成本
        subSystemCodes.add("schjj");// 市场化计价
        subSystemCodes.add("schjj-qyqdmbk");// 市场化计价-企业清单模板库
        subSystemCodes.add("schjj-qyjzjgk");// 市场化计价-企业基准价格库
        subSystemCodes.add("schjj-qylwfbk");// 市场化计价-企业劳务分包库
        subSystemCodes.add("schjj-cbcs");//  市场化计价-成本测算
        subSystemCodes.add("schjj-qydek");//  市场化计价-企业定额库
        subSystemCodes.add("zbsq");//  指标神器
        subSystemCodes.add("zjyglpt");//造价云管理平台
        subSystemCodes.add("zjyqykj");// 造价云企业空间
        subSystemCodes.add("gldzjyrj");//广联达造价云软件
        subSystemCodes.add("qyclk");//企业材料库
        subSystemCodes.add("qyzbk");//企业指标库
        subSystemCodes.add("fbjgk");//分包价格库
        subSystemCodes.add("fbqdbz");//分包清单编制
        subSystemCodes.add("fbgcjs");//分包过程结算
        subSystemCodes.add("fbzbqdbz");//分包招标清单编制
        subSystemCodes.add("glcs");//公路测算
        subSystemCodes.add("zhtjs");//中铁建设北分系统
        subSystemCodes.add("sgf-mcc");//中国二十二冶集团工程造价管理平台
        subSystemCodes.add("free.module"); // 24统一门户 - 加入免费模块

        largeMachinerySubSystemCodes.add("fbjgk-bp");
        // 20250409 FIX BUG 目前分包价格库权限等同材料库权限
        largeMachinerySubSystemCodes.add("qyclk");

        subGmsPids.add("13318");//分包招标模块
        subGmsPids.add("43014");// 目标成本
        subGmsPids.add("12526");// 市场化计价
        subGmsPids.add("12526");// 市场化计价-企业清单模板库
        subGmsPids.add("13072");// 市场化计价-企业基准价格库
        subGmsPids.add("23135");// 市场化计价-企业劳务分包库
        subGmsPids.add("23134");// 市场化计价-成本测算
        subGmsPids.add("13292");// 市场化计价-企业定额库
        subGmsPids.add("43003");// 指标神器
        subGmsPids.add("42512");// 企业材料库
        subGmsPids.add("43008");// 企业指标库
        subGmsPids.add("42429");//广联达造价云管理平台
        subGmsPids.add("42428");//造价云企业空间
        subGmsPids.add("42430");//广联达造价云软件
        subGmsPids.add("43001");//慧果，中台已处理，2022年4.1日后不再售卖，后续可以删除
        subGmsPids.add("13332");//分包价格库
        subGmsPids.add("13333");//分包清单编制
        subGmsPids.add("13334");//分包过程结算
        subGmsPids.add("13391");//分包招标清单编制
        subGmsPids.add("43021");//公路测算
        subGmsPids.add("13415");//中铁建设北分系统
        subGmsPids.add("13399");//中国二十二冶集团工程造价管理平台
        subGmsPids.add("free"); // 24统一门户 - 加入免费模块

        hgGmsPids.add("42515"); //广联达数字新咨询作业管理平台
        hgGmsPids.add("43002"); //广联达数字新咨询全过程造价管理平台(WEB+手机端》-年费版
        hgGmsPids.add("43015"); //广联达数字新咨询全过程工程咨询管理平台
        hgGmsPids.add("43037"); //广联达数字新咨询-基础平台

    }

    /**
     * 响应状态 200 ok
     */
    public static final Integer RESULT_CODE_200 = 200;

    public static final Integer RESULT_CODE_409 = 409;
    /**
     * 账号没有登录	 200008
     */
    public static final Integer RESULT_CODE_200008 = 200008;
    /**
     * key  code
     */
    public static final String RESULT_KEY_CODE = "code";
    /**
     * key  message
     */
    public static final String RESULT_KEY_MESSAGE = "message";
    /**
     * key  data
     */
    public static final String RESULT_KEY_DATA = "data";
    /**
     * key 施工token geipToken
     */
    public static final String RESULT_KEY_DATA_GEIP_TOKEN = "geipToken";
    /**
     * key gldToken，广联云的token
     */
    public static final String RESULT_KEY_DATA_GLD_TOKEN = "gldToken";

    public static final String ACCESS_TOKEN_WITH_UNDERLINE = "access_token";
    public static final String CONTENT_TYPE_HEADER = "Content-type";

}
