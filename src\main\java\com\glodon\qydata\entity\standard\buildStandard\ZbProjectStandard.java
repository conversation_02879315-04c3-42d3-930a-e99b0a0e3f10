package com.glodon.qydata.entity.standard.buildStandard;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.glodon.qydata.dto.StandardPositionDto;
import com.glodon.qydata.entity.system.QYFlag;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 　　* @description: 项目标准实体
 　　* <AUTHOR>
 　　* @date 2021/8/16 17:22
 　　*/
@Data
@TableName(value = "zb_standards_build_standard")
public class ZbProjectStandard extends QYFlag implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;
    @TableField(exist = false)
    private Long originId;
    private String name;

    private String categoryCode;

    private String categoryName;

    private String customerCode;

    private Long globalId;

    private Integer isExample;

    private Boolean isDeleted;

    private Boolean categoryDeleted;

    private Boolean isUsed;

    @TableField(value = "is_enabled")
    private Integer isUsing;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 版本号，每更新一次标准或标准细则加一
     */
    private Integer version;
    /**
     * 是否被编辑过（包括标准本身的编辑以及对应细则的增删改）
     */
    @TableField(exist = false)
    private Integer isUpdated;
    /**
     * 产品定位
     */
    @TableField(exist = false)
    private String positions;
    @TableField(exist = false)
    private String positionCategoryCodes;
    /**
     * 原建造标准id
     */
    private Long srcStandardId;
    @TableField(exist = false)
    private String srcStandardName;


    /**
     * 分类和定位
     */
    @TableField(exist = false)
    private List<StandardPositionDto> categoryAndPosition;
}
