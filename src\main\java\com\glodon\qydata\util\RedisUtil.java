package com.glodon.qydata.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import java.text.MessageFormat;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Redis工具类 方法的命名与Jedis官方命名保持一致
 * <AUTHOR>
 */
@Component
@Slf4j
public class RedisUtil {

    @Value("${spring.data.redis.host}")
    private   String hostname;
    @Value("${spring.data.redis.password}")
    private  String  password ;
    @Value("${spring.data.redis.port}")
    private  int port ;
    @Value("${spring.data.redis.jedis.pool.max-idle}")
    private  int maxIdle ;
    @Value("${spring.data.redis.maxTotal}")
    private  int maxTotal;
    @Value("${spring.data.redis.jedis.pool.max-wait}")
    private  long maxWait ;

    private JedisPool jedisPool = null;

    private static final int TIMES_TO_TRY = 5;

    private void init() {
        JedisPoolConfig config = new JedisPoolConfig();
        config.setMaxIdle(maxIdle);
        config.setMaxTotal(maxTotal);
        config.setMaxWaitMillis(maxWait);
        config.setTestOnBorrow( false );

        // 在还回 jedis pool 时，是否提前进行 validate 操作
        config.setTestOnReturn( false );
        // idle 时进行连接扫描
        config.setTestWhileIdle( true );
        // 表示 idle object evitor 两次扫描之间要sleep的毫秒数
        config.setTimeBetweenEvictionRunsMillis( 30000 );
        // 表示 idle object evitor 每次扫描的最多的对象数
        config.setNumTestsPerEvictionRun( 10 );
        // 表示一个对象至少停留在 idle 状态的最短时间，然后才能被 idle object evitor 扫描并驱逐；这一项只有在 timeBetweenEvictionRunsMillis 大于 0 时才有意义
        config.setMinEvictableIdleTimeMillis( 60000 );

        if (StringUtils.isBlank(password)) {
            jedisPool = new JedisPool(config, hostname, port, 3000);
        } else {
            jedisPool = new JedisPool(config, hostname, port, 3000, password);
        }

    }

    /**
     * 获取jedis
     *
     * @return
     */
    public Jedis getJedis() {

        if (jedisPool == null) {
            init();
        }
        Jedis jedis = null;
        boolean broken = true;
        int times = 1;
        while (broken && times <= TIMES_TO_TRY) {
            try {
                times++;
                jedis = jedisPool.getResource();
                broken = false;
                return jedis;
            } catch (Exception e) {
                broken = true;
                e.printStackTrace();
                if (times > TIMES_TO_TRY) {
                    log.error("缓存 redisUtil.getJedis error",e);
                }
            }
        }
        return jedis;
    }

    /**
     * 获取缓存标记
     * @param redisKeyEnum
     * @param field
     * @return
     */
    public boolean getCacheFlag(RedisKeyEnum redisKeyEnum, String field) {
        String flag = hget(redisKeyEnum.getKey(), field, redisKeyEnum.getDbIndex());
        if (StringUtils.isBlank(flag) || "false".equals(flag)) {
            return false;
        }
        return true;
    }

    /**
     * 更新缓存标记
     * @param redisKeyEnum
     * @param field
     */
    public void updateCacheFlag(RedisKeyEnum redisKeyEnum,String field){
        hset(redisKeyEnum.getKey(),field,"true",redisKeyEnum.getDbIndex());
    }

    /**
     *  将字符串保存到redis中
     * @param redisKeyEnum
     * @param value
     * @param params
     */
    public   void setString(RedisKeyEnum redisKeyEnum, String value, String... params){
        if(redisKeyEnum.getSeconds()<1){
            setString( MessageFormat.format(redisKeyEnum.getKey(),params),value,redisKeyEnum.getDbIndex());
        } else {
            setStringWithExpire( MessageFormat.format(redisKeyEnum.getKey(),params),value,redisKeyEnum.getSeconds(),redisKeyEnum.getDbIndex());
        }
    }

    /**
     *   * 获取存储在指定键中的值。如果键不存在，则返回null。 如果返回的值不是字符串，则返回错误
     * @param redisKeyEnum
     * @param params
     * @return 简单字符串回复。字符串值或键或null
     */
    public String getString(RedisKeyEnum redisKeyEnum,String... params) {
        return getString( MessageFormat.format(redisKeyEnum.getKey(),params),redisKeyEnum.getDbIndex());
    }

    /**
     * 查询字节数组
     * @param redisKeyEnum
     * @param params
     * @return
     */
    public byte[] getBytes(RedisKeyEnum redisKeyEnum,String... params) {
        return getBytes(MessageFormat.format(redisKeyEnum.getKey(),params),redisKeyEnum.getDbIndex());
    }


    /**
     * 将对象保存到redis中
     * @param redisKeyEnum
     * @param obj
     * @param params
     */
    public void setObject(RedisKeyEnum redisKeyEnum,Object obj,String... params){
        String s = JSON.toJSONString(obj);
        if(redisKeyEnum.getSeconds()<1){
            setString( MessageFormat.format(redisKeyEnum.getKey(),params),s,redisKeyEnum.getDbIndex());
        } else {
            setStringWithExpire( MessageFormat.format(redisKeyEnum.getKey(),params),s,redisKeyEnum.getSeconds(),redisKeyEnum.getDbIndex());
        }
    }

    /**
     *   * 获取存储在指定键中的值。如果键不存在，则返回null。 如果返回的值不是字符串，则返回错误
     * @param redisKeyEnum
     * @param T
     * @param params
     * @return 简单字符串回复。字符串值或键或null
     */
    public <T> T getObject(RedisKeyEnum redisKeyEnum, Class<T> T, String... params) {
        String value=  getString( MessageFormat.format(redisKeyEnum.getKey(),params),redisKeyEnum.getDbIndex());
        if(StringUtils.isNotBlank(value)){
            return JSON.parseObject(value,T);
        }
        return null;
    }


    /**
     * 将对象保存到redis中
     * @param redisKeyEnum
     * @param list
     * @param params
     */
    public void  setList(RedisKeyEnum redisKeyEnum,List list,String... params){
        String s = JSON.toJSONString(list);
        if(redisKeyEnum.getSeconds()<1){
            setString( MessageFormat.format(redisKeyEnum.getKey(),params),s,redisKeyEnum.getDbIndex());
        } else {
            setStringWithExpire( MessageFormat.format(redisKeyEnum.getKey(),params),s,redisKeyEnum.getSeconds(),redisKeyEnum.getDbIndex());
        }

    }


    /**
     *   * 获取存储在指定键中的值。如果键不存在，则返回null。 如果返回的值不是字符串，则返回错误
     * @param redisKeyEnum
     * @param E
     * @param params
     * @return 简单字符串回复。字符串值或键或null
     */
    public <E> List<E> getListObject(RedisKeyEnum redisKeyEnum,Class<E> E , String... params){
        String value=  getString( MessageFormat.format(redisKeyEnum.getKey(),params),redisKeyEnum.getDbIndex());
        if(StringUtils.isNotBlank(value)){
            return JSONArray.parseArray(value, E);
        }
        return null;
    }

    /**
     * 添加成员设置保存在key。如果成员已经存在，那么它忽略。如果键不存在，那么新的集合创建和成员被添加进去。如果没有设置储存在键的值，则返回一个错误。
     */
    public void addToSet(RedisKeyEnum redisKeyEnum, Set<String> set, String... params){
        String key = MessageFormat.format(redisKeyEnum.getKey(),params);
        if (redisKeyEnum.getSeconds() < 1) {
            addToSet(key, set, redisKeyEnum.getDbIndex());
        } else {
            addToSet(key, set, redisKeyEnum.getSeconds(),redisKeyEnum.getDbIndex());
        }
    }

    /**
     * 移除list中跟value相等的值
     * @param redisKeyEnum
     * @param count
     * @param value
     * @param params
     */
    public void removeListValue(RedisKeyEnum redisKeyEnum, Long count,String value, String... params){
        String key = MessageFormat.format(redisKeyEnum.getKey(),params);
        removeListValue(key,count,value,redisKeyEnum.getDbIndex());

    }

    /**
     * 在Redis键中设置指定的字符串值，并返回其旧值
     * @param value
     * @return 返回一个字符串，也就是键的旧值。 如果键不存在，则返回null
     */
    public String getSet(RedisKeyEnum redisKeyEnum, String value, String... params){
        String key = MessageFormat.format(redisKeyEnum.getKey(),params);
        return getSet(key, value ,redisKeyEnum.getDbIndex());
    }

    /**
     * 在存储的关键值的散列设置字段。如果键不存在，新的key由哈希创建。如果字段已经存在于哈希值那么将被覆盖。
     * @return
     */
    public Long hset(RedisKeyEnum redisKeyEnum, String field, String value, String... params){
        String key = MessageFormat.format(redisKeyEnum.getKey(), params);
        if(redisKeyEnum.getSeconds() < 1) {
            return hset(key, field, value, redisKeyEnum.getDbIndex());
        } else {
            return hsetWithExpire(key, field, value, redisKeyEnum.getSeconds(), redisKeyEnum.getDbIndex());
        }
    }

    /**
     * 获取map中field key的数值
     * @param redisKeyEnum
     * @param field
     * @param params
     * @return
     */
    public String hget(RedisKeyEnum redisKeyEnum,String field, String... params){
        String key = MessageFormat.format(redisKeyEnum.getKey(), params);
        return hget(key,field,redisKeyEnum.getDbIndex());
    }



    /**
     * 添加到List 在保存在key列表的头部
     * @param redisKeyEnum
     * @param list
     * @param params
     */
    public void addList(RedisKeyEnum redisKeyEnum, List<String> list, String... params){
        String key = MessageFormat.format(redisKeyEnum.getKey(),params);
        int second = redisKeyEnum.getSeconds();
        if (second < 1){
            addList(key, list, redisKeyEnum.getDbIndex());
        } else {
            addList(key, list, second, redisKeyEnum.getDbIndex());
        }
    }

    /**
     * 添加到List 在保存在key列表的头部
     * @param redisKeyEnum
     * @param str
     * @param params
     */
    public void addList(RedisKeyEnum redisKeyEnum, String str, String... params){
        String key = MessageFormat.format(redisKeyEnum.getKey(),params);
        int second = redisKeyEnum.getSeconds();
        if (second < 1){
            addList(key, str, redisKeyEnum.getDbIndex());
        } else {
            addList(key, str, second, redisKeyEnum.getDbIndex());
        }
    }

    /**
     *  获取List
     */
    public List<String> getList(RedisKeyEnum redisKeyEnum, String... params){
        return getList(MessageFormat.format(redisKeyEnum.getKey(), params),redisKeyEnum.getDbIndex());
    }
    /**
     * 删除List 删除，并返回保存列表在key的第一个元素
     */
    private String removeList(RedisKeyEnum redisKeyEnum, String... params){
        return removeList(MessageFormat.format(redisKeyEnum.getKey(), params),redisKeyEnum.getDbIndex());
    }

    /**
     * 检查List长度 将返回存储在key列表的长度。如果key不存在，它被解释为一个空列表，则返回0。当存储在关key的值不是一个列表，则会返回错误
     */
    public Long countList(RedisKeyEnum redisKeyEnum, String... params){
        return countList(MessageFormat.format(redisKeyEnum.getKey(), params),redisKeyEnum.getDbIndex());
    }

    /**
     * 弹出list中的最后一个元素
     */
    public String  rpop(RedisKeyEnum redisKeyEnum, String... params){
        String key = MessageFormat.format(redisKeyEnum.getKey(),params);
        return rpop(key,redisKeyEnum.getDbIndex());
    }

    /**
     * 将值插入到list的头部
     * @param redisKeyEnum
     * @param string
     * @param params
     * @return
     */
    public Long lPushList(RedisKeyEnum redisKeyEnum, String string, String... params) {
        String key  = MessageFormat.format(redisKeyEnum.getKey(), params);
        if (redisKeyEnum.getSeconds() < 1){
            return  lPushList(key,string,redisKeyEnum.getDbIndex());
        } else {
            return  lPushList(key,string,redisKeyEnum.getSeconds(),redisKeyEnum.getDbIndex());
        }
    }


    //-------------------------------------------------------------------------------------------------------------//


    private Long lPushList(String key , String string, int seconds,int indexDb) {
        Long result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            result = jedis.lpush(key, string);
            jedis.expire(key, seconds);
        } catch (Exception e) {
            returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            returnResource(jedis);

        }

        return result;
    }

    private Long lPushList(String key , String string,int indexDb) {
        Long result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            result = jedis.lpush(key, string);
        } catch (Exception e) {
            returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            returnResource(jedis);

        }

        return result;
    }


    /**
     * 用于在Redis键中设置永久性的字符串值 如果在键中设置了值，返回简单字符串回复：OK。如果值没有设置则返回 Null
     *
     * @param key
     * @param s
     * @param indexDb
     */
    private void setString(String key, String s, int indexDb) {
        Jedis jedis = getJedis();
        if(jedis==null){
            return ;
        }
        try {
            jedis.select(indexDb);
            jedis.set(key, s);

        } catch (Exception e) {
            returnBrokenResource(jedis);
            e.printStackTrace();
        } finally {
            returnResource(jedis);
        }
    }

    /**
     * 用于在Redis键中设置有时效的字符串值
     *
     * @param key
     * @param value
     * @param seconds
     * @param indexDb
     */
    public void setStringWithExpire(String key, String value, int seconds, int indexDb) {

        Jedis jedis = getJedis();
        if(jedis==null){
            return ;
        }
        try {
            jedis.select(indexDb);
            jedis.set(key, value);
            jedis.expire(key, seconds);
        } catch (Exception e) {
            log.error("缓存 redisUtil.setStringWithExpire error,e={},key={},value={}",e.getMessage(),key,value);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
    }

    /**
     * 获取存储在指定键中的值。如果键不存在，则返回null。 如果返回的值不是字符串，则返回错误
     *
     * @param key
     * @param indexDb
     * @return 简单字符串回复。字符串值或键或null
     */
    private String getString(String key, int indexDb) {
        boolean broken = true;
        int times = 1;
        String s = "";
        while (broken && times <= TIMES_TO_TRY) {
            times++;
            Jedis jedis = null;
            try {
                jedis = getJedis();
                jedis.select(indexDb);
                s = jedis.get(key);
                broken = false;
                return s;
            } catch (Exception e) {
                broken = true;
                returnBrokenResource(jedis);
                if (times > TIMES_TO_TRY) {
                    log.error("缓存 redisUtil.getString error,e={},key={},indexDb={}",e.getMessage(),key,indexDb);
                }
            } finally {
                returnResource(jedis);
            }
        }
        return s;
    }

    /**
     * 取出字节
     * @param key
     * @param indexDb
     * @return
     */
    private byte[] getBytes(String key, int indexDb) {
        boolean broken = true;
        int times = 1;
        byte[] bs = null;
        while (broken && times <= TIMES_TO_TRY) {
            times++;
            Jedis jedis = null;
            try {
                jedis = getJedis();
                jedis.select(indexDb);
                bs = jedis.get(key.getBytes());
                broken = false;
                return bs;
            } catch (Exception e) {
                broken = true;
                returnBrokenResource(jedis);
                if (times > TIMES_TO_TRY) {
                    log.error("缓存 redisUtil.getBytes error,e={},key={},indexDb={}",e.getMessage(),key,indexDb);
                }
            } finally {
                returnResource(jedis);
            }
        }
        return bs;
    }

    /**
     * 添加成员设置保存在key。如果成员已经存在，那么它忽略。如果键不存在，那么新的集合创建和成员被添加进去。如果没有设置储存在键的值，则返回一个错误。
     *
     * @param key
     * @param set
     * @param indexDb
     */
    private void addToSet(String key, Set<String> set, int indexDb) {
        Jedis jedis = getJedis();
        if(jedis==null){
            return ;
        }
        try {
            jedis.select(indexDb);
            for (String s : set) {
                jedis.sadd(key, s);
            }
        } catch (Exception e) {
            returnBrokenResource(jedis);
            log.error("缓存 redisUtil.addToSet error,e={},key={},set={},indexDb={}",e.getMessage(),key,set,indexDb);
        } finally {
            returnResource(jedis);
        }
    }

    private void addToSet(String key, Set<String> set,int seconds, int indexDb) {
        Jedis jedis = getJedis();
        if(jedis==null){
            return ;
        }
        try {
            jedis.select(indexDb);
            for (String s : set) {
                jedis.sadd(key, s);
            }
            jedis.expire(key,seconds);
        } catch (Exception e) {
            returnBrokenResource(jedis);
            log.error("缓存 redisUtil.addToSet error,e={},key={},set={}",e.getMessage(),key,set);
        } finally {
            returnResource(jedis);
        }
    }

    /**
     * 在Redis键中设置指定的字符串值，并返回其旧值
     *
     * @param key
     * @param value
     * @param indexDb
     * @return 返回一个字符串，也就是键的旧值。 如果键不存在，则返回null
     */
    private String getSet(String key, String value, int indexDb) {
        String s = "";
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.getSet(key, value);
        } catch (Exception e) {
            returnBrokenResource(jedis);
            log.error("缓存 redisUtil.getSet error,e={},key={},value={}",e.getMessage(),key,value);
            return null;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 在存储的关键值的散列设置字段。如果键不存在，新的key由哈希创建。如果字段已经存在于哈希值那么将被覆盖。
     *
     * @param key
     * @param field
     * @param value
     * @param indexdDb
     * @return 1 如果字段是哈希值和一个新字段被设置。
     * <p>
     * 0 如果字段已经存在于哈希并且值被更新。
     */
    private Long hset(String key, String field, String value, int indexdDb) {
        Long result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexdDb);
            result = jedis.hset(key, field, value);
        } catch (Exception e) {
            returnBrokenResource(jedis);
            log.error("缓存 redisUtil.hset error,e={},key={},field={},value={}",e.getMessage(),key,field,value);
        } finally {
            returnResource(jedis);

        }

        return result;
    }

    /**
     * 在存储的关键值的散列设置字段。如果键不存在，新的key由哈希创建。如果字段已经存在于哈希值那么将被覆盖。
     *
     * @param key
     * @param field
     * @param value
     * @param indexdDb
     * @return 1 如果字段是哈希值和一个新字段被设置。
     * <p>
     * 0 如果字段已经存在于哈希并且值被更新。
     */
    private Long hsetWithExpire(String key, String field, String value, int seconds, int indexdDb) {
        Long result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexdDb);
            result = jedis.hset(key, field, value);
            jedis.expire(key, seconds);
        } catch (Exception e) {
            log.error("缓存 redisUtil.hsetWithExpire error,e={},key={},field={},value={}",e.getMessage(),key,field,value);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);

        }

        return result;
    }

    /**
     * 添加到List(只新增)
     *
     * @param key
     * @return
     */
    private boolean addList(String key, List<String> list, int indexDb) {
        if (key == null || CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (String value : list) {
            addList(key, value, indexDb);
        }
        return true;
    }

    /**
     * 添加到List 在保存在key列表的头部插入所有指定的值。如果key不存在，则执行推操作之前创建的空列表。当key持有的值不是列表，则返回一个错误
     *
     * @param key
     * @param string
     * @param indexDb
     * @return 返回整数 - 推送操作后列表的长度
     */
    private Long addList(String key, String string, int indexDb) {
        Long result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            result = jedis.rpush(key, string);
        } catch (Exception e) {
            log.error("缓存 redisUtil.addList error,e={},key={},value={}",e.getMessage(),key,string);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);

        }

        return result;
    }

    /**
     * 添加到List(只新增)
     *
     * @param key
     * @return
     */
    private boolean addList(String key, List<String> list, int seconds, int indexDb) {
        if (key == null || CollectionUtils.isEmpty(list)) {
            return false;
        }
        for (String value : list) {
            addList(key, value, seconds, indexDb);
        }
        return true;
    }

    /**
     * 添加到List 在保存在key列表的头部插入所有指定的值。如果key不存在，则执行推操作之前创建的空列表。当key持有的值不是列表，则返回一个错误
     *
     * @param key
     * @param string
     * @param indexDb
     * @return 返回整数 - 推送操作后列表的长度
     */
    public Long addList(String key, String string, int seconds, int indexDb) {
        Long result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            result = jedis.rpush(key, string);
            jedis.expire(key, seconds);
        } catch (Exception e) {
            log.error("缓存 redisUtil.addList error,e={},key={},value={}",e.getMessage(),key,string);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);

        }

        return result;
    }


    /**
     * 获取List
     *
     * @param key
     * @param indexDb
     * @return
     */
    private List<String> getList(String key, int indexDb) {
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            return jedis.lrange(key, 0, -1);
        } catch (Exception e) {
            log.error("缓存 redisUtil.getList error,e={},key={},indexDb={}",e.getMessage(),key,indexDb);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return null;
    }

    /**
     * 删除List 删除，并返回保存列表在key的第一个元素
     *
     * @param key
     * @param indexDb
     * @return
     */
    private String removeList(String key, int indexDb) {
        String result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            result = jedis.lpop(key);
        } catch (Exception e) {
            log.error("缓存 redisUtil.removeList error,e={},key={},indexDb={}",e.getMessage(),key,indexDb);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return result;
    }

    /**
     * 检查List长度 将返回存储在key列表的长度。如果key不存在，它被解释为一个空列表，则返回0。当存储在关key的值不是一个列表，则会返回错误
     *
     * @param key
     * @param indexDb
     * @return
     */
    private Long countList(String key, int indexDb) {
        Long result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            result = jedis.llen(key);
        } catch (Exception e) {
            log.error("缓存 redisUtil.countList error,e={},key={},indexDb={}",e.getMessage(),key,indexDb);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);

        }
        return result;
    }

    /**
     * 删除，并返回列表保存在key的最后一个元素
     *
     * @param key
     * @param indexDb
     * @return
     */
    private String rpop(String key, int indexDb) {
        String result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            result = jedis.rpop(key);
        } catch (Exception e) {
            log.error("缓存 redisUtil.rpop error,e={},key={},indexDb={}",e.getMessage(),key,indexDb);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);

        }
        return result;
    }

    /**
     * 从list中删除value
     *
     * @param key
     * @param count 要删除个数
     * @param value
     * @return 成功删除的键的数量
     */
    private long removeListValue(String key, long count, String value, int indexDb) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return 0L;
        }
        try {
            jedis.select(indexDb);
            s = jedis.lrem(key, count, value);
        } catch (Exception e) {
            log.error("缓存 redisUtil.removeListValue error,e={},key={},value={}",e.getMessage(),key,value);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 修改list中的值
     *
     * @param key
     * @param index   list的下标
     * @param value
     * @param indexDb
     * @return
     */
    public String lset(String key, long index, String value, int indexDb) {
        String s = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.lset(key, index, value);
        } catch (Exception e) {
            log.error("缓存 redisUtil.lset error,e={},key={},value={}",e.getMessage(),key,value);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 修改list中的值，并设置过期时间
     *
     * @param key
     * @param index   list的下标
     * @param value
     * @param seconds 过期时间
     * @param indexDb
     * @return
     */
    public String lsetWithExpire(String key, long index, String value, int seconds, int indexDb) {
        String s = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.lset(key, index, value);
            jedis.expire(key, seconds);
        } catch (Exception e) {
            log.error("缓存 redisUtil.lsetWithExpire error,e={},key={},value={}",e.getMessage(),key,value);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 删除Redis中现有/存在的键
     *
     * @param key
     * @param indexDb
     * @return 成功删除的键的数量
     */
    public Long delete(String key, int indexDb) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.del(key);
        } catch (Exception e) {
            log.error("缓存 redisUtil.delete error,e={},key={},indexDb={}",e.getMessage(),key,indexDb);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 删除Redis中现有/存在的键
     *
     * @param redisKeyEnum
     * @param params
     * @return 成功删除的键的数量
     */
    public Long delete(RedisKeyEnum redisKeyEnum,String... params) {
        return delete(MessageFormat.format(redisKeyEnum.getKey(),params),redisKeyEnum.getDbIndex());
    }

    /**
     * 删除Redis中所有匹配的键
     * @param redisKeyEnum
     * @param params
     */
    public void deleteAll(RedisKeyEnum redisKeyEnum, String...  params) {
        String redisKey = MessageFormat.format(redisKeyEnum.getKey(),params);
        Set<String> keys = keys(redisKey,redisKeyEnum.getDbIndex());
        if(keys!=null && !keys.isEmpty()) {
            keys.forEach(x -> delete(x,redisKeyEnum.getDbIndex()));
        }
    }

    /**
     * 删除Redis中现有/存在的键
     *
     * @param indexDb
     * @param keys
     * @return 成功删除的键的数量
     */
    public Long delete(Set keys,int indexDb) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        if(CollectionUtils.isEmpty(keys)){
            return s;
        }
        try {
            //Set[] 转成String[]
            Object[] keysObj = keys.toArray();
            String[] result = new String[keysObj.length];
            int i=0;
            for (Object o : keysObj) {
                if(o instanceof String){
                    result[i]=(String)o;
                }
                i++;
            }

            jedis.select(indexDb);
            s = jedis.del(result);
        } catch (Exception e) {
            log.error("缓存 redisUtil.delete error,e={},keys={},indexDb={}",e.getMessage(),keys,indexDb);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 获取与字段中存储的键哈希相关联的值
     *
     * @param key
     * @param field
     * @param indexDb
     * @return 字符串值关联字段，或null当字段时不存在哈希或键不存在值
     */
    public String hget(String key, String field, int indexDb) {
        String result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            result = jedis.hget(key, field);
        } catch (Exception e) {
            log.error("缓存 redisUtil.hget error,e={},key={},field={}",e.getMessage(),key,field);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return result;
    }

    /**
     * 获取存储在键的散列的所有字段和值
     *
     * @param key
     * @param indexDb
     * @return
     */
    public Map<String, String> hgetAll(String key, int indexDb) {
        Map<String, String> result = null;
        boolean broken = true;
        int times = 1;
        while (broken && times <= TIMES_TO_TRY) {
            times++;
            Jedis jedis = getJedis();
            try {
                jedis.select(indexDb);
                result = jedis.hgetAll(key);
                broken = false;
            } catch (Exception e) {
                e.printStackTrace();
                broken = true;
                returnBrokenResource(jedis);
                if (times > TIMES_TO_TRY) {
                    log.error("缓存 redisUtil.hgetAll error,e={},key={}",e.getMessage(),key);
                }
            } finally {
                returnResource(jedis);
            }
        }
        return result;
    }

    /**
     * 检查哈希字段是否存在
     *
     * @param key
     * @param field
     * @param indexDb
     * @return 整数，1或0。 1, 如果哈希包含字段。 0 如果哈希不包含字段，或key不存在。
     */
    public boolean hexist(String key, String field, int indexDb) {
        boolean result = false;
        Jedis jedis = getJedis();
        if(jedis==null){
            return result;
        }
        try {
            jedis.select(indexDb);
            result = jedis.hexists(key, field);
        } catch (Exception e) {
            log.error("缓存 redisUtil.hexist error,e={},key={},field={}",e.getMessage(),key,field);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return result;
    }

    /**
     * 从存储在键散列删除指定的字段。如果没有这个哈希中存在指定的字段将被忽略。如果键不存在，它将被视为一个空的哈希与此命令将返回0
     *
     * @param key
     * @param field
     * @param indexDb
     * @return 整数，从散列中删除的字段的数量，不包括指定的但不是现有字段。
     */
    public Long hdel(String key, String field, int indexDb) {
        Long result = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            result = jedis.hdel(key, field);
        } catch (Exception e) {
            log.error("缓存 redisUtil.hdel error,e={},key={},field={}",e.getMessage(),key,field);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return result;
    }

    /**
     * 设置指定字段各自的值，在存储于键的散列。此命令将覆盖哈希任何现有字段。如果键不存在，新的key由哈希创建
     *
     * @param key
     * @param indexDb
     * @param hash
     * @return
     */
    public String hmset(String key, int indexDb, Map<String, String> hash) {
        String s = "";
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.hmset(key, hash);
        } catch (Exception e) {
            log.error("缓存 redisUtil.hmset error,e={},key={},Map={}",e.getMessage(),key,hash);
            returnBrokenResource(jedis);
            return null;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 存放Map，有有效期时间
     *
     * @param key
     * @param hash
     * @param expireSeconds
     * @param indexDb
     * @return
     */
    public String hmsetWithExpire(String key, Map<String, String> hash, int expireSeconds, int indexDb) {
        String s = "";
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.hmset(key, hash);
            jedis.expire(key, expireSeconds);
        } catch (Exception e) {
            log.error("缓存 redisUtil.hmsetWithExpire error,e={},key={},Map={}",e.getMessage(),key,hash);
            returnBrokenResource(jedis);
            return null;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 获取与存储在键散列指定的字段相关联的值。如果字段中哈希不存在，则null值被返回
     *
     * @param key
     * @param indexDb
     * @param fields
     * @return 回复数组，给定字段相关联的值的列表，与在请求时它们的顺序相同。
     */
    public List<String> hmget(String key, int indexDb, String... fields) {
        List<String> s = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.hmget(key, fields);
        } catch (Exception e) {
            log.error("缓存 redisUtil.hmget error,e={},key={},fields={}",e.getMessage(),key,fields);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 从存储在键散列删除指定的字段。如果没有这个哈希中存在指定的字段将被忽略。如果键不存在，它将被视为一个空的哈希与此命令将返回0
     *
     * @param key
     * @param indexDb
     * @param fields
     * @return 整数，从散列中删除的字段的数量，不包括指定的但不是现有字段
     */
    public Long hdel(String key, int indexDb, String... fields) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.hdel(key, fields);
        } catch (Exception e) {
            log.error("缓存 redisUtil.hdel error,e={},key={},fields={}",e.getMessage(),key,fields);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 增加存储在字段中存储由增量键哈希的数量。如果键不存在，新的key被哈希创建。如果字段不存在，值被设置为0之前进行操作。
     *
     * @param key
     * @param field
     * @param number
     * @param indexDb
     * @return 整数，字段的增值操作后的值
     */
    public Long hincrBy(String key, String field, int number, int indexDb) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.hincrBy(key, field, number);
        } catch (Exception e) {
            log.error("缓存 redisUtil.hincrBy error,e={},key={},number={}",e.getMessage(),key,number);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 从所述一组保存在键中删除指定的元素。如果成员不存在，则命令返回0，如果没有设置在关键存储的值，则返回一个错误
     *
     * @param key
     * @param value
     * @param indexDb
     */
    public void delToSet(String key, String value, int indexDb) {
        Jedis jedis = getJedis();
        if(jedis==null){
            return ;
        }
        try {
            jedis.select(indexDb);
            jedis.srem(key, value);
        } catch (Exception e) {
            log.error("缓存 redisUtil.delToSet error,e={},key={},value={}",e.getMessage(),key,value);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
    }

    /**
     * 元素已经存在于集存储在键的键或不是
     *
     * @param key
     * @param value
     * @param indexDb
     * @return 返回整型 1 如果该元素是集合的成员。 0 如果该元素不是集合的成员，或者如果键不存在
     */
    public boolean isExist(String key, String value, int indexDb) {
        Jedis jedis = getJedis();
        if(jedis==null){
            return false;
        }
        boolean s = false;
        try {
            jedis.select(indexDb);
            s = jedis.sismember(key, value);
        } catch (Exception e) {
            log.error("缓存 redisUtil.isExist error,e={},key={},value={}",e.getMessage(),key,value);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 检查键是否存在于Redis中
     *
     * @param key
     * @param indexDb
     * @return 如果键存在，返回 1 如果键不存在，返回 0
     */
    public boolean isKeyExist(String key, int indexDb) {
        Jedis jedis = getJedis();
        boolean s = false;
        if(jedis==null){
            return s;
        }
        try {
            jedis.select(indexDb);
            s = jedis.exists(key);
        } catch (Exception e) {
            log.error("缓存 redisUtil.isKeyExist error,e={},key={}",e.getMessage(),key);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 检查键是否存在于Redis中
     *
     * @param redisKeyEnum
     * @param params
     * @return 如果键存在，返回 1 如果键不存在，返回 0
     */
    public boolean isKeyExist(RedisKeyEnum redisKeyEnum,String... params) {
        return isKeyExist(MessageFormat.format(redisKeyEnum.getKey(),params),redisKeyEnum.getDbIndex());
    }

    /**
     * 所有的元素存在于集存储在指定的键
     *
     * @param key
     * @param indexDb
     * @return 数组返回该集合的所有元素
     */
    public Set<String> listAll(String key, int indexDb) {
        Set<String> s = null;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.smembers(key);
        } catch (Exception e) {
            log.error("缓存 redisUtil.listAll error,e={},key={},indexDb={}",e.getMessage(),key,indexDb);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 用于将存储在键上的数字按指定的值减少。如果键不存在，则在执行操作之前将其设置为0。
     * 如果键包含错误类型的值或包含无法表示为整数的字符串，则会返回错误
     *
     * @param key
     * @param number
     * @param indexDb
     * @return 返回一个整数，递减后键的值
     */
    public Long decrby(String key, long number, int indexDb) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.decrBy(key, number);
        } catch (Exception e) {
            log.error("缓存 redisUtil.decrby error,e={},key={}",e.getMessage(),key);
            returnBrokenResource(jedis);
            e.printStackTrace();
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 将键的整数值递增1。如果键不存在，则在执行操作之前将其设置为0。 如果键包含错误类型的值或包含无法表示为整数的字符串，则会返回错误。
     *
     * @param key
     * @param indexDb
     * @return 返回一个整数，增加后键的值
     */
    public Long incr(String key, int indexDb) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.incr(key);
        } catch (Exception e) {
            log.error("缓存 redisUtil.incr error,e={},key={}",e.getMessage(),key);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 将存储在键上的数字按指定的值增加。 如果键不存在，则在执行操作之前将其设置为0。如果键包含错误类型的值或包含无法表示为整数的字符串，则会返回错误。
     *
     * @param key
     * @param increnumber
     * @param indexDb
     * @return 返回一个整数，增加后键的值
     */
    public Long incrby(String key, long increnumber, int indexDb) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.incrBy(key, increnumber);
        } catch (Exception e) {
            log.error("缓存 redisUtil.incrby error,e={},key={},increnumber={}",e.getMessage(),key,increnumber);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 将所有元素的参数保存在指定为第一个参数的键名的HyperLogLog数据结构 可用作IP统计,和pfcount一起使用
     *
     * @param key
     * @param indexDb
     * @param value
     * @return 返回整数, 1 或 0.
     */
    public Long pfadd(String key, int indexDb, String... value) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return s;
        }
        try {
            jedis.select(indexDb);
            s = jedis.pfadd(key, value);
        } catch (Exception e) {
            log.error("缓存 redisUtil.pfadd error,e={},key={},value={}",e.getMessage(),key,value);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 获得近似的基数由存储在指定变量的HyperLogLog数据结构进行计算。如果键不存在，则返回0
     *
     * @param key
     * @param indexDb
     * @return 返回整型，近似独特的元素数量。 当PFCOUNT命令使用多个键，则返回HyperLogLog联合近似基数。
     */
    public Long pfcount(String key, int indexDb) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return s;
        }
        try {
            jedis.select(indexDb);
            s = jedis.pfcount(key);
        } catch (Exception e) {
            log.error("缓存 redisUtil.pfadd error,e={},key={},indexDb={}",e.getMessage(),key,indexDb);
            returnBrokenResource(jedis);
            return s;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 删除当前选择的数据块的键。此命令不会失败
     *
     * @param indexDb
     * @return OK
     */
    public String flushDB(Integer indexDb) {
        String s = "";
        Jedis jedis = getJedis();
        if(jedis==null){
            return s;
        }
        try {
            if(indexDb!=null) {
                jedis.select(indexDb);
            }
            s = jedis.flushDB();
        } catch (Exception e) {
            log.error("缓存 redisUtil.flushDB error,e={},indexDb={}",e.getMessage(),indexDb);
            returnBrokenResource(jedis);
            return null;
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     * 用来得到所选数据库key数量
     *
     * @param indexDb db
     * @return 整型, 数量
     */
    public Long size(int indexDb) {
        long s = 0L;
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            s = jedis.dbSize();
        } catch (Exception e) {
            log.error("缓存 redisUtil.size error,e={},indexDb={}",e.getMessage(),indexDb);
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }
        return s;
    }

    /**
     查找所有符合给定模式 pattern 的 key 。

     KEYS * 匹配数据库中所有 key 。
     KEYS h?llo 匹配 hello ， hallo 和 hxllo 等。
     KEYS h*llo 匹配 hllo 和 heeeeello 等。
     KEYS h[ae]llo 匹配 hello 和 hallo ，但不匹配 hillo 。
     特殊符号用 \ 隔开

     KEYS 的速度非常快，但在一个大的数据库中使用它仍然可能造成性能问题，如果你需要从一个数据集中查找特定的 key ，你最好还是用 Redis 的集合结构(set)来代替
     * @param pattern
     * @param indexDb
     * @return 符合给定模式的 key 列表
     */
    public Set<String> keys(String pattern,int indexDb) {
        Jedis jedis = getJedis();
        if(jedis==null){
            return null;
        }
        try {
            jedis.select(indexDb);
            return jedis.keys(pattern);
        } catch (Exception e) {
            log.error("缓存 redisUtil.keys error,e={},pattern={},indexDb={}",e.getMessage(),pattern,indexDb);
            returnBrokenResource(jedis);
            return null;
        } finally {
            returnResource(jedis);
        }
    }

    /**
     * 释放资源
     * @param jedis redis java 客户端
     */
    private void returnResource(Jedis jedis) {
        // jedisPool.returnResource(jedis);//jedis 2.7.0以后
        // jedis.close()替代了returnResource
        if (jedis != null) {
            try {
                jedis.close();
            }catch (Exception e){
                closeBrokenResource(jedis);
            }
        }
    }

    /**
     * 释放资源
     * @param jedis redis java 客户端
     */
    private void returnBrokenResource(Jedis jedis) {
        // jedisPool.returnBrokenResource(jedis);//jedis 2.7.0以后
        // jedis.close()替代了returnBrokenResource
        if (jedis != null) {
            try {
                jedis.close();
            }catch (Exception e){
                closeBrokenResource(jedis);
            }
        }
    }

    public void closeBrokenResource( Jedis jedis ) {
        try {
            jedisPool.returnBrokenResource( jedis );
        } catch ( Exception e ) {
            destroyJedis( jedis );
        }
    }

    /**
     * 在 Jedis Pool 以外强行销毁 Jedis
     */
    public static void destroyJedis( Jedis jedis ) {
        if ( jedis != null ) {
            try {
                jedis.quit();
            } catch ( Exception e ) {
            }

            try {
                jedis.disconnect();
            } catch ( Exception e ) {
            }
        }
    }

    /**
     * Acquires the lock only if it is free at the time of invocation.
     *
     * <p>Acquires the lock if it is available and returns immediately
     * with the value {@code true}.
     * If the lock is not available then this method will return
     * immediately with the value {@code false}.
     *
     * <p>A typical usage idiom for this method would be:
     * <pre> {@code
     * Lock lock = ...;
     * if (lock.tryLock()) {
     *   try {
     *     // manipulate protected state
     *   } finally {
     *     lock.unlock();
     *   }
     * } else {
     *   // perform alternative actions
     * }}</pre>
     * <p>
     * This usage ensures that the lock is unlocked if it was acquired, and
     * doesn't try to unlock if the lock was not acquired.
     *
     * @return {@code true} if the lock was acquired and
     * {@code false} otherwise
     * @param redisKeyEnum
     * <AUTHOR>
     * @date 2022/7/6 15:57
     */
    public boolean tryLock(String lockValueString, RedisKeyEnum redisKeyEnum, String... params) {
        Jedis jedis = getJedis();

        if (redisKeyEnum == null || jedis == null){
            return false;
        }

        try {
            String key = MessageFormat.format(redisKeyEnum.getKey(), params);
            jedis.select(redisKeyEnum.getDbIndex());

            //SET IF NOT EXIST，而且还是原子的
            //key: 来当锁 value: 解铃还须系铃人,用于验证客户端  nxxx: NX  (SET IF NOT EXIST)，就是:key不存在则执行set操作
            //expx: EX:秒  PX:毫秒 time: 间隔，单位就是参数4
            redis.clients.jedis.params.SetParams setParams = redis.clients.jedis.params.SetParams.setParams().nx().ex(redisKeyEnum.getSeconds());
            String code = jedis.set(key, lockValueString, setParams);

            return "OK".equals(code);
        }catch (Exception e){
            log.error("Redis分布式锁tryLock失败,{}", e.getMessage());
            returnBrokenResource(jedis);
        }finally {
            returnResource(jedis);
        }
        return false;
    }

    /**
     * @description: 抢锁，失败重试n次
     * @param lockValueString
     * @param redisKeyEnum
     * @param params
     * @return boolean
     * <AUTHOR>
     * @date 2022/7/8 13:59
     */
    public boolean tryLockRetry(String lockValueString, RedisKeyEnum redisKeyEnum, String... params) {

        for (int i = 0; i < 10; i++) {
            if (tryLock(lockValueString, redisKeyEnum, params)) {
                return true;
            }else {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.error("tryLockRetry-sleep异常", e);
                }
            }
        }

        return false;
    }

    public String generateLockValue() {
        String id = SnowflakeIdUtils.getSystemUuid();
        long threadId = Thread.currentThread().getId();
        return id + ":" + threadId;
    }

    /**
     * Releases the lock.
     *
     * <p><b>Implementation Considerations</b>
     *
     * <p>A {@code Lock} implementation will usually impose
     * restrictions on which thread can release a lock (typically only the
     * holder of the lock can release it) and may throw
     * an (unchecked) exception if the restriction is violated.
     * Any restrictions and the exception
     * type must be documented by that {@code Lock} implementation.
     * <AUTHOR>
     * @date 2022/7/6 15:57
     */
    public void unlock(String lockValueString, RedisKeyEnum redisKeyEnum, String... params) {
        Jedis jedis = getJedis();

        if (redisKeyEnum == null || jedis == null){
            return;
        }

        try{
            String key = MessageFormat.format(redisKeyEnum.getKey(), params);
            jedis.select(redisKeyEnum.getDbIndex());

            String luaScript = "if redis.call('get',KEYS[1]) == ARGV[1] then " +
                    "return redis.call('del',KEYS[1]) else return 0 end";

            jedis.evalsha(jedis.scriptLoad(luaScript), Collections.singletonList(key), Collections.singletonList(lockValueString));
        }catch (Exception e){
            log.error("Redis分布式锁解锁失败,{}", e.getMessage());
            returnBrokenResource(jedis);
        } finally {
            returnResource(jedis);
        }

    }

}
