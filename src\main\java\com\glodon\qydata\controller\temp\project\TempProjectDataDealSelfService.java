package com.glodon.qydata.controller.temp.project;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.controller.temp.TempConstant;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.mapper.standard.projectOrContractInfo.StandardsProjectInfoMapper;
import com.glodon.qydata.mapper.temp.TempStandardsProjectInfoMapper;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 用户暂存----在【工程分类】下方增加一行【产品定位】，默认内置枚举值：A档、B档、C档、D档，默认启用和必填    todo 线上执行后，可直接删除掉
 * @date 2022/7/18 17:55
 */
@Service
@Slf4j
public class TempProjectDataDealSelfService {
    @Autowired
    private StandardsProjectInfoMapper standardsProjectInfoMapper;

    @Autowired
    private TempStandardsProjectInfoMapper tempStandardsProjectInfoMapper;

    /**
     * @description: 获取需要处理的globalId
     * @param
     * @return java.util.List<java.lang.String>
     * <AUTHOR>
     * @date 2022/7/18 18:06
     */
    public List<StandardsProjectInfoEntity> getAllGlobalIdList(){
        // 查询已经有暂存数据，需要新增【产品定位】的用户
        QueryWrapper<StandardsProjectInfoEntity> allGlobalIdQuery = new QueryWrapper<>();
//        allGlobalIdQuery.select("global_id,customer_code,SUM(`name`='" + TempConstant.NAME + "') conditionSum").lambda()
//                .eq(StandardsProjectInfoEntity::getStandardDataType, Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO)
//                .groupBy(StandardsProjectInfoEntity::getGlobalId).having("conditionSum < 1");

        return standardsProjectInfoMapper.selectList(allGlobalIdQuery);
    }

    /**
     * @description: 分用户执行
     * @param globalId
     * @return void
     * <AUTHOR>
     * @date 2022/7/18 18:08
     */
    @Transactional(rollbackFor = Exception.class)
    public void dealData(String customerCode, String globalId, Long originId){
        // 获取用户的暂存数据
        List<StandardsProjectInfoEntity> selfProjectInfo = this.getSelfProjectInfo(globalId);

        if (CollectionUtils.isEmpty(selfProjectInfo)){
            return;
        }

        // '名称->类型'  为key的map
        Map<String, Integer> nameTypeMap = selfProjectInfo.parallelStream()
                .collect(Collectors.toMap(x -> x.getName() + "->" + x.getTypeCode(), StandardsProjectInfoEntity::getOrd, (v1, v2) -> v2));

        // 已经存在【产品定位】，跳过
        if (nameTypeMap.containsKey(TempConstant.MAP_KEY)){
            return;
        }

        // 构建新增实体
        StandardsProjectInfoEntity selfProjectInfoEntity = this.buildProjectInfoEntitySelf(customerCode, globalId, originId);

        Integer ordAndMove = this.getOrdAndMove(nameTypeMap, selfProjectInfo, globalId);
        selfProjectInfoEntity.setOrd(ordAndMove);

        standardsProjectInfoMapper.insert(selfProjectInfoEntity);
    }

    /**
     * @description: 获取用户的项目信息
     * @param globalId
     * @return java.util.List<com.glodon.qydata.entity.standard.projectOrContractInfo.TempStandardsProjectInfoEntity>
     * <AUTHOR>
     * @date 2022/7/18 18:09
     */
    private List<StandardsProjectInfoEntity> getSelfProjectInfo(String globalId){
        // 查询该用户的项目信息，排序字段只维护未删除的
        QueryWrapper<StandardsProjectInfoEntity> customerProjectInfoQuery = new QueryWrapper<>();
//        customerProjectInfoQuery.select("id, name, type_code, ord").lambda()
//                .eq(StandardsProjectInfoEntity::getGlobalId, globalId)
//                .eq(StandardsProjectInfoEntity::getStandardDataType, Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO)
//                .eq(StandardsProjectInfoEntity::getIsDeleted, Constants.StandardsProjectOrContractInfoConstants.IS_NOT_DELETED);
        return standardsProjectInfoMapper.selectList(customerProjectInfoQuery);
    }

    /**
     * @description: 构建新增实体self
     * @param globalId
     * @return com.glodon.qydata.entity.standard.projectOrContractInfo.TempStandardsProjectInfoEntity
     * <AUTHOR>
     * @date 2022/7/18 18:09
     */
    private StandardsProjectInfoEntity buildProjectInfoEntitySelf(String customerCode, String globalId, Long originId){
        // 数据实体 默认启用和必填
        StandardsProjectInfoEntity standardsProjectInfoEntity = new StandardsProjectInfoEntity();
//        standardsProjectInfoEntity.setId(SnowflakeIdUtils.getNextId());
//        standardsProjectInfoEntity.setName(TempConstant.NAME);
//        standardsProjectInfoEntity.setTypeCode(TempConstant.TYPE_CODE);
//
//        standardsProjectInfoEntity.setSelectList(TempConstant.SELECT_LIST);
//        standardsProjectInfoEntity.setIsUsing(Constants.StandardsProjectOrContractInfoConstants.IS_ENABLE);
//        standardsProjectInfoEntity.setIsRequired(TempConstant.IS_REQUIRED);
//        standardsProjectInfoEntity.setCustomerCode(customerCode);
//        standardsProjectInfoEntity.setStandardDataType(Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO);
//
//        standardsProjectInfoEntity.setGlobalId(globalId);
//        standardsProjectInfoEntity.setOriginId(originId);
        return standardsProjectInfoEntity;
    }

    /**
     * @description: 【产品定位】放在【工程分类】后，后面的ord字段都+1
     * @param targetOrd
     * @param globalId
     * @return void
     * <AUTHOR>
     * @date 2022/7/19 9:23
     */
    private void moveOrd(Integer targetOrd, String globalId){
        UpdateWrapper<StandardsProjectInfoEntity> updateWrapper = new UpdateWrapper<>();
//        updateWrapper.setSql("ord = ord + 1").lambda()
//                .eq(StandardsProjectInfoEntity::getGlobalId, globalId)
//                .eq(StandardsProjectInfoEntity::getStandardDataType, Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO)
//                .eq(StandardsProjectInfoEntity::getIsDeleted, Constants.StandardsProjectOrContractInfoConstants.IS_NOT_DELETED)
//                .gt(StandardsProjectInfoEntity::getOrd, targetOrd);

        standardsProjectInfoMapper.update(null, updateWrapper);
    }

    /**
     * @description: 获取【产品定位】的排序，如果需要，后移其他
     * @param nameTypeMap
     * @param selfProjectInfo
     * @param globalId
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2022/7/19 10:17
     */
    private Integer getOrdAndMove(Map<String, Integer> nameTypeMap, List<StandardsProjectInfoEntity> selfProjectInfo, String globalId){
        int ord;
        // 处理ord--存在【工程分类】，放在工程分类后面；不存在【工程分类】，放在最后面
        if (nameTypeMap.containsKey(TempConstant.TARGET)){
            Integer targetOrd = nameTypeMap.get(TempConstant.TARGET);
            ord = targetOrd + 1;
            this.moveOrd(targetOrd, globalId);
        }else {
            Integer maxOrd = selfProjectInfo.parallelStream().map(StandardsProjectInfoEntity::getOrd).max(Comparator.comparingInt(o -> o)).orElse(0);
            ord = maxOrd + 1;
        }
        return ord;
    }

    /**
     * @description: 获取企业级【产品定位】对应的id
     * @param
     * @return java.util.Map<java.lang.String, java.lang.Long>
     * <AUTHOR>
     * @date 2022/7/21 11:28
     */
    public Map<String, Long> getOriginIdMap(){
        QueryWrapper<TempStandardsProjectInfoEntity> customerOriginIdQuery = new QueryWrapper<>();
        customerOriginIdQuery.select("id,customer_code").lambda()
                .eq(TempStandardsProjectInfoEntity::getStandardDataType, Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO)
                .eq(TempStandardsProjectInfoEntity::getName, TempConstant.NAME)
                .eq(TempStandardsProjectInfoEntity::getTypeCode, TempConstant.TYPE_CODE);

        List<TempStandardsProjectInfoEntity> customerOriginIdList = tempStandardsProjectInfoMapper.selectList(customerOriginIdQuery);

        if (CollectionUtils.isNotEmpty(customerOriginIdList)){
            return customerOriginIdList.parallelStream()
                    .collect(Collectors.toMap(TempStandardsProjectInfoEntity::getCustomerCode, TempStandardsProjectInfoEntity::getId, (v1, v2) -> v2));
        }

        return Maps.newHashMap();
    }
}
