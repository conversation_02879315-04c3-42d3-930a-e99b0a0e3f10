package com.glodon.qydata.config.idsequence;

import com.glodon.cost.bdp.component.distributedlock.core.IDistributedLock;
import com.glodon.cost.bdp.component.distributedlock.core.IDistributedLockFactory;

import javax.sql.DataSource;
import java.util.concurrent.TimeUnit;

/**
 * DB分布式锁的工厂
 *
 * @author: xinzp
 * @date: 2022/3/11
 */
public class DistributedLockMysqlAdaptorFactory implements IDistributedLockFactory {

    private DataSource dataSource;

    public DistributedLockMysqlAdaptorFactory(DataSource dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public IDistributedLock getDistributedLock(String lockName) {
        DistributedLockMysqlAdaptor distributedLockMysqlAdaptor = new DistributedLockMysqlAdaptor(lockName, this.dataSource);
        try {
            boolean lock = distributedLockMysqlAdaptor.lock(5, TimeUnit.SECONDS);
            if (lock) {
                return distributedLockMysqlAdaptor;
            } else {
                throw new RuntimeException("Get DistributedLock Exception");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
