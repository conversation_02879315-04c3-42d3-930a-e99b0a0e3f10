package com.glodon.qydata.config.thread;

import lombok.extern.log4j.Log4j2;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池管理类
 *
 * <AUTHOR>
 */
@SuppressWarnings("DuplicatedCode")
@Configuration
@EnableAsync
@Log4j2
public class ThreadExecutorPool {

    private static final String THREAD_NAME_PREFIX = "async-service";


    @Bean("delSelfExecutor")
    public ThreadPoolTaskExecutor refreshDataServiceExecutor() {
        log.info("start delSelfExecutor");
        log.info("delSelfExecutor核心线程数:{}", 7);
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //配置核心线程数
        executor.setCorePoolSize(10);
        //配置最大线程数
        executor.setMaxPoolSize(12);
        //配置队列大小
        executor.setQueueCapacity(999999);
        //配置线程池中的线程的名称前缀
        executor.setThreadNamePrefix(THREAD_NAME_PREFIX);
        executor.setKeepAliveSeconds(180);
        // rejection-policy：当pool已经达到max size的时候，如何处理新任务
        // CALLER_RUNS：不在新线程中执行任务，而是有调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(runnable -> runnable);
        //执行初始化
        executor.initialize();
        return executor;
    }
}
