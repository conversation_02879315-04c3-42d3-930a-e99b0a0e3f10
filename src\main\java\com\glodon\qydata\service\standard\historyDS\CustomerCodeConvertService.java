package com.glodon.qydata.service.standard.historyDS;

import com.glodon.qydata.service.system.IGlodonUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description: 市场化计价中的企业id转换成企业编码
 * @date 2022/1/21 17:52
 */
@Service
@Slf4j
public class CustomerCodeConvertService {

    @Autowired
    private IGlodonUserService glodonUserService;

    /**
     * @description: 市场化计价中企业id为企业下最高管理员的globalId
     * @param globalId
     * @return java.lang.String
     * <AUTHOR>
     * @date 2022/1/21 18:03
     */
    public String getCustomerCode(String globalId){
        return glodonUserService.getCustomerCode(globalId);
    }
}
