package com.glodon.qydata.common.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 公共的常量类
 */
public class Constants {

    public static final String HEADER_USER_ID = "globalId";
    public static final String HEADER_TOKEN = "token";
    public static final String TENANT_ID = "tenantId";

    /**
     * 默认列表页每页显示条数
     */
    public static final Integer DEFAULT_LIST_PAGE_SIZE = 10;

    /**
     * 批量保存的数量
     */
    public static final int BATCH_INSERT_SIZE = 500;
    /**
     * 数据库批量插入1000常量值
     */
    public static final int DB_BATCH_COUNT_1000 = 1000;

    /**
     * 系统：0：禁用  1：启用
     */
    public static final Integer STATUS_VALID = 1;
    public static final Integer STATUS_INVALID = 0;

    /**
     * 0：未删除  1：已删除
     */
    public static final Integer DEL_STATUS_NO_DEL = 0;
    public static final Integer DEL_STATUS_IS_DEL = 1;

    /**
     * 0：定位  1：业态
     */
    public static final Integer CATEGORY = 1;
    public static final Integer POSITION = 0;

    /**
     * 插入的建造标准行数据为兄弟节点
     */
    public static final Integer BROTHER = 1;

    /**
     * 原始结构
     */
    public static final Integer ORIGINAL_STRUCTURE = 0;


    /**
     * 上移
     */
    public static final Integer MOVE_UP = 1;
    public static final String MOVE_TYPE_UP = "up";
    public static final String MOVE_TYPE_DOWN = "down";

    public static final String KEY_CUSTOMER_CODE = "customerCode";
    public static final String KEY_QY_CODE = "qyCode";
    public static final String KEY_QY_CODE_OLD = "qyCodeOld";
    public static final String KEY_QY_FLAG = "qyFlag";

    public static final int MAX_NAME_LENGTH = 30;
    public static final int MAX_REMARK_LENGTH = 200;
    public static final int STANDARD_MAX_NAME_LENGTH = 200;
    public static final int MAX_DESC_LENGTH = 200;
    public static final int MAX_UNIT_LENGTH = 10;
    public static final int MAX_OPTION_LENGTH = 1000;
    public static final int STANDARD_MAX_OPTION_LENGTH = 2000;
    public static final int MAX_OPTION_NUM = 60;
    public static final String DESC_PREFIX = "标准说明";

    /**
     * 工程分类
     */
    public static final class CategoryConstants{
        /**
         * 工程分类  新增类型(0:新增分类，1:新增子分类,2:修改)
         */
        public static final Integer ADD_TYPE_THIS = 0;
        public static final Integer ADD_TYPE_CHILD = 1;
        public static final Integer ADD_TYPE_UPDATE = 2;

        /**
         * 工程分类  等级
         */
        public static final Integer LEVEL_1 = 1;
        public static final Integer LEVEL_2 = 2;
        public static final Integer LEVEL_3 = 3;
        public static final Integer LEVEL_4 = 4;

        /**
         * 工程分类  单等级长度    长度为3
         */
        public static final int SINGLE_LEVEL_LEN = 3;

        /**
         * 工程分类  各等级对应长度    如001，长度为3    001002，长度为6
         */
        public static final int LEVEL_1_LEN = 3;
        public static final int LEVEL_2_LEN = 6;
        public static final int LEVEL_3_LEN = 9;
        public static final int LEVEL_4_LEN = 12;

        /**
         * 工程分类  默认初始code
         */
        public static final String DEFAULT_CODE = "001";

        /**
         * 工程分类  新增一级分类默认归属房建模板
         */
        public static final String DEFAULT_CATEGORY_TYPE_CODE = "8001";
        public static final String DEFAULT_CATEGORY_TYPE_NAME = "房建模板";

        /**
         * 工程分类  上移下移
         */
        public static final String OPERATE_DOWN = "down";
        public static final String OPERATE_UP = "up";

        /**
         * 工程分类  是1 否0
         */
        public static final int WHETHER_TRUE = 1;
        public static final int WHETHER_FALSE = 0;

        /**
         * 工程分类  更新操作类型(0:修改名称，1:启用禁用，2:修改备注)
         */
        public static final int UPDATE_NAME = 0;
        public static final int UPDATE_ENABLE = 1;
        public static final int UPDATE_REMARK = 2;

        /**
         * 工程分类-对外 返回数据的结构（0：原始结构；1：树结构）
         */
        public static final Integer RESULT_ORIGINAL = 0;
        public static final Integer RESULT_TREE = 1;

        /**
         * 系统内置专业，企业编码为-100
         */
        public static final String SYSTEM_CUSTOMER_CODE = "-100";
    }

    /**
     * 企业标准数据-项目/合同信息静态值
     */
    public static class StandardsProjectOrContractInfoConstants {
        /**
         * 上移标识
         */
        public static final Integer MOVE_UP = 1;
        /**
         * 下移标识
         */
        public static final Integer MOVE_DOWN = 2;
        /**
         * 内置数据企业编码
         */
        public static final String BUILT_IN_DATA_CUSTOMER_CODE = "-1";
        /**
         * 未被删除
         */
        public static final Integer IS_NOT_DELETED = 0;
        /**
         * 已被删除
         */
        public static final Integer IS_DELETED = 1;
        /**
         * 项目信息
         */
        public static final Integer PROJECT_INFO = 1;
        /**
         * 合同信息
         */
        public static final Integer CONTRACT_INFO = 2;
        /**
         * 项目信息的内置不可编辑数据名称集合
         */
        public static final List<String> PROJECT_INFO_BUILT_IN_NON_EDITABLE_NAME_LIST = Arrays.asList("项目名称", "项目地点",
                "项目规模", "工程分类");
        /**
         * 合同信息的内置不可编辑数据名称集合
         */
        public static final List<String> CONTRACT_INFO_BUILT_IN_NON_EDITABLE_NAME_LIST = Arrays.asList("工程名称", "造价类型", "工程规模", "工程地点", "工程分类");
        /**
         * 合同信息中前端不展示的数据名称
         */
        public static final String INVISIBLE_INFO_NAME = "工程分类";
        /**
         * 是否启用：已启用
         */
        public static final Integer IS_ENABLE = 1;

        public static final String BUILT_IN_FLAG = "-1";

        /**
         * 查询项目信息是否展示分组：1不展示；2展示
         */
        public static final Integer SHOW_ALL = 2;

        /**
         * 查询项目信息是否展示分组：1不展示；2展示
         */
        public static final Integer SHOW_ITEM = 1;

        public static final Integer ITEM = 1;

        public static final Integer GROUP = 2;

        /**
         * 按照树结构查询：1树结构；2原始结构
         */
        public static final Integer SHOW_TREE = 1;

        /**
         * 按照树结构查询：1树结构；2原始结构
         */
        public static final Integer SHOW_ORIGINAL = 2;

        /**
         * 项目信息升级
         */
        public static final Integer PROMOTION = 1;

        /**
         * 项目信息降级
         */
        public static final Integer DEMOTION = 2;

        /**
         * 项目信息pid为-1
         */
        public static final Long FIRST_LEVEL_PID = -1L;
    }

    /**
     * @description: 标准打通--工程特征名称类型表及计算口径静态类
     * <AUTHOR>
     * @date 2021/10/25 11:49
     */
    public static class ZbExpressionConstants {

        /**
         * 数据类型，对应dictionary表中type_name为数据类型的记录，text文本类，number数值类，date日期类，select单选类，selects多选类
         */
        public static final String TYPE_TEXT = "text";
        public static final String TYPE_NUMBER = "number";
        public static final String TYPE_DATE = "date";
        public static final String TYPE_SELECT = "select";
        public static final String TYPE_SELECTS = "selects";

        /**
         * 是1 否0
         */
        public static final int WHETHER_TRUE = 1;
        public static final int WHETHER_FALSE = 0;

        /**
         * 启用 1 不启用 0
         */
        public static final int OPERATE_ENABLE = 1;
        public static final int OPERATE_DISABLE = 0;

    }

    /**
     * @description: 标准打通--工程特征全局静态类
     * <AUTHOR>
     * @date 2021/10/25 11:49
     */
    public static class ZbFeatureConstants {
        /**
         * 分类json的处理类型， 0 剔除掉指定一级分类下的所有分类， 1 只要某一分类下的
         */
        public static final int JSON_DEAL_1 = 1;

        /**
         * 分类json的处理类型， 0 剔除掉指定一级分类下的所有分类， 1 只要某一分类下的
         */
        public static final int JSON_DEAL_0 = 0;

        /**
         * 工程分类  上移下移
         */
        public static final String OPERATE_DOWN = "down";
        public static final String OPERATE_UP = "up";

        /**
         * 是1 否0
         */
        public static final int WHETHER_TRUE = 1;
        public static final int WHETHER_FALSE = 0;

        /**
         * 视图类型， 0 专业视图 1 分类视图
         */
        public static class ViewType{
            /** 0 专业视图 */
            public static final Integer TRADE_VIEW = 0;
            /** 1 分类视图 */
            public static final Integer CATEGORY_VIEW = 1;
        }

        /**
         * 系统内置专业，企业编码为-100
         */
        public static final String SYSTEM_CUSTOMER_CODE = "-100";

        // 默认工程分类类别
        public static final int CATEGORY_DEFAULT_TYPE = 1;
    }

    /**
     * @description: 标准打通--专业数据全局静态类
     * @author: lijj-h
     * @create: 2021-07-02 09:20
     */
    public static class ZbStandardsTradeConstants {

        /**
         * 内置专业被引用，限制最大次数为20
         */
        public static final Integer TRADE_REFER_MAX_COUNT = 20;
        /**
         * 内置专业目前设置17个
         */
        public static final Integer TRADE_SYSTEM_COUNT = 17;

        /**
         * 系统内置专业，企业编码为-100
         */
        public static final String SYSTEM_CUSTOMER_CODE = "-100";

        /**
         * 默认数据类别
         */
        public static final Integer CATEGORY_DEFAULT_TYPE = 1;

        /**
         * 专业类别， 0 系统内置 1 用户自定义
         */
        public static class TradeType{
            /**
             * 0 系统内置
             */
            public static final Byte SYSTEM_TRADE = 0;
            /**
             * 1 用户自定义
             */
            public static final Byte COMPANY_TRADE = 1;
        }

        /**
         * 专业是否删除， 0 未删除 1 删除
         */
        public static class DeletedStatus{
            /**
             * 0  未删除
             */
            public static final Integer NO_DELETED = 0;
            /**
             * 1  删除
             */
            public static final Integer DELETED = 1;

        }

        public static class UpOrDownOperation {
            /**
             * 向上移
             */
            public static final String UP = "UP";

            /**
             * 下移
             */
            public static final String DOWN = "DOWN";
        }

        public static class EnableStatus {
            /**
             * 启用
             */
            public static final int ENABLED = 1;

            /**
             * 禁用
             */
            public static final int DIS_ENABLED = 0;
        }
    }

    /**
     * @description: 标准打通--专业数据全局静态类
     * @author: lijj-h
     * @create: 2021-07-02 09:20
     */
    public static class ZbStandardsMainQuantityConstants {


        /**
         * 系统内置专业，企业编码为-100
         */
        public static final String SYSTEM_CUSTOMER_CODE = "-100";

        /**
         * 其他专业  编码
         */
        public static final String TRADE_CODE_OTHER = "1100";

        /**
         * 主要量指标类别， 0 系统内置 1 用户自定义
         */
        public static class MainQuantityType{
            /**
             * 0 系统内置
             */
            public static final Byte SYSTEM_TYPE = 0;
            /**
             * 1 用户自定义
             */
            public static final Byte COMPANY_TYPE = 1;
        }

        /**
         * 主要量是否删除， 0 未删除 1 删除
         */
        public static class DeletedStatus{
            /**
             * 0  未删除
             */
            public static final Integer NO_DELETED = 0;
            /**
             * 1  删除
             */
            public static final Integer DELETED = 1;

        }

        /**
         * 操作类型 新增 0  更新  1
         */
        public static class HANDLE_FLAG{
            /**
             * 新增 0
             */
            public static final Integer INSERT = 0;
            /**
             * 更新
             */
            public static final Integer UPDATE = 1;
        }

        public static class MoveUpDownFlag {
            /**
             * 上移
             */
            public static final Integer MOVE_UP = 1;
            /**
             * 下移
             */
            public static final Integer MOVE_DOWN = 2;
        }
    }
    public static final String HEADER_SG_TOKEN = "sgToken";

    public static final class BuildStandardConstants{
        /**
         * 产品定位类型   0:定位，1工程分类
         */
        public static final Integer POSITION_GRADE = 0;
        /**
         * 产品定位类型  0:定位，1工程分类
         */
        public static final Integer POSITION_CATEGORY = 1;

        public static final String OPERATE_TYPE_RENAME = "rename";
        public static final String OPERATE_TYPE_SET_USING = "setUsing";
        public static final String OPERATE_TYPE_SET_USING_WITH_CHECK = "setUsingWithCheck";
    }

    /**
     * 升序降序  0 升序 1 降序
     */
    public static final int ORDER_ASC = 0;
    public static final int ORDER_DESC = 1;

    /**
     * 是否默认：：是【1】、否【0】
     */
    public static final Integer DEFAULT = 1;

    /**
     * 0：否  1：是
     */
    public static final Integer WHETHER_TRUE = 1;
    public static final Integer WHETHER_FALSE = 0;

    /**
     * 地区范围：ALL-所有；ONLY_VALID-仅有效
     */
    public static final String AREA_ALL = "ALL";
    public static final String AREA_ONLY_VALID = "ONLY_VALID";
    /**
     * 数据是否是无效数据
     * 0有效，1无效
     */
    public static class DataInvalid {
        public static final Integer VALID = 0;
        public static final Integer INVALID = 1;
    }

    public static class Assets {
        public static final String CALLBACK_SUC_CODE = "00";
        public static final String CALLBACK_SUC_MSG = "消息处理成功";

        public static final String CALLBACK_ERO_CODE = "99";

        public static final String CALLBACK_ERO_MSG = "消息处理失败：";

        public static final String PRODUCT_CODE_DCOST = "dcost";

    }

    public static class BeanName {
        public static final String BASE_CONTROLLER = "baseController";
        public static final String EXPRESSION_MOVE_SERVICE = "expressionMoveService";
    }
    public static final String SELF_DATA_NO_FIND = "该企业下其他用户已发布了最新数据，请刷新后再试";

    public static final String SYSTEM_ADMIN = "SystemAdmin";

    public static final class CategoryTagConstants {

        public static final String COLUM_CODE_CATEGORY_CODE = "categoryCode";

        public static final String COLUM_CODE_CATEGORY_CODE_NAME = "一级分类编码";

        public static final String COLUM_CODE_CATEGORY_NAME = "categoryName";

        public static final String COLUM_CODE_CATEGORY_NAME_NAME = "一级分类";

        /**
         * 系统内置专业，企业编码为-100
         */
        public static final String SYSTEM_CUSTOMER_CODE = "-100";

        /**
         * 工程分类列表类型，0：工程分类
         */
        public static final Integer DATA_TYPE_CATEGORY = 0;

        /**
         * 工程分类列表类型，1：标签
         */
        public static final Integer DATA_TYPE_TAG = 1;

        /**
         * 工程分类列表查询方式，1：查询带着标签
         */
        public static final String QUERY_TYPE_TAG = "1";

        /**
         * 工程分类标签树根节点
         */
        public static final String ROOT_PARENT_PATH = "-1";
    }

    /**
     * 个人账号的customer_code 基于此customer_code进行数据初始化
     */
    public static final String PERSONAL_CUSTOMER_CODE = "-006688" + "test";
}
