package com.glodon.qydata.controller.system;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.service.system.ISysRightInfoService;
import com.glodon.qydata.util.PublishLockerUtil;
import com.glodon.qydata.util.SpringUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.system.RightInfoVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公共的查询权限相关的Controller
 * Created by weijf on 2021/7/22.
 */
@Slf4j
@RestController
@RequestMapping("/basicInfo/common")
@Tag(name = "公共的查询权限相关接口类", description = "公共的查询权限相关接口类")
public class CommonAuthController extends BaseController {

    @Autowired
    private ISysRightInfoService sysRightInfoService;

    @Autowired
    private PublishLockerUtil publishLockerUtil;


    /**
     * 获取登录用户的授权及权限信息
     * 不查询缓存
     * @return
     * @throws BusinessException
     * <AUTHOR>
     */
    @Operation(summary = "获取登录用户的授权及权限信息")
    @GetMapping("/getRights")
    public ResponseVo getRights() throws BusinessException {
        String globalId = getGlobalId();
        String sgToken = getSgToken();
        if(StringUtils.isEmpty(globalId) || StringUtils.isBlank(sgToken)){
            throw new BusinessException("无效请求,"+"globalId："+globalId+"，sgToken："+sgToken);
        }
        try {
            RightInfoVo rightInfo = sysRightInfoService.getRightInfo(globalId, sgToken, true);

            return ResponseVo.success(rightInfo);
        }catch (BusinessException be){
            return ResponseVo.error(be.getMessage());
        }
    }


    @Operation(summary = "续期用修订发布锁")
    @GetMapping("/renewalPublishLock")
    public ResponseVo renewalPublishLock(@RequestParam("type") String type) {
        String customerCode = getCustomerCode();
        String globalId = getGlobalId();
        publishLockerUtil.renewalLock(type, globalId, customerCode);
        return ResponseVo.success();
    }


    @GetMapping("/selectEnterpriseInformationByGlobalIdTest")
    public ResponseVo<String> selectEnterpriseIdByGlobalIdTest() {
        String globalId = getGlobalId();
        try {
            IGlodonUserService glodonUserService = SpringUtil.getBean(IGlodonUserService.class);
            JSONObject json = glodonUserService.getEntInforFromCached(globalId, StrUtil.EMPTY);
            String customerCode = getCustomerCode();
            log.info("当前的企业信息为:[{}] customerCode为:[{}]", json, customerCode);
            return ResponseVo.success();
        }catch (BusinessException be){
            return ResponseVo.error(be.getMessage());
        }
    }
}
