package com.glodon.qydata.service.excel.impl;


import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.mapper.standard.category.CommonProjCategorySelfMapper;
import com.glodon.qydata.service.excel.ExcelParseService;
import com.glodon.qydata.service.excel.ExcelParseUtil;
import com.glodon.qydata.service.standard.category.CommonProjCategorySelfService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Class com.glodon.qydata.service.excel ExcelOutAndInService
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 解析excel
 * @Date 11:34 2022/7/6
 **/
@Service("excelParseCategoryServiceImpl")
public class ExcelParseCategoryServiceImpl implements ExcelParseService {

    @Autowired
    private CommonProjCategorySelfService commonProjCategorySelfService;
    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;
    @Autowired
    private CommonProjCategorySelfMapper commonProjCategorySelfMapper;

    @Override
    public String modelType() {
        return OperateConstants.CATEGORY;
    }

    /***
     * @description: 下载模板
     * @param excelData 1
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 11:35
     */
    @Override
    public String parse(List<List<String>> excelData, Map<String, String> param) {
        String msg = "";
        boolean flg1 = false;
        boolean flg2 = false;
        boolean flg3 = false;

        List<CommonProjCategory> excelCategoryList = new ArrayList<>();
        Map<Integer, Set<String>> nameMap = new HashMap<>();
        Set<String> nameSetLevel1 = new HashSet<>();
        Set<String> nameSetLevel2 = new HashSet<>();
        Set<String> nameSetLevel3 = new HashSet<>();
        Set<String> nameSetLevel4 = new HashSet<>();
        nameMap.put(1, nameSetLevel1);
        nameMap.put(2, nameSetLevel2);
        nameMap.put(3, nameSetLevel3);
        nameMap.put(4, nameSetLevel4);
        Set<String>commonprojcategoryidSet = new HashSet<>();
        Map<String,List<String>> excelDataMap = excelData.stream().filter(item->{
            String code  =item.get(1);
            return !StringUtils.isBlank(code) && checkCode(code);
        }).collect(Collectors.toMap(x->x.get(1),x->x,(k1,k2)->k1));
        for (int i = 0; i < excelData.size(); i ++) {
            String num = excelData.get(i).get(0);
            String code = excelData.get(i).get(1);
            String name = excelData.get(i).get(2);
            String remark = excelData.get(i).get(3);
            if (num.isEmpty() && code.isEmpty() && name.isEmpty() && remark.isEmpty()) {
                continue;
            }
            if (name.isEmpty()) {
                flg3 = true;
                continue;
            }
//            名字编码为空舍弃
            if (StringUtils.isBlank(name) || StringUtils.isBlank(code)) {
                flg3 = true;
                continue;
            }
            code = code.trim();
            name = name.trim();
            remark = remark.trim();
            remark = ExcelParseUtil.getRemark(remark);
//            层级大于4或者等于0舍弃
            Integer length =code.split("\\.").length;
            if (length > 4 || length == 0) {
                flg3 = true;
                continue;
            }
//            名字重复舍弃
            if (length == 1) {
                if (nameMap.get(length).contains(name)) {
                    continue;
                }
            } else {
                if (nameMap.get(length).contains(code.substring(0, code.trim().lastIndexOf(".")) + name)) {
                    continue;
                }
            }
//            编码重复舍弃
            if (commonprojcategoryidSet.contains(code)) {
                continue;
            }
            //判断是否跨级

            if(!checkLevel(code, length, excelDataMap)){
                flg3 = true;
                continue;
            }
            //校验编码只能为样式数字编码，不支持汉字、字母或其他特殊字符；
            if(!checkCode(code)){
                flg3 = true;
                continue;
            }
//            编码超过999舍弃 TODO
            boolean flg = false;
            for (String s : code.split("\\.")) {
                if (StringUtils.isNumeric(s) && s.length() > 3) {
                    flg = true;
                    break;
                }
            }
            if (flg) {
                continue;
            }
            if (name.length() > 30) {
                flg1 = true;
                continue;
            }
            if (remark.length() > 200) {
                flg2 = true;
                continue;
            }
            if (length == 1) {
                nameMap.get(length).add(name);
            } else {
                nameMap.get(length).add(code.substring(0, code.lastIndexOf(".")) + name);
            }
            commonprojcategoryidSet.add(code);
//            构造level、名字、备注和addType
            CommonProjCategory commonProjCategory = new CommonProjCategory();
            excelCategoryList.add(commonProjCategory);
            commonProjCategory.setCategoryname(name);
            commonProjCategory.setLevel(length.longValue());
            commonProjCategory.setRemark(remark);
            commonProjCategory.setParentId(code);
        }
        if (flg3) {
            msg = "内容格式不正确，请按模板调整格式后导入";
        } else if (flg2 || flg1) {
            msg = "名称/备注超过限制字数，请调整后导入";
        } else {
            commonProjCategorySelfService.saveCommonProjCategoryList(excelCategoryList);
        }
        return msg;
    }

    /**
     * 校验编码只能为样式数字编码，不支持汉字、字母或其他特殊字符；
     * @return
     */
    private boolean checkCode(String code) {
        if(StringUtils.isBlank(code)){
            return false;
        }
        code = code.replaceAll("\\.","");
        String pattern = "^(\\d+\\.)*\\d+$";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(code);
        return m.matches();
    }

    private boolean checkLevel (String code, int level, Map<String,List<String>> excelDataMap) {
        String []codeArr = code.split("\\.");
        if (level == 1) {
            return true;
        } else if (level == 2) {
            return excelDataMap.containsKey(codeArr[0]);
        } else if (level == 3) {
            return excelDataMap.containsKey(codeArr[0]) &&
                    excelDataMap.containsKey(codeArr[0] + "." + codeArr[1]);
        } else if (level == 4) {
            return excelDataMap.containsKey(codeArr[0]) &&
                    excelDataMap.containsKey(codeArr[0] + "." + codeArr[1]) &&
                    excelDataMap.containsKey(codeArr[0] + "." + codeArr[1] + "." + codeArr[2]);
        } else {
            return false;
        }
    }
}
