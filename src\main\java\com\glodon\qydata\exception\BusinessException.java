package com.glodon.qydata.exception;


import com.glodon.qydata.common.enums.ResponseCode;

/**
 * 自定义业务异常
 * Created by weij<PERSON> on 2017/11/29.
 */
public class BusinessException extends RuntimeException {

	private int code;

	private String message;

	public BusinessException(ResponseCode responseCode, String msg) {
		super(responseCode.getMessage());
		this.code = responseCode.getCode();
		this.message = msg;
	}

	public BusinessException(String message){
		this.code = ResponseCode.ERROR.getCode();
		this.message = message;
	}

	public BusinessException(int code, String msg) {
		this.code = code;
		this.message = msg;
	}

	public BusinessException(ResponseCode responseCode, String msg, Throwable cause) {
		super(responseCode.getMessage(), cause);
		this.code = responseCode.getCode();
		this.message = msg;
	}

	public BusinessException(ResponseCode responseCode){
		this.code = responseCode.getCode();
		this.message = responseCode.getMessage();
	}

	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	@Override
	public String getMessage() {
		return message;
	}

	public void setMessage(String message) {
		this.message = message;
	}
}
