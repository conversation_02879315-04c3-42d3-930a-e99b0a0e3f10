<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.category.CommonProjCategorySelfMapper">
    <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.category.CommonProjCategory" >
        <id column="commonprojcategoryid" property="commonprojcategoryid" jdbcType="VARCHAR" />
        <result column="qy_code" property="qyCode" jdbcType="VARCHAR" />
        <result column="categoryname" property="categoryname" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="INTEGER" />
        <result column="categorycode1" property="categorycode1" jdbcType="VARCHAR" />
        <result column="categorycode2" property="categorycode2" jdbcType="VARCHAR" />
        <result column="categorycode3" property="categorycode3" jdbcType="VARCHAR" />
        <result column="categorycode4" property="categorycode4" jdbcType="VARCHAR" />
        <result column="category_type_code" property="categoryTypeCode" jdbcType="VARCHAR" />
        <result column="category_type_name" property="categoryTypeName" jdbcType="VARCHAR" />
        <result column="level" property="level" jdbcType="DECIMAL" />
        <result column="projcount" property="counter" jdbcType="INTEGER" />
        <result column="mainparamname" property="mainparamname" jdbcType="VARCHAR" />
        <result column="unit" property="unit" jdbcType="VARCHAR" />
        <result column="province_process" property="provinceProcess" jdbcType="VARCHAR" />
        <result column="accountname" property="accountname" jdbcType="VARCHAR" />
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
        <result column="is_usable" property="isUsable" jdbcType="INTEGER" />
        <result column="is_using" property="isUsing" jdbcType="INTEGER" />
        <result column="update_global_id" property="updateGlobalId" jdbcType="VARCHAR" />
        <result column="global_id" property="globalId" jdbcType="VARCHAR" />
        <result column="id" property="id" jdbcType="INTEGER" />
        <result column="remark" property="remark" jdbcType="VARCHAR" />
        <result column="is_deleted" property="isDeleted" jdbcType="INTEGER" />
        <result column="ord" property="ord" jdbcType="INTEGER" />
        <result column="origin_id" property="originId" jdbcType="VARCHAR" />
    </resultMap>

    <sql id="Base_Column_List">
          id,qy_code,commonprojcategoryid,categoryname,`type`,categorycode1,categorycode2,categorycode3,categorycode4,category_type_code,category_type_name,level,
          projcount,mainparamname,unit,province_process,accountname,create_time,update_time,is_usable,global_id,is_using,update_global_id,remark,is_deleted,ord,origin_id
    </sql>

    <select id="queryTopCategoryList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List" /> from tb_commonprojcategory_standards_self
        where qy_code = #{customerCode}
          and level = 1
          and province_process is null
          and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
        order by `ord`
    </select>

    <select id="getByCategoryCode" resultType="com.glodon.qydata.entity.standard.category.CommonProjCategory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_commonprojcategory_standards_self
        where commonprojcategoryid = #{categoryCode}
            and qy_code = #{customerCode}
            and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
            and is_deleted = 0 limit 0, 1
    </select>

    <select id="listDirectChildrenByCode" resultMap="BaseResultMap">
        select * from tb_commonprojcategory_standards_self
        where commonprojcategoryid like concat(#{parentCategoryCode},'___')
          and qy_code = #{customerCode}
          and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
        order by `ord`
    </select>


    <insert id="saveCommonProjCategory" useGeneratedKeys="true" keyProperty="id" >
        insert into tb_commonprojcategory_standards_self (global_id,accountname,qy_code,commonprojcategoryid, categoryname,`type`, categorycode1, categorycode2, categorycode3, categorycode4, category_type_code, category_type_name, level, mainparamname, unit, ord,province_process,update_time,qy_code_old,qy_flag)
        VALUES (#{globalId},#{accountname},#{qyCode},#{commonprojcategoryid},#{categoryname},#{type},#{categorycode1},#{categorycode2},#{categorycode3},#{categorycode4},#{categoryTypeCode},#{categoryTypeName},#{level},#{mainparamname},#{unit},#{ord},#{provinceProcess},null,#{qyCodeOld},#{qyFlag})
    </insert>
    <insert id="saveCommonProjCategoryList">
        insert into tb_commonprojcategory_standards_self (remark,global_id,accountname,qy_code,commonprojcategoryid, categoryname,`type`, categorycode1, categorycode2, categorycode3, categorycode4, category_type_code, category_type_name, level, mainparamname, unit, ord,province_process,update_time,qy_code_old,qy_flag)
        VALUES
        <foreach collection="list" index="index" separator="," item="item" >
        (#{item.remark},#{item.globalId},#{item.accountname},#{item.qyCode},#{item.commonprojcategoryid},#{item.categoryname},#{item.type},#{item.categorycode1},#{item.categorycode2},#{item.categorycode3},#{item.categorycode4},#{item.categoryTypeCode},#{item.categoryTypeName},#{item.level},#{item.mainparamname},#{item.unit},#{item.ord},#{item.provinceProcess},null,
        #{item.qyCodeOld},#{item.qyFlag})
        </foreach>
    </insert>

    <select id="queryNameCount" resultType="java.lang.Integer">
        select
        count(1)
        from tb_commonprojcategory_standards_self
        where is_deleted = 0
          and qy_code = #{customerCode}
          and categoryname = #{categoryName}
          and level = #{level}
          and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
        <if test="level == 2" >
            AND  categorycode1 = #{pCode}
        </if>
        <if test="level == 3" >
            AND  categorycode2 = #{pCode}
        </if>
        <if test="level == 4" >
            AND  categorycode3 = #{pCode}
        </if>
        <if test="divideSelf != null and divideSelf != '' " >
            AND  commonprojcategoryid != #{divideSelf}
        </if>
    </select>

    <update id="batchUpdateOrd" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update tb_commonprojcategory_standards_self set ord=#{item.ord, jdbcType=INTEGER}
            <if test="item.updateTime == null">
                ,update_time = null
            </if>
            where id = #{item.id, jdbcType=INTEGER}
        </foreach>
    </update>

    <select id="queryRepeatName" resultType="com.glodon.qydata.entity.standard.category.CommonProjCategory">
        select
        <include refid="Base_Column_List"/>
        from tb_commonprojcategory_standards_self
        where qy_code = #{customerCode}
          and categoryname = #{categoryName}
          and level = #{level}
          and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
        <if test="level == 2" >
            AND  categorycode1 = #{pCode}
        </if>
        <if test="level == 3" >
            AND  categorycode2 = #{pCode}
        </if>
        <if test="level == 4" >
            AND  categorycode3 = #{pCode}
        </if>
    </select>

    <update id="updateByPrimaryKey" parameterType="com.glodon.qydata.entity.standard.category.CommonProjCategory">
        update tb_commonprojcategory_standards_self
        <set >
            <if test="commonprojcategoryid != null" >
                commonprojcategoryid = #{commonprojcategoryid,jdbcType=VARCHAR},
            </if>
            <if test="categoryname != null" >
                categoryname = #{categoryname,jdbcType=VARCHAR},
            </if>
            <if test="type != null" >
                `type` = #{type,jdbcType=INTEGER},
            </if>
            <if test="categorycode1 != null" >
                categorycode1 = #{categorycode1,jdbcType=VARCHAR},
            </if>
            <if test="categorycode2 != null" >
                categorycode2 = #{categorycode2,jdbcType=VARCHAR},
            </if>
            <if test="categorycode3 != null" >
                categorycode3 = #{categorycode3,jdbcType=VARCHAR},
            </if>
            <if test="categorycode4 != null" >
                categorycode4 = #{categorycode4,jdbcType=VARCHAR},
            </if>
            <if test="categoryTypeCode != null" >
                category_type_code = #{categoryTypeCode,jdbcType=VARCHAR},
            </if>
            <if test="categoryTypeName != null" >
                category_type_name = #{categoryTypeName,jdbcType=VARCHAR},
            </if>
            <if test="level != null" >
                level = #{level,jdbcType=INTEGER},
            </if>
            <if test="mainparamname != null" >
                mainparamname = #{mainparamname,jdbcType=VARCHAR},
            </if>
            <if test="unit != null" >
                unit = #{unit,jdbcType=VARCHAR},
            </if>
            <if test="ord != null" >
                ord = #{ord,jdbcType=INTEGER},
            </if>
            <if test="provinceProcess != null" >
                province_process = #{provinceProcess,jdbcType=VARCHAR},
            </if>
            <if test="globalId != null" >
                global_id = #{globalId,jdbcType=VARCHAR},
            </if>
            <if test="accountname != null" >
                accountname = #{accountname,jdbcType=VARCHAR},
            </if>
            <if test="isUsable != null" >
                is_usable = #{isUsable,jdbcType=INTEGER},
            </if>
            <if test="qyCode != null" >
                qy_code = #{qyCode,jdbcType=VARCHAR},
            </if>
            <if test="isDeleted != null" >
                is_deleted = #{isDeleted,jdbcType=INTEGER},
            </if>
            <if test="createTime != null" >
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null" >
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="isUsing != null" >
                is_using = #{isUsing,jdbcType=INTEGER},
            </if>
            <if test="updateGlobalId != null" >
                update_global_id = #{updateGlobalId,jdbcType=VARCHAR},
            </if>
            <if test="remark != null" >
                remark = #{remark,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKeys">
        update tb_commonprojcategory_standards_self set
                is_using = #{isUsing},
                update_global_id = #{globalId}
        where id in
           <foreach collection="ids" separator="," index="index" item="item" open="(" close=")">#{item}</foreach>
    </update>

    <delete id="deleteChildCategory" parameterType="map">
        update tb_commonprojcategory_standards_self set is_deleted = 1
        where qy_code = #{customerCode} and `type` = #{type}
        <if test="level1s != null || level2s != null || level3s != null || level4s != null" >
            AND( 1=2
        <if test="level1s != null" >
             or categorycode1 in
            <foreach collection="level1s" item="level1" index="index" open="(" close=")" separator=",">
                #{level1}
            </foreach>
        </if>
        <if test="level2s != null" >
            or categorycode2 in
            <foreach collection="level2s" item="level2" index="index" open="(" close=")" separator=",">
                #{level2}
            </foreach>
        </if>
        <if test="level3s != null" >
            or categorycode3 in
            <foreach collection="level3s" item="level3" index="index" open="(" close=")" separator=",">
                #{level3}
            </foreach>
        </if>
        <if test="level4s != null" >
            or commonprojcategoryid in
            <foreach collection="level4s" item="level4" index="index" open="(" close=")" separator=",">
                #{level4}
            </foreach>
        </if>
        )
        </if>
    </delete>

    <select id="selectSelfAll" resultMap="BaseResultMap" useCache="false" flushCache="true">
        select
        <include refid="Base_Column_List" />
        from tb_commonprojcategory_standards_self
        where qy_code = #{customerCode}
          and (province_process is null or province_process != 'ShangHai')
          and invalid = 0
        <if test="type != null" >
            and `type` = #{type}
        </if>
        <if test="isShowDelete == null or isShowDelete == 0" >
            AND is_deleted = 0
        </if>
        order by ord
    </select>

    <insert id="saveBatchSelfProjCategory" parameterType="java.util.List" >
        insert into tb_commonprojcategory_standards_self (origin_id,commonprojcategoryid,categoryname,`type`,categorycode1,categorycode2,
        categorycode3,categorycode4,category_type_code,category_type_name,level,ord,is_usable,qy_code,is_deleted,
        is_using,create_time,update_time,global_id,accountname,update_global_id,remark,qy_code_old,qy_flag) values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.originId},
            #{item.commonprojcategoryid},
            #{item.categoryname},
            #{item.type},
            #{item.categorycode1},
            #{item.categorycode2},
            #{item.categorycode3},
            #{item.categorycode4},
            #{item.categoryTypeCode},
            #{item.categoryTypeName},
            #{item.level},
            #{item.ord},
            #{item.isUsable},
            #{item.qyCode},
            #{item.isDeleted},
            #{item.isUsing},
            #{item.createTime},
            #{item.updateTime},
            #{item.globalId},
            #{item.accountname},
            #{item.updateGlobalId},
            #{item.remark},
            #{item.qyCodeOld},
            #{item.qyFlag}
            )
        </foreach>
    </insert>

    <select id="getSelfCategoryByPrimaryKey" resultType="com.glodon.qydata.entity.standard.category.CommonProjCategory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_commonprojcategory_standards_self where id = #{categoryId}
    </select>
    <select id="getSelfCategoryByIds" resultType="com.glodon.qydata.entity.standard.category.CommonProjCategory" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from tb_commonprojcategory_standards_self where id in
        <foreach collection="list" separator="," index="index" item="item" open="(" close=")">#{item.id}</foreach>
    </select>

    <delete id="deleteSelfByCustomerCode">
        delete from tb_commonprojcategory_standards_self where qy_code = #{customerCode}
        <if test="type != null" >
            and `type` = #{type}
        </if>
    </delete>

    <update id="updateBatch" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update tb_commonprojcategory_standards_self
            <set >
                <if test="item.commonprojcategoryid != null" >
                    commonprojcategoryid = #{item.commonprojcategoryid,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryname != null" >
                    categoryname = #{item.categoryname,jdbcType=VARCHAR},
                </if>
                <if test="item.type != null" >
                    `type` = #{item.type,jdbcType=INTEGER},
                </if>
                <if test="item.categorycode1 != null" >
                    categorycode1 = #{item.categorycode1,jdbcType=VARCHAR},
                </if>
                <if test="item.categorycode2 != null" >
                    categorycode2 = #{item.categorycode2,jdbcType=VARCHAR},
                </if>
                <if test="item.categorycode3 != null" >
                    categorycode3 = #{item.categorycode3,jdbcType=VARCHAR},
                </if>
                <if test="item.categorycode4 != null" >
                    categorycode4 = #{item.categorycode4,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryTypeCode != null" >
                    category_type_code = #{item.categoryTypeCode,jdbcType=VARCHAR},
                </if>
                <if test="item.categoryTypeName != null" >
                    category_type_name = #{item.categoryTypeName,jdbcType=VARCHAR},
                </if>
                <if test="item.level != null" >
                    level = #{item.level,jdbcType=INTEGER},
                </if>
                <if test="item.mainparamname != null" >
                    mainparamname = #{item.mainparamname,jdbcType=VARCHAR},
                </if>
                <if test="item.unit != null" >
                    unit = #{item.unit,jdbcType=VARCHAR},
                </if>
                <if test="item.ord != null" >
                    ord = #{item.ord,jdbcType=INTEGER},
                </if>
                <if test="item.provinceProcess != null" >
                    province_process = #{item.provinceProcess,jdbcType=VARCHAR},
                </if>
                <if test="item.globalId != null" >
                    global_id = #{item.globalId,jdbcType=VARCHAR},
                </if>
                <if test="item.accountname != null" >
                    accountname = #{item.accountname,jdbcType=VARCHAR},
                </if>
                <if test="item.isUsable != null" >
                    is_usable = #{item.isUsable,jdbcType=INTEGER},
                </if>
                <if test="item.qyCode != null" >
                    qy_code = #{item.qyCode,jdbcType=VARCHAR},
                </if>
                <if test="item.isDeleted != null" >
                    is_deleted = #{item.isDeleted,jdbcType=INTEGER},
                </if>
                <if test="item.createTime != null" >
                    create_time = #{item.createTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.updateTime != null" >
                    update_time = #{item.updateTime,jdbcType=TIMESTAMP},
                </if>
                <if test="item.isUsing != null" >
                    is_using = #{item.isUsing,jdbcType=INTEGER},
                </if>
                <if test="item.updateGlobalId != null" >
                    update_global_id = #{item.updateGlobalId,jdbcType=VARCHAR},
                </if>
                <if test="item.remark != null" >
                    remark = #{item.remark,jdbcType=VARCHAR},
                </if>
            </set>
            where id = #{item.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="selectAllGlobalIds" parameterType="java.lang.String" resultType="java.lang.Long">
        select distinct self_global_id from tb_commonprojcategory_standards_self where qy_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
    </select>
    <update id="batchUpdateDelStatus" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update tb_commonprojcategory_standards_self set is_deleted = 1
            where id = #{item, jdbcType=INTEGER}
        </foreach>
    </update>
</mapper>
