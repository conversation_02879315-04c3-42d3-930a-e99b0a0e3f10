package com.glodon.qydata.vo.standard.category;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;

/**
 * 工程分类新增 vo
 * <AUTHOR>
 * @date 2021/10/15 17:11
 */
@Data
@ToString
public class CommonProjCategoryAddVo implements Serializable {

    private static final long serialVersionUID = -4782014539644458282L;

    /**
     * 选中的工程分类
     */
    private String baseCommonprojcategoryid;

    /**
     * 新增类型(0:新增分类，1:新增子分类)
     */
    private Integer addType;

    /**
     * 分类名称
     */
    private String categoryName;

    private String categoryTypeCode;
    private String categoryTypeName;
    private String accountName;

    private Integer tagLevel;

    /**
     * 普通插入：0；在标签上插入：1
     */
    private Integer insertFlag;

    private String relIdPath;

    /**
     * 当insertFlag为1时才会可能有值
     */
    private String firstCategoryCode;

    /**
     * 当insertFlag为1时才会可能有值
     */
    private String lastCategoryCode;

    /**
     * 当insertFlag为0时才会可能有值
     */
    private String preCategoryCode;

}
