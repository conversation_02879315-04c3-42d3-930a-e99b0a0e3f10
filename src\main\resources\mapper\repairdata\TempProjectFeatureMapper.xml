<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.repairdata.TempRepairFeatureMapper" >
    <update id="updateBatchById" parameterType="list">
        <foreach collection="list" index="index" item="item" separator=";">
            update zb_project_feature_standards
            <set >
                <if test="item.expressionId != null" >
                    expression_id = #{item.expressionId,jdbcType=BIGINT},
                    repair_expression_id = #{item.repairExpressionId,jdbcType=BIGINT}
                </if>

            </set>
            where customer_code = #{customerCode,jdbcType=VARCHAR} and id = #{item.id,jdbcType=BIGINT} and repair_expression_id is null
        </foreach>
    </update>
    <select id="selectRepeatRecord" resultType="java.lang.Integer">
        SELECT count(1) as repeatCount FROM basic_info.zb_project_feature_standards
        where customer_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0
        group by customer_code,type,type_code ,expression_code, `option`, trade_id, name having repeatCount>1 limit 1
     </select>
    <update id="setFeatureInvalid" parameterType="java.lang.Long">
        update zb_project_feature_standards  set invalid = 1 where customer_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
        <foreach collection="ids" index="index" item="item" separator="," open="id IN (" close=")">
            #{item}
        </foreach>
    </update>
    <update id="synsSelfFeature" parameterType="java.lang.Long">
        update zb_project_feature_standards_self  set invalid = 1 where customer_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
        <foreach collection="originIds" index="index" item="item" separator="," open="origin_id IN (" close=")">
            #{item}
        </foreach>
    </update>

    <update id="setCategoryViewInvalid" parameterType="java.lang.Long">
        update zb_project_feature_category_view_standards  set invalid = 1 where customer_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
        <foreach collection="ids" index="index" item="item" separator="," open="id IN (" close=")">
            #{item}
        </foreach>
    </update>
    <update id="syncSelfCategoryView" parameterType="java.lang.Long">
        update zb_project_feature_category_view_standards_self  set invalid = 1 where customer_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
        <foreach collection="originIds" index="index" item="item" separator="," open="origin_id IN (" close=")">
            #{item}
        </foreach>
    </update>

</mapper>