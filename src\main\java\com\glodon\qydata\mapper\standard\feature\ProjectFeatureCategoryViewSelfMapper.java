package com.glodon.qydata.mapper.standard.feature;

import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.util.mover.ElementMover;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * 
 * <AUTHOR>
 * @date 2021/11/5 8:42
 */
@Repository
public interface ProjectFeatureCategoryViewSelfMapper extends ElementMover.DatabaseSaver<ProjectFeatureCategoryView> {

    /**
     *  批量插入
     * @param list
     * @return int
     * <AUTHOR>
     * @date 2021/11/5 9:05 
     */
    int saveBatch(@Param("list") List<ProjectFeatureCategoryView> list);
    
    /**
     *  批量删除
     * @param featureId
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 9:05 
     */
    void deleteByFeatureId(@Param("featureId") Long featureId);
    /**
     *  批量删除
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 9:05
     */
    void deleteByFeatureIds(List<Long> list);

    /**
     *  根据分类和专业查询
     * @param tradeId
     * @param categoryCode
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeatureCategoryView>
     * <AUTHOR>
     * @date 2021/11/5 9:05 
     */
    List<ProjectFeatureCategoryView> selectByCategoryAndTrade(String customerCode, Long tradeId, String categoryCode, Integer type);
    
    /**
     * 批量更新ord
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 9:05 
     */
    @Override
    void batchUpdateOrd(@Param("list") List<ProjectFeatureCategoryView> list);

    /**
     *  删除特征的某个分类
     * @param featureIdList
     * @param categoryCode
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 9:06
     */
    void batchDeleteFeatureIds(@Param("featureIdList") List<Long> featureIdList, @Param("categoryCode") String categoryCode);
    
    /**
     *  批量删除分类视图表中某个特征下的分类
     * @param featureId
     * @param categoryCodeList
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 9:06 
     */
    void batchDeleteFeatureCategory(@Param("featureId") Long featureId, @Param("categoryCodeList") Set<String> categoryCodeList);

    /**
     *  根据企业和工程分类查询特征
     * @param categoryCode
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeatureCategoryView>
     * <AUTHOR>
     * @date 2022/2/10 16:26
     */
    List<ProjectFeatureCategoryView> selectBySelfCategoryAndCustomerCode(@Param("categoryCode")String categoryCode,
                                                                         @Param("customerCode") String customerCode,
                                                                         @Param("type") Integer type);

    /**
     *  根据企业查询特征
     * @param customerCode
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeatureCategoryView>
     * <AUTHOR>
     * @date 2022/2/10 16:26
     */
    List<ProjectFeatureCategoryView> selectBySelfCustomerCode(@Param("customerCode") String customerCode,
                                                              @Param("type") Integer type);

    /**
     * 物理删除指定企业编码下的暂存工程特征分类数据
     * @param customerCode
     */
    void deleteBySelfCustomerCode(@Param("customerCode") String customerCode, @Param("type") Integer type);

    /**
     *  批量插入
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2022/2/10 16:26
     */
    void insertSelfBatch(@Param("list") List<ProjectFeatureCategoryView> list);

    /**
     　　* @description: 批量删除专业下所有工程特征的分类视图
     　　* @param
     　　* @return
     　　* <AUTHOR>
     　　* @date 2022/1/7 16:06
     　　*/
    void deleteByTradeId(@Param("tradeId") Long tradeId);

    /**
     * 校验某个专业下工程特征 名称 是否重复(未删除的)
     * @param tradeId
     * @param name
     * @param customerCode
     * @return int
     * <AUTHOR>
     * @date 2021/10/21 20:55
     */
    int countByCategoryCodeExpressionId(@Param("tradeId") Long tradeId, @Param("name") String name,
                                   @Param("customerCode") String customerCode, @Param("id")Long featureId,
                                   @Param("type") Integer type, @Param("categoryCode") String categoryCode);

    List<ProjectFeatureCategoryView> selectByCategoryAndCustomerCodeAndTradeId(@Param("categoryCode")String categoryCode,
                                                                               @Param("customerCode")String customerCode,
                                                                               @Param("tradeId")Long tradeId,
                                                                               @Param("type") Integer type);

    List<Long> selectAllGlobalIds(@Param("customerCode")String customerCode);

    void batchDeleteByIds(@Param("ids")List<Long> ids);
    /**
     *  批量删除
     * @param tradeIds
     * @return void
     * <AUTHOR>
     * @date 2021/11/5 9:05
     */
    void batchDeleteByTradeId(List<Long> tradeIds);

    /**
     * 查询暂存表是否存在数据
     * @param customerCode
     * @param type
     * @return
     */
    Integer selectRecordCount(@Param("customerCode")String customerCode,
                              @Param("type") Integer type);

}