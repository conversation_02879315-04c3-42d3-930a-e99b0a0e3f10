package com.glodon.qydata.controller.openapi;

import com.glodon.qydata.vo.common.ResponseVo;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletResponse;

/**
 * monitor 存活检测
 *
 * <AUTHOR>
 * @program qydata-basic-info-server
 * @create 2022-02-15
 */
@RestController
@RequestMapping("/basicInfo/openApi")
public class BasicInfoMonitorController {

    @GetMapping(value = "/monitor")
    public ResponseVo monitor(HttpServletResponse response) {
        response.setStatus(200);
        return ResponseVo.success();
    }
}
