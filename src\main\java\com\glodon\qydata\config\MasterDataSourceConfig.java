package com.glodon.qydata.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.baomidou.mybatisplus.autoconfigure.SpringBootVFS;
import com.baomidou.mybatisplus.core.MybatisConfiguration;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.glodon.qydata.util.page.MysqlPaginationPlugin;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.io.VFS;
import org.apache.ibatis.logging.log4j2.Log4j2Impl;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.lang.Nullable;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.Properties;

@Configuration
@MapperScan(basePackages = MasterDataSourceConfig.PACKAGE, sqlSessionFactoryRef = "masterSqlSessionFactory")
@EnableTransactionManagement
@Slf4j
public class MasterDataSourceConfig {
    static final String PACKAGE = "com.glodon.qydata.mapper";
    static final String MAPPER_LOCATION = "classpath*:/mapper/**/*Mapper.xml";


    @Bean(name = "masterDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.master")
    public DataSource masterDataSource() {
        DruidDataSource dataSource = new DruidDataSource();
        dataSource.setInitialSize(5);
        dataSource.setMaxActive(100);
        dataSource.setMaxWait(1000);
        return dataSource;
    }

    @Bean(name = "masterTransactionManager")
    public DataSourceTransactionManager masterTransactionManager(@Qualifier("masterDataSource") DataSource masterDataSource,
                                                                 @Nullable @Qualifier("shardingDataSource") DataSource shardingDataSource) {
        DataSource dataSource = selectDataSource(masterDataSource, shardingDataSource);
        return new DataSourceTransactionManager(dataSource);
    }

    @Bean
    public MysqlPaginationPlugin mysqlPaginationPlugin(){
        MysqlPaginationPlugin mysqlPaginationPlugin = new MysqlPaginationPlugin();
        Properties p = new Properties();
        p.setProperty("sqlIdRegex",".*Page$");
        mysqlPaginationPlugin.setProperties(p);
        return mysqlPaginationPlugin;
    }

    @Bean(name = "masterSqlSessionFactory")
    @Primary
    public SqlSessionFactory masterSqlSessionFactory(@Qualifier("masterDataSource") DataSource masterDataSource,
                                                     @Nullable @Qualifier("shardingDataSource") DataSource shardingDataSource,
                                                     MysqlPaginationPlugin mysqlPaginationPlugin
    ) throws Exception {
        VFS.addImplClass(SpringBootVFS.class);
        MybatisSqlSessionFactoryBean sessionFactory = new MybatisSqlSessionFactoryBean();

        DataSource dataSource = selectDataSource(masterDataSource, shardingDataSource);
        sessionFactory.setDataSource(dataSource);
        sessionFactory.setMapperLocations(new PathMatchingResourcePatternResolver()
                .getResources(MasterDataSourceConfig.MAPPER_LOCATION));

        MybatisConfiguration mybatisConfiguration = new MybatisConfiguration();
        mybatisConfiguration.setMapUnderscoreToCamelCase(true);
        mybatisConfiguration.setLogImpl(Log4j2Impl.class);
        sessionFactory.setConfiguration(mybatisConfiguration);

        //添加插件
        sessionFactory.setPlugins(mysqlPaginationPlugin);

        sessionFactory.setTypeHandlersPackage("com.glodon.qydata.config.handler");
        return sessionFactory.getObject();
    }

    /**
     * 智能选择数据源
     * 根据ShardingSphere数据源是否可用来决定使用哪个数据源
     */
    private DataSource selectDataSource(DataSource masterDataSource, DataSource shardingDataSource) {
        if (shardingDataSource != null) {
            log.info("🎯 数据源选择: ShardingSphere分片数据源");
            log.info("分片功能: 已启用");
            return shardingDataSource;
        } else {
            log.info("🎯 数据源选择: 普通主数据源");
            log.info("分片功能: 未启用");
            return masterDataSource;
        }
    }
}