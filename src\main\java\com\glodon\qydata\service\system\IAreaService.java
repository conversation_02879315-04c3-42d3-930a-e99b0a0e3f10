package com.glodon.qydata.service.system;

import com.glodon.qydata.controller.temp.areaRepair.AreaDiffResult;
import com.glodon.qydata.entity.system.TbArea;
import com.glodon.qydata.vo.system.UpdateAreaVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 地区数据
 * @date 2021/12/22 8:50
 */
public interface IAreaService {

    List<TbArea> getAreaTree(String pid, String scope);

    List<TbArea> getAllArea(String scope);

    List<TbArea> getAreaByAreaIds(List<String> areaIds);

    TbArea getAreaByName(String city, String area, String level);

    void dealArea(UpdateAreaVO updateAreaVO, String user);
    AreaDiffResult compareHandler();
}
