package com.glodon.qydata.controller.temp.deleteself;

import com.glodon.qydata.common.annotation.PassAuth;
import com.glodon.qydata.service.deleteself.DeleteSelfService;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.vo.common.ResponseVo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: luoml-b
 * @date: 2024/4/23 10:44
 * @description: 删除暂存表数据
 */
@RestController
@RequestMapping("/basicInfo/deleteSelf")
public class DeleteSelfController {

    @Autowired
    private DeleteSelfService deleteSelfService;

    @Autowired
    private IGlodonUserService glodonUserService;


    @PassAuth
    @GetMapping("/batch")
    public ResponseVo batch(@RequestParam("globalId") String globalId) {
        String customerCode = StringUtils.isNotBlank(globalId) ? glodonUserService.getCustomerCode(globalId) : StringUtils.EMPTY;
        deleteSelfService.batchExecute(customerCode);
        return ResponseVo.success();
    }

    @PassAuth
    @GetMapping("/single")
    public ResponseVo single(@RequestParam("globalId") String globalId,
                             @RequestParam("type") String type) {
        String customerCode = StringUtils.isNotBlank(globalId) ? glodonUserService.getCustomerCode(globalId) : StringUtils.EMPTY;
        deleteSelfService.singleExecute(type, customerCode);
        return ResponseVo.success();
    }
}
