package com.glodon.qydata.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 建造标准细则
 * @date 2022/8/1 16:19
 */
@Data
public class StandardDetailDto implements Serializable {
    private static final long serialVersionUID = -502788858308868136L;

    private Long standardId;

    private String standardName;

    /** 列表数据 */
    private List<ZbProjectStandardDetailTreeDto> standardDetail;

    /** 动态表头 */
    private List<StandardsBuildPositionDto> dynamicCol;

    /** 分类和定位 */
    private List<StandardPositionDto> categoryAndPosition;
}
