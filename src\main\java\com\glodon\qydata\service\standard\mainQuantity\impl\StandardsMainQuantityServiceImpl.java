package com.glodon.qydata.service.standard.mainQuantity.impl;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.CacheDataCommon;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.common.enums.TradeReferEnum;
import com.glodon.qydata.dto.ZbStandardsMainQuantityDto;
import com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.mainQuantity.ZbStandardsMainQuantityMapper;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.init.enterprise.InitTradeService;
import com.glodon.qydata.service.init.self.InitMainQuantityService;
import com.glodon.qydata.service.standard.historyDS.CustomerCodeConvertService;
import com.glodon.qydata.service.standard.mainQuantity.IStandardsMainQuantityService;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.util.EmptyUtil;
import com.glodon.qydata.util.ListUtils;
import com.glodon.qydata.util.mover.ElementMover;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.mainQuantity.StandardsMainQuantityBO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.ZbStandardsMainQuantityConstants.MoveUpDownFlag.MOVE_UP;

/**
 　　* @description: 标准打通--权限控制服务类
 　　* <AUTHOR>
 　　* @date 2021/10/19 9:52
 　　*/
@Slf4j
@Service
public class StandardsMainQuantityServiceImpl implements IStandardsMainQuantityService {
    @Autowired
    private ZbStandardsMainQuantityMapper mainQuantityMapper;
    @Autowired
    private ZbStandardsTradeMapper tradeMapper;
    @Autowired
    private IGlodonUserService glodonUserService;
    @Autowired
    private CustomerCodeConvertService customerCodeConvertService;
    @Autowired
    private InitTradeService initTradeService;
    @Autowired
    private InitMainQuantityService initMainQuantityService;
    @Resource
    private PublishInfoService publishInfoServiceImpl;

    /**
    　　* @description: 专业id正确性验证
    　　* @param customerCode 企业编码 description 专业名称 id 专业id
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/22 9:27
    　　*/
    private void paramValidationByTradeId(Long tradeId){
        ZbStandardsTrade trade = tradeMapper.selectById(tradeId);
        if(trade == null){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR,"该企业下无此专业，请核对专业id");
        }
    }


    /**
     　　* @description: 同一个企业下，标准名称唯一
     id为空，企业下同名称记录存在  则重复名称
     不存在  则正常
     id有值 企业下同名称记录存在  id相同  则正常
     id不相同  则重复名称
     企业下同名称记录不存在  则正常
     　　* @param  name:要判断的标准名称 id：修改接口下的编辑标准id
     　　* @return 是否存在，true存在，false不存在
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/26 15:40
     　　*/
    private Boolean checkedName(String name, Long tradeId, Long id) {
        ZbStandardsMainQuantity zbStandardsMainQuantity = mainQuantityMapper.searchByTradeIdAndName(name, tradeId);
        if (EmptyUtil.isEmpty(zbStandardsMainQuantity)) {
            return false;
        }
        return EmptyUtil.isEmpty(id) || zbStandardsMainQuantity.getId().compareTo(id) != 0;
    }

    /**
    　　* @description: 专业新增时，初始化一份主要量指标
    　　* @param customerCode 企业编码 globalId 用户id tradeIdSys 系统专业id  newTradeId 新专业id
    　　* @return List<ZbStandardsTrade 专业列表
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/22 20:25
    　　*/
    @Override
    public void initMainQuantityList(String customerCode, Long globalId, Long tradeIdSys, Long newTradeId, String tradeCode, String tradeName){
        try{

            //查询出指定专业对应的所有系统内置主要量指标
            List<ZbStandardsMainQuantity> mainQuantityList = mainQuantityMapper.selectByCusAndTradeIdNoDelNoDel(customerCode, tradeIdSys);
            if(CollectionUtils.isEmpty(mainQuantityList)){
                return ;
            }
            List<Long> idList = SnowflakeIdUtils.getNextId(mainQuantityList.size());
            List<ZbStandardsMainQuantity> newList = ListUtils.deepCopy(mainQuantityList);
            for (int i = 0; i < newList.size(); i++) {
                ZbStandardsMainQuantity mainQuantity = newList.get(i);
                mainQuantity.setId(idList.get(i));
                mainQuantity.setTradeId(newTradeId);
                mainQuantity.setTradeCode(tradeCode);
                mainQuantity.setTradeName(tradeName);
                mainQuantity.setCustomerCode(customerCode);
                mainQuantity.setCreateGlobalId(globalId);
            }

            //入库保存
            mainQuantityMapper.batchInsert(newList);
        }catch (Exception e){
            log.error("复制工程特征出错");
            throw  new BusinessException(ResponseCode.ERROR,"复制工程特征出错");
        }
    }

    @Override
    public void executeInit(String customerCode){
        List<ZbStandardsTrade> zbStandardsTrades = tradeMapper.selectListByCustomerCode(customerCode);

        if (CollectionUtils.isEmpty(zbStandardsTrades)){
            throw new BusinessException("专业还未初始化...");
        }

        Map<String, ZbStandardsTrade> tradeCodeMap = zbStandardsTrades.stream().collect(Collectors.toMap(ZbStandardsTrade::getTradeCode, Function.identity(), (v1, v2) -> v2));

        // 查询出指定专业对应的所有系统内置主要量指标
        List<ZbStandardsMainQuantity> sysMainQuantity = mainQuantityMapper.selectByCusAndTradeIdNoDelNoDel(Constants.ZbStandardsTradeConstants.SYSTEM_CUSTOMER_CODE, null);

        if(CollectionUtils.isEmpty(sysMainQuantity)){
            return;
        }

        Map<String, List<ZbStandardsMainQuantity>> tradeCodeGroup = sysMainQuantity.stream().collect(Collectors.groupingBy(ZbStandardsMainQuantity::getTradeCode));
        List<ZbStandardsMainQuantity> insertList = new ArrayList<>();

        for (Map.Entry<String, List<ZbStandardsMainQuantity>> entry : tradeCodeGroup.entrySet()) {
            String tradeCode = entry.getKey();

            if (!tradeCodeMap.containsKey(tradeCode)) {
                continue;
            }

            ZbStandardsTrade zbStandardsTrade = tradeCodeMap.get(tradeCode);
            for (ZbStandardsMainQuantity mainQuantity : entry.getValue()) {
                mainQuantity.setId(SnowflakeIdUtils.getNextId());
                mainQuantity.setTradeId(zbStandardsTrade.getId());
                mainQuantity.setTradeName(zbStandardsTrade.getDescription());
                mainQuantity.setCustomerCode(customerCode);
                mainQuantity.setCreateGlobalId(null);
            }
            insertList.addAll(entry.getValue());
        }

        // 入库保存
        if (CollectionUtils.isNotEmpty(insertList)){
            mainQuantityMapper.batchInsert(insertList);
        }
    }


    /**
     　　* @description: 查询指定专业下的主要量指标
        页面流程为：查询专业列表，再根据专业列表查询对应主要量指标列表
     　　* @param customerCode 企业编码 tradeId 专业id
     　　* @return List<ZbStandardsMainQuantity> 主要量指标列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/27 10:48
     　　*/
    @Override
    @BusinessCache(customerCode = "${customerCode}")
    public List<ZbStandardsMainQuantity> getListByTradeId(String customerCode, Long tradeId) throws BusinessException {
        this.paramValidationByTradeId(tradeId);
        return mainQuantityMapper.selectByCusAndTradeIdNoDelNoDel(customerCode, tradeId);
    }


    /**
     * 解析工程特征库文件
     */
    @Override
    public void insertFeatureDataFile(String customerCode, Long globalId, MultipartFile file) {
        try {
            //CommonsMultipartFile cFile = (CommonsMultipartFile) file;
            //DiskFileItem fileItem = (DiskFileItem) cFile.getFileItem();
            //InputStream inputStream = fileItem.getInputStream();
            //// 获取Excel工作簿
            //Workbook wb = WorkbookFactory.create(inputStream);
            //Sheet sheet = wb.getSheetAt(1);
            //parseExcelData(sheet);

        } catch (Exception e) {
            //e.printStackTrace();
        }
    }


    /**
     * 解析工程特征库文件
     */
    public void parseExcelData(Sheet sheet){
        int mastRowIndex = 254;
        Long tradeId = null;
        Integer ord = 0;
        List<ZbStandardsMainQuantity> quantityList = new ArrayList<>();
        for (int i = 0; i < mastRowIndex; i++) {
            Row row = sheet.getRow(i);
            String index = row.getCell(0).getStringCellValue();
            String name = row.getCell(1).getStringCellValue();
            String unit = row.getCell(2).getStringCellValue();

            List<ZbStandardsTrade> tradeList = CacheDataCommon.TRADE_LIST;
            Map<String,Long> systemTradeMap = tradeList.stream().collect(Collectors.toMap(ZbStandardsTrade::getDescription,ZbStandardsTrade::getId));
            if(index.equals("专业")){
                tradeId = systemTradeMap.get(name);
                ord = 0;
                continue;
            }else{
                ord++;
            }

            ZbStandardsMainQuantity mainQuantity = new ZbStandardsMainQuantity();
            mainQuantity.setDescription(name);
            mainQuantity.setCustomerCode(Constants.ZbStandardsMainQuantityConstants.SYSTEM_CUSTOMER_CODE);
            mainQuantity.setId(SnowflakeIdUtils.getNextId());
            mainQuantity.setIsDeleted(Constants.ZbStandardsMainQuantityConstants.DeletedStatus.NO_DELETED);
            mainQuantity.setOrd(ord);
            mainQuantity.setTradeId(tradeId);
            mainQuantity.setType(Constants.ZbStandardsMainQuantityConstants.MainQuantityType.SYSTEM_TYPE);
            mainQuantity.setUnit(unit);

            quantityList.add(mainQuantity);
        }
        mainQuantityMapper.batchInsert(quantityList);
    }

    /**
     　　* @description: 单个删除主要量指标
     　　* @param  customerCode 企业编码 id 主要量指标id
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/28 8:44
     　　*/
    @Override
    public void deleteById(String customerCode, String id) throws BusinessException {
        if (id.split(",").length < 1) {
            return;
        }
        String globalId = RequestContent.getGlobalId();
        List<ZbStandardsMainQuantity> mainQuantity = mainQuantityMapper.selectSelfById(id.split(","));
        if (mainQuantity == null || mainQuantity.size() == 0) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "不存在此主要量指标");
        }
        mainQuantity.forEach(item -> {
            String customerCodeDb = item.getCustomerCode();
            if (!customerCodeDb.equals(customerCode)) {
                throw new BusinessException(ResponseCode.PARAMETER_ERROR, "不能删除非本企业下的主要量指标信息");
            }
        });
        mainQuantityMapper.deleteByIds(id.split(","));
        log.info("删除主要工程量指标，删除人:{}", globalId);

        // 更新后面数据的ord
        mainQuantityMapper.updateOrdOneMinusBehind(mainQuantity.get(mainQuantity.size() - 1), customerCode);
    }


    /**
     　　* @description: 根据专业批量删除主要量指标
     　　* @param    tradeId 专业id
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/28 8:44
     　　*/
    @Override
    public void deleteByTradeId(Long tradeId){
        mainQuantityMapper.deleteByTradeId(tradeId);
    }

    /**
     　　* @description: 增加一条记录
     　　* @param  customerCode 企业编码 globalId 用户id StandardsMainQuantityBO 待插入记录
     　　* @return ZbStandardsMainQuantity 插库之后的返回值
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/28 10:03
     　　*/
    @Override
    public ZbStandardsMainQuantity insertRecord(String customerCode, Long globalId, StandardsMainQuantityBO bo) throws BusinessException {
        // 验证专业有效性
        this.selfParamValidationByTradeId(bo.getTradeId());
        // 重名校验
        this.checkedName(bo.getDescription(), bo.getTradeId(), bo.getId());
        return this.insertOrUpdateRecord(customerCode, globalId, bo, Constants.ZbStandardsMainQuantityConstants.HANDLE_FLAG.INSERT);
    }

    /**
     　　* @description: 修改一条记录
     　　* @param  customerCode 企业编码 globalId 用户id StandardsMainQuantityBO 待更新记录
     　　* @return ZbStandardsMainQuantity 插库之后的返回值
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/28 10:03
     　　*/
    @Override
    public ZbStandardsMainQuantity updateRecord(String customerCode, Long globalId, StandardsMainQuantityBO bo) throws BusinessException {

        // 验证专业有效性
        this.paramValidationByTradeId(bo.getTradeId());
        // 重名校验
        this.checkedName(bo.getDescription(), bo.getTradeId(), bo.getId());
        return this.insertOrUpdateRecord(customerCode, globalId, bo, Constants.ZbStandardsMainQuantityConstants.HANDLE_FLAG.UPDATE);
    }

    /**
     　　* @description: 增加一条记录
     　　* @param  customerCode 企业编码 globalId 用户id StandardsMainQuantityBO 待插入、更新记录
     　　* @return ZbStandardsMainQuantity 插库之后的返回值
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/28 10:03
     　　*/
    private ZbStandardsMainQuantity insertOrUpdateRecord(String customerCode, Long globalId, StandardsMainQuantityBO bo, Integer flag) {
        String name = bo.getDescription();
        Long tradeId = bo.getTradeId();
        Long id = bo.getId();
        //检测同专业下同名情况
        this.selfParamValidationByTradeId(tradeId);

        ZbStandardsMainQuantity mainQuantity = new ZbStandardsMainQuantity();
        mainQuantity.setDescription(name);
        mainQuantity.setUnit(bo.getUnit());
        mainQuantity.setRemark(bo.getRemark());
        ZbStandardsTrade zbStandardsTrade = tradeMapper.selectById(tradeId);
        mainQuantity.setTradeName(zbStandardsTrade.getDescription());
        mainQuantity.setTradeCode(zbStandardsTrade.getTradeCode());

         //新增入库，修改换值
        if (flag.compareTo(Constants.ZbStandardsMainQuantityConstants.HANDLE_FLAG.UPDATE) == 0 && EmptyUtil.isNotEmpty(id)) {
            mainQuantity.setUpdateGlobalId(globalId);
            mainQuantity.setId(id);
            mainQuantityMapper.updateSelfById(mainQuantity);
            return mainQuantity;
        }
        if (flag.compareTo(Constants.ZbStandardsMainQuantityConstants.HANDLE_FLAG.INSERT) == 0) {
            mainQuantity.setId(SnowflakeIdUtils.getNextId());
            mainQuantity.setTradeId(tradeId);
            mainQuantity.setType(Constants.ZbStandardsMainQuantityConstants.MainQuantityType.COMPANY_TYPE);
            mainQuantity.setCustomerCode(customerCode);
            mainQuantity.setIsDeleted(Constants.ZbStandardsMainQuantityConstants.DeletedStatus.NO_DELETED);
            mainQuantity.setCreateGlobalId(globalId);

            if (EmptyUtil.isEmpty(bo.getBaseId())) {
                mainQuantity.setOrd(1);
            } else {
                // 新增一条数据，集合大小为1
                List<Integer> ordList = this.updateOrdInList(1, customerCode, tradeId, bo.getBaseId());
                mainQuantity.setOrd(ordList.get(0));
            }

            mainQuantityMapper.insert(mainQuantity);
        }
        return mainQuantity;
    }

    /**
    　　* @description: 替换列表中ord，并更新后续记录中ord
    　　* @param  List<Integer> 入库list的ord集合，按顺序
    　　* @return List<Integer> 传入参数大小的ord集合
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/10/28 11:15
    　　*/
    private List<Integer> updateOrdInList(int size, String customerCode, Long tradeId, Long baseId) {
        List<Integer> ordList = new ArrayList<>(size);
        /**
         * 库中查询专业下所有主要量指标，循环得出基准专业ord，以及后续主要量指标列表
         */
        List<ZbStandardsMainQuantity> mainQuantityListDb = mainQuantityMapper.selectSelfByCusAndTradeIdNoDelNoDel(customerCode, tradeId, 0);
        if (CollectionUtils.isEmpty(mainQuantityListDb)) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "此专业下无主要量指标信息，请核对");
        }

        // 基准记录的ord
        Integer baseOrd = null;
        List<ZbStandardsMainQuantity> needUpdateOrdList = new ArrayList<>();
        for (ZbStandardsMainQuantity zbStandardsMainQuantity : mainQuantityListDb) {
            Integer ordDB = zbStandardsMainQuantity.getOrd();
            Long idDB = zbStandardsMainQuantity.getId();
            if (idDB.compareTo(baseId) == 0) {
                baseOrd = ordDB;
                for (int i = 1; i <= size; i++) {
                    ordList.add(ordDB + i);
                }
                continue;
            }
            if (EmptyUtil.isNotEmpty(baseOrd) && ordDB.compareTo(baseOrd) > 0) {
                zbStandardsMainQuantity.setOrd(ordDB + size);
                needUpdateOrdList.add(zbStandardsMainQuantity);
            }
        }

        if (CollectionUtils.isNotEmpty(needUpdateOrdList)) {
            // 库中更新后续ord集合
            mainQuantityMapper.batchUpdate(needUpdateOrdList);
        }
        return ordList;
    }

    /**
     　　* @description: 查询指定专业编码下的主要量指标
     　　* @param customerCode 企业编码 tradeCode 专业编码 isShowDelete:是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     　　* @return List<ZbStandardsMainQuantityDto> 主要量指标列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/27 10:48
     　　*/
    @Override
    public List<ZbStandardsMainQuantityDto> getListByTradeCode(String customerCode, String tradeCode, Integer isShowDelete) throws BusinessException{
        if(EmptyUtil.isEmpty(tradeCode)){
           throw  new BusinessException(ResponseCode.PARAMETER_ERROR,"专业编码不能为空");
        }

        List<ZbStandardsMainQuantity> mainQuantityList = null;
        if(EmptyUtil.isEmpty(isShowDelete) || isShowDelete.compareTo(Constants.ZbStandardsMainQuantityConstants.DeletedStatus.NO_DELETED) == 0){
            mainQuantityList = mainQuantityMapper.selectByCusAndTradeCodeNoDelNoDel(customerCode, tradeCode);
        }else{
            mainQuantityList = mainQuantityMapper.selectAllByCusAndTradeCode(customerCode, tradeCode);
        }
        // 处理引用专业编码
        return this.initReferTradeCode(mainQuantityList, tradeCode);
    }


    /**
     　　* @description: 查询企业下所有的主要量指标
     　　* @param customerCode 企业编码 isShowDelete:是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     　　* @return List<ZbStandardsMainQuantity> 主要量指标列表
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/11/19 10:48
     　　*/
    @Override
    public List<ZbStandardsMainQuantityDto> selectAllMainQuantityList(String customerCode,Integer isShowDelete,Long globalId) throws BusinessException{
        List<ZbStandardsMainQuantity> mainQuantityList = null;
        if(EmptyUtil.isEmpty(isShowDelete) || isShowDelete.compareTo(Constants.ZbStandardsMainQuantityConstants.DeletedStatus.NO_DELETED) == 0){
            mainQuantityList = mainQuantityMapper.selectByCusAndTradeCodeNoDelNoDel(customerCode, null);
            if (CollectionUtils.isEmpty(mainQuantityList)){
                initTradeService.initData(customerCode);
                mainQuantityList = mainQuantityMapper.selectByCusAndTradeCodeNoDelNoDel(customerCode, null);
            }
        }else{
            mainQuantityList = mainQuantityMapper.selectAllByCusAndTradeCode(customerCode,null);
            if (CollectionUtils.isEmpty(mainQuantityList)){
                initTradeService.initData(customerCode);
                mainQuantityList = mainQuantityMapper.selectAllByCusAndTradeCode(customerCode,null);
            }
        }

        // 处理引用专业编码
        return this.initReferTradeCode(mainQuantityList,null);
    }


    /**
    　　* @description: 传入主要量指标集合，处理其中引用专业编码属性
    　　* @param
    　　* @return
    　　* @throws
    　　* <AUTHOR>
    　　* @date 2021/11/23 11:23
    　　*/
    private List<ZbStandardsMainQuantityDto> initReferTradeCode(List<ZbStandardsMainQuantity> mainQuantities,String tradeCode){
        if(CollectionUtils.isEmpty(mainQuantities)){
            return null;
        }
        List<ZbStandardsMainQuantityDto> returnList = new ArrayList<>();
        /**
         * 专业未指定，需要全部匹配，循环中查找判断
         */
        if(EmptyUtil.isEmpty(tradeCode)){
            // 在内置专业列表中筛选专业编码，判断该专业是否为内置专业
            List<ZbStandardsTrade> tradeList = CacheDataCommon.TRADE_LIST;
            List<String> tradeCodeList = tradeList.stream().map(ZbStandardsTrade::getTradeCode).collect(Collectors.toList());
            for (ZbStandardsMainQuantity mainQuantity : mainQuantities) {
                ZbStandardsMainQuantityDto dto  = new ZbStandardsMainQuantityDto();
                BeanUtils.copyProperties(mainQuantity,dto);

                String currentTradeCode = mainQuantity.getTradeCode();
                if(!tradeCodeList.contains(currentTradeCode)){
                    // 主要量指标专业不是内置专业，可以在枚举类中找到具体引用专业编码
                    TradeReferEnum enums = TradeReferEnum.getEnumByTradeCode(currentTradeCode);
                    if(enums != null){
                        dto.setReferTradeCode(enums.getCode());
                    }
                }
                returnList.add(dto);
            }
            return returnList;
        }

        /**
         * 专业已指定，先确定引用专业，再循环放置，可以缩减循环次数
         */
        List<ZbStandardsTrade> tradeList = CacheDataCommon.TRADE_LIST;
        long count = tradeList.stream().filter(zbStandardsTrade -> zbStandardsTrade.getTradeCode().equals(tradeCode)).count();
        String referCode = null;
        if(count == 0 ){
            // 主要量指标专业不是内置专业，可以在枚举类中找到具体引用专业编码
            TradeReferEnum enums = TradeReferEnum.getEnumByTradeCode(tradeCode);
            if(enums != null){
                referCode = enums.getCode();
            }
        }
        for (ZbStandardsMainQuantity mainQuantity : mainQuantities) {
            ZbStandardsMainQuantityDto dto = new ZbStandardsMainQuantityDto();
            BeanUtils.copyProperties(mainQuantity, dto);
            dto.setReferTradeCode(referCode);
            returnList.add(dto);
        }
        return returnList;
    }

    /**
     * 上下移
     * @param flag 1：上移，2：下移
     * @param bo 主要量指标前端数据载体
     * @throws BusinessException
     */
    @Override
    public void moveUpDown(Integer flag, StandardsMainQuantityBO bo) {
        String customerCode = RequestContent.getCustomerCode();

        List<ZbStandardsMainQuantity> mainQuantitySelfList =
                mainQuantityMapper.selectSelfByCusAndTradeIdNoDelNoDel(customerCode, bo.getTradeId(), 0);

        String moveType = MOVE_UP.equals(flag) ? Constants.MOVE_TYPE_UP : Constants.MOVE_TYPE_DOWN;
        ElementMover.move(mainQuantitySelfList, bo.getId(), moveType, mainQuantityMapper);
    }

    /**
     　　* @description: 历史数据同步---主要工程量
     　　* @param
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/3 0:05
     　　*/
    @Override
    public void insertHistoryData(Map<String,List<ZbStandardsMainQuantity>> map){

        List<ZbStandardsMainQuantity> needInsertList = new ArrayList<>();
        for (String customerCode : map.keySet()) {
            List<ZbStandardsMainQuantity> list =  map.get(customerCode);
            if(CollectionUtils.isEmpty(list)){
                continue;
            }
            // 为了支持多次同步同一企业，首先删除此批企业下所有主要量指标信息
            String newCustomerCode = customerCodeConvertService.getCustomerCode(customerCode);
            mainQuantityMapper.deleteByCustomerCode(newCustomerCode);
            mainQuantityMapper.deleteSelfByCustomerCode(newCustomerCode);


            //复制属性，放置主键与企业编码
            int size = list.size();
            List<Long> idList = SnowflakeIdUtils.getNextId(size);
            for (int i = 0; i < list.size(); i++) {
                ZbStandardsMainQuantity mainQuantity = new ZbStandardsMainQuantity();
                ZbStandardsMainQuantity dto = list.get(i);
                BeanUtils.copyProperties(dto,mainQuantity);
                mainQuantity.setId(idList.get(i));
                mainQuantity.setCustomerCode(newCustomerCode);
                needInsertList.add(mainQuantity);
            }
        }

        // 入库保存
        if(CollectionUtils.isNotEmpty(needInsertList)){
            mainQuantityMapper.batchInsert(needInsertList);
        }
    }

    /**
     　　* @description: 获取系统的主要量指标数据
     　　* @param
     　　* @return ZbStandardsMainQuantity
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/6 10:48
     　　*/
    @Override
    public List<ZbStandardsMainQuantity> getSystemData(){
       return  mainQuantityMapper.selectAllByCusAndTradeCode(Constants.ZbStandardsMainQuantityConstants.SYSTEM_CUSTOMER_CODE,null);
    }

    /**
     　　* @description: 将旧企业主要量标准数据复制到新企业主要量标准
     　　* @param  Map<Long,Long> tradeIdRelationMap 新旧专业id的对应关系集合，key:oldId  value:newId  调用前控制不为空
     oldCustomerCode:原企业编码
     newCustomerCode 新企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 9:55
     　　*/
    @Override
    public void initFeatureData(Map<Long,Long> tradeIdRelationMap,String oldCustomerCode,String newCustomerCode){
        // 根据原企业编码查询所有状态下的主要量指标，若为空，不处理
        List<ZbStandardsMainQuantity> mainQuantityList = mainQuantityMapper.selectAllByCusAndTradeCode(oldCustomerCode,null);
        if (CollectionUtils.isEmpty(mainQuantityList)) {
            return;
        }

        // 复制一份新数据入库
        int size = mainQuantityList.size();
        List<Long> ids = SnowflakeIdUtils.getNextId(size);
        for (int i = 0; i < size; i++) {
            ZbStandardsMainQuantity mainQuantity = mainQuantityList.get(i);

            Long oldTradeId = mainQuantity.getTradeId();
            if(!tradeIdRelationMap.containsKey(oldTradeId)){
              continue;
            }
            mainQuantity.setTradeId(tradeIdRelationMap.get(oldTradeId));
            mainQuantity.setId(ids.get(i));
            mainQuantity.setCustomerCode(newCustomerCode);
            mainQuantity.setCreateGlobalId(-100L);
            mainQuantity.setCreateTime(new Date());
            mainQuantity.setUpdateGlobalId(null);
            mainQuantity.setUpdateTime(null);
        }

        // 入库保存
        mainQuantityMapper.batchInsert(mainQuantityList);
    }

    /**
     　　* @description: 根据企业编码删除所有主要量指标标准
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:41
     　　*/
    @Override
    public void deleteByCustomerCode(String customerCode){
        mainQuantityMapper.deleteByCustomerCode(customerCode);
    }

    /**
     　　* @description: 专业id正确性验证
     　　* @param customerCode 企业编码 description 专业名称 id 专业id
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/22 9:27
     　　*/
    private void selfParamValidationByTradeId(Long tradeId){
        ZbStandardsTrade trade = tradeMapper.selectById(tradeId);
        if(trade == null){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR,"该企业下无此专业，请核对专业id");
        }
    }

    @Override
    public List<ZbStandardsMainQuantity> getSelfListByTradeId(String customerCode, Long tradeId) throws BusinessException {

        this.selfParamValidationByTradeId(tradeId);

        List<ZbStandardsMainQuantity> mainSelfQuantityList = mainQuantityMapper.selectSelfByCusAndTradeIdNoDelNoDel(customerCode, tradeId, 0);

        if (CollectionUtils.isEmpty(mainSelfQuantityList)) {
            initMainQuantityService.initData(customerCode, tradeId);
            mainSelfQuantityList = mainQuantityMapper.selectSelfByCusAndTradeIdNoDelNoDel(customerCode, tradeId, 0);
        }

        return mainSelfQuantityList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void publish(String customerCode, String globalId) throws BusinessException {

        List<ZbStandardsMainQuantity> selfList = mainQuantityMapper.selectSelfByCusAndTradeIdNoDelNoDel(customerCode, null, 1);
        if (CollUtil.isEmpty(selfList)) {
            return;
        }
        List<ZbStandardsMainQuantity> updateList = new ArrayList<>();
        List<ZbStandardsMainQuantity> insertList = new ArrayList<>();
        for (ZbStandardsMainQuantity mainQuantityEntity : selfList) {
            Long originId = mainQuantityEntity.getOriginId();
            if (originId == null) {
                insertList.add(mainQuantityEntity);
            } else {
                updateList.add(mainQuantityEntity);
            }
        }
        // 更新修改的数据
        if (!CollectionUtils.isEmpty(updateList)) {
            mainQuantityMapper.batchUpdatePublish(updateList);
        }
        // 插入新增数据
        if (!CollectionUtils.isEmpty(insertList)) {
            mainQuantityMapper.batchInsert(insertList);
        }
        mainQuantityMapper.deleteSelfByCustomerCode(customerCode);
        publishInfoServiceImpl.updateVersion(customerCode, globalId, OperateConstants.MAIN_QUANTITY);
    }
}
