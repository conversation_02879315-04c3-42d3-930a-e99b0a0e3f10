package com.glodon.qydata.controller.standard.category;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.dto.CategoryTagRelListDto;
import com.glodon.qydata.entity.standard.category.*;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.standard.category.CategoryTagSelfService;
import com.glodon.qydata.service.standard.category.CommonProjCategorySelfService;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.util.CategoryUtil;
import com.glodon.qydata.util.PublishLockerUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.category.CommonProjCategoryAddVo;
import com.glodon.qydata.vo.standard.category.CommonProjCategoryUpdateVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 标准统一专项-工程分类
 * <AUTHOR>
 * @date 2021/10/15 17:18
 */
@RestController
@RequestMapping("/basicInfo/standards/zbCategory")
@Slf4j
@Tag(name = "工程分类相关接口类", description = "工程分类相关接口类")
public class ZbCategoryController extends BaseController {

    @Autowired
    private CommonProjCategoryService commonProjCategoryService;

    @Autowired
    private CommonProjCategorySelfService commonProjCategorySelfService;

    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;

    @Autowired
    private PublishLockerUtil publishLockerUtil;

    @Autowired
    private CategoryTagSelfService categoryTagSelfService;

    //------------------------------------------------------以下是企业级别-------------------------------------------------------------------
    /**
     * 获取所有工程类别信息--企业级
     * @return List<CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/15 17:19
     */
    @Operation(summary = "获取所有工程类别信息--企业级")
    @GetMapping("/category")
    public List<CommonProjCategory> getCategoryList(@RequestParam(value = "queryType", required = false ) String queryType) throws BusinessException{
        try {
            String customerCode = getCustomerCode();
            Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);

            log.info("获取所有工程类别信息-customerCode：{}", customerCode);
            List<CommonProjCategory> categoryListTree = commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE);
            // 填充用户信息
            Map<String, String> globalIdNameMap = getGlobalIdNameMap(commonProjCategoryService.getAllGlobalIds(categoryListTree));
            if (Objects.nonNull(globalIdNameMap) && !globalIdNameMap.isEmpty()){
                for (CommonProjCategory category : categoryListTree) {
                    CategoryUtil.fillName(category, globalIdNameMap);
                }
            }
            CategoryUtil.setUpdateTimeByUpdateGlobalId(categoryListTree);
            return categoryTagSelfService.dealWithTag(customerCode, CategoryUtil.createProjcategoryTree(categoryListTree),
                    false, Constants.CategoryTagConstants.QUERY_TYPE_TAG.equals(queryType), null);
            //return CategoryUtil.createProjcategoryTree(categoryListTree);
        } catch (Exception e) {
            log.error("获取所有工程类别信息列表错误", e);
        }
        return null;
    }

    /**
     * @description: 查询指定企业下的所有一级工程分类--企业级
     * @param
     * @return java.util.List<com.glodon.qydata.entity.standard.category.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/11/19 18:22
     */
    @Operation(summary = "查询指定企业下的所有一级工程分类--企业级")
    @GetMapping("/getCategoryListLevelOne")
    public List<CommonProjCategory> getTopOneList(){
        String customerCode = getCustomerCode();
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> categoryList = commonProjCategoryService.getTopOneList(customerCode, type);
        CategoryUtil.setUpdateTimeByUpdateGlobalId(categoryList);
        return categoryTagSelfService.dealWithTag(customerCode, categoryList, false, false, null);
        //return categoryList;
    }

    /**
     * @description: 获取指定企业的所有分类code和名称的关系（包括已删除的）--企业级
     * @param
     * @return java.util.Map<java.lang.String, java.lang.String>
     * <AUTHOR>
     * @date 2021/11/29 16:03
     */
    @Operation(summary = "获取指定企业的所有分类code和名称的关系（包括已删除的）--企业级")
    @GetMapping("/getCodeNameMap")
    public Map<String, String> getCodeNameMap(){
        Map<String, String> codeNameMap = new HashMap<>(16);
        String customerCode = getCustomerCode();
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);

        List<CommonProjCategory> allCategoryList = commonProjCategoryService.getAllCategoryList(customerCode, type, Constants.CategoryConstants.WHETHER_TRUE);
        if (CollectionUtils.isNotEmpty(allCategoryList)){
            codeNameMap = allCategoryList.stream().collect(Collectors.toMap(CommonProjCategory::getCommonprojcategoryid, CommonProjCategory::getCategoryname, (v1, v2) -> v2));
        }
        return codeNameMap;
    }

    /**
     * @description: 获取指定code直接下一级的类别列表--企业级
     * @param
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/11/30 16:47
     */
    @Operation(summary = "获取指定code直接下一级的类别列表--企业级")
    @GetMapping("/selectByLevelAndCode")
    public List<CommonProjCategory> selectByLevelAndCode(String code, Integer level){
        String customerCode = getCustomerCode();
        List<CommonProjCategory> categoryList = commonProjCategoryService.selectByLevelAndCode(customerCode, code, level);
        CategoryUtil.setUpdateTimeByUpdateGlobalId(categoryList);
        //return categoryTagSelfService.dealWithTag(customerCode, categoryList, false, false);
        return categoryList;
    }

    /**
    　　* @description: 将旧企业工程分类数据复制到新企业工程分类标准--企业级
    　　* <AUTHOR>
    　　* @date 2021/12/9 17:39
    　　*/
    @Operation(summary = "将旧企业工程分类数据复制到新企业工程分类标准--企业级")
    @PostMapping("/category/initData")
    public void initData(@RequestBody CategoryFeignParam categoryFeignParam){
        commonProjCategoryService.initCategoryData(categoryFeignParam.getOldCustomerCode(),categoryFeignParam.getNewCustomerCode());
    }

    /**
     　　* @description: 根据企业编码删除所有工程分类标准--企业级
     　　* @param  customerCode 企业编码
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/12/9 19:41
     　　*/
    @Operation(summary = "根据企业编码删除所有工程分类标准--企业级")
    @DeleteMapping("/category/deleteByCustomerCode")
    public void deleteByCustomerCode(String customerCode){
        commonProjCategoryService.deleteByCustomerCode(customerCode);
    }

    /**
     * @description: 内置数据处理--企业级（非业务接口，可废弃）
     * <AUTHOR>
     * @date 2021/11/23 16:23
     */
    @Operation(summary = "内置数据处理--企业级")
    @PutMapping(value = "other/dealHistoryData")
    public ResponseVo dealHistoryData(){
//        commonProjCategoryService.dealHistoryData();
        return ResponseVo.success();
    }

    /**
     　　* @description: 删除慧果数据（非业务接口，可废弃）
     　　* <AUTHOR>
     　　* @date 2022/03/23 17:39
     　　*/
    @Operation(summary = "删除慧果数据")
    @PostMapping("/deleteHuiguoData")
    public ResponseVo deleteHuiguoData(){
        return commonProjCategoryService.deleteHuiguoData();
    }

    /**
     　　* @description: 将慧果数据迁移到基础信息库（非业务接口，可废弃）
     　　* <AUTHOR>
     　　* @date 2022/03/08 17:39
     　　*/
    @Operation(summary = "将慧果数据迁移到基础信息库")
    @PostMapping("/migrationHuiguoData")
    public ResponseVo dataMigration(){
        return commonProjCategoryService.migrationHuiguoData();
    }

    //------------------------------------------------------以下是个人级别-------------------------------------------------------------------

    /**
     * @description: 新增工程分类--个人级
     * @param categoryVo
     * @return com.glodon.zbw.dataManager.common.ResponseVo<java.lang.String>
     * <AUTHOR>
     * @date 2021/10/15 17:19
     */
    @Operation(summary = "新增工程分类--个人级")
    @Permission
    @PostMapping("/category")
    public ResponseVo saveCategory(@RequestBody CommonProjCategoryAddVo categoryVo) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        log.info("新增工程分类-参数：{}，globalId：{}，customerCode：{}", categoryVo, globalId, customerCode);

        String baseCommonprojcategoryid = categoryVo.getBaseCommonprojcategoryid();
        Integer addType = categoryVo.getAddType();
        // 校验
        if (categoryVo.getInsertFlag() != 1 && Objects.isNull(baseCommonprojcategoryid) && !Constants.CategoryConstants.ADD_TYPE_THIS.equals(addType)) {
            return ResponseVo.error("插入子分类，选中的工程分类不能为空！");
        }

        if (Objects.isNull(addType)) {
            return ResponseVo.error("新增类型不能为空！");
        }
        // 等级
        int level;
        if (StringUtils.isEmpty(baseCommonprojcategoryid)){
            level = Constants.CategoryConstants.LEVEL_1;
        } else {
            level = baseCommonprojcategoryid.length() / Constants.CategoryConstants.SINGLE_LEVEL_LEN;
        }
        if (level == Constants.CategoryConstants.LEVEL_4 && Constants.CategoryConstants.ADD_TYPE_CHILD.equals(addType)){
            return ResponseVo.error("4级分类不能新增子分类！");
        }

        // 名称校验
        String categoryName = categoryVo.getCategoryName();
        if (StringUtils.isEmpty(categoryName)){
            return ResponseVo.error("工程分类名称不能为空！");
        }
        if(categoryName.length() > Constants.MAX_NAME_LENGTH){
            return ResponseVo.error("工程分类名称不能超过30字！");
        }
        int count = commonProjCategorySelfService.nameRepeatCheck(baseCommonprojcategoryid, addType, categoryName, customerCode, globalId, type);
        if(count > 0){
            return ResponseVo.error("工程分类在当前父级分类下名称已存在！");
        }

        // 新增分类
        CommonProjCategory category = commonProjCategorySelfService.saveCommonProjCategory(categoryVo, customerCode, globalId, type);
        if (Objects.nonNull(category)) {
            category.setTagLevel(categoryVo.getTagLevel());
            Map<String, String> globalIdNameMap = getGlobalIdNameMap(commonProjCategoryService.getAllGlobalIds(Collections.singletonList(category)));
            CategoryUtil.fillName(category, globalIdNameMap);
            return ResponseVo.success(category);
        } else {
            return ResponseVo.error("新增分类失败！");
        }
    }

    /**
     * @description: 上移、下移--个人级
     * <AUTHOR>
     * @date 2021/10/19 17:19
     */
    @Operation(summary = "上移下移--个人级")
    @Permission
    @PutMapping("/downOrUp/{commonprojcategoryid}/{operate}")
    public ResponseVo downOrUp(@PathVariable(value = "commonprojcategoryid") String commonprojcategoryid, @PathVariable(value = "operate")String operate) throws BusinessException{
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        try {
            Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
            log.info("上移、下移工程分类-commonprojcategoryid：{}，operate：{}，customerCode：{}", commonprojcategoryid, operate, customerCode);
            CommonProjCategory commonProjCategory = commonProjCategorySelfService.downOrUp(commonprojcategoryid, operate, customerCode, type);
            Map<String, String> globalIdNameMap = getGlobalIdNameMap(commonProjCategoryService.getAllGlobalIds(Collections.singletonList(commonProjCategory)));
            CategoryUtil.fillName(commonProjCategory, globalIdNameMap);
            return ResponseVo.success(commonProjCategory);
        } catch (Exception e) {
            log.error("上移、下移工程分类错误", e);
        }
        return ResponseVo.error();
    }

    /**
     * @description: 删除工程分类--个人级
     * <AUTHOR>
     * @date 2021/10/19 17:19
     * @return
     */
    @Operation(summary = "删除工程分类--个人级")
    @Permission
    @DeleteMapping("/category/{commonprojcategoryid}")
    public ResponseVo deleteCategory(@PathVariable("commonprojcategoryid") String commonprojcategoryid) throws BusinessException {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        log.info("删除工程分类-commonprojcategoryid：{}，customerCode：{}", commonprojcategoryid, customerCode);
        try {
            commonProjCategorySelfService.deleteCategory(commonprojcategoryid, customerCode, type);
            return ResponseVo.success("删除工程分类成功");
        } catch (Exception e) {
            return ResponseVo.error("删除工程分类失败！");
        }
    }

    /**
     * @description: 修改工程分类--个人级
     * @param updateVo
     * @return com.glodon.zbw.dataManager.common.ResponseVo<java.lang.String>
     * <AUTHOR>
     * @date 2021/10/20 10:12
     */
    @Operation(summary = "修改工程分类--个人级")
    @Permission
    @PutMapping(value = "/category")
    public ResponseVo updateCategory(@RequestBody @Validated List<CommonProjCategoryUpdateVo> updateVo) throws BusinessException{
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        try {
            Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
            log.info("修改工程分类-参数：{}，customerCode：{}，globalId：{}", updateVo, customerCode, globalId);
            ResponseVo<CommonProjCategory> commonProjCategoryR = commonProjCategorySelfService.updateCategory(updateVo, customerCode, globalId, type);
            if (ResponseCode.SUCCESS.getCode().equals(commonProjCategoryR.getCode())) {
                if (commonProjCategoryR.getData() instanceof List) {
                    List<CommonProjCategory> list = (List<CommonProjCategory>) commonProjCategoryR.getData();
                    Map<Integer, Integer> tagLevelMap = updateVo.stream()
                            .filter(x -> ObjectUtil.isNotNull(x.getTagLevel()))
                            .collect(Collectors.toMap(CommonProjCategoryUpdateVo::getId, CommonProjCategoryUpdateVo::getTagLevel));
                    list.forEach(x -> x.setTagLevel(tagLevelMap.getOrDefault(x.getId(), null)));
                    Map<String, String> globalIdNameMap = getGlobalIdNameMap(commonProjCategoryService.getAllGlobalIds(list));
                    list.forEach(item -> {
                        CategoryUtil.fillName(item, globalIdNameMap);
                    });
                } else {
                    Map<String, String> globalIdNameMap = getGlobalIdNameMap(commonProjCategoryService.getAllGlobalIds(Collections.singletonList(commonProjCategoryR.getData())));
                    CategoryUtil.fillName(commonProjCategoryR.getData(), globalIdNameMap);
                    commonProjCategoryR.getData().setTagLevel(updateVo.get(0).getTagLevel());
                }
                return commonProjCategoryR;
            }
        } catch (Exception e) {
            log.error("修改工程分类数据错误", e);
        }
        return ResponseVo.error();
    }

    /**
     * 获取用户暂存所有工程类别信息--个人级
     * @return
     */
    @Operation(summary = "获取用户暂存所有工程类别信息--个人级")
    @Permission
    @GetMapping("/self")
    public List<CommonProjCategory> getSelfCategoryList(@RequestParam(value = "queryType", required = false ) String queryType) throws BusinessException{
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        //加锁
        publishLockerUtil.lock(OperateConstants.CATEGORY, globalId, customerCode);
        try {
            Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
            log.info("获取用户暂存所有工程类别信息-globalId：{}", globalId);
            List<CommonProjCategory> categoryListTree = commonProjCategorySelfService.getSelfCategoryListTree(customerCode, type);
            Map<String, String> globalIdNameMap = getGlobalIdNameMap(commonProjCategoryService.getAllGlobalIds(categoryListTree));
            if (Objects.nonNull(globalIdNameMap) && !globalIdNameMap.isEmpty()){
                for (CommonProjCategory category : categoryListTree) {
                    CategoryUtil.fillName(category, globalIdNameMap);
                }
            }
            CategoryUtil.setUpdateTimeByUpdateGlobalId(categoryListTree);
            return categoryTagSelfService.dealWithTag(customerCode, CategoryUtil.createProjcategoryTree(categoryListTree),
                    true, Constants.CategoryTagConstants.QUERY_TYPE_TAG.equals(queryType), null);
            //return CategoryUtil.createProjcategoryTree(categoryListTree);
        } catch (Exception e) {
            log.error("获取用户暂存所有工程类别信息列表错误", e);
        }
        return null;
    }

    /**
     * 发布数据--个人级
     */
    @Operation(summary = "发布数据--个人级")
    @Permission
    @PostMapping("/publish")
    public ResponseVo publish() throws BusinessException{
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        commonProjCategorySelfService.publish(customerCode, globalId, type);

        long l = System.currentTimeMillis();
        publishLockerUtil.unLock(OperateConstants.CATEGORY, globalId, customerCode);
        System.out.println("发布数据--个人级耗时：" + (System.currentTimeMillis() - l));
        return ResponseVo.success();
    }


    @Operation(summary = "获取用户暂存分类标签--个人级")
    @Permission
    @GetMapping("/categoryTag")
    public ResponseVo<List<CategoryTag>> getCategoryTag() {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        return ResponseVo.success(categoryTagSelfService.getCategoryTag(customerCode));
    }

    @Operation(summary = "编辑用户分类标签--个人级")
    @Permission
    @PostMapping("/categoryTag/modify")
    public ResponseVo<String> modifyCategoryTag(@RequestBody List<CategoryTag> categoryTagList) {
        if (CollUtil.isEmpty(categoryTagList)
                || categoryTagList.stream().anyMatch(x -> StringUtils.isBlank(x.getName()))) {
            return ResponseVo.error(ResponseCode.PARAMETER_ERROR,"名称不能为空");
        }
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        categoryTagSelfService.modifyCategoryTag(customerCode, categoryTagList);
        return ResponseVo.success("编辑分类标签成功");
    }

    @Operation(summary = "获取用户暂存分类标签枚举--个人级")
    @Permission
    @GetMapping("/categoryTagEnum/{tagId}")
    public ResponseVo<List<CategoryTagEnum>> getCategoryTagEnum(@PathVariable(value = "tagId") String tagId) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        return ResponseVo.success(categoryTagSelfService.getCategoryTagEnum(customerCode, "all".equals(tagId) ? null : Long.valueOf(tagId)));
    }

    @Operation(summary = "编辑用户分类标签枚举--个人级")
    @Permission
    @PostMapping("/categoryTagEnum/modify/{tagId}")
    public ResponseVo<String> modifyCategoryTagEnum(@PathVariable("tagId") String tagId,
                                                    @RequestBody List<CategoryTagEnum> categoryTagEnumList) {
        if (categoryTagEnumList.stream().anyMatch(x -> StringUtils.isEmpty(x.getName()))) {
            return ResponseVo.error(ResponseCode.PARAMETER_ERROR,"枚举值不能为空");
        }
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        categoryTagSelfService.modifyCategoryTagEnum(customerCode, Long.valueOf(tagId), categoryTagEnumList);
        return ResponseVo.success("编辑分类标签枚举成功");
    }

    @Operation(summary = "获取用户暂存分类关系列表--个人级")
    @Permission
    @GetMapping("/categoryTagRelList")
    public ResponseVo<CategoryTagRelListDto> categoryTagRelList() {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        return ResponseVo.success(categoryTagSelfService.getCategoryTagRelList(customerCode));
    }

    @Operation(summary = "保存分类标签设置--个人级")
    @Permission
    @PostMapping("/saveCategoryTagSettings")
    public ResponseVo<Boolean> saveCategoryTagSettings(@RequestBody List<CategoryTagEnumRel> CategoryTagSettingsDto) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.CATEGORY, globalId, customerCode);
        return ResponseVo.success(categoryTagSelfService.relSaveBatch(customerCode, CategoryTagSettingsDto));
    }

}
