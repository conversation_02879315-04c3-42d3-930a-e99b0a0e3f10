package com.glodon.qydata.config.feign;

import com.glodon.qydata.config.CommonConfig;
import com.glodon.qydata.util.SignGeneratorUtils;
import feign.Request;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import feign.Target;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @description:
 * @date 2023-08-16 17:49
 * @email <EMAIL>
 */
@Slf4j
public class ApiAuthFeignConfiguration implements RequestInterceptor {

    @Autowired
    private CommonConfig commonConfig;
    
    /**
     * 重写Feign请求过滤器 在请求之前重写生成签名参数
     * <AUTHOR>
     * @date 2023-08-16 17:46:23
     * @param requestTemplate
     * @return void
     */
    @Override
    public void apply(RequestTemplate requestTemplate) {
        Request request = requestTemplate.request();
        String url = request.url();
        Target<?> feignTarget = requestTemplate.feignTarget();
        String host = feignTarget.url();
        if(host.contains(commonConfig.getAuthUrl())) {
            requestTemplate.uri(SignGeneratorUtils.signTheUrl(url, getRequestBody(request.body())));
            log.info("拦截后的[url:{}]", requestTemplate.url());
        }
    }

    private String getRequestBody(byte[] requestBody) {
        if (requestBody == null || requestBody.length == 0) {
            return StringUtils.EMPTY;
        }
        return new String(requestBody, StandardCharsets.UTF_8);
    }
}
