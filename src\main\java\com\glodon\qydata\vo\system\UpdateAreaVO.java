package com.glodon.qydata.vo.system;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 {
 "add": [
     {
     "pAreaId": "7001",
     "pName": "塔城地区",
     "name": "塔城市",
     "shortName": "塔城市",
     "areaCode": "654201"
     }
 ],
 "del": [
     {
     "areaId": "7000",
     "name": "襄阳市",
     "relatedAreaCode": "关联地区编码"
     }
 ],
 "rename": [
     {
     "areaId": "968",
     "oldName": "金阳新区",
     "name": "观山湖区",
     "shortName": "观山湖区",
     "areaCode": "520115"
     }
 ],
 "recodeIdFlag": true 【true:间隔，id和areaId取最大并向上1000取整；false:连续，取最大id+1】
 }

 新增、删除、重命名3级地区，可直接使用;
 3级挂错父级的，需将错误地区都del，然后再add;
 对2级进行改名或删除的，需要对3级做相应处理。
 */
@Data
public class UpdateAreaVO {
    private List<AddData> add;
    private List<DelData> del;
    private List<RenameData> rename;
    private boolean recodeIdFlag;

    @Data
    public static class AddData {
        private String pAreaId;
        private String pName;
        private String name;
        private String shortName;
        private String areaCode;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DelData {
        private String areaId;
        private String name;
        private String relatedAreaCode;
    }

    @Data
    public static class RenameData {
        private String areaId;
        private String oldName;
        private String name;
        private String shortName;
        private String areaCode;
    }
}
