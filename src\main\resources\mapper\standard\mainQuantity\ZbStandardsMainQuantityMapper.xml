<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.mainQuantity.ZbStandardsMainQuantityMapper">
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="trade_id" jdbcType="BIGINT" property="tradeId" />
    <result column="trade_code" jdbcType="VARCHAR" property="tradeCode" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="customer_code" jdbcType="VARCHAR" property="customerCode" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ord" jdbcType="INTEGER" property="ord" />
    <result column="create_global_id" jdbcType="BIGINT" property="createGlobalId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_global_id" jdbcType="BIGINT" property="updateGlobalId" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="trade_name" jdbcType="VARCHAR" property="tradeName" />
  </resultMap>

  <sql id="Base_Column_List">
      id, trade_id,trade_code, description, unit, remark, `type`,customer_code, is_deleted, ord,
      create_global_id, create_time, update_global_id, update_time, trade_name
  </sql>
  <insert id="insert" parameterType="com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity">
    insert into zb_standards_main_quantity_self (id, trade_id,trade_code, description,
      unit, remark, `type`,
      customer_code, is_deleted, ord, 
      create_global_id, create_time, trade_name, qy_code_old, qy_flag)
    values (#{id,jdbcType=BIGINT}, #{tradeId,jdbcType=BIGINT},#{tradeCode,jdbcType=VARCHAR},  #{description,jdbcType=VARCHAR},
      #{unit,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{type,jdbcType=TINYINT}, 
      #{customerCode,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ord,jdbcType=INTEGER}, 
      #{createGlobalId,jdbcType=BIGINT}, now(), #{tradeName,jdbcType=VARCHAR},
      #{qyCodeOld,jdbcType=VARCHAR}, #{qyFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity">
    insert into zb_standards_main_quantity
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tradeId != null">
        trade_id,
      </if>
      <if test="tradeCode != null">
        trade_code,
      </if>
      <if test="tradeName != null">
        trade_name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="customerCode != null">
        customer_code,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ord != null">
        ord,
      </if>
      <if test="createGlobalId != null">
        create_global_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateGlobalId != null">
        update_global_id,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="qyCodeOld != null">
        qy_code_old,
      </if>
      <if test="qyFlag != null">
        qy_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="tradeId != null">
        #{tradeId,jdbcType=BIGINT},
      </if>
      <if test="tradeCode != null">
        #{tradeCode,jdbcType=VARCHAR},
      </if>
      <if test="tradeName != null">
        #{tradeName,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="customerCode != null">
        #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ord != null">
        #{ord,jdbcType=INTEGER},
      </if>
      <if test="createGlobalId != null">
        #{createGlobalId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateGlobalId != null">
        #{updateGlobalId,jdbcType=BIGINT},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="qyCodeOld != null">
        #{qyCodeOld,jdbcType=VARCHAR},
      </if>
      <if test="qyFlag != null">
        #{qyFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
      insert into zb_standards_main_quantity (id, trade_id,trade_code, description,
      unit, remark, `type`,
      customer_code, is_deleted, ord,
      create_global_id, create_time, trade_name, qy_code_old, qy_flag)
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (
      #{item.id,jdbcType=BIGINT}, #{item.tradeId,jdbcType=BIGINT}, #{item.tradeCode,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
      #{item.unit,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.type,jdbcType=TINYINT},
      #{item.customerCode,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=TINYINT}, #{item.ord,jdbcType=INTEGER},
      #{item.createGlobalId,jdbcType=BIGINT}, now(), #{item.tradeName, jdbcType=VARCHAR},
      #{item.qyCodeOld,jdbcType=VARCHAR}, #{item.qyFlag,jdbcType=INTEGER}
      )
    </foreach>
  </insert>
  <update id="deleteByIds">
    update zb_standards_main_quantity_self set is_deleted = 1 where id in
    <foreach collection="ids" close=")" open="(" separator="," item="item">#{item}</foreach>
  </update>
  <update id="batchUpdate">
    <foreach collection="list" item="item" index="index" separator=";">
      update zb_standards_main_quantity_self
      <set>
        <if test="item.description != null">
          description = #{item.description},
        </if>
        <if test="item.unit != null">
          unit = #{item.unit},
        </if>
        <if test="item.remark != null">
          remark = #{item.remark},
        </if>
        <if test="item.isDeleted != null">
          is_deleted = #{item.isDeleted},
        </if>
        <if test="item.ord != null">
          ord = #{item.ord},
        </if>
        <if test="item.updateGlobalId != null">
          update_global_id = #{item.updateGlobalId}
        </if>
      </set>
      where id = #{item.id}
    </foreach>
  </update>

  <select id="searchByTradeIdAndName" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from zb_standards_main_quantity_self
    where is_deleted  = 0 and description = BINARY #{description} and trade_id = #{tradeId} limit 0, 1
  </select>

  <select id="selectByCusAndTradeIdNoDelNoDel" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from zb_standards_main_quantity
    where is_deleted  = 0
    and customer_code = #{customerCode}
    <if test="tradeId != null">
      and trade_id = #{tradeId}
    </if>
     order by trade_id,ord ASC
  </select>


  <select id="selectByCusAndTradeCodeNoDelNoDel" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from zb_standards_main_quantity
    where is_deleted  = 0
    and customer_code = #{customerCode}
    <if test="tradeCode != null">
      and trade_code = #{tradeCode}
    </if>
    order by trade_id,ord ASC
  </select>

  <select id="selectAllByCusAndTradeCode" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from zb_standards_main_quantity
    where customer_code = #{customerCode}
    <if test="tradeCode != null">
      and trade_code = #{tradeCode}
    </if>
    order by trade_id,ord ASC
  </select>

  <update id="updateOrdOneMinusBehind">
    update zb_standards_main_quantity_self
    set ord = ord - 1
    where customer_code = #{customerCode,jdbcType=VARCHAR}
      and trade_id = #{entity.tradeId,jdbcType=BIGINT}
      and ord > #{entity.ord,jdbcType=INTEGER}
  </update>

  <delete id="deleteByCustomerCode" parameterType="java.lang.String">
    delete from zb_standards_main_quantity where customer_code = #{customerCode}
  </delete>

  <delete id="deleteByTradeId" parameterType="java.lang.Long">
    delete from zb_standards_main_quantity_self where trade_id = #{tradeId}
  </delete>

  <select id="selectSelfByCusAndTradeIdNoDelNoDel" resultMap="BaseResultMap" useCache="false" flushCache="true">
    select <include refid="Base_Column_List"/>,
    origin_id
    from zb_standards_main_quantity_self
    where customer_code = #{customerCode}
    <if test="tradeId != null">
      and trade_id = #{tradeId}
    </if>
    <if test="isShowDelete == 0">
      and is_deleted = 0
    </if>
    order by trade_id,ord ASC, id DESC
  </select>

  <update id="updateSelfById" parameterType="com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity">
    update zb_standards_main_quantity_self
    <set >
      <if test="tradeId != null" >
        trade_id = #{tradeId,jdbcType=BIGINT},
      </if>
      <if test="description != null" >
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="unit != null" >
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="remark != null" >
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="type != null" >
        type = #{type,jdbcType=TINYINT},
      </if>
      <if test="customerCode != null" >
        customer_code = #{customerCode,jdbcType=VARCHAR},
      </if>
      <if test="ord != null" >
        ord = #{ord,jdbcType=INT},
      </if>
      <if test="updateTime != null" >
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="batchUpdatePublish">
    <foreach collection="list" item="item" index="index" separator=";">
      update zb_standards_main_quantity
      <set>
        <if test="item.description != null">
          description = #{item.description},
        </if>
        <if test="item.unit != null">
          unit = #{item.unit},
        </if>
        <if test="item.remark != null">
          remark = #{item.remark},
        </if>
        <if test="item.isDeleted != null">
          is_deleted = #{item.isDeleted},
        </if>
        <if test="item.ord != null">
          ord = #{item.ord},
        </if>
        <if test="item.updateGlobalId != null">
          update_global_id = #{item.updateGlobalId}
        </if>
      </set>
      where id = #{item.originId}
    </foreach>
  </update>
  <delete id="deleteSelfByCustomerCode" parameterType="java.lang.String">
    delete from zb_standards_main_quantity_self where customer_code = #{customerCode}
  </delete>
  <insert id="selfBatchInsert">
    insert into zb_standards_main_quantity_self (id, trade_id,trade_code, description,
    unit, remark, `type`,
    customer_code, is_deleted, ord,
    create_global_id, create_time, trade_name, origin_id, qy_code_old, qy_flag)
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (
      #{item.id,jdbcType=BIGINT}, #{item.tradeId,jdbcType=BIGINT}, #{item.tradeCode,jdbcType=VARCHAR}, #{item.description,jdbcType=VARCHAR},
      #{item.unit,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR}, #{item.type,jdbcType=TINYINT},
      #{item.customerCode,jdbcType=VARCHAR}, #{item.isDeleted,jdbcType=TINYINT}, #{item.ord,jdbcType=INTEGER},
      #{item.createGlobalId,jdbcType=BIGINT}, now(), #{item.tradeName, jdbcType=VARCHAR},
      #{item.originId, jdbcType=VARCHAR},#{item.qyCodeOld,jdbcType=VARCHAR}, #{item.qyFlag,jdbcType=INTEGER}
      )
    </foreach>
  </insert>
  <select id="selectSelfById" resultMap="BaseResultMap">
    select <include refid="Base_Column_List"/>
    from zb_standards_main_quantity_self
    where id in
    <foreach collection="ids" close=")" open="(" separator="," item="item">#{item}</foreach>
  </select>
  <update id="batchUpdateOrd" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_standards_main_quantity_self set `ord` = #{item.ord, jdbcType=INTEGER}
      where id = #{item.id, jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>