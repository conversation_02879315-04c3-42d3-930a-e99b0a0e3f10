package com.glodon.qydata.vo.cloud;

import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * @version V1.0
 * @{DESCRIPTION} : 返回到前台实体对象
 * @auther zhangzq-a
 * @create 2017/11/27
 **/
@Schema(name="接口返回对象",description="接口返回对象")
@Getter
@Setter
public class CloudR<T> extends ResponseVo<T> {

    private T object;

    public CloudR() {
        super(200, "success");
    }
    public CloudR(int code, String msg) {
        super(code, msg);
    }

    public CloudR(int code, String msg, T data) {
        super(code,msg,data);
    }

    public CloudR(ResponseCode responseCode, T data) {
        super(responseCode,data);
    }
    public CloudR(ResponseCode responseCode) {
        super(responseCode);
    }
}
