package com.glodon.qydata.entity.standard.category;

import com.glodon.qydata.entity.system.QYFlag;
import lombok.Data;

import java.io.Serializable;

@Data
public class CommonProjectCategoryUsed extends QYFlag implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字典表主键
     */
    private Integer id;

    /**
     * 企业编码
     */
    private String qyCode;

    /**
     * 慧果租户id
     */
    private String tenantId;

    /**
     * 当前使用的工程分类类别
     */
    private Integer type;

    /**
     * 当前使用的工程分类类别
     */
    private String tenantglobalId;

    /**
     * 慧果数据迁移时使用，后续可删
     */
    private Integer total;
}