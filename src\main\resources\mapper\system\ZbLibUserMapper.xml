<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.system.ZbLibUserMapper">
    <insert id="insertUser" parameterType="com.glodon.qydata.entity.system.ZbLibUser">
        insert into zb_lib_user (
            id, global_id, utype, status, accoun_tname, user_name, is_main, is_manager, join_main_global_id, join_manager_global_id, company_id, customer_code, create_time, full_name,auth_enterprise_id)
        VALUES
            (#{user.id},#{user.globalId},#{user.uType},#{user.status},#{user.accountName},#{user.userName},#{user.isMain},#{user.isManager},#{user.joinMainGlobalId},#{user.joinManagerGlobalId},#{user.companyId},#{user.customerCode},now(),#{user.fullName},#{user.authEnterpriseId})

             on duplicate KEY UPDATE
                global_id = values(global_id),
                customer_code = values(customer_code),
                auth_enterprise_id = values(auth_enterprise_id),
                accoun_tname = values(accoun_tname)
    </insert>
    <insert id="batchInsert">
         insert into zb_lib_user (
            id, global_id, utype, status, accoun_tname, user_name, is_main, is_manager, join_main_global_id, join_manager_global_id, company_id, customer_code, create_time, full_name)
        VALUES
        <foreach collection="list" separator="," item="user" index="index">
            (
            #{user.id},#{user.globalId},#{user.uType},#{user.status},#{user.accountName},#{user.userName},
            #{user.isMain},#{user.isManager},#{user.joinMainGlobalId},#{user.joinManagerGlobalId},
            #{user.companyId},#{user.customerCode},now(),#{user.fullName}
            )
        </foreach>
    </insert>


    <update id="updateUser" parameterType="com.glodon.qydata.entity.system.ZbLibUser">
        update zb_lib_user
        <set>
            <if test="uType != null">
                utype = #{uType},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="accountName != null">
                accoun_tname = #{accountName},
            </if>
            <if test="userName != null">
                user_name = #{userName},
            </if>
            <if test="isMain != null">
                is_main = #{isMain},
            </if>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="customerCode != null">
                customer_code = #{customerCode},
            </if>
            <if test="fullName != null">
                full_name = #{fullName},
            </if>
            <if test="guideFlag != null">
                guide_flag = #{guideFlag},
            </if>
            <if test="authEnterpriseId != null">
                auth_enterprise_id = #{authEnterpriseId},
            </if>
        </set>
        where global_id = #{globalId}
    </update>

    <update id="updateByPrimaryKey" parameterType="com.glodon.qydata.entity.system.ZbLibUser">
        update zb_lib_user
        <set>
            <if test="uType != null">
                utype = #{uType},
            </if>
            <if test="globalId != null">
                global_id = #{globalId},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="accountName != null">
                accoun_tname = #{accountName},
            </if>
            <if test="userName != null">
                user_name = #{userName},
            </if>
            <if test="isMain != null">
                is_main = #{isMain},
            </if>
            <if test="companyId != null">
                company_id = #{companyId},
            </if>
            <if test="customerCode != null">
                customer_code = #{customerCode},
            </if>
            <if test="vipAccountName != null">
                vip_account_name = #{vipAccountName}
            </if>
        </set>
        where id = #{id}
    </update>
    <delete id="deleteByIdList">
        delete from zb_lib_user where id in
        <foreach collection="list" separator="," index="index" item="item" open="(" close=")">
         #{item}
        </foreach>
    </delete>

    <select id="getByGlobalId" resultType="com.glodon.qydata.entity.system.ZbLibUser">
        select id id, global_id globalId, utype utype, status status, accoun_tname accountName, user_name userName, is_main isMain, is_manager isManager, join_main_global_id joinMainGlobalId, join_manager_global_id joinManagerGlobalId, company_id companyId, customer_code customerCode, create_time createTime, guide_num guideNum, guide_flag guideFlag,auth_enterprise_id authEnterpriseId, full_name fullName
        from zb_lib_user where global_id = #{globalId} limit 1
    </select>

    <select id="getCustomerCodeByEnterpriseId" parameterType="java.lang.Long" resultType="java.lang.String">
        select customer_code from zb_lib_user
        where auth_enterprise_id = #{enterpriseId} and customer_code is not null and customer_code != '-1'
        order by create_time desc limit 1
    </select>

    <!--根据globalIds查询用户信息-->
    <select id="getListByGlobalIds" resultType="com.glodon.qydata.entity.system.ZbLibUser">
        select id id, global_id globalId, utype utype, status status, accoun_tname accountName, user_name userName, is_main isMain, is_manager isManager, join_main_global_id joinMainGlobalId, join_manager_global_id joinManagerGlobalId, company_id companyId, customer_code customerCode, create_time createTime, full_name fullName
        from zb_lib_user
        where global_id in
        <foreach collection="globalIds" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </select>

    <select id="getMainAccountUserList" resultType="com.glodon.qydata.entity.system.ZbLibUser" parameterType="java.lang.String">
        select id id,global_id globalId,accoun_tname accountName from zb_lib_user where is_main = 1
        <if test="customerCode != null and customerCode !=''" >
            and customer_code = #{customerCode}
        </if>
    </select>
    <!--根据企业编码查询用户列表-->
    <select id="getListByGlobalId" resultType="com.glodon.qydata.entity.system.ZbLibUser">
        select id id, global_id globalId, utype utype, status status, accoun_tname accountName, user_name userName, is_main isMain, is_manager isManager, join_main_global_id joinMainGlobalId, join_manager_global_id joinManagerGlobalId, company_id companyId, customer_code customerCode, create_time createTime, full_name fullName
        from zb_lib_user
        where customer_code = #{customerCode}
    </select>
    <update id="addGuideNum" parameterType="com.glodon.qydata.entity.system.ZbLibUser">
        update zb_lib_user set  guide_num = #{guideNum} + 1
        where global_id = #{globalId} and guide_num = #{guideNum} and #{guideNum} &lt; 3
    </update>

    <select id="getListByCustomerCode" resultType="com.glodon.qydata.entity.system.ZbLibUser">
        select id id, global_id globalId, utype utype, status status, accoun_tname accountName, user_name userName, is_main isMain, is_manager isManager, join_main_global_id joinMainGlobalId, join_manager_global_id joinManagerGlobalId, company_id companyId, customer_code customerCode, create_time createTime, full_name fullName
        from zb_lib_user
        where customer_code = #{customerCode} or auth_enterprise_id = #{customerCode}
    </select>
    <select id="selectByCount" resultType="com.glodon.qydata.entity.system.ZbLibUser">
        select
	id id, global_id globalId, utype utype, status status, accoun_tname accountName, user_name userName,
	is_main isMain, is_manager isManager, join_main_global_id joinMainGlobalId, join_manager_global_id joinManagerGlobalId,
	company_id companyId, customer_code customerCode, create_time createTime, full_name fullName
from
	zb_lib_user
where
	global_id in (
	select
		global_id
	from
		(
		select
			global_id,
			COUNT(*) as cou ,
			create_time
		from
			`zb_lib_user`
		group by
			global_id) a
	where
		a.cou>1 )
order by
	global_id ,create_time
    </select>

    <select id="getEntCustomerDup" parameterType="string" resultType="java.lang.String">
        select 1
        from sys_zb_customer_dup
        where customer_code = #{customerCode} or enterprise_id = #{enterpriseId}
        limit 1
    </select>
    <select id="getCustomerCodeByEntIdInEntCustomerDup" parameterType="string" resultType="java.lang.String">
        select customer_code
        from sys_zb_customer_dup
        where enterprise_id = #{enterpriseId} and customer_code not like concat(#{enterpriseId}, '%')
        limit 1
    </select>
</mapper>