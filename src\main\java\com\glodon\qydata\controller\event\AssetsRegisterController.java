package com.glodon.qydata.controller.event;

import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.annotation.PassAuth;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.service.event.AssetsRegisterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: luoml-b
 * @date: 2023/7/4 14:05
 * @description:
 */
@RestController
@RequestMapping("/basicInfo/event/assets")
@Slf4j
public class AssetsRegisterController {

    @Autowired
    private AssetsRegisterService assetRegisterService;

    /**
     * @description: 企业资产注册or修改回调接口
     * @author: luoml-b
     * @date: 2023/7/4 17:21
     * @param: req
     * @return: com.alibaba.fastjson.JSONObject
     **/
    @PassAuth
    @PostMapping("/register")
    public JSONObject register(@RequestBody JSONObject req) {
        log.info("接口/basicInfo/event/assets/register入参req: {}", req);
        JSONObject response = new JSONObject();
        String code = Constants.Assets.CALLBACK_SUC_CODE;
        String message = Constants.Assets.CALLBACK_SUC_MSG;
//        try {
//            assetRegisterService.register(req);
//        } catch (Exception e) {
//            code = Constants.Assets.CALLBACK_ERO_CODE;
//            message = Constants.Assets.CALLBACK_ERO_MSG + e.getMessage();
//        }
        response.put("code", code);
        response.put("message", message);
        log.info("接口/basicInfo/event/assets/register出参response: {}", response);
        return response;
    }
}
