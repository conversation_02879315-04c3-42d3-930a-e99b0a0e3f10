package com.glodon.qydata.controller.standard.buildStandard;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.dto.StandardPositionDto;
import com.glodon.qydata.service.standard.buildStandard.ZbStandardsBuildPositionService;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsProjectInfoService;
import com.glodon.qydata.util.PublishLockerUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品定位相关接口类
 *
 * <AUTHOR>
 * @date 2022-8-3
 */
@RestController
@RequestMapping("/basicInfo/standards/buildStandard/position")
@Tag(name = "产品定位相关接口类", description = "产品定位相关接口类")
public class ZbStandardPositionController extends BaseController {
    @Autowired
    private ZbStandardsBuildPositionService positionService;
    @Autowired
    private IStandardsProjectInfoService standardsProjectInfoService;
    @Autowired
    private PublishLockerUtil publishLockerUtil;

    /**
     * 查询产品定位列表
     *
     * @return
     */
    @Operation(summary = "查询产品定位列表")
    @Permission
    @GetMapping("positionList/self/{buildStandardId}")
    public ResponseVo<List<StandardPositionDto>> getPositionList(@PathVariable("buildStandardId") Long standardId) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        List<StandardPositionDto> standardPositionList = positionService.getPositionList(standardId, customerCode, true);
        return ResponseVo.success(standardPositionList);
    }

    /**
     * 查询产品定位字典列表
     *
     * @return
     */
    @Operation(summary = "查询产品定位字典列表")
    @GetMapping("getPositionDict")
    public ResponseVo getPositionDict() {
        String customerCode = getCustomerCode();
        List<String> selectList = standardsProjectInfoService.getProductPositioning(customerCode);
        return ResponseVo.success(selectList);
    }

    /**
     * 保存产品定位信息
     *
     * @param standardId
     * @param standardPositionDtoList
     * @return
     */
    @Operation(summary = "保存产品定位信息")
    @Permission
    @PostMapping("/self/savePosition/{buildStandardId}")
    public ResponseVo savePosition(@PathVariable("buildStandardId") Long standardId, @RequestBody List<StandardPositionDto> standardPositionDtoList) {
        String globalId = getGlobalId();
        String customerCode = getCustomerCode();
        publishLockerUtil.verifyLockUser(OperateConstants.BUILDING, globalId, customerCode);
        positionService.savePosition(customerCode, globalId, standardId, standardPositionDtoList);
        return ResponseVo.success();
    }

    /**
     * 查询企业是否含有产品定位
     *
     * @return
     */
    @Operation(summary = "查询企业是否含有产品定位")
    @GetMapping("withoutPosition")
    public ResponseVo withoutPosition() {
        String customerCode = getCustomerCode();
        return ResponseVo.success(standardsProjectInfoService.withoutPosition(customerCode));
    }
}
