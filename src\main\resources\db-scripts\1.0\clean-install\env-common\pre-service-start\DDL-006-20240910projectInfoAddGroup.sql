use `db_cost_data_platform_pro`;
alter table `db_cost_data_platform_pro`.zb_standards_project_info
    add pid bigint default -1 not null comment '父级ID' after id;

alter table `db_cost_data_platform_pro`.zb_standards_project_info
    add type tinyint default 1 not null comment '1: item; 2: group' after id;

alter table `db_cost_data_platform_pro`.zb_standards_project_info_self
    add pid bigint default -1 not null comment '父级ID' after id;

alter table `db_cost_data_platform_pro`.zb_standards_project_info_self
    add type tinyint default 1 not null comment '1: item; 2: group' after id;
