package com.glodon.qydata.service.excel.impl;


import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.excel.ExcelParseService;
import com.glodon.qydata.service.excel.ExcelParseUtil;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureAddVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;

import static com.glodon.qydata.common.constant.Constants.WHETHER_FALSE;
import static com.glodon.qydata.common.constant.Constants.WHETHER_TRUE;
import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.ViewType.TRADE_VIEW;

/**
 * @Class com.glodon.qydata.service.excel ExcelOutAndInService
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 解析excel
 * @Date 11:34 2022/7/6
 **/
@Service("excelParseZbfeatureServiceImpl")
public class ExcelParseZbfeatureServiceImpl implements ExcelParseService {
    @Resource
    private ExcelSaveService excelSaveService;

    @Override
    public String modelType() {
        return OperateConstants.FEATURE;
    }

    /***
     * @description: 解析excel数据并保存到数据库
     * @param excelData 1
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 11:35
     */
    @Override
    public String parse(List<List<String>> excelData, Map<String, String> param) {
        try {
            parseExcel(excelData, param);
        } catch (BusinessException e) {
            return e.getMessage();
        }
        return "";
    }

    public void parseExcel(List<List<String>> excelData, Map<String, String> param) {
        if (param.get(BusinessConstants.TRADE_ID) == null) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR);
        }
        String customerCode = RequestContent.getCustomerCode();
        String globalId = RequestContent.getGlobalId();
        Long tradeId = Long.parseLong(param.get(BusinessConstants.TRADE_ID));
        String tradeName = param.get(BusinessConstants.TRADE_NAME);

        // 去重和结果集
        Set<String> uniqueNames = new HashSet<>();
        List<ProjectFeatureAddVO> featureAddVOList = new ArrayList<>();

        for (List<String> row : excelData) {
            String name = row.get(1).trim();
            String fieldType = row.get(2).trim();
            String unit = row.get(3).trim();
            String option = row.get(4).trim();
            String remark = row.get(5).trim();

            String typeCode = ExcelParseUtil.getTypeCode(fieldType);
            remark = ExcelParseUtil.getRemark(remark);

            if (ExcelParseUtil.isRowEmpty(row, new int[]{1, 2, 3, 4, 5}) || uniqueNames.contains(name)) {
                continue;
            }
            ExcelParseUtil.validateName(name);
            ExcelParseUtil.validateRemark(remark);
            ExcelParseUtil.validateUnit(typeCode, unit);
            option = ExcelParseUtil.validateOption(typeCode, option, modelType());

            ProjectFeatureAddVO newEntity = createFeatureAddVO(name, typeCode, unit, option, remark, tradeId, tradeName, customerCode, globalId);
            featureAddVOList.add(newEntity);
            uniqueNames.add(name);
        }

        excelSaveService.saveData(featureAddVOList, globalId, customerCode, tradeId);
    }

    private ProjectFeatureAddVO createFeatureAddVO(String name, String typeCode, String unit, String option, String remark,
                                                   Long tradeId, String tradeName, String customerCode, String globalId) {
        ProjectFeatureAddVO vo = new ProjectFeatureAddVO();
        vo.setTradeId(tradeId);
        vo.setTradeName(tradeName);
        vo.setName(name);
        vo.setTypeCode(typeCode);
        vo.setRemark(remark);
        vo.setViewType(TRADE_VIEW);
        vo.setIsUsing(WHETHER_TRUE);
        vo.setIsRequired(WHETHER_FALSE);
        vo.setIsExpression(WHETHER_FALSE);
        vo.setCustomerCode(customerCode);
        vo.setCreateGlobalId(Long.parseLong(globalId));
        if (ExcelParseUtil.isNumberType(typeCode) && StringUtils.isNotBlank(unit)) {
            vo.setUnit(unit);
        }
        if (ExcelParseUtil.isSelectType(typeCode) && StringUtils.isNotBlank(option)) {
            vo.setOption(option);
        }
        return vo;
    }

}
