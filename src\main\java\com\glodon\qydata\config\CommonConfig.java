package com.glodon.qydata.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties()
public class CommonConfig {

    /** 环境变量 **/
    @Value("${spring.profiles.active}")
    public String environment;

    /**
     * 依赖广联云服务域名
     */
    @Value("${config.depend.glodonUrl}")
    private String glodonUrl;
    /**
     * 依赖广联云企业服务域名
     */
    @Value("${config.depend.glodonEntUrl}")
    private String glodonEntUrl;


    /** IP地址文件路径 **/
    @Value("${ip2region.path}")
    public String ip2regionPath;

    @Value("${config.gcw_cloud.APPId}")
    public String GCW_APPID;
    @Value("${config.gcw_cloud.secret}")
    public String GCW_SECRET;
    @Value("${config.gcw_cloud.secretKey}")
    public String GCW_SECRET_KEY;

    /** 数字成本平台接口URL **/
    @Value("${config.depend.digitalCostApiUrl}")
    public String digitalCostApiUrl;
    /**
     * 依赖广联云授权服务域名
     */
    @Value("${config.depend.glodonApiAuthUrl}")
    private String glodonApiAuthUrl;

    /** 委托服务域名 **/
    @Value("${config.depend.trustUrl}")
    private String trustUrl;

    @Value("${apiAuth.url}")
    private String authUrl;

    @Value("${apiAuth.privateStat:false}")
    private boolean privateStat;

    /**
     * 授权中心
     */
    @Value("${apiAuth.appKey}")
    public String appKey;

    @Value("${apiAuth.g-signature}")
    @Deprecated
    /** 现在签名通过url动态生成 原有固定写法过时作废 **/
    public String gSignature;

}
