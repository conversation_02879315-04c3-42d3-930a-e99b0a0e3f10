package com.glodon.qydata.mapper.standard.tag;

import com.glodon.qydata.entity.standard.tag.TagIntegration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: luoml-b
 * @date: 2024/10/22 15:00
 * @description:
 */
@Mapper
public interface TagIntegrationMapper {

    List<TagIntegration> selectByEnterpriseId(@Param("enterpriseId") String enterpriseId, @Param("tagType") Integer type);


    void save(TagIntegration tagIntegration);
}
