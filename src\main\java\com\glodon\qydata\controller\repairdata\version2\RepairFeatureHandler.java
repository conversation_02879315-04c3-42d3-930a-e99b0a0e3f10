package com.glodon.qydata.controller.repairdata.version2;

import com.glodon.qydata.controller.repairdata.common.BaseRepairDataHandler;
import com.glodon.qydata.controller.repairdata.common.RepairConst;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.repairdata.TempRepairFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @description: 修复工程特征重复数据
 * @date 2023/4/21 14:14
 */
@Service
@Slf4j
public class RepairFeatureHandler extends BaseRepairDataHandler<ProjectFeature> {
    @Resource
    ProjectFeatureMapper projectFeatureMapper;
    @Resource
    TempRepairFeatureMapper tempRepairFeatureMapper;

    @Resource
    ProjectFeatureCategoryViewMapper projectFeatureCategoryViewMapper;
    /**
     * 检查数据是否需要修复
     * @param customerCode
     * @return 是否需要修复
     */
    @Override
    public boolean isNeedRepair(String customerCode){
        Integer repeatCount = tempRepairFeatureMapper.selectRepeatRecord(customerCode);
        return repeatCount != null && repeatCount > 0;
    }


    @Override
    public boolean repairData(String customerCode) {
        // 以此处理三个类型的数据
        List<Integer> typeList = Arrays.asList(1, 2, 3);
        typeList.forEach(type -> repairDataOneType(customerCode, type));
        return false;
    }
    List<ProjectFeature> repairDataOneType(String customerCode, Integer type) {
        List<ProjectFeature> featureList = projectFeatureMapper.selectByCustomeCode(customerCode, type, 1);
        if(featureList == null) {
            return new ArrayList<>();
        }
        if (featureList.isEmpty()) {
            return featureList;
        }
        Map<String, List<ProjectFeature>> groupMap = featureList.stream().collect(Collectors.groupingBy(entity -> buildDelimiter(entity.getCustomerCode(), String.valueOf(entity.getType()), entity.getTypeCode(),
                String.valueOf(entity.getTradeId()) , String.valueOf(entity.getExpressionCode()), entity.getName(), String.valueOf(entity.getInvalid()), entity.getOption())));

        groupMap.keySet().forEach(key -> {
            List<ProjectFeature> repeatData = groupMap.get(key);
            if(repeatData.size() < 2) {
                return;
            }
            List<ProjectFeature> sortList = repeatData.stream().sorted(Comparator.comparing(ProjectFeature::getIsDeleted).reversed()).collect(Collectors.toList());
            // 重复数据标记为待删除
            for(int i = 0; i < sortList.size(); ++ i) {
                if(sortList.stream().filter(item -> !RepairConst.data_status_invalid.equals(item.getInvalid())).count() <= 1) {
                    break;
                }
                sortList.get(i).setInvalid(RepairConst.data_status_invalid);
            }
        });
        List<Long> repairFeatureIds = featureList.stream().filter(item -> RepairConst.data_status_invalid.equals(item.getInvalid())).map(ProjectFeature::getId).collect(Collectors.toList());
        if(repairFeatureIds.isEmpty()) {
            return featureList;
        }
        List<ProjectFeatureCategoryView> categoryViewList = getRepairCategoryView(customerCode, featureList);
        List<Long> repairCategoryIds = categoryViewList.stream().map(ProjectFeatureCategoryView::getId).collect(Collectors.toList());
        tempRepairFeatureMapper.setFeatureInvalid(customerCode, repairFeatureIds);
        tempRepairFeatureMapper.synsSelfFeature(customerCode, repairFeatureIds);
        if(!repairCategoryIds.isEmpty()) {
            tempRepairFeatureMapper.setCategoryViewInvalid(customerCode, repairCategoryIds);
            tempRepairFeatureMapper.syncSelfCategoryView(customerCode, repairCategoryIds);
        }
        return featureList;
    }
    List<ProjectFeatureCategoryView> getRepairCategoryView(String customerCode, List<ProjectFeature> featureList) {
        List<Long> repairFeatureIds = featureList.stream().filter(item -> RepairConst.data_status_invalid.equals(item.getInvalid())).map(ProjectFeature::getId).collect(Collectors.toList());
        return projectFeatureCategoryViewMapper.selectByFeatureIds(customerCode, repairFeatureIds);
    }
    @Override
    public boolean checkDataAfterRepair(String customerCode) {
        if(isNeedRepair(customerCode)) {
            throw new BusinessException("修复结果不及预期");
        }
        return false;
    }
}
