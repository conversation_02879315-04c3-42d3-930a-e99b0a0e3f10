package com.glodon.qydata.service.standard.buildStandard;

import com.baomidou.mybatisplus.extension.service.IService;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;
import com.glodon.qydata.vo.standard.buildStandard.DetailDescAddVo;
import com.glodon.qydata.vo.standard.buildStandard.DetailDescUpdateVo;
import com.glodon.qydata.vo.standard.buildStandard.StandardsBuildStandardDetailDescVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_standard_detail_desc((企业标准数据-建造标准-标准说明表))】的数据库操作Service
* @createDate 2022-07-29 19:03:33
*/
public interface ZbStandardsBuildStandardDetailDescService extends IService<ZbStandardsBuildStandardDetailDesc> {
    /**
     * @description: 标准说明上下移
     * @param flag 上下移标识，1：上移，2：下移
     * @param descId
     * @param standardDetailId
     * @return com.glodon.qydata.vo.common.ResponseVo<java.util.List < com.glodon.qydata.dto.ZbProjectStandardDetailTreeDto>>
     * <AUTHOR>
     * @date 2022/8/4 9:42
     */
    Long descMoveUpDown(Integer flag, Long descId, Long standardDetailId);

    /**
     * 根据建造标准和globalId查询所有数据
     * @param standIds
     */
    List<ZbStandardsBuildStandardDetailDesc> selectSelfByStandardIds(List<Long> standIds);

    /**
     * 根据建造标准id批量删除 企业表数据
     * @param allOriginIds
     */
    void delByStandardIds(List<Long> allOriginIds);

    /**
     * 根据建造标准批量删除暂存表数据
     * @param allSelfStandardIds
     */
    void delSelfByStandardIds(List<Long> allSelfStandardIds);

    /**
     * @description: 新增标准说明
     * @param addVo
     * @return com.glodon.qydata.entity.standard.buildStandard.ZbProjectStandard
     * <AUTHOR>
     * @date 2022/8/5 14:52
     */
    StandardsBuildStandardDetailDescVo addDesc(DetailDescAddVo addVo);

    StandardsBuildStandardDetailDescVo updateDesc(DetailDescUpdateVo updateVo);
}
