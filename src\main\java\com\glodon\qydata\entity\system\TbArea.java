package com.glodon.qydata.entity.system;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@JSONType(orders = {"areaid","areaCode","name","shortName","pid","parentAreaCode","ord","abbrSpell","fullSpell","level","area","longitude","latitude","children"})
public class TbArea implements Serializable {

    private static final long serialVersionUID = 1L;

    @JsonIgnore
    private Integer id;

    private String areaid;

    private String pid;

    private Integer ord;

    private String areaCode;

    private String name;

    private String shortName;

    @JsonIgnore
    private Integer parentId;

    private String parentAreaCode;

    private String abbrSpell;

    private String fullSpell;

    private Integer level;

    @JsonIgnore
    private Short sort;

    private String area;

    private Double longitude;

    private Double latitude;

    private Byte displayable;

    @JsonIgnore
    private Date createdAt;

    @JsonIgnore
    private Date updatedAt;

    private List<TbArea> children;

    @JsonIgnore
    private String namePath;
    private String relatedAreaCode;
    @JsonIgnore
    private boolean flag;

}