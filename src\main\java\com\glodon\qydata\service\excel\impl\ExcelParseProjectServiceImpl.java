package com.glodon.qydata.service.excel.impl;


import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.excel.ExcelParseService;
import com.glodon.qydata.service.excel.ExcelParseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import jakarta.annotation.Resource;
import java.util.*;

import static com.glodon.qydata.common.constant.Constants.StandardsProjectOrContractInfoConstants.PROJECT_INFO;

/**
 * @Class com.glodon.qydata.service.excel ExcelOutAndInService
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 解析excel
 * @Date 11:34 2022/7/6
 **/
@Service("excelParseProjectServiceImpl")
public class ExcelParseProjectServiceImpl implements ExcelParseService {
    @Resource
    private ExcelSaveService excelSaveService;

    /**
     * 以下字段为项目基本信息，已内置在项目登记中，无需重复导入。当用户Excel表中有其中一个或多个字段时，跳过不导入。
     */
    private static final List<String> PROJECT_FIXED_FIELDS = new ArrayList<>(
            Arrays.asList("项目名称", "项目地点", "工程分类", "项目来源", "所属组织", "创建账号", "创建时间", "项目阶段", "是否启用"));

    @Override
    public String modelType() {
        return OperateConstants.PROJECT;
    }

    /***
     * @description: 解析excel数据并保存到数据库
     * @param excelData 1
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 11:35
     */
    @Override
    public String parse(List<List<String>> excelData, Map<String, String> param) {
        try {
            parseExcel(excelData, param);
        } catch (BusinessException e) {
            return e.getMessage();
        }
        return "";
    }

    public void parseExcel(List<List<String>> excelData, Map<String, String> params) {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        Set<String> uniqueNames = new HashSet<>();
        List<StandardsProjectInfoEntity> newEntities = new ArrayList<>();

        for (List<String> row : excelData) {
            String name = row.get(1).trim();
            String fieldType = row.get(2).trim();
            String unit = row.get(3).trim();
            String option = row.get(4).trim();
            String remark = row.get(5).trim();

            String typeCode = ExcelParseUtil.getTypeCode(fieldType);
            remark = ExcelParseUtil.getRemark(remark);

            if (ExcelParseUtil.isRowEmpty(row, new int[]{1, 2, 3, 4, 5}) || uniqueNames.contains(name) || PROJECT_FIXED_FIELDS.contains(name)) {
                continue;
            }

            ExcelParseUtil.validateName(name);
            ExcelParseUtil.validateRemark(remark);
            ExcelParseUtil.validateUnit(typeCode, unit);
            option = ExcelParseUtil.validateOption(typeCode, option, modelType());

            StandardsProjectInfoEntity newEntity = createEntity(name, typeCode, unit, option, remark);
            newEntities.add(newEntity);
            uniqueNames.add(name);
        }

        excelSaveService.saveData(newEntities, PROJECT_INFO, globalId, customerCode);
    }

    private StandardsProjectInfoEntity createEntity(String name, String typeCode, String unit, String option, String remark) {
        StandardsProjectInfoEntity entity = new StandardsProjectInfoEntity();
        entity.setIsRequired(0);
        entity.setIsUsing(1);
        entity.setName(name);
        entity.setRemark(remark);
        entity.setStandardDataType(PROJECT_INFO);
        entity.setTypeCode(typeCode);
        if (ExcelParseUtil.isNumberType(typeCode) && StringUtils.isNotBlank(unit)) {
            entity.setUnit(unit);
        }
        if (ExcelParseUtil.isSelectType(typeCode) && StringUtils.isNotBlank(option)) {
            entity.setSelectList(option);
        }
        return entity;
    }


}
