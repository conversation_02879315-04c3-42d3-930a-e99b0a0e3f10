package com.glodon.qydata.controller.standard.projectOrContractInfo;


import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.Permission;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.dto.ProjectInfoPromotionOrDemotionDTO;
import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.standard.projectOrContractInfo.IStandardsProjectInfoService;
import com.glodon.qydata.util.PublishLockerUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.dto.ProjectInfoMoveToDTO;
import com.glodon.qydata.vo.standard.projectOrContractInfo.ProjectInfoShowVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.lang.Nullable;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

import static com.glodon.qydata.common.constant.Constants.StandardsProjectOrContractInfoConstants.*;
import static com.glodon.qydata.common.constant.Constants.WHETHER_FALSE;

/**
 * <p>
 * zb_standards_project_info - 主键 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@RestController
@RequestMapping("/basicInfo/standards/projectOrContractData")
@Tag(name = "项目信息相关接口类", description = "项目信息相关接口类")
public class StandardsProjectInfoController extends BaseController {
    @Autowired
    private IStandardsProjectInfoService standardsProjectInfoService;
    @Autowired
    private PublishLockerUtil publishLockerUtil;

    //------------------------------------------------------以下是企业级别-------------------------------------------------------------------
    /**
     * 查询项目/合同信息列表--企业级
     *
     * @param isShowDelete 是否展示已删除数据（0：展示不包含已删除的；1：展示包含已删除的 ；不传值，默认为0。）
     * @param type         信息类型（1：项目信息；2：合同信息。）
     * @return {@link ResponseVo<List<ProjectInfoShowVo>>}
     * @throws
     * <AUTHOR>
     * @date 2021/10/15 14:30
     */
    @Operation(summary = "查询项目/合同信息列表--企业级")
    @GetMapping
    public ResponseVo<List<ProjectInfoShowVo>> get(@RequestParam @Nullable Integer isShowDelete,
                                                   @RequestParam Integer type) throws BusinessException {
        String customerCode = getCustomerCode();
        //初始化内置数据
        List<StandardsProjectInfoEntity> list = standardsProjectInfoService.initBuiltInData(customerCode, isShowDelete, type);
        return ResponseVo.success(standardsProjectInfoService.convertData(list, type, false, WHETHER_FALSE, SHOW_ALL, SHOW_TREE, customerCode));
    }

    //------------------------------------------------------以下是个人级别-------------------------------------------------------------------
    @Operation(summary = "获取修订列表--个人级")
    @Permission
    @GetMapping("/self")
    public ResponseVo<List<ProjectInfoShowVo>> getSelfProjectInfoList(@RequestParam @Nullable Integer isShowDelete,
                                                                      @RequestParam Integer type) throws BusinessException {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        // 加锁
        String publishType = type == 1 ? OperateConstants.PROJECT : OperateConstants.CONTRACT;
        publishLockerUtil.lock(publishType, globalId, customerCode);
        //初始化内置数据
        List<StandardsProjectInfoEntity> list = standardsProjectInfoService.getSelfProjectInfoList(isShowDelete, type);
        return ResponseVo.success(standardsProjectInfoService.convertData(list, type, false, WHETHER_FALSE, SHOW_ALL, SHOW_TREE, customerCode));
    }

    /**
     * 新增项目/合同信息--个人级
     *
     * @return {@link ResponseVo<Void>}
     * @throws
     * <AUTHOR>
     * @date 2021/10/19 11:33
     */
    @Operation(summary = "新增项目/合同信息--个人级")
    @Permission
    @PostMapping
    public ResponseVo<StandardsProjectInfoEntity> add(@RequestBody StandardsProjectInfoEntity entity) throws BusinessException {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        String operateType = Objects.equals(entity.getStandardDataType(), PROJECT_INFO) ? OperateConstants.PROJECT : OperateConstants.CONTRACT;
        publishLockerUtil.verifyLockUser(operateType, globalId, customerCode);
        return ResponseVo.success(standardsProjectInfoService.addInfoData(entity));
    }

    /**
     * 根据id删除项目/合同信息--个人级
     *
     * @param ids 主键
     * @return {@link ResponseVo<Void>}
     * @throws
     * <AUTHOR>
     * @date 2021/10/15 15:18
     */
    @Operation(summary = "根据id删除项目/合同信息--个人级")
    @Permission
    @DeleteMapping
    public ResponseVo delete(@RequestParam String ids, @RequestParam Integer type) throws BusinessException {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        String operateType = Objects.equals(type, PROJECT_INFO) ? OperateConstants.PROJECT : OperateConstants.CONTRACT;
        publishLockerUtil.verifyLockUser(operateType, globalId, customerCode);
        standardsProjectInfoService.deleteInfoData(ids, customerCode, operateType);
        return ResponseVo.success();
    }

    /**
     * 编辑项目/合同信息--个人级
     *
     * @param vo
     * @return {@link ResponseVo<StandardsProjectInfoEntity>}
     * @throws
     * <AUTHOR>
     * @date 2021/10/19 14:15
     */
    @Operation(summary = "编辑项目/合同信息--个人级")
    @Permission
    @PutMapping
    public ResponseVo<ProjectInfoShowVo> update(@RequestBody ProjectInfoShowVo vo) throws BusinessException {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        String operateType = Objects.equals(vo.getStandardDataType(), PROJECT_INFO) ? OperateConstants.PROJECT : OperateConstants.CONTRACT;
        publishLockerUtil.verifyLockUser(operateType, globalId, customerCode);
        return ResponseVo.success(standardsProjectInfoService.updateInfoData(vo));
    }

    /**
     * 上下移动--个人级
     *
     * @param flag   1：上移，2：下移
     * @param entity
     * @return {@link ResponseVo< Void>}
     * @throws
     * <AUTHOR>
     * @date 2021/10/19 16:42
     */
    @Operation(summary = "上下移动--个人级")
    @Permission
    @PutMapping("/moveUpDown/{flag}")
    public ResponseVo moveUpDown(@PathVariable Integer flag, @RequestBody StandardsProjectInfoEntity entity) throws BusinessException {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        String operateType = Objects.equals(entity.getStandardDataType(), PROJECT_INFO) ? OperateConstants.PROJECT : OperateConstants.CONTRACT;
        publishLockerUtil.verifyLockUser(operateType, globalId, customerCode);

        standardsProjectInfoService.moveUpDown(flag, entity);
        return ResponseVo.success();
    }

    @Operation(summary = "发布")
    @Permission
    @PostMapping("/publish")
    public ResponseVo publish(@RequestParam Integer type) throws BusinessException {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        String operateType = Objects.equals(type, PROJECT_INFO) ? OperateConstants.PROJECT : OperateConstants.CONTRACT;
        publishLockerUtil.verifyLockUser(operateType, globalId, customerCode);

        standardsProjectInfoService.publish(customerCode, globalId, type);

        String publishType = type == 1 ? OperateConstants.PROJECT : OperateConstants.CONTRACT;
        publishLockerUtil.unLock(publishType, globalId, customerCode);
        return ResponseVo.success();
    }

    @Operation(summary = "移动到")
    @Permission
    @PostMapping("/moveTo")
    public ResponseVo moveTo(@RequestBody ProjectInfoMoveToDTO projectInfoMoveToDTO) {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        projectInfoMoveToDTO.setCustomerCode(customerCode);
        publishLockerUtil.verifyLockUser(OperateConstants.PROJECT, globalId, customerCode);
        standardsProjectInfoService.moveTo(projectInfoMoveToDTO);
        return ResponseVo.success();
    }

    @Operation(summary = "升降级")
    @Permission
    @PostMapping("/promotionOrDemotion")
    public ResponseVo promotionOrDemotion(@RequestBody ProjectInfoPromotionOrDemotionDTO projectInfoPromotionOrDemotionDTO) {
        String globalId = RequestContent.getGlobalId();
        String customerCode = RequestContent.getCustomerCode();
        projectInfoPromotionOrDemotionDTO.setCustomerCode(customerCode);
        publishLockerUtil.verifyLockUser(OperateConstants.PROJECT, globalId, customerCode);
        standardsProjectInfoService.promotionOrDemotion(projectInfoPromotionOrDemotionDTO);
        return ResponseVo.success();
    }
}
