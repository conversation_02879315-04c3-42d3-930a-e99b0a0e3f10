spring:
  application:
    name: qydata-basic-info
  profiles:
    active: '@spring.profiles.active@'
  banner:
    location: banner.txt
  main:
    allow-circular-references: true
    allow-bean-definition-overriding: true
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
#是否埋点，默认埋点，私有化部署不买点
sendIoLog: true

sendLogDebug: true
springdoc:
  api-docs:
    enabled: true
    path: /v3/api-docs
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
