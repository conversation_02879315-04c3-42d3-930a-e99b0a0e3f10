package com.glodon.qydata.service.excel;

import java.util.List;
import java.util.Map;

/**
 * @Class com.glodon.qydata.service.excel ExcelOutAndInService
 * <AUTHOR>
 * @Email <EMAIL>
 * @Description 解析excel
 * @Date 11:34 2022/7/6
 **/
public interface ExcelParseService {
    String modelType();
    /***
     * @description: 下载模板
     * @param excelData 1
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/7/6 11:35
     */
    String parse(List<List<String>> excelData, Map<String, String> param);
}
