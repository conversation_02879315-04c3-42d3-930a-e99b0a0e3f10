package com.glodon.qydata.entity.standard.feature;


import com.glodon.qydata.common.annotation.ExcelParam;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/10/22 18:32
 */
@Data
public class TzExcel {
    @ExcelParam(index = 1)
    private String num;
    @ExcelParam(value = "名称", index = 2)
    private String name;
    @ExcelParam(value = "数据类型", index = 3)
    private String typeCode;
    @ExcelParam(value = "枚举值", index = 4)
    private String option;
    @ExcelParam(value = "工程分类", index = 5)
    private String projectType;
    @ExcelParam(value = "是否启用", index = 6)
    private String isUsing;
    @ExcelParam(value = "是否必填", index = 7)
    private String isRequired;
    @ExcelParam(value = "是否计算口径", index = 8)
    private String isExpression;
}
