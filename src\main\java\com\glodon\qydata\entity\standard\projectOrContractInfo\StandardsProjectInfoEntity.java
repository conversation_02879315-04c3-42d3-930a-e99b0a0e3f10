package com.glodon.qydata.entity.standard.projectOrContractInfo;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.glodon.qydata.entity.system.QYFlag;
import com.glodon.qydata.util.mover.Movable;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * zb_standards_project_info_self - 主键
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("zb_standards_project_info_self")
@Schema(name="StandardsProjectInfoEntity对象", description="zb_standards_project_info_self")
public class StandardsProjectInfoEntity extends QYFlag implements Serializable, Movable {

    private static final long serialVersionUID = 1L;

    @Schema(name = "主键")
    private Long id;

    @Schema(name = "项目信息名")
    private String name;

    @Schema(name = "数据类型，对应dictionary表中type_name为数据类型的记录，text文本类，number数值类，date日期类，select单选类，selects多选类")
    private String typeCode;

    @Schema(name = "单位")
    private String unit;

    @Schema(name = "枚举值")
    private String selectList;

    @Schema(name = "是否已启用，0：未启用，1：已启用")
    private Integer isUsing;

    @Schema(name = "是否必填，0：非必填，1：必填")
    private Integer isRequired;

    @Schema(name = "是否计算口径, 0：否，1：是")
    private Integer isExpression;

    @Schema(name = "创建人")
    private Long creatorId;

    @Schema(name = "创建时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime createTime;

    @Schema(name = "更新人")
    private Long updaterId;

    @Schema(name = "更新时间")
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    private LocalDateTime updateTime;

    @Schema(name = "备注")
    private String remark;

    @Schema(name = "企业编码")
    private String customerCode;

    @Schema(name = "是否已被删除，0：未删除，1：已删除")
    private Integer isDeleted;

    @Schema(name = "排序字段")
    private Integer ord;

    @Schema(name = "标准数据的类型，1：项目信息，2：合同信息")
    private Integer standardDataType;

    @Schema(name = "发布表对应id")
    private Long originId;

    @Schema(name = "口径编码")
    @TableField(exist = false)
    private String expressionCode;

    private String globalId;
    /**
     * 是否无效 1 无效，空为有效
     */
    private Integer invalid;


    @Schema(name = "项目信息类型，1：item，2：group")
    private Integer type;

    @Schema(name = "项目信息父级id")
    private Long pid;

    @Schema(name = "是否通用信息(0：否；1：是)")
    private Integer isCommon;

    @Schema(name = "一级工程分类编码，以逗号隔开")
    private String categoryCode;

    @Schema(name = "子节点")
    @TableField(exist = false)
    private List<StandardsProjectInfoEntity> childrenList;


    @Override
    public Long getUniqueIdentity() {
        return id;
    }

    @Override
    public Integer getOrdValue() {
        return ord;
    }

    @Override
    public void setOrdValue(Integer ordValue) {
        this.ord = ordValue;
    }
}
