<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.repairdata.TempRepairExpressionMapper">
  <resultMap id="ProjectFeature" type="com.glodon.qydata.entity.standard.feature.ProjectFeature" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="expression_id" property="expressionId" jdbcType="BIGINT" />
    <result column="feature_level" property="featureLevel" jdbcType="VARCHAR" />
    <result column="is_sync" property="isSync" jdbcType="BIT" />
    <result column="type" property="type" jdbcType="INTEGER"/>
    <result column="project_type" property="projectType" jdbcType="VARCHAR" />
    <result column="is_using" property="isUsing" jdbcType="BIT" />
    <result column="is_default" property="isDefault" jdbcType="BIT" />
    <result column="is_required" property="isRequired" jdbcType="BIT" />
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="is_from_system" property="isFromSystem" jdbcType="BIT" />
    <result column="zb_project_feature_id" property="zbProjectFeatureId" jdbcType="BIGINT" />
    <result column="customer_code" property="customerCode" jdbcType="VARCHAR" />
    <result column="is_deleted" property="isDeleted" jdbcType="BIT" />
    <result column="create_global_id" property="createGlobalId" jdbcType="BIGINT" />
    <result column="update_global_id" property="updateGlobalId" jdbcType="BIGINT" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="product_pro_type" property="productProType" jdbcType="INTEGER"/>
    <result column="is_expression" property="isExpression" jdbcType="INTEGER"/>
    <result column="ord_trade" property="ordTrade" jdbcType="INTEGER"/>
    <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
    <result column="is_search_condition" property="isSearchCondition" jdbcType="INTEGER"/>
    <result column="trade_name" property="tradeName" jdbcType="VARCHAR" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="option" jdbcType="VARCHAR" property="option" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="expression_code" jdbcType="VARCHAR" property="expressionCode" />
    <result column="is_expression_default" jdbcType="INTEGER" property="isExpressionDefault" />
  </resultMap>
  <select id="selectRepeatCode" resultType="java.lang.Integer">
  select count(distinct name) as nameCount
  from zb_standards_expression where `qy_code` = #{customerCode,jdbcType=VARCHAR} and invalid = 0 and type_code = 'number' group by qy_code ,`type`,expression_code,invalid having nameCount > 1 limit 1
  </select>

  <select id="selectRepeatName" resultType="java.lang.Integer">
  select count(distinct expression_code) as codeCount
  from zb_standards_expression where `qy_code` = #{customerCode,jdbcType=VARCHAR} and invalid = 0 and type_code = 'number' group by qy_code ,`type`,name,invalid having codeCount > 1 limit 1
  </select>

  <select id="selectRepeatRecord" resultType="java.lang.Integer">
  select count(1) as repeateCount
  from zb_standards_expression where `qy_code` = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and type_code = 'number' group by qy_code ,`type`,expression_code, name,invalid having repeateCount > 1  limit 1
  </select>


  <select id="selectFeature" resultMap="ProjectFeature">
    select id, expression_id, expression_code from zb_project_feature_standards
    where customer_code = #{customerCode,jdbcType=VARCHAR} and
    <foreach collection="expressionIds" index="index" item="item" separator="," open="expression_id IN (" close=")">
      #{item}
    </foreach>
  </select>

  <select id="checkFeature" resultMap="ProjectFeature">
    select id, expression_id, expression_code from zb_project_feature_standards
    where customer_code = #{customerCode,jdbcType=VARCHAR}
    and expression_id
    in (select id from zb_standards_expression where qy_code = #{customerCode,jdbcType=VARCHAR} and invalid = 1)
  </select>
  <update id="updateExpression" parameterType="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
    update zb_standards_expression
    <set>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="repairName != null">
        `repair_name` = #{repairName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT} and qy_code = #{qyCode,jdbcType=VARCHAR}
  </update>
  <update id="updateSelfExpression" parameterType="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
    update zb_standards_expression_self
    <set>
      <if test="name != null">
      `name` = #{name,jdbcType=VARCHAR},
    </if>
    </set>
    where origin_id = #{id,jdbcType=BIGINT} and qy_code = #{qyCode,jdbcType=VARCHAR}
  </update>
  <update id="setExpressionInvalid" parameterType="java.lang.Long">
    update zb_standards_expression  set invalid = 1 where qy_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
    <foreach collection="expressionIds" index="index" item="item" separator="," open="id IN (" close=")">
      #{item}
    </foreach>
  </update>
  <update id="synsSelfExpression" parameterType="java.lang.Long">
  update zb_standards_expression_self  set invalid = 1 where qy_code = #{customerCode,jdbcType=VARCHAR}  and invalid = 0 and
  <foreach collection="expressionIds" index="index" item="item" separator="," open="origin_id IN (" close=")">
    #{item}
  </foreach>
  </update>

</mapper>