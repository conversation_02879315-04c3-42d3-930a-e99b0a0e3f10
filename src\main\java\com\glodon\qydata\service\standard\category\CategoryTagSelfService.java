package com.glodon.qydata.service.standard.category;

import com.glodon.qydata.dto.CategoryTagRelListDto;
import com.glodon.qydata.entity.standard.category.CategoryTag;
import com.glodon.qydata.entity.standard.category.CategoryTagEnum;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.category.CategoryTagEnumRel;

import java.util.List;
import java.util.Map;

/**
 * @author: luoml-b
 * @date: 2024/10/22 14:22
 * @description:
 */
public interface CategoryTagSelfService {

    List<CategoryTag> getCategoryTag(String enterpriseId);

    List<CategoryTagEnum> getCategoryTagEnum(String enterpriseId, Long tagId);

    CategoryTagRelListDto getCategoryTagRelList(String enterpriseId);

    void modifyCategoryTag(String enterpriseId, List<CategoryTag> categoryTagList);

    void modifyCategoryTagEnum(String enterpriseId, Long tagId, List<CategoryTagEnum> categoryTagEnumList);

    List<CommonProjCategory> dealWithTag(String enterpriseId, List<CommonProjCategory> categoryList, boolean selfFlag, boolean includeTag, String categoryCode1);

    List<CommonProjCategory> assembleTagTree(List<CommonProjCategory> categoryList,
                                             List<CategoryTagEnumRel> categoryTagEnumRels,
                                             Map<Long, List<CategoryTagEnum>> tagIdMap);

    Boolean relSaveBatch(String enterpriseId, List<CategoryTagEnumRel> categoryTagRelList);
}
