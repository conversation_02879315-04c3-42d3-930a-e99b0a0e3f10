package com.glodon.qydata.mapper.temp;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.qydata.controller.temp.project.TempStandardsProjectInfoEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * zb_standards_project_info - 主键 Mapper 接口  todo 线上执行后，可直接删除掉
 * </p>
 *
 * <AUTHOR>
 * @since 2021-10-15
 */
public interface TempStandardsProjectInfoMapper extends BaseMapper<TempStandardsProjectInfoEntity> {
    Integer selectRepeatRecord(@Param("customerCode") String customerCode);
    void setProjectInfoInvalId(@Param("customerCode") String customerCode, @Param("ids") List<Long> ids);
    void synsSelfProjectInfo(@Param("customerCode") String customerCode, @Param("originIds") List<Long> originIds);
}
