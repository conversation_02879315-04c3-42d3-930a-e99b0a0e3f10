package com.glodon.qydata.controller.temp.areaRepair;

import lombok.Data;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Data
public class AreaDiffResult {
    private List<AreaDiff> addList = new ArrayList<>();
    private List<AreaDiff> delList = new ArrayList<>();
    private List<AreaDiff> nameChangeList = new ArrayList<>();
    private List<AreaDiff> dataChangeList = new ArrayList<>();

    public String toMarkdownMsg() {
        List<String> addStrList = addList.stream().sorted(Comparator.comparing(AreaDiff::getAreaCode)).map(x -> "【" + x.getAreaCode() + "】\t" + x.getNewNamePath()).collect(Collectors.toList());
        List<String> delStrList = delList.stream().sorted(Comparator.comparing(AreaDiff::getAreaCode)).map(x -> "【" + x.getAreaCode() + "】\t" + x.getNamePath()).collect(Collectors.toList());
        List<String> nameChangeStrList = nameChangeList.stream().sorted(Comparator.comparing(AreaDiff::getAreaCode)).map(x -> "【" + x.getAreaCode() + "】\tdb：" + x.getNamePath() + "\tgd：" + x.getNewNamePath()).collect(Collectors.toList());
        List<String> dataChangeStrList = dataChangeList.stream().sorted(Comparator.comparing(AreaDiff::getAreaCode)).map(x -> "【" + x.getAreaCode() + "】\t" + x.getRemark()).collect(Collectors.toList());

        return "### <font face=‘华云彩绘’ color=#DC143C>新增地区（" + addList.size() + "）：</font>\n        " + String.join("\n        ", addStrList) + "\n" +
                "### <font face=‘华云彩绘’ color=#DC143C>删除地区（" + delList.size() + "）：</font>\n        " + String.join("\n        ", delStrList) + "\n" +
                "### <font face=‘华云彩绘’ color=#DC143C>名称变更（" + nameChangeList.size() + "）：</font>\n        " + String.join("\n        ", nameChangeStrList) + "\n" +
                "### <font face=‘华云彩绘’ color=#DC143C>数据变更（" + dataChangeList.size() + "）：</font>\n        " + String.join("\n        ", dataChangeStrList);
    }
}