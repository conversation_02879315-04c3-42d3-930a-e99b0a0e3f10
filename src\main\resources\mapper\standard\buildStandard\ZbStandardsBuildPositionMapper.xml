<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildPositionMapper">

    <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPosition">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="buildCategoryId" column="build_category_id" jdbcType="BIGINT"/>
            <result property="standardId" column="standard_id" jdbcType="BIGINT"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="ord" column="ord" jdbcType="INTEGER"/>
            <result property="type" column="type" jdbcType="TINYINT"/>
            <result property="categoryCode" column="category_code" jdbcType="VARCHAR"/>
            <result property="enterpriseId" column="enterprise_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,build_category_id,standard_id,
        name,ord,type,
        category_code,enterprise_id,create_time,
        update_time
    </sql>
    <sql id="Temp_Column_List">
        id,build_category_id,standard_id,
        name,ord,type,
        category_code,origin_id,create_time,
        update_time
    </sql>
    <select id="selectByStandardId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from zb_standards_build_position where standard_id = #{standardId} order by `ord`
    </select>

    <select id="selectByStandardIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from zb_standards_build_position where standard_id in
         <foreach collection="list" item="item" close=")" open="(" separator=",">
             #{item}
         </foreach>
         order by `ord`
    </select>

    <select id="selectSelfByStandardIds" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPosition">
        select <include refid="Temp_Column_List"/> from zb_standards_build_position_self where standard_id in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        order by `ord`
    </select>
    <select id="selectSelfByStandardId" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPosition">
        select <include refid="Temp_Column_List"/> from zb_standards_build_position_self where standard_id = #{standardId} order by `ord`
    </select>

    <select id="selectListByPositionInfoAndStandardIds" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPosition">
        select <include refid="Base_Column_List"/> from zb_standards_build_position where standard_id in
        <foreach collection="standardIds" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
        and category_code = #{categoryCode}
         <if test="positionName != null and positionName != ''">
             and name = #{positionName}
             and type = 0
         </if>
        order by create_time DESC
    </select>

    <delete id="deleteSelfByStandardId">
        delete from zb_standards_build_position_self where standard_id = #{standardId}
    </delete>
    <insert id="batchSaveSelf">
        insert into zb_standards_build_position_self
        ( id,build_category_id,standard_id,name,ord,type,category_code,origin_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},#{item.buildCategoryId},#{item.standardId},#{item.name},
            #{item.ord},#{item.type},#{item.categoryCode},#{item.originId}
            )
        </foreach>
    </insert>
    <insert id="batchSave">
        insert into zb_standards_build_position
        ( id,build_category_id,standard_id,
        name,ord,type,
        category_code,enterprise_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},#{item.buildCategoryId},#{item.standardId},#{item.name},
            #{item.ord},#{item.type},#{item.categoryCode},
            #{item.enterpriseId}
            )
        </foreach>
    </insert>
    <delete id="deleteByStandardIds">
        delete
        from zb_standards_build_position
        where standard_id in
        <foreach collection="standardIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteSelfByStandardIds">
        delete
        from zb_standards_build_position_self
        where standard_id in
        <foreach collection="standardIds" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </delete>
</mapper>
