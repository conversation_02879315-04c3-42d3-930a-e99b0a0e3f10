package com.glodon.qydata.mapper.standard.category;


import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.util.mover.ElementMover;
import com.glodon.qydata.vo.standard.category.CommonProjCategoryUpdateVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/17 16:50
 */
@Repository
public interface CommonProjCategorySelfMapper extends ElementMover.DatabaseSaver<CommonProjCategory> {

    /**
     * 查询某企业的所有一级分类
     * @param customerCode
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/19 14:53
     */
    List<CommonProjCategory> queryTopCategoryList(String customerCode, Integer type);

    /**
     * 根据commonprojcategoryid查询某企业的分类
     * @return com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory
     * <AUTHOR>
     * @date 2021/10/19 14:53
     */
    CommonProjCategory getByCategoryCode(String categoryCode, String customerCode, Integer type);

    /**
     * 查询某分类的所有下一级子节点
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/19 14:54
     */
    List<CommonProjCategory> listDirectChildrenByCode(String parentCategoryCode, String customerCode, Integer type);
    
    /**
     * 新增工程分类
     * @param category
     * @return int
     * <AUTHOR>
     * @date 2021/10/19 14:54 
     */
    int saveCommonProjCategory(CommonProjCategory category);
    /***
     * @description: 导入分类
     * @param list 1
     * @return void
     * @throws 
     * <AUTHOR>
     * @date 2022/7/8 16:36
     */
    void saveCommonProjCategoryList(List<CommonProjCategory> list);
    
    /**
     * 同父级重名校验
     * @param customerCode
     * @param categoryName
     * @param level
     * @param pCode
     * @param divideSelf
     * @return int
     * <AUTHOR>
     * @date 2021/10/19 10:53
     */
    int queryNameCount(@Param("customerCode") String customerCode,
                       @Param("categoryName") String categoryName,
                       @Param("level") int level,
                       @Param("pCode") String pCode,
                       @Param("divideSelf")String divideSelf,
                       @Param("type") Integer type);
    
    /**
     * 同父级重名的分类（包括已删除的）
     * @param customerCode
     * @param categoryName
     * @param level
     * @param pCode
     * @return com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory
     * <AUTHOR>
     * @date 2021/10/19 14:54 
     */
    CommonProjCategory queryRepeatName(@Param("customerCode") String customerCode,
                                       @Param("categoryName") String categoryName,
                                       @Param("level") int level,
                                       @Param("pCode") String pCode,
                                       @Param("type") Integer type);
    
    /**
     * 批量更新ord
     * @param list
     * @return void
     * <AUTHOR>
     * @date 2021/10/19 14:54 
     */
    @Override
    void batchUpdateOrd(@Param("list") List<CommonProjCategory> list);
    
    /**
     * 根据主键更新
     * @param commonProjCategory
     * @return int
     * <AUTHOR>
     * @date 2021/10/19 14:54 
     */
    int updateByPrimaryKey(CommonProjCategory commonProjCategory);
    /**
     * 根据主键更新
     * @param ids
     * @param globalId
     * @param isUsing
     * @return int
     * <AUTHOR>
     * @date 2021/10/19 14:54
     */
    int updateByPrimaryKeys(@Param("ids")List<String> ids, @Param("globalId")String globalId, @Param("isUsing")int isUsing);
    
    /**
     * 逻辑删除工程子分类
     * @return java.lang.Integer
     * <AUTHOR>
     * @date 2021/10/19 14:54 
     */
    Integer deleteChildCategory(@Param("customerCode") String customerCode,
                                @Param("type") int type,
                                @Param("level1s") List<String> level1s,
                                @Param("level2s") List<String> level2s,
                                @Param("level3s") List<String> level3s,
                                @Param("level4s") List<String> level4s);

    /**
     * 物理删除指定企业编码下的暂存工程分类数据
     * @param customerCode
     */
    void deleteSelfByCustomerCode(@Param("customerCode") String customerCode,
                                  @Param("type") Integer type);

    /**
     * 查询某用户暂存的的所有分类
     * @return java.util.List<com.glodon.zbw.dataManager.standardData.category.domain.CommonProjCategory>
     * <AUTHOR>
     * @date 2021/10/19 14:53
     */
    List<CommonProjCategory> selectSelfAll( @Param("customerCode") String customerCode,
                                            @Param("type") Integer type,
                                            @Param("isShowDelete") Integer isShowDelete);

    /**
     * 批量插入暂存数据
     * @param list
     * @return int
     * <AUTHOR> @date 2022/01/10 14:55
     */
    int saveBatchSelfProjCategory(@Param("list") List<CommonProjCategory> list);

    /**
     * 根据主键查询暂存工程分类
     * @param categoryId
     * @return
     */
    CommonProjCategory getSelfCategoryByPrimaryKey(@Param("categoryId") Integer categoryId);
    /**
     * 根据主键查询暂存工程分类
     * @param list
     * @return
     */
    List<CommonProjCategory> getSelfCategoryByIds(List<CommonProjCategoryUpdateVo> list);

    void updateBatch(List<CommonProjCategory> list);

    List<Long> selectAllGlobalIds(@Param("customerCode") String customerCode);

    void batchUpdateDelStatus(@Param("list") List<Integer> list);
}
