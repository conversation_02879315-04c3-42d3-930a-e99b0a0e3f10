package com.glodon.qydata.config.handler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

import java.sql.*;
import java.time.LocalDate;

/**
 * @className: LocalDateTypeHandler
 * @description: LocalDateTypeHandler
 * @author: zhaoyj-g
 * @date: 2020/7/15
 **/
public class LocalDateTypeHandler extends BaseTypeHandler<LocalDate> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i,
                                    LocalDate parameter, JdbcType jdbcType) throws SQLException {
        ps.setDate(i, Date.valueOf(parameter));
    }

    @Override
    public LocalDate getNullableResult(ResultSet rs, String columnName)
            throws SQLException {
        try {
            return rs.getDate(columnName).toLocalDate();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public LocalDate getNullableResult(CallableStatement cs, int columnIndex)
            throws SQLException {
        try {
            return cs.getDate(columnIndex).toLocalDate();
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public LocalDate getNullableResult(ResultSet rs, int columnIndex)
            throws SQLException {
        return null;
    }
}
