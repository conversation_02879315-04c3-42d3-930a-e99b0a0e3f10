package com.glodon.qydata.config;

import cn.hutool.core.collection.CollUtil;
import com.glodon.qydata.common.NotPrivateDeploymentCondition;
import com.glodon.qydata.common.enums.ShardingTableNameEnum;
import com.glodon.qydata.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingAlgorithm;
import org.apache.shardingsphere.sharding.api.sharding.complex.ComplexKeysShardingValue;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Conditional;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * @author: luoml-b
 * @date: 2023/7/14 08:33
 * @description: 自定义分片算法，根据路由表判断最终去哪一张表
 */
@Component
@Slf4j
@Conditional(NotPrivateDeploymentCondition.class)
public class CustomShardingAlgorithm implements ComplexKeysShardingAlgorithm<String> {


    private static ShardingConfig shardingConfig;


    @Autowired
    public void setShardingService(ShardingConfig shardingConfig) {
        CustomShardingAlgorithm.shardingConfig = shardingConfig;
    }


    /**
     * @description: 自定义分片逻辑，根据路由表决定份表名称
     * @author: luoml-b
     * @date: 2023/7/18 16:29
     * @param: collection
     * @param: preciseShardingValue
     * @return: java.lang.String
     **/
    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, ComplexKeysShardingValue<String> shardingValue) {
        Map<String, Collection<String>> columnNameAndShardingValuesMap = shardingValue.getColumnNameAndShardingValuesMap();
        try {
            String customerCode = CollUtil.getFirst(CollUtil.getFirst(columnNameAndShardingValuesMap.values()));
            String logicTableName = shardingValue.getLogicTableName();
            if (StringUtils.isNotBlank(customerCode)) {
                ShardingTableNameEnum shardingTableNameEnum = ShardingTableNameEnum.getShardingTableNameEnumByTableName(logicTableName);
                if (Objects.isNull(shardingTableNameEnum)) {
                    throw new BusinessException("没有对应的ShardingTableNameEnum");
                }
                return CollUtil.toList(shardingConfig.getTableName(customerCode, shardingTableNameEnum.getTableType()));
            }
            throw new BusinessException("customerCode为空");
        } catch (Exception e) {
            log.error("获取分表错误，customerCode:{}", columnNameAndShardingValuesMap, e);
            throw new BusinessException("获取分表错误");
        }
    }

    public Properties getProps() {
        // ShardingSphere 5.3.2 API要求：返回算法属性
        return new Properties();
    }

    @Override
    public void init(Properties properties) {

    }
}
