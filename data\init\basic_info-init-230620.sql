-- db_cost_data_platform_pro.repair_log definition

CREATE TABLE `repair_log` (
  `id` bigint(20) NOT NULL COMMENT '�û�ID',
  `customer_code` varchar(100) DEFAULT NULL COMMENT '��ҵ����',
  `is_edited` tinyint(1) DEFAULT NULL COMMENT '�Ƿ����ݱ��û��޸Ĺ�',
  `is_repeat` tinyint(1) DEFAULT NULL COMMENT '�Ƿ������ظ�',
  `erro_type` varchar(100) DEFAULT NULL COMMENT '���ݴ�������',
  `repair_status` int(11) DEFAULT NULL COMMENT '�޸�״̬1-���޸���2���޸�ʧ��',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '����ʱ��',
  `version` int(11) DEFAULT NULL COMMENT '�޸��汾��',
  PRIMARY KEY (`id`),
  KEY `zb_lib_user_repair_customer_code_IDX` (`customer_code`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='�����޸���¼���޸������ã�';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression ADD repair_name varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '�޸�ǰ����ھ���,�ƶ�Ӧ�ֶ�name';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '�����Ƿ���Ч��1Ϊ��Ч��0Ϊ��Ч';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression_self ADD invalid int(11) DEFAULT 0 NOT NULL COMMENT '�����Ƿ���Ч��1Ϊ��Ч��0Ϊ��Ч';
ALTER TABLE db_cost_data_platform_pro.zb_standards_expression_self COMMENT='���������������ͼ�����ھ��ݴ��(2023��5��29��ȷ��ҵ���ϴ˱��Ѿ�û������)';
ALTER TABLE db_cost_data_platform_pro.zb_project_feature_standards ADD repair_expression_id bigint(20) NULL COMMENT '�޸�����ʱexpression_id�ı���';