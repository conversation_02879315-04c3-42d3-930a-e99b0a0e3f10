package com.glodon.qydata.controller.system;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.service.system.IoDataService;
import com.glodon.qydata.util.IPUtils;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.system.UserBehaviorVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 埋点发送统计
 * Created by weijf on 2022/3/25.
 */
@Slf4j
@RestController
@RequestMapping("/basicInfo/common")
public class SendIoController extends BaseController {
    @Autowired
    private IoDataService ioDataService;
    @Value("${sendIoLog}")
    private Boolean sendIoLog;
    @Value("${privateDeployment}")
    private Boolean privateDeployment;
    final ThreadPoolExecutor ioExecutor = new ThreadPoolExecutor(5, 10, 3000, TimeUnit.SECONDS, new LinkedBlockingQueue<Runnable>());

    @PostMapping(value = "/sendIoLog")
    public ResponseVo sendLog(@RequestBody UserBehaviorVO userBehavior, HttpServletRequest request) {
        if (Boolean.FALSE.equals(sendIoLog) || Boolean.TRUE.equals(privateDeployment)) {
            return ResponseVo.success();
        }
        try {
            userBehavior.setGlobalId(getGlobalId());
            userBehavior.setGldToken(getToken());
            userBehavior.setSgToken(getSgToken());
            userBehavior.setUserAgent(request.getHeader("User-Agent") == null ? request.getHeader("user-agent") : request.getHeader("User-Agent"));
            userBehavior.setSessionid(request.getSession().getId());
            String ip = IPUtils.getIpAddr(request);

            ioExecutor.execute(() ->{
                try {
                    ioDataService.sendLog(userBehavior, ip);
                } catch (Exception e) {
                    log.error("Failed to send log: " + e.getMessage(), e);
                }
            });
        } catch (Exception e) {
            log.error("Failed to send log: " + e.getMessage(), e);
        }

        return ResponseVo.success();
    }
}
