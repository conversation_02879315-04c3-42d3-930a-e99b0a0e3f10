package com.glodon.qydata.vo.init;

import com.glodon.qydata.common.annotation.ExcelParam;
import lombok.Data;

/**
 * @Description: 导入内置工程分类Excel接收的VO
 * @author: weijf
 * @date: 2022-11-01
 */
@Data
public class CommonCategoryExcelVo {

    @ExcelParam(value = "序号", index = 0) //未使用
    private String indexNum;

    @ExcelParam(value = "层次码", index = 1) //未使用
    private String levelCode;

    @ExcelParam(value = " 分类名称① ② ③", index = 2) //未使用
    private String typeName;

    @ExcelParam(value = "修改后分类名称", index = 3)
    private String categoryName;

    @ExcelParam(value = "业态层级", index = 4)
    private String categoryLevel;

    @ExcelParam(value = "说明", index = 5)
    private String desc;
}
