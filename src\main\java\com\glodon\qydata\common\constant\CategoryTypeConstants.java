package com.glodon.qydata.common.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @author: weijf
 * @date: 2022-10-11
 */
public class CategoryTypeConstants {

    /**
     * 工程分类种类，
     * 1：系统内置
     * 2 慧果《建设工程分类标准》GB/T 50841-2013
     * 3 慧果工程造价指标分类及编制指南(中价协2021)
     */
    public static final Integer TYPE_DEFAULT = 1;
    public static final Integer TYPE_HG_STANDARD = 2;
    public static final Integer TYPE_HG_ZJX = 3;

    public static final List<Integer> categoryTypeList = new ArrayList<>();

    static {
        categoryTypeList.add(TYPE_DEFAULT);
        categoryTypeList.add(TYPE_HG_STANDARD);
        categoryTypeList.add(TYPE_HG_ZJX);
    }

}
