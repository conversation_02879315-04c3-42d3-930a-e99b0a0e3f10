package com.glodon.qydata.vo.standard.expression;

import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: 计算口径字典表返回vo
 * <AUTHOR>
 * @date 2021/11/5 14:49
 */
@Data
@ToString
public class ExpressionResultVo implements Serializable {

    private static final long serialVersionUID = -982225972470171882L;

    private Long id;

    /**
     * 工程特征名称
     */
    private String name;

    /**
     * 单位
     */
    private String unit;

    /**
     * 计算口径排序
     */
    private Integer expressionOrd;

    /**
     * 计算口径是否系统内置：1 是； 0 否
     */
    private Integer expressionIsFromSystem;

    /**
     * 计算口径是否启用:1启用；0未启用；
     */
    private Integer expressionIsUsing;

    /**
     * 计算口径创建时间
     */
    private Date expressionCreateTime;

    /**
     * 计算口径创建用户ID
     */
    private Long expressionCreateGlobalId;

    /**
     * 创建用户姓名
     */
    private String expressionCreateGlobalName;

    /**
     * 计算口径备注
     */
    private String expressionRemark;

    /**
     * 计算口径编码
     */
    private String expressionCode;
    /**
     * 是否无效 1 无效，空为有效
     */
    private Integer invalid;

    /**
     * 工程特征修复前名称
     */
    private String repairName;
}