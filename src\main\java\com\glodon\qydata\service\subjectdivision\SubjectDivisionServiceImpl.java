package com.glodon.qydata.service.subjectdivision;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.log.Log;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.DigitalCostConstants;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.zbsq.Item;
import com.glodon.qydata.entity.zbsq.ItemData;
import com.glodon.qydata.entity.zbsq.ZbsqCategory;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.vo.common.ResponseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * @author: luoml-b
 * @date: 2024/5/9 14:47
 * @description: 项目划分service实现类
 */
@Service
@Slf4j
public class SubjectDivisionServiceImpl implements SubjectDivisionService {

    @Autowired
    private SubjectDivisionFeignService subjectDivisionFeignService;

    @Autowired
    private CommonProjCategoryService commonProjCategoryService;

    @Autowired
    private SubjectDivisionService subjectDivisionService;


    @Override
    public List<Item> getTemplateByCategory(String categoryCode) {
        ResponseVo<ItemData> itemDataResponseVo;
        try {
            itemDataResponseVo = subjectDivisionFeignService.getTemplateByCategory(categoryCode, 1);
        } catch (Exception e) {
            log.error("调用项目划分科目模板出错，categoryCode：{}", categoryCode, e);
            throw new BusinessException("调用项目划分科目模板出错，请稍后再试");
        }
        if (DigitalCostConstants.RESULT_CODE_200.equals(itemDataResponseVo.getCode())) {
            List<Item> itemList = itemDataResponseVo.getData().getItemList();
            if (CollUtil.isNotEmpty(itemList)) {
                itemList.forEach(item -> item.setFullName(getFullItemName(item.getId(), itemList)));
                return itemList;
            }
            return Collections.emptyList();
        } else if (DigitalCostConstants.RESULT_CODE_409.equals(itemDataResponseVo.getCode())) {
            throw new BusinessException(DigitalCostConstants.RESULT_CODE_409, "工程分类不存在");
        }
        throw new BusinessException("调用项目划分科目模板出错，请稍后再试");
    }

    @Override
    public String getFullItemName(String getItemDivisionSubjectId, List<Item> templateByCategory) {
        Optional<Item> item = templateByCategory.stream()
                .filter(i -> i.getId().equals(getItemDivisionSubjectId))
                .findFirst();

        return item.map(value -> getParentItemName(value, templateByCategory)).orElse(null);

    }

    @Override
    public List<ZbsqCategory> getAllCategory() {
        try {
            ResponseVo<List<ZbsqCategory>> responseVo = subjectDivisionFeignService.getAllCategory(0);
            if (DigitalCostConstants.RESULT_CODE_200.equals(responseVo.getCode())) {
                return responseVo.getData();
            }
        } catch (Exception e) {
            throw new BusinessException("调用项目获取所有工程分类出错！");
        }
        return Collections.emptyList();
    }

    @Override
    public List<Item> getTemplateByCategoryWithInit(String categoryCode) {
//        CommonProjCategory categoryByCode = commonProjCategoryService.getCategoryByCode(categoryCode, RequestContent.getCustomerCode());
//        if (ObjectUtil.isNull(categoryByCode)) {
//            return Collections.emptyList();
//        }

        getAllCategory();
        List<Item> templateByCategory;
        try {
            templateByCategory = getTemplateByCategory(categoryCode);
        } catch (Exception e) {
            if (e instanceof BusinessException && DigitalCostConstants.RESULT_CODE_409.equals(((BusinessException) e).getCode())){
                return Collections.emptyList();
            }
            throw e;
        }
        return templateByCategory;
    }

    private String getParentItemName(Item item, List<Item> templateByCategory) {
        Optional<Item> parentItem = templateByCategory.stream()
                .filter(i -> i.getId().equals(item.getPid()))
                .findFirst();

        if (parentItem.isPresent()) {
            String parentName = getParentItemName(parentItem.get(), templateByCategory);
            return parentName + "/" + item.getName();
        }

        return item.getName();
    }
}
