use `db_cost_data_platform_pro`;
CREATE TABLE `db_cost_data_platform_pro`.`sharding_route` (
                                  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键',
                                  `customer_code` varchar(100) DEFAULT NULL COMMENT '企业编码',
                                  `enterprise_id` varchar(30) DEFAULT NULL COMMENT '主键id',
                                  `table_type` int(5) DEFAULT NULL COMMENT '分表类型(0：zb_project_feature_category_view_standards；1：tb_commonprojcategory_standards，对应枚举ShardingTableNameEnum)',
                                  `current_table_name` varchar(100) DEFAULT NULL COMMENT '分表名称',
                                  `create_time` datetime NOT NULL,
                                  `update_time` datetime DEFAULT NULL,
                                  PRIMARY KEY (`id`),
                                  KEY `customer_code_IDX` (`customer_code`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 DEFAULT CHARSET = utf8mb4 COMMENT = '路由表';
