package com.glodon.qydata.util;

import java.io.File;
import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;

public class EmptyUtil {
    public static boolean isNotEmpty(Object obj) {
        if (isNull(obj)) {
            return false;
        }
        if (obj.getClass().isArray()) {
            return Array.getLength(obj) > 0;
        }
        if ((obj instanceof Collection)) {
            return ((Collection) obj).size() > 0;
        }
        if ((obj instanceof Map)) {
            return ((Map) obj).size() > 0;
        }
        if ((obj instanceof String)) {
            return obj.toString().trim().length() > 0;
        }
        if ((obj instanceof File)) {
            return ((File) obj).exists();
        }
        if ((obj instanceof Boolean)) {
            return ((Boolean) obj).booleanValue();
        }
        return true;
    }

    /**
     * @Title: isStringNull
     * @Description: 是否等于字符串格式的null  "null"
     * @throws
     * @param: [str]
     * @return: boolean
     * @auther: zhaohy-c
     * @date: 2019/12/25 15:12
     */
    public static boolean isStringNull(String str){
        return "null".equals(str);
    }

    public static boolean isEmpty(Object obj) {
        return !isNotEmpty(obj);
    }

    public static boolean isNull(Object obj) {
        return obj == null;
    }

    public static boolean isNotNull(Object obj) {
        return !isNull(obj);
    }

    public static String toString(Object obj) {
        if (obj != null) {
            return obj.toString();
        } else {
            return "";
        }
    }

}
