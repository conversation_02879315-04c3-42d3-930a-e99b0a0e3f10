<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.category.CategoryTagEnumMapper">

    <select id="selectByTagId" resultType="com.glodon.qydata.entity.standard.category.CategoryTagEnum">
        select * from category_tag_enum
        where enterprise_id = #{enterpriseId}
        <if test="tagId != null">
            and tag_id = #{tagId}
        </if>
    </select>

    <delete id="deleteByTagId">
        delete from category_tag_enum
        where enterprise_id = #{enterpriseId}
        <if test="tagId != null">
            and tag_id = #{tagId}
        </if>
    </delete>

    <insert id="saveBatch" parameterType="java.util.List" useGeneratedKeys="false">
        insert into category_tag_enum (id,name,enterprise_id,create_time,tag_id,ord) values
        <foreach collection="list" item="item" index="index" separator="," >
            (
            #{item.id},
            #{item.name},
            #{item.enterpriseId},
            #{item.createTime},
            #{item.tagId},
            #{item.ord}
            )
        </foreach>
    </insert>



</mapper>
