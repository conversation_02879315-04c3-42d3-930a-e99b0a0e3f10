package com.glodon.qydata.util.tree;

import com.google.common.collect.Lists;

import java.util.List;

@SuppressWarnings("serial")
public class TreeNodeDefault<T extends DataNode> implements TreeNode<T> {
	private T data;	
	private TreeNode<T> parent;
	private List<TreeNode<T>> children;
	private Integer ord;
	public TreeNodeDefault(){
		this.children = Lists.newArrayList();
	}
	public TreeNodeDefault(T data){
		this();
		this.data = data;
	}
	@Override
	public String getLevelcode() {
		return data.getLevelcode();
	}

	@Override
	public void setLevelcode(String levelcode) {
		data.setLevelcode(levelcode);		
	}

	@Override
	public String getSerialNo() {
		return data.getSerialNo();
	}

	@Override
	public void setSerialNo(String serialNo) {
		data.setSerialNo(serialNo);		
	}

	@Override
	public NodeType getNodeType() {
		return data.getNodeType();
	}

	@Override
	public void setNodeType(NodeType nodeType) {
		data.setNodeType(nodeType);	
	}

	@Override
	public List<TreeNode<T>> getChildren() {
		return children;
	}
	public T getData() {
		return data;
	}	
	public void setData(T data) {
		this.data = data;
	}
	@Override
	public TreeNode<T> getParent() {
		// TODO Auto-generated method stub
		return parent;
	}
	@Override
	public void setParent(TreeNode<T> parent) {
		this.parent = parent;		
	}
	@Override
	public Boolean getIsLeaf() {
		return data.getIsLeaf();
	}
	@Override
	public void setIsLeaf(Boolean isLeaf) {
		data.setIsLeaf(isLeaf);		
	}
	@Override
    public Integer getOrd() {
		return ord;
	}
	@Override
	public void setOrd(Integer ord) {
		this.ord = ord;
	}

	@Override
	public int getLevel() {
		return 0;
	}

}
