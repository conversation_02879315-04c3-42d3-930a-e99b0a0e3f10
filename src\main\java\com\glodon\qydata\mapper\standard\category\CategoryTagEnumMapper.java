package com.glodon.qydata.mapper.standard.category;

import com.glodon.qydata.entity.standard.category.CategoryTagEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: luoml-b
 * @date: 2024/10/22 15:00
 * @description:
 */
@Mapper
public interface CategoryTagEnumMapper {

    List<CategoryTagEnum> selectByTagId(@Param("enterpriseId") String enterpriseId, @Param("tagId") Long tagId);

    //List<CategoryTagEnum> selectByTagIds(@Param("enterpriseId") String enterpriseId, @Param("tagIds") List<Long> tagId);

    void deleteByTagId(@Param("enterpriseId") String enterpriseId, @Param("tagId") Long tagId);

    void saveBatch(@Param("list") List<CategoryTagEnum> list);
}
