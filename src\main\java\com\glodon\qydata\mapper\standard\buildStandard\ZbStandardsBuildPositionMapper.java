package com.glodon.qydata.mapper.standard.buildStandard;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPosition;
import com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildStandardDetailDesc;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【zb_standards_build_position(企业标准数据-建造标准定位)】的数据库操作Mapper
* @createDate 2022-08-01 10:57:17
* @Entity generator.domain.ZbStandardsBuildPosition
*/
@Repository
public interface ZbStandardsBuildPositionMapper extends BaseMapper<ZbStandardsBuildPosition> {

    List<ZbStandardsBuildPosition> selectByStandardId(Long standardId);

    void batchSaveSelf(List<ZbStandardsBuildPosition> list);

    /**
     * 根据建造标准查询暂存产品定位列表
     * @param standardId
     * @return
     */
    List<ZbStandardsBuildPosition> selectSelfByStandardId(Long standardId);

    /**
     * 根据建造标准id查询所有定位列表
     * @param list
     * @return
     */
    List<ZbStandardsBuildPosition> selectByStandardIds(List<Long> list);

    /**
     * 根据建造标准删除产品定位信息
     * @param standardId
     */
    void deleteSelfByStandardId(Long standardId);

    /**
     * 根据建造标准查询数据
     * @param list
     * @return
     */
    List<ZbStandardsBuildPosition> selectSelfByStandardIds(@Param("list") List<Long> list);

    /**
     * 根据建造标准ids 删除数据
     * @param standardIds
     */
    void deleteByStandardIds(List<Long> standardIds);

    /**
     * 批量保存企业数据
     * @param list
     */
    void batchSave(List<ZbStandardsBuildPosition> list);

    /**
     * 根据建造标准删除所有数据
     * @param standardIds
     */
    void deleteSelfByStandardIds(List<Long> standardIds);

    /**
     * 根据产品定位名称、工程分类编码、标准id集合查询数据列表
     * @param positionName
     * @param categoryCode
     * @param standardIds
     * @return List<ZbStandardsBuildPosition>
     */
    List<ZbStandardsBuildPosition> selectListByPositionInfoAndStandardIds( @Param("positionName") String positionName,
                                                                           @Param("categoryCode") String categoryCode,
                                                                           @Param("standardIds")  List<Long> standardIds);
}




