package com.glodon.qydata.service.init.self;

import com.glodon.qydata.entity.standard.buildStandard.*;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildCategoryMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildPositionDetailMapper;
import com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildPositionMapper;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 初始化产品定位相关表
 * <AUTHOR>
 * @date 2022-8-1
 */
@Service
public class InitBuildStandardPositionService {
    @Autowired
    private ZbStandardsBuildCategoryMapper zbStandardsBuildCategoryMapper;
    @Autowired
    private ZbStandardsBuildPositionMapper standardsBuildPositionMapper;
    @Autowired
    private ZbStandardsBuildPositionDetailMapper standardsBuildPositionDetailMapper;
    /**
     * 初始化产品定位数据表
     *
     */
    public void initData(ZbProjectStandard standard, List<ZbStandardsBuildStandardDetailDesc> detailDescLists){
        //初始化企业标准数据-建造标准-业态
        List<ZbStandardsBuildCategory> standardsBuildCategories = initBuildCategory(standard);
        //初始化产品定位列表
        List<ZbStandardsBuildPosition> positionList = initBuildPosition(standard, standardsBuildCategories);
        //初始化产品定位结果表
        initBuildPositionDetail(standard, detailDescLists, positionList);
    }

    /**
     * 初始化定位结果信息
     * @param standard
     * @param detailDescLists
     * @param positionList
     */
    private void initBuildPositionDetail(ZbProjectStandard standard, List<ZbStandardsBuildStandardDetailDesc> detailDescLists,
                                         List<ZbStandardsBuildPosition> positionList) {
        List<ZbStandardsBuildPositionDetail> list = Lists.newArrayList();
        List<ZbStandardsBuildPositionDetail> originList = standardsBuildPositionDetailMapper.selectByStandardId(standard.getOriginId());
        if(CollectionUtils.isEmpty(originList) || CollectionUtils.isEmpty(detailDescLists) || CollectionUtils.isEmpty(positionList)){
            return;
        }
        Map<Long,ZbStandardsBuildStandardDetailDesc> detailDescMap = detailDescLists.stream().collect(Collectors.toMap(ZbStandardsBuildStandardDetailDesc::getOriginId, x->x));
        Map<Long,ZbStandardsBuildPosition> buildPositionMap = positionList.stream().collect(Collectors.toMap(ZbStandardsBuildPosition::getOriginId, x->x));
        for(ZbStandardsBuildPositionDetail positionDetail : originList){
            ZbStandardsBuildPositionDetail detail = new ZbStandardsBuildPositionDetail();
            BeanUtils.copyProperties(positionDetail, detail);
            ZbStandardsBuildStandardDetailDesc detailDesc = detailDescMap.get(positionDetail.getStandardDetailDescId());
            if(detailDesc != null){
                detail.setStandardDetailDescId(detailDesc.getId());
                detail.setStandardDetailId(detailDesc.getStandardDetailId());

            }
            detail.setStandardId(standard.getId());
            ZbStandardsBuildPosition buildPosition = buildPositionMap.get(detail.getPositionId());
            if(buildPosition != null){
                detail.setPositionId(buildPosition.getId());
            }
            detail.setOriginId(positionDetail.getId());
            detail.setId(SnowflakeIdUtils.getNextId());
            list.add(detail);
        }
        if(CollectionUtils.isNotEmpty(list)){
            standardsBuildPositionDetailMapper.batchInsertSelf(list);
        }
    }

    /**
     * 初始化产品定位信息类表
     * @param standard
     * @param standardsBuildCategories
     * @return
     */
    private List<ZbStandardsBuildPosition> initBuildPosition(ZbProjectStandard standard, List<ZbStandardsBuildCategory> standardsBuildCategories) {
        List<ZbStandardsBuildPosition> list = Lists.newArrayList();
        //查询企业数据
        List<ZbStandardsBuildPosition> positionList = standardsBuildPositionMapper.selectByStandardId(standard.getOriginId());
        if(CollectionUtils.isEmpty(positionList)||CollectionUtils.isEmpty(standardsBuildCategories)){
            return list;
        }
        Map<Long,ZbStandardsBuildCategory> categoryMap = standardsBuildCategories.stream().collect(Collectors.toMap(ZbStandardsBuildCategory::getOriginId, x->x));
        for(ZbStandardsBuildPosition position : positionList){
            ZbStandardsBuildPosition selfPosition = new ZbStandardsBuildPosition();
            ZbStandardsBuildCategory buildCategory = categoryMap.get(position.getBuildCategoryId());
            BeanUtils.copyProperties(position,selfPosition);
            selfPosition.setStandardId(standard.getId());
            selfPosition.setBuildCategoryId(buildCategory.getId());
            selfPosition.setId(SnowflakeIdUtils.getNextId());
            selfPosition.setOriginId(position.getId());
            list.add(selfPosition);
        }
        if(CollectionUtils.isNotEmpty(list)){
            standardsBuildPositionMapper.batchSaveSelf(list);
        }
        return list;
    }

    /**
     * 初始化企业标准数据-建造标准-业态
     * @param standard
     * @return
     */
    private List<ZbStandardsBuildCategory> initBuildCategory(ZbProjectStandard standard) {
        List<ZbStandardsBuildCategory> list = Lists.newArrayList();
        List<ZbStandardsBuildCategory> categoryList = zbStandardsBuildCategoryMapper.selectByStandardId(standard.getOriginId());
        if(CollectionUtils.isEmpty(categoryList)){
            return list;
        }
        for(ZbStandardsBuildCategory zbStandardsBuildCategory : categoryList){
            ZbStandardsBuildCategory selfCategory = new ZbStandardsBuildCategory();
            BeanUtils.copyProperties(zbStandardsBuildCategory,selfCategory);
            selfCategory.setStandardId(standard.getId());
            selfCategory.setOriginId(zbStandardsBuildCategory.getId());
            selfCategory.setId(SnowflakeIdUtils.getNextId());
            list.add(selfCategory);
        }
        if(CollectionUtils.isNotEmpty(list)){
            zbStandardsBuildCategoryMapper.batchSaveSelf(list);
        }
        return list;
    }

}
