<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper" >
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
    <result column="category_code" property="categoryCode" jdbcType="VARCHAR"/>
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="feature_id" property="featureId" jdbcType="BIGINT"/>
    <result column="ord_category" property="ordCategory" jdbcType="INTEGER"/>
    <result column="customer_code" property="customerCode" jdbcType="VARCHAR"/>
    <result column="trade_name" property="tradeName" jdbcType="VARCHAR"/>
    <result column="type" property="type" jdbcType="BIGINT"/>
  </resultMap>

  <sql id="Base_Column_List" >
    id, `trade_id`, `category_code`, `type`,`feature_id`, `ord_category`, `customer_code`, trade_name
  </sql>

  <insert id="saveBatch" parameterType="com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView" >
    insert into zb_project_feature_category_view_standards (trade_id,category_code,type,feature_id,ord_category,customer_code,trade_name,qy_code_old,qy_flag) values
    <foreach collection="list" item="item" index="index" separator="," >
      (
      #{item.tradeId},
      #{item.categoryCode},
      #{item.type},
      #{item.featureId},
      #{item.ordCategory},
      #{item.customerCode},
      #{item.tradeName},
      #{item.qyCodeOld},
      #{item.qyFlag}
      )
    </foreach>
  </insert>

  <!--根据工程专业ID集合删除工程特征-->
  <delete id="deleteByTradeId" parameterType="java.lang.Long" >
    delete from zb_project_feature_category_view_standards
    where trade_id = #{tradeId,jdbcType=BIGINT}
  </delete>


  <delete id="batchDeleteByTradeId" parameterType="java.util.List" >
    delete from zb_project_feature_category_view_standards
    where
    trade_id in
    <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </delete>

  <!--根据分类和专业查询-->
  <select id="selectByCategoryAndTrade" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_category_view_standards
    where trade_id = #{tradeId,jdbcType=BIGINT} and invalid = 0
    <if test="categoryCode != null" >
      and category_code = #{categoryCode,jdbcType=VARCHAR}
    </if>
    <if test="type != null" >
      and `type` = #{type,jdbcType=INTEGER}
    </if>
  </select>

  <select id="selectByCategoryAndCustomerCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_category_view_standards
    where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
    <if test="categoryCode != null" >
      and category_code = #{categoryCode,jdbcType=VARCHAR}
    </if>
    ORDER BY ord_category
  </select>

  <delete id="deleteByCustomerCode">
    delete from zb_project_feature_category_view_standards
    where customer_code = #{customerCode,jdbcType=VARCHAR}
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
  </delete>

  <select id="selectByCategoryAndCustomerCodeAndTradeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_category_view_standards
    where customer_code = #{customerCode,jdbcType=VARCHAR}
    and category_code = #{categoryCode,jdbcType=VARCHAR}
    and trade_id = #{tradeId,jdbcType=BIGINT}
    and invalid = 0
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
    ORDER BY ord_category
  </select>
  <select id="selectAllCustomerCode" resultType="java.lang.String">
    select distinct customer_code from zb_project_feature_category_view_standards where invalid = 0;
  </select>
  <delete id="batchDeleteByIds">
    delete from zb_project_feature_category_view_standards
    where id in
    <foreach collection="ids" separator="," open="(" close=")" item="item">
      #{item}
    </foreach>
  </delete>
  <select id="selectByFeatureIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_category_view_standards
    where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
    and feature_id in
    <foreach collection="ids" item="item" index="index" separator="," open="(" close=")">
       #{item}
    </foreach>
  </select>

  <select id="selectByCategoryAndFeatureIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from zb_project_feature_category_view_standards
    where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
    <if test="categoryCode != null" >
      and category_code = #{categoryCode,jdbcType=VARCHAR}
    </if>
    and feature_id in
    <foreach collection="ids" item="item" index="index" separator="," open="(" close=")">
      #{item}
    </foreach>
  </select>

  <update id="batchUpdateOrd" parameterType="list">
    <foreach collection="list" index="index" item="item" separator=";">
      update zb_project_feature_category_view_standards set ord_category=#{item.ordCategory, jdbcType=INTEGER}
      where
      customer_code = #{item.customerCode,jdbcType=VARCHAR}
      and trade_id = #{item.tradeId, jdbcType=BIGINT}
      and category_code = #{item.categoryCode, jdbcType=VARCHAR}
      and feature_id = #{item.featureId, jdbcType=BIGINT}
      and type = #{item.type, jdbcType=INTEGER}
    </foreach>
  </update>

  <update id="updateOrd" parameterType="com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView">
    update zb_project_feature_category_view_standards set ord_category=#{ordCategory, jdbcType=INTEGER}
    where
      customer_code = #{customerCode,jdbcType=VARCHAR}
      and trade_id = #{tradeId, jdbcType=BIGINT}
      and category_code = #{categoryCode, jdbcType=VARCHAR}
      and feature_id = #{featureId, jdbcType=BIGINT}
      and type = #{type, jdbcType=INTEGER}
  </update>

  <select id="selectMaxOrdByCondition" resultType="java.lang.Integer">
    select max(ord_category)
    from zb_project_feature_category_view_standards
    where customer_code = #{customerCode,jdbcType=VARCHAR} and invalid = 0
    AND `type` = #{type,jdbcType=INTEGER}
    and category_code = #{categoryCode,jdbcType=VARCHAR}
    ORDER BY ord_category
  </select>
</mapper>