<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionSelfMapper">
  <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="expression_code" jdbcType="VARCHAR" property="expressionCode" />
    <result column="data_type" jdbcType="TINYINT" property="dataType" />
    <result column="rule" jdbcType="VARCHAR" property="rule" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="scope" jdbcType="INTEGER" property="scope" />
    <result column="qy_code" jdbcType="VARCHAR" property="qyCode" />
    <result column="is_usable" jdbcType="BIT" property="isUsable" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type_code" jdbcType="VARCHAR" property="typeCode" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="option" jdbcType="VARCHAR" property="option" />
    <result column="is_expression" jdbcType="BIT" property="isExpression" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="expression_ord" jdbcType="INTEGER" property="expressionOrd" />
    <result column="expression_is_using" jdbcType="BIT" property="expressionIsUsing" />
    <result column="expression_is_from_system" jdbcType="BIT" property="expressionIsFromSystem" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_global_id" jdbcType="BIGINT" property="createGlobalId" />
    <result column="update_global_id" jdbcType="BIGINT" property="updateGlobalId" />
    <result column="expression_create_time" jdbcType="TIMESTAMP" property="expressionCreateTime" />
    <result column="expression_create_global_id" jdbcType="BIGINT" property="expressionCreateGlobalId" />
    <result column="expression_remark" jdbcType="VARCHAR" property="expressionRemark" />
    <result column="origin_id" property="originId" jdbcType="BIGINT" />
    <result column="self_global_id" property="selfGlobalId" jdbcType="BIGINT" />
  </resultMap>

  <sql id="Base_Column_List" >
    id, expression_code, data_type, rule, status, `scope`, qy_code, is_usable, is_deleted, `name`, type_code, `type`, `option`,
    is_expression, unit, expression_ord, expression_is_using, expression_is_from_system, create_time, update_time, create_global_id,
    update_global_id, expression_create_time, expression_create_global_id, expression_remark,origin_id,self_global_id
  </sql>

  <sql id="Temp_Column_List" >
    id, expression_code, data_type, rule, status, `scope`, qy_code, is_usable, is_deleted, `name`, type_code, `type`, `option`,
    is_expression, unit, expression_ord, expression_is_using, expression_is_from_system, create_time, update_time, create_global_id,
    update_global_id, expression_create_time, expression_create_global_id, expression_remark, origin_id,self_global_id
  </sql>
  <insert id="insertSelectiveList" parameterType="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
    insert into zb_standards_expression_self(
        id,
        expression_code,
        data_type,
        rule,
        status,
        `scope`,
        qy_code,
        is_usable,
        is_deleted,
        `name`,
        type_code,
        `type`,
        `option`,
        is_expression,
        unit,
        expression_ord,
        expression_is_using,
        expression_is_from_system,
        create_time,
        update_time,
        create_global_id,
        update_global_id,
        expression_create_time,
        expression_create_global_id,
        expression_remark,
        origin_id,
        qy_code_old,
        qy_flag)
    values
    <foreach collection="list" item="item" index="index" separator="," >(
        #{item.id,jdbcType=BIGINT},
        #{item.expressionCode,jdbcType=VARCHAR},
        #{item.dataType,jdbcType=TINYINT},
        #{item.rule,jdbcType=VARCHAR},
        #{item.status,jdbcType=INTEGER},
        #{item.scope,jdbcType=INTEGER},
        #{item.qyCode,jdbcType=VARCHAR},
        #{item.isUsable,jdbcType=BIT},
        #{item.isDeleted,jdbcType=BIT},
        #{item.name,jdbcType=VARCHAR},
        #{item.typeCode,jdbcType=VARCHAR},
        #{item.type,jdbcType=INTEGER},
        #{item.option,jdbcType=VARCHAR},
        #{item.isExpression,jdbcType=BIT},
        #{item.unit,jdbcType=VARCHAR},
        #{item.expressionOrd,jdbcType=INTEGER},
        #{item.expressionIsUsing,jdbcType=BIT},
        #{item.expressionIsFromSystem,jdbcType=BIT},
        #{item.createTime,jdbcType=TIMESTAMP},
        #{item.updateTime,jdbcType=TIMESTAMP},
        #{item.createGlobalId,jdbcType=BIGINT},
        #{item.updateGlobalId,jdbcType=BIGINT},
        #{item.expressionCreateTime,jdbcType=TIMESTAMP},
        #{item.expressionCreateGlobalId,jdbcType=BIGINT},
        #{item.expressionRemark,jdbcType=VARCHAR},
        #{item.originId,jdbcType=BIGINT},
        #{item.qyCodeOld,jdbcType=VARCHAR},
        #{item.qyFlag,jdbcType=INTEGER}
       )
    </foreach>
  </insert>
  <insert id="insertSelective" parameterType="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
    insert into zb_standards_expression_self
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="expressionCode != null">
        expression_code,
      </if>
      <if test="dataType != null">
        data_type,
      </if>
      <if test="rule != null">
        rule,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="scope != null">
        `scope`,
      </if>
      <if test="qyCode != null">
        qy_code,
      </if>
      <if test="isUsable != null">
        is_usable,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="name != null">
        `name`,
      </if>
      <if test="typeCode != null">
        type_code,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="option != null">
        `option`,
      </if>
      <if test="isExpression != null">
        is_expression,
      </if>
      <if test="unit != null">
        unit,
      </if>
      <if test="expressionOrd != null">
        expression_ord,
      </if>
      <if test="expressionIsUsing != null">
        expression_is_using,
      </if>
      <if test="expressionIsFromSystem != null">
        expression_is_from_system,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="createGlobalId != null">
        create_global_id,
      </if>
      <if test="updateGlobalId != null">
        update_global_id,
      </if>
      <if test="expressionCreateTime != null">
        expression_create_time,
      </if>
      <if test="expressionCreateGlobalId != null">
        expression_create_global_id,
      </if>
      <if test="expressionRemark != null">
        expression_remark,
      </if>
      <if test="originId != null">
        origin_id,
      </if>
      <if test="selfGlobalId != null">
        self_global_id,
      </if>
      <if test="qyCodeOld != null">
        qy_code_old,
      </if>
      <if test="qyFlag != null">
        qy_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="expressionCode != null">
        #{expressionCode,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        #{dataType,jdbcType=TINYINT},
      </if>
      <if test="rule != null">
        #{rule,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="scope != null">
        #{scope,jdbcType=INTEGER},
      </if>
      <if test="qyCode != null">
        #{qyCode,jdbcType=VARCHAR},
      </if>
      <if test="isUsable != null">
        #{isUsable,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null">
        #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="option != null">
        #{option,jdbcType=VARCHAR},
      </if>
      <if test="isExpression != null">
        #{isExpression,jdbcType=BIT},
      </if>
      <if test="unit != null">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="expressionOrd != null">
        #{expressionOrd,jdbcType=INTEGER},
      </if>
      <if test="expressionIsUsing != null">
        #{expressionIsUsing,jdbcType=BIT},
      </if>
      <if test="expressionIsFromSystem != null">
        #{expressionIsFromSystem,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createGlobalId != null">
        #{createGlobalId,jdbcType=BIGINT},
      </if>
      <if test="updateGlobalId != null">
        #{updateGlobalId,jdbcType=BIGINT},
      </if>
      <if test="expressionCreateTime != null">
        #{expressionCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expressionCreateGlobalId != null">
        #{expressionCreateGlobalId,jdbcType=BIGINT},
      </if>
      <if test="expressionRemark != null">
        #{expressionRemark,jdbcType=VARCHAR},
      </if>
      <if test="originId != null">
        #{originId,jdbcType=BIGINT},
      </if>
      <if test="selfGlobalId != null">
        #{selfGlobalId,jdbcType=BIGINT},
      </if>
      <if test="qyCodeOld != null">
        #{qyCodeOld,jdbcType=VARCHAR},
      </if>
      <if test="qyFlag != null">
        #{qyFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

  <select id="findExistByNameTypeOption" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" /> FROM `zb_standards_expression_self`
    WHERE `qy_code` = #{customerCode,jdbcType=VARCHAR}
    AND `self_global_id` = #{selfGlobalId,jdbcType=BIGINT}
    AND `name` = #{name,jdbcType=VARCHAR}
    AND `type_code` = #{typeCode,jdbcType=VARCHAR}
    <if test="option != null">
      AND `option` = #{option,jdbcType=VARCHAR}
    </if>
    AND `type` = #{type,jdbcType=INTEGER}
  </select>
  <select id="findExistByGlobalId" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" /> FROM `zb_standards_expression_self`
    WHERE `qy_code` = #{customerCode,jdbcType=VARCHAR}
    AND `self_global_id` = #{selfGlobalId,jdbcType=BIGINT}
    AND `type` = #{type,jdbcType=INTEGER}
  </select>

  <select id="findExistByNameType" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" /> FROM `zb_standards_expression_self`
    WHERE `qy_code` = #{customerCode,jdbcType=VARCHAR}
    AND `self_global_id` = #{selfGlobalId,jdbcType=BIGINT}
    AND `name` = #{name,jdbcType=VARCHAR}
    AND `type_code` = #{typeCode,jdbcType=VARCHAR}
    AND `type` = #{type,jdbcType=INTEGER}
  </select>

  <select id="selectByPrimaryKey" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" /> FROM `zb_standards_expression_self`
    WHERE `id` = #{id,jdbcType=BIGINT}
  </select>

  <update id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    update zb_standards_expression_self set is_expression = 0
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateSelective" parameterType="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
    update zb_standards_expression_self
    <set>
      <if test="expressionCode != null">
        expression_code = #{expressionCode,jdbcType=VARCHAR},
      </if>
      <if test="dataType != null">
        data_type = #{dataType,jdbcType=TINYINT},
      </if>
      <if test="rule != null">
        `rule` = #{rule,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        `status` = #{status,jdbcType=INTEGER},
      </if>
      <if test="scope != null">
        `scope` = #{scope,jdbcType=INTEGER},
      </if>
      <if test="qyCode != null">
        qy_code = #{qyCode,jdbcType=VARCHAR},
      </if>
      <if test="isUsable != null">
        is_usable = #{isUsable,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="name != null">
        `name` = #{name,jdbcType=VARCHAR},
      </if>
      <if test="typeCode != null">
        `type_code` = #{typeCode,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=INTEGER},
      </if>
      <if test="option != null">
        `option` = #{option,jdbcType=VARCHAR},
      </if>
      <if test="isExpression != null">
        is_expression = #{isExpression,jdbcType=BIT},
      </if>
      <if test="unit != null">
        unit = #{unit,jdbcType=VARCHAR},
      </if>
      <if test="expressionOrd != null">
        expression_ord = #{expressionOrd,jdbcType=INTEGER},
      </if>
      <if test="expressionIsUsing != null">
        expression_is_using = #{expressionIsUsing,jdbcType=BIT},
      </if>
      <if test="expressionIsFromSystem != null">
        expression_is_from_system = #{expressionIsFromSystem,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time =  #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createGlobalId != null">
        create_global_id = #{createGlobalId,jdbcType=BIGINT},
      </if>
      <if test="updateGlobalId != null">
        update_global_id = #{updateGlobalId,jdbcType=BIGINT},
      </if>
      <if test="expressionCreateTime != null">
        expression_create_time = #{expressionCreateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="expressionCreateGlobalId != null">
        expression_create_global_id = #{expressionCreateGlobalId,jdbcType=BIGINT},
      </if>
      <if test="expressionRemark != null">
        expression_remark = #{expressionRemark,jdbcType=VARCHAR},
      </if>
      <if test="originId != null">
        origin_id = #{originId,jdbcType=BIGINT},
      </if>
      <if test="selfGlobalId != null">
        self_global_id = #{selfGlobalId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" parameterType="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
    insert into zb_standards_expression_self (id, expression_code, data_type,
    rule, `status`, `scope`, qy_code, is_usable, is_deleted, `name`, type_code, `type`, `option`,
    is_expression, `unit`, expression_ord, expression_is_using, expression_is_from_system, create_time,
    update_time, create_global_id, update_global_id, expression_create_time, expression_create_global_id, expression_remark,origin_id,self_global_id,
    qy_code_old, qy_flag)
    values
    <foreach collection="list" item="item" index="index" separator="," >
      (#{item.id,jdbcType=BIGINT}, #{item.expressionCode,jdbcType=VARCHAR}, #{item.dataType,jdbcType=TINYINT},
      #{item.rule,jdbcType=VARCHAR}, #{item.status,jdbcType=INTEGER}, #{item.scope,jdbcType=INTEGER},
      #{item.qyCode,jdbcType=VARCHAR}, #{item.isUsable,jdbcType=BIT}, #{item.isDeleted,jdbcType=BIT},
      #{item.name,jdbcType=VARCHAR}, #{item.typeCode,jdbcType=VARCHAR}, #{item.type,jdbcType=INTEGER}, #{item.option,jdbcType=VARCHAR},
      #{item.isExpression,jdbcType=BIT}, #{item.unit,jdbcType=VARCHAR}, #{item.expressionOrd,jdbcType=INTEGER},
      #{item.expressionIsUsing,jdbcType=BIT}, #{item.expressionIsFromSystem,jdbcType=BIT}, #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP}, #{item.createGlobalId,jdbcType=BIGINT}, #{item.updateGlobalId,jdbcType=BIGINT},
      #{item.expressionCreateTime,jdbcType=TIMESTAMP}, #{item.expressionCreateGlobalId,jdbcType=BIGINT}, #{item.expressionRemark,jdbcType=VARCHAR},
      #{item.originId,jdbcType=BIGINT}, #{item.selfGlobalId,jdbcType=BIGINT}, #{item.qyCodeOld,jdbcType=VARCHAR}, #{item.qyFlag,jdbcType=INTEGER}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    expression_code = VALUES(expression_code),
    data_type = VALUES(data_type),
    `rule` = VALUES(rule),
    `status` = VALUES(status),
    `scope` = VALUES(`scope`),
    qy_code = VALUES(qy_code),
    is_usable = VALUES(is_usable),
    is_deleted = VALUES(is_deleted),
    `name` = VALUES(`name`),
    type_code = VALUES(type_code),
    `type` = VALUES(`type`),
    `option` = VALUES(`option`),
    is_expression = VALUES(is_expression),
    `unit` = VALUES(unit),
    expression_ord = VALUES(expression_ord),
    expression_is_using = VALUES(expression_is_using),
    expression_is_from_system = VALUES(expression_is_from_system),
    create_time = VALUES(create_time),
    update_time = VALUES(update_time),
    create_global_id = VALUES(create_global_id),
    update_global_id = VALUES(update_global_id),
    expression_create_time = VALUES(expression_create_time),
    expression_create_global_id = VALUES(expression_create_global_id),
    expression_remark = VALUES(expression_remark),
    origin_id = VALUES(origin_id),
    self_global_id = VALUES(self_global_id),
    qy_code_old = VALUES(qy_code_old),
    qy_flag = VALUES(qy_flag)
  </insert>

  <select id="getMaxExpressionOrd" resultType="java.lang.Integer">
    SELECT IFNULL(MAX(expression_ord),0) FROM zb_standards_expression_self
    WHERE qy_code=#{customerCode,jdbcType=VARCHAR}
    AND `self_global_id` = #{selfGlobalId,jdbcType=BIGINT}
    AND `type` = #{type,jdbcType=INTEGER}
    AND is_expression=1
  </select>

  <select id="selectListBySelfCustomCode" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" /> FROM `zb_standards_expression_self`
    WHERE `qy_code` = #{customerCode,jdbcType=VARCHAR} AND `self_global_id` = #{selfGlobalId,jdbcType=BIGINT}
    <if test="type != null" >
      AND `type` = #{type,jdbcType=INTEGER}
    </if>
  </select>

  <select id="selectSelfExpression" resultMap="BaseResultMap">
    SELECT
    <include refid="Temp_Column_List"/>
    FROM `zb_standards_expression_self`
    WHERE `qy_code` = #{customerCode,jdbcType=VARCHAR}
    AND `self_global_id` = #{selfGlobalId,jdbcType=BIGINT}
    AND `type` = #{type,jdbcType=INTEGER}
    AND is_expression = 1 and type_code = 'number' ORDER BY expression_ord
  </select>

  <select id="selectSelfExpressionWhenPublish" resultMap="BaseResultMap">
    SELECT
    <include refid="Temp_Column_List"/>
    FROM `zb_standards_expression_self`
    WHERE `qy_code` = #{customerCode,jdbcType=VARCHAR}
    and self_global_id = #{selfGlobalId,jdbcType=BIGINT}
    AND `type` = #{type,jdbcType=INTEGER}
    ORDER BY expression_ord
  </select>

  <delete id="deleteSelfByCustomerCode">
    delete from zb_standards_expression_self where qy_code = #{customerCode,jdbcType=VARCHAR} AND `type` = #{type,jdbcType=INTEGER}
  </delete>

  <select id="selectAllCustomerCodeGlobalIds" resultType="map">
    select distinct qy_code,self_global_id from zb_standards_expression_self where  self_global_id is not null ;
  </select>

  <select id="selectAllByCustomCode" resultMap="BaseResultMap">
    SELECT  <include refid="Base_Column_List" /> FROM `zb_standards_expression_self`
    WHERE `qy_code` = #{customerCode,jdbcType=VARCHAR}
    <if test="type != null">
      and `type` = #{type,jdbcType=INTEGER}
    </if>
    <if test="selfGlobalId != null">
      and `self_global_id`= #{selfGlobalId,jdbcType=BIGINT}
    </if>
  </select>

  <update id="batchUpdateSelective" parameterType="com.glodon.qydata.entity.standard.expression.ZbStandardsExpression">
    <foreach collection="list" item="item" index="index" separator=";">
      update zb_standards_expression_self
      <set>
        <if test="item.expressionCode != null">
          expression_code = #{item.expressionCode,jdbcType=VARCHAR},
        </if>
        <if test="item.dataType != null">
          data_type = #{item.dataType,jdbcType=TINYINT},
        </if>
        <if test="item.rule != null">
          `rule` = #{item.rule,jdbcType=VARCHAR},
        </if>
        <if test="item.status != null">
          `status` = #{item.status,jdbcType=INTEGER},
        </if>
        <if test="item.scope != null">
          `scope` = #{item.scope,jdbcType=INTEGER},
        </if>
        <if test="item.qyCode != null">
          qy_code = #{item.qyCode,jdbcType=VARCHAR},
        </if>
        <if test="item.isUsable != null">
          is_usable = #{item.isUsable,jdbcType=BIT},
        </if>
        <if test="item.isDeleted != null">
          is_deleted = #{item.isDeleted,jdbcType=BIT},
        </if>
        <if test="item.name != null">
          `name` = #{item.name,jdbcType=VARCHAR},
        </if>
        <if test="item.typeCode != null">
          type_code = #{item.typeCode,jdbcType=VARCHAR},
        </if>
        <if test="item.type != null">
          type = #{item.type,jdbcType=INTEGER},
        </if>
        <if test="item.option != null">
          `option` = #{item.option,jdbcType=VARCHAR},
        </if>
        <if test="item.isExpression != null">
          is_expression = #{item.isExpression,jdbcType=BIT},
        </if>
        <if test="item.unit != null">
          unit = #{item.unit,jdbcType=VARCHAR},
        </if>
        <if test="item.expressionOrd != null">
          expression_ord = #{item.expressionOrd,jdbcType=INTEGER},
        </if>
        <if test="item.expressionIsUsing != null">
          expression_is_using = #{item.expressionIsUsing,jdbcType=BIT},
        </if>
        <if test="item.expressionIsFromSystem != null">
          expression_is_from_system = #{item.expressionIsFromSystem,jdbcType=BIT},
        </if>
        <if test="item.createTime != null">
          create_time =  #{item.createTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.updateTime != null">
          update_time = #{item.updateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.createGlobalId != null">
          create_global_id = #{item.createGlobalId,jdbcType=BIGINT},
        </if>
        <if test="item.updateGlobalId != null">
          update_global_id = #{item.updateGlobalId,jdbcType=BIGINT},
        </if>
        <if test="item.expressionCreateTime != null">
          expression_create_time = #{item.expressionCreateTime,jdbcType=TIMESTAMP},
        </if>
        <if test="item.expressionCreateGlobalId != null">
          expression_create_global_id = #{item.expressionCreateGlobalId,jdbcType=BIGINT},
        </if>
        <if test="item.expressionRemark != null">
          expression_remark = #{item.expressionRemark,jdbcType=VARCHAR},
        </if>
        <if test="item.oldName != null">
          old_name = #{item.oldName,jdbcType=VARCHAR},
        </if>
        <if test="item.originId != null">
          origin_id = #{item.originId,jdbcType=BIGINT},
        </if>
        <if test="item.selfGlobalId != null">
          self_global_id = #{item.selfGlobalId,jdbcType=BIGINT},
        </if>
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>