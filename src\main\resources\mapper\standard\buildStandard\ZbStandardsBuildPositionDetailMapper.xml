<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.glodon.qydata.mapper.standard.buildStandard.ZbStandardsBuildPositionDetailMapper">

    <resultMap id="BaseResultMap" type="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="positionId" column="position_id" jdbcType="BIGINT"/>
            <result property="standardDetailDescId" column="standard_detail_desc_id" jdbcType="BIGINT"/>
            <result property="standardDetailId" column="standard_detail_id" jdbcType="BIGINT"/>
            <result property="standardId" column="standard_id" jdbcType="BIGINT"/>
            <result property="value" column="value" jdbcType="VARCHAR"/>
            <result property="enterpriseId" column="enterprise_id" jdbcType="BIGINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,position_id,standard_detail_desc_id,
        standard_detail_id,standard_id,value,
        enterprise_id,create_time,update_time
    </sql>

    <select id="selectByStandardId" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/> from zb_standards_build_position_detail where standard_id = #{standardId}
    </select>
    <select id="selectSelfByStandardId" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail">
        select *  from zb_standards_build_position_detail_self where standard_id = #{standardId}
    </select>
    <select id="selectSelfByStandardIds" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail">
        select *  from zb_standards_build_position_detail_self where standard_id in
        <foreach collection="list" item="item" close=")" open="(" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectSelfByPositionIds"  resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail">
        select *  from zb_standards_build_position_detail_self where position_id in
        <foreach collection="positionIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>

    </select>
    <select id="selectByPositionId"
            resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail">
         select *  from zb_standards_build_position_detail where position_id = #{positionId}
    </select>
    <delete id="batchSelfByPositionIds">
        delete from zb_standards_build_position_detail_self where
        position_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="deleteByStandardIds">
        delete from zb_standards_build_position_detail where
        standard_id in
        <foreach collection="standardIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>

    <delete id="delSelfByStandardIds">
        delete from zb_standards_build_position_detail_self where
        standard_id in
        <foreach collection="standardIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
    <insert id="batchInsertSelf">
        insert into zb_standards_build_position_detail_self
        (id,position_id,standard_detail_desc_id,standard_detail_id,standard_id,value,origin_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},#{item.positionId},#{item.standardDetailDescId},
            #{item.standardDetailId},#{item.standardId},#{item.value},#{item.originId}
            )
        </foreach>
    </insert>

    <insert id="batchSave">
        insert into zb_standards_build_position_detail
        (id,position_id,standard_detail_desc_id,
        standard_detail_id,standard_id,value,
        enterprise_id)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.id},#{item.positionId},#{item.standardDetailDescId},
            #{item.standardDetailId},#{item.standardId},#{item.value},
            #{item.enterpriseId}
            )
        </foreach>
    </insert>
    <delete id="batchDelSelfByDescId">
        delete from zb_standards_build_position_detail_self where standard_detail_desc_id in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </delete>
    <update id="batchUpdateValueSelf">
        <foreach collection="positionDetails" item="detail" separator=";">
            update zb_standards_build_position_detail_self set
            value = #{detail.value,jdbcType=VARCHAR}
            where
            id = #{detail.id,jdbcType=BIGINT}
        </foreach>
    </update>
    <select id="selectSelfByDescId" resultType="com.glodon.qydata.entity.standard.buildStandard.ZbStandardsBuildPositionDetail">
        select *  from zb_standards_build_position_detail_self where standard_detail_desc_id = #{standardId}
    </select>
</mapper>
