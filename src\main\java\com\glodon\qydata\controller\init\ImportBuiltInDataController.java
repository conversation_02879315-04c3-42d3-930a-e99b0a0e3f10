package com.glodon.qydata.controller.init;

import cn.hutool.core.util.StrUtil;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.init.ImportBuiltInDataService;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.util.ExcelUtil;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.init.BuildStandardExcelVo;
import com.glodon.qydata.vo.init.CommonCategoryExcelVo;
import com.glodon.qydata.vo.init.ExpressionExcelVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description: 导入内置数据相关Controller
 * @author: weijf
 * @date: 2022-10-10
 */
@Slf4j
@RestController
@RequestMapping("/basicInfo/init")
@Tag(name = "导入内置数据相关接口", description = "导入内置数据相关接口")
@SuppressWarnings("squid:S1192")
public class ImportBuiltInDataController {

    @Autowired
    private ImportBuiltInDataService importBuiltInDataService;
    @Autowired
    private IGlodonUserService glodonUserService;
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 1、导入内置建造标准
     *  注：
     *  1、第一行不解析
     *  2、Sheet列顺序与BuildStandardExcelVo 保持一致：科目编码、科目名称、标准说明、数据类型、枚举值
     *  3、科目编码类似于：*******.1
     *  4、多个枚举值，用|隔开
     *
     * @param file Excel文件
     * @param sheetName 若为空，或者该 sheet名称没找到，则默认查找第一个Sheet
     * @param buildStandardName 待导入的建造标准名称，若没有，则新建，若有，则覆盖原有内置数据
     * @param defaultBuildStandardId 新建时，默认建造标准ID，若不传，则随机
     * <AUTHOR>
     * @return
     */
    @Operation(summary = "导入内置建造标准")
    @PostMapping("/importBuildStandardData")
    public ResponseVo importBuildStandardData(MultipartFile file, String sheetName, String categoryCode, String categoryName, String buildStandardName, Long defaultBuildStandardId) {
        if(file==null || file.isEmpty() || StringUtils.isBlank(file.getOriginalFilename())){
            return ResponseVo.error("请选择Excel文件");
        }
        if(StringUtils.isBlank(buildStandardName)){
            return ResponseVo.error("待导入的建造标准名称 不能为空");
        }

        // 1、解析Excel
        List<BuildStandardExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, BuildStandardExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)) return ResponseVo.error("Excel数据 不能为空");

        return importBuiltInDataService.importBuildStandardData(excelDataList,categoryCode,categoryName,buildStandardName,defaultBuildStandardId);
    }

    /**
     * 2、导入内置计算口径
     *  注意：导入完成后，需要初始化用户计算口径
     *  TempProjectDataDealController. expressionDataDealALL  或者 （expressionDataDeal + expressionDataDealSelf）
     * @param file Excel文件
     * @param sheetName 若为空，或者该 sheet名称没找到，则默认查找第一个Sheet
     * @param standardTradeName 【产品设计指标】的数据，同步到某个工程特征下，若名称为空，则不同步
     * @return
     */
    @Operation(summary = "导入内置计算口径")
    @PostMapping("/importExpressionData")
    public ResponseVo importExpressionData(MultipartFile file,String sheetName,String standardTradeName){
        if(file==null || file.isEmpty() || StringUtils.isBlank(file.getOriginalFilename())){
            return ResponseVo.error("请选择Excel文件");
        }
        // 1、解析Excel
        List<ExpressionExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName,ExpressionExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)){
            return ResponseVo.error("待导入Excel数据为空");
        }

        return importBuiltInDataService.importExpressionData(excelDataList,standardTradeName);
    }

    /**
     * 3、导入工程特征+暂存的数据，把某专业下的数据与内置计算口径的数据刷新成一致的
     * @return
     * <AUTHOR>
     */
    @Operation(summary = "导入工程特征+暂存的数据")
    @PostMapping("/refreshUserFeatureData")
    public ResponseVo refreshUserFeatureData(MultipartFile file,String sheetName,String standardTradeName,String defaultInitCustomerCode){
        if(StringUtils.isBlank(standardTradeName)){
            return ResponseVo.error("standardTradeName 不能为空");
        }
        //1、解析文件，获得待导入的数据
        List<ExpressionExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, ExpressionExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)) return ResponseVo.error("文件 不能为空");

        Runnable r = () -> importBuiltInDataService.refreshUserFeatureData(excelDataList,standardTradeName,defaultInitCustomerCode);
        new Thread(r).start();
        return ResponseVo.success();
    }

    /**
     * 修复：只初始化工程特征暂存数据
     * @param file
     * @param sheetName
     * @param standardTradeName
     * @return
     */
    @Operation(summary = "初始化工程特征暂存数据")
    @PostMapping("/initSelfFeatureOnly")
    public ResponseVo initSelfFeatureOnly(MultipartFile file,String sheetName,String standardTradeName,String defaultInitCustomerCode){
        //1、解析文件，获得待导入的数据
        List<ExpressionExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, ExpressionExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)) return ResponseVo.error("文件 不能为空");

        Runnable r = () -> {
            importBuiltInDataService.initSelfFeatureOnly(excelDataList,standardTradeName,defaultInitCustomerCode);
            log.info("---------------只初始化工程特征暂存数据完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }

    /**
     * 4、只初始化分类视图+暂存数据
     * @param file
     * @param sheetName
     * @param standardTradeName
     * @return
     */
    @Operation(summary = "初始化分类视图+暂存数据")
    @PostMapping("/initCategoryViewOnly")
    public ResponseVo initCategoryViewOnly(MultipartFile file,String sheetName,String standardTradeName,String defaultInitCustomerCode){
        //1、解析文件，获得待导入的数据
        List<ExpressionExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, ExpressionExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)) return ResponseVo.error("文件 不能为空");

        Runnable r = () -> {
            importBuiltInDataService.initCategoryViewOnly(excelDataList,standardTradeName,defaultInitCustomerCode);
            log.info("---------------只初始化分类视图数据完成-------------");
        };
        new Thread(r).start();
        return ResponseVo.success();
    }


    /**
     * 5、导入内置工程分类
     *  注意：导入完成后，需要初始化用户工程分类
     * @param file Excel文件
     * @param sheetName 若为空，或者该 sheet名称没找到，则默认查找第一个Sheet
     * @param parentCategoryName 上级分类名称，多级用|隔开
     * @param categoryType 需要导入到的分类，多个用英文逗号隔开，数据取自：CategoryTypeConstants
     * @param categoryTypeCode 如果导入到一级分类的话，此参数为导入到的模板Code，如果非一级，则此参数不生效，默认取父级模板Code
     * @param categoryTypeName 如果导入到一级分类的话，此参数为导入到的模板名称，如果非一级，则此参数不生效，默认取父级模板名称
     * @return
     */
    @Operation(summary = "导入内置工程分类")
    @PostMapping("/importCommonprojCategoryData")
    public ResponseVo importCommonprojCategoryData(MultipartFile file,String sheetName,String parentCategoryName,String categoryType,String categoryTypeCode,String categoryTypeName){
        if(file==null || file.isEmpty() || StringUtils.isBlank(file.getOriginalFilename())){
            return ResponseVo.error("请选择Excel文件");
        }
        // 解析Excel
        List<CommonCategoryExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, CommonCategoryExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)){
            return ResponseVo.error("待导入Excel数据为空");
        }

        return importBuiltInDataService.importCommonprojCategoryData(excelDataList,parentCategoryName,categoryType,categoryTypeCode,categoryTypeName);
    }
    /**
     * 6、初始化用户工程分类（发布、暂存）
     * @param file Excel文件
     * @param sheetName 若为空，或者该 sheet名称没找到，则默认查找第一个Sheet
     * @param parentCategoryName 上级分类名称，多级用|隔开
     * @param categoryType 需要导入到的分类，多个用英文逗号隔开，数据取自：CategoryTypeConstants
     * @return
     */
    @Operation(summary = "初始化用户工程分类（发布、暂存）")
    @PostMapping("/initUserCommonprojCategoryData")
    public ResponseVo initUserCommonprojCategoryData(MultipartFile file,String sheetName,String parentCategoryName,String categoryType,String defaultInitCustomerCode){
        if(file==null || file.isEmpty() || StringUtils.isBlank(file.getOriginalFilename())){
            return ResponseVo.error("请选择Excel文件");
        }
        // 解析Excel
        List<CommonCategoryExcelVo> excelDataList = ExcelUtil.readExcel(file, sheetName, CommonCategoryExcelVo.class);
        if(CollectionUtils.isEmpty(excelDataList)){
            return ResponseVo.error("待导入Excel数据为空");
        }

        throw  new BusinessException("此函数会导致工程分类刷错不要使用");
    }

    /**
     * 作业端导出的企业 + 当前建造标准表里的企业 -> 刷入3套旧内置
     * 【建造标准-居住建筑、建造标准-办公建筑、建造标准-工业建筑】；【推荐建造标准-居住建筑】
     * 3套旧建造标准--刷数据前：维护categoryName，将qy_flag和qy_code_old置null；数据刷完后：is_deleted改成1删除，is_example改成0用户自建，is_enabled改成0未启用，
     * 此接口功能：
     * 刷入3套旧内置。
     *      企业数据入企业数据入zb_standards_build_standard、zb_standards_build_standard_detail、zb_standards_build_standard_detail_desc
     *      暂存数据入企业数据入zb_standards_build_standard_self
     * 启用逻辑：用户启用>新内置>旧内置
     * 非一级分类刷成一级分类
     */
    @Operation(summary = "给用户导入内置建造标准")
    @PostMapping("/refreshBuildStandardData")
    public ResponseVo refreshBuildStandardData(MultipartFile file, String globalIds, String type){
        List<String> typeList = Arrays.asList("by-globalId", "by-all", "initGlobal", "unlock");
        if (!typeList.contains(type)){
            return ResponseVo.error("type参数错误");
        }

        if ("unlock".equals(type)){
            redisUtil.unlock("refreshBuildStandardData", RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, "refreshBuildStandardData");
            return ResponseVo.success("解锁成功");
        }

        if (!redisUtil.tryLockRetry("refreshBuildStandardData", RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, "refreshBuildStandardData")) {
           return ResponseVo.error("操作过于频繁，请稍后再试");
        }

        Runnable r = () -> {
            try {
                Set<String> customerCodeSet = new HashSet<>();
                if ("by-globalId".equals(type) && StringUtils.isNotEmpty(globalIds)){
                    List<String> globalIdList = StrUtil.split(globalIds, StrUtil.COMMA);
                    customerCodeSet = globalIdList.stream().map(globalId -> glodonUserService.getCustomerCode(globalId)).collect(Collectors.toSet());
                }else if ("by-all".equals(type)){
                    // 作业端导入的数据
                    Set<String> importCustomerCodeSet = new HashSet<>();
                    if (file != null && !file.isEmpty()){
                        List<List<String>> excelData = ExcelUtil.readExcel(file);
                        List<String> globalIdList = excelData.stream().map(x -> x.get(0)).collect(Collectors.toList());
                        importCustomerCodeSet = globalIdList.stream().map(globalId -> redisUtil.getString(RedisKeyEnum.CUSTOMER_CODE_BAK, globalId)).collect(Collectors.toSet());
                    }
                    // 当前建造标准表里的企业
                    Set<String> buildStandardCustomerCodeList = importBuiltInDataService.buildStandardCustomerCodeList();
                    customerCodeSet.addAll(importCustomerCodeSet);
                    customerCodeSet.addAll(buildStandardCustomerCodeList);
                }else if ("initGlobal".equals(type) && file != null && !file.isEmpty()){
                    List<List<String>> excelData = ExcelUtil.readExcel(file);
                    List<String> globalIdList = excelData.stream().map(x -> x.get(0)).collect(Collectors.toList());
                    Set<String> customerCodes = new HashSet<>();
                    globalIdList.forEach(globalId -> {
                        try {
                            String customerCode = redisUtil.getString(RedisKeyEnum.CUSTOMER_CODE_BAK, globalId);
                            if (StringUtils.isEmpty(customerCode)) {
                                Thread.sleep(2000);
                                customerCode = glodonUserService.getCustomerCode(globalId);
                                redisUtil.setString(RedisKeyEnum.CUSTOMER_CODE_BAK, customerCode, globalId);
                            }else {
                                redisUtil.setString(RedisKeyEnum.CUSTOMER_CODE_BAK, customerCode, globalId);
                            }
                            customerCodes.add(customerCode);
                        }catch (Exception e){
                            log.error("初始化用户全局ID失败：{}", globalId, e);
                        }
                    });
                    log.info("=======初始化用户全局ID完成，共{}个globalId，{}个企业", globalIdList.size(), customerCodes.size());
                    log.info("customerCodes:{}", customerCodes);

                    Set<String> buildStandardCustomerCodeList = importBuiltInDataService.buildStandardCustomerCodeList();
                    log.info("=======当前建造标准表里的企业数量：{}", buildStandardCustomerCodeList.size());
                    log.info("buildStandardCustomerCodeList：{}", buildStandardCustomerCodeList);

                    Set<String> countSet = new HashSet<>();
                    countSet.addAll(customerCodes);
                    countSet.addAll(buildStandardCustomerCodeList);
                    log.info("=======共{}个企业", countSet.size());
                }

                if(CollectionUtils.isNotEmpty(customerCodeSet)){
                    log.info("开始刷入3套旧内置，共{}个企业", customerCodeSet.size());
                    importBuiltInDataService.refreshBuildStandard(customerCodeSet);
                }
            } finally {
                redisUtil.unlock("refreshBuildStandardData", RedisKeyEnum.TEMP_ENT_ID_DEAL_LOCK, "refreshBuildStandardData");
            }
        };
        new Thread(r).start();
        return ResponseVo.success();
    }
}
