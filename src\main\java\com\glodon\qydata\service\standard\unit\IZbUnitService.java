package com.glodon.qydata.service.standard.unit;

import com.glodon.qydata.entity.standard.unit.ZbUnit;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.unit.SearchUnitVo;

import java.util.List;

public interface IZbUnitService {

    List<ZbUnit> getUnitList(SearchUnitVo searchUnitVo) throws BusinessException;

    ZbUnit getDetail(String customerCode,String unitId) throws BusinessException;

    List<ZbUnit> addUnit(String unitName, String globalId) throws BusinessException;

    ResponseVo updateUnit(String id, String name, String globalId);

    void batchAddUnitIfNotExist(List<String> unitList, String globalId, String customerCode);
}
