package com.glodon.qydata.mapper.standard.category;

import com.glodon.qydata.entity.standard.category.CategoryTag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: luoml-b
 * @date: 2024/10/22 15:00
 * @description:
 */
@Mapper
public interface CategoryTagMapper {

    List<CategoryTag> selectByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    void deleteByEnterpriseId(@Param("enterpriseId") String enterpriseId);

    void saveBatch(@Param("list") List<CategoryTag> list);
}
