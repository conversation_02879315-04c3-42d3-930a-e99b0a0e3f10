server:
  port: 8083

log:
  level: INFO

spring:
  datasource:
    #主数据源
    master:
      url: jdbc:mysql://${mysql.url}/${mysql.database2}?rewriteBatchedStatements=true&useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&allowMultiQueries=true&zeroDateTimeBehavior=CONVERT_TO_NULL&sessionVariables=group_concat_max_len=204800&scrollTolerantForwardOnly=true
      username: ${mysql.username}
      password: ${mysql.password}
      driverClassName: com.mysql.cj.jdbc.Driver
      type: com.alibaba.druid.pool.DruidDataSource
  main:
    allow-bean-definition-overriding: true
  shardingsphere:
    props:
      sql:
        show: false
    datasource:
      names: ds1
      ds1:
        driver-class-name: com.mysql.cj.jdbc.Driver
        url: jdbc:mysql://${mysql.url}/${mysql.database2}?rewriteBatchedStatements=true&useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai&allowMultiQueries=true&zeroDateTimeBehavior=CONVERT_TO_NULL&sessionVariables=group_concat_max_len=204800&scrollTolerantForwardOnly=true
        username: ${mysql.username}
        password: ${mysql.password}
  data:
    #缓存
    redis:
      host: ${redis.host}
      port: ${redis.port}
      password: ${redis.password}
      timeout: 10000ms
      maxTotal: 300
      jedis:
        pool:
          max-wait: 18000
          max-active: 800
          max-idle: 400
          min-idle: 1
  #上传文件大小设置
  servlet:
    multipart:
      max-file-size: 80MB
      max-request-size: 100MB
config:
  #依赖服务相关配置
  depend:
    #依赖广联云服务域名
    glodonUrl: ${account.domain}
    glodonApiAuthUrl: ${api.auth.domain}
    glodonEntUrl: ${account.ent.domain}
    #数字成本平台接口URL
    digitalCostApiUrl: ${ditital.cost.domain}
    #委托服务域名
    trustUrl: ${dcost.sub.domain}
    # 指标神器项目划分
    zbsqItemUrl: ${dcost.sub.domain}/item
  gcw_cloud:
    APPId: dcost_zblib
    secret: 0L4M99Jl
    secretKey: 1deb69b1411600c5df6f3832b7b96a6b
glodon:
  getUserinfoByIdentityHost: ${account.domain}
  middlegroundhost: http://server-gcdp-mbcb.${gcost.namespace}/
  gcw_cloud_url: http://cloud-api.gldjc.com
#IP地址查询，db文件下载地址：https://gitee.com/lionsoul/ip2region/tree/master/data
ip2region:
  path: /opt/project/data/ip2region.db
basic_auth:
  service.key: MiOWRrW6hYBmW6xQE8OGRH2f9I5wfxUG
  server.secret: vG23xiwByuRxKdhAehgKaDEK89jyKzsC

#SpringBoot 内部服务监控 Actuator 暴露端点prometheus 接入MyOps对JVM进行监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,prometheus,metrics
      base-path: /ebq-actuator
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
    distribution:
      percentiles-histogram:
        http:
          server:
            requests: true
    tags:
      application: ${artifactId}
# 授权中心服务
apiAuth:
  url: ${api.auth.domain}
  appKey: kqIUvqfcH7obwsZRx4U3L3yG5M3sLuRL
  appSecret: EVmzN0G5ebSGh44q9NNhVlwDywvBxSXd
  g-signature: F864378123E9BA92E14E6C7862257FCC

# 当前最新分表名称(新注册企业入当前最新分表)
sharding_table:
  current_table_name: "{0:'zb_project_feature_category_view_standards_0', 1:'tb_commonprojcategory_standards_0'}"

sequence:
  defaultCacheSize: 1000
  defaultIntegerIdStartValue: 100000000

sendLogDebug: ${send.log.debug}

area-scheduled-enabled: ${isAreaScheduled}

privateDeployment: ${is.private}

gcdp-monitor:
  at-telephone: 17600252980
  # 异常多久触发一次 单位s
  lock-expire: 20
  # 机器人发送的key
  send-key: 5a3ba100-884b-4faa-bfb4-e95e21b79e47

#mybatis-plus:
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
