package com.glodon.qydata.service.system;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.vo.system.EntInfoVo;

import java.util.List;

/**
 * 外部用户相关查询
 * Created by weijf on 2021/12/22.
 */
public interface IGlodonUserService {

    /**
     * 查询企业下的用户列表
     * @param globalId
     * @param identity
     * @return
     * @throws BusinessException
     * <AUTHOR>
     */
    JSONArray getEntMemberList(String globalId, String identity) throws BusinessException;

    /**
     * 查询企业ID
     * @param globalId
     * @param accessToken
     * @return
     * <AUTHOR>
     */
    String getEnterpriseId(String globalId, String accessToken);

    /**
     * 根据globalId从广材网查询企业信息
     * @param globalId
     * @return
     * <AUTHOR>
     */
    EntInfoVo getEntInfo(String globalId);


    /**
     * 根据globalId查询CustomerCode
     * @param globalId
     * @return
     * @throws BusinessException
     * @auther: weijf
     */
    String getCustomerCode(String globalId)throws BusinessException;

    /**
     * 设置企业标识缓存, 确定使用企业id还是企业编码
     * @param customerCode
     * @param enterpriseId
     * @return
     * <AUTHOR>
     */
    String setQyFlagCache(String customerCode, String enterpriseId);

    /**
     * 根据globalId查询CustomerCode（内部方法）
     * @param globalId
     * @return
     * @throws BusinessException
     * @auther: weijf
     */
    String getCustomerCodeInner(String globalId)throws BusinessException;

    /**
     * 获取用户名
     * @param globalId
     * @param accessToken
     * @return
     * <AUTHOR>
     */
    String getUserName(String globalId, String accessToken);

    /**
     * 查询企业信息
     * @throws
     * @param globalId
     * @param accessToken
     * <AUTHOR>
     * @return {@link JSONObject}
     * @date 2022/7/27 15:28
     */
    JSONObject getEntInforFromCached(String globalId, String accessToken);

    List<String> getUserIdsByGlobalIds(List<String> globalIds);

    JSONArray getUserInfoByUserIds(List<String> userIds);

    String getAccountNameById(String userId, String sgToken);
}
