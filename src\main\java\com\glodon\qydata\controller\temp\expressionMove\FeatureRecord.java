package com.glodon.qydata.controller.temp.expressionMove;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("feature_record")
public class FeatureRecord implements Serializable {

    private static final long serialVersionUID = -2719957245485285827L;

    private Long id;

    /**
     * 企业编码
     */
    private String customerCode;

    /**
     * expressionToFeature状态
     */
    private Integer toFeatureStatus;

    /**
     * 增加内置状态
     */
    private Integer addStatus;

    private String errorMsg;

    public FeatureRecord() {}

    public FeatureRecord(String customerCode) {
        this.customerCode = customerCode;
    }

    /**
     * 专业、主要量指标、工程特征的初始化次数
     */
    private Integer initCount;

    /**
     * 是否存在同专业下【名称+类型】重复
     */
    private Integer isRepeat;

    private String repeatInfo;
}