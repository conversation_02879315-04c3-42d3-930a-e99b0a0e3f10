package com.glodon.qydata.dto;

import com.glodon.qydata.entity.standard.projectOrContractInfo.StandardsProjectInfoEntity;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: luoml-b
 * @date: 2024/8/30 15:03
 * @description: 移动到入参
 */
@Data
public class ProjectInfoMoveToDTO implements Serializable {

    private static final long serialVersionUID = 6380740481515812263L;
    /**
     * 需要移动的子项
     */
    private List<StandardsProjectInfoEntity> items;

    /**
     * 移动到分组
     */
    private StandardsProjectInfoEntity group;

    private String customerCode;

}
