package com.glodon.qydata.controller.openapi;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.dto.StandardTradeHistoryDataDto;
import com.glodon.qydata.entity.standard.mainQuantity.ZbStandardsMainQuantity;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.service.standard.mainQuantity.IStandardsMainQuantityService;
import com.glodon.qydata.service.standard.trade.IStandardsTradeService;
import com.glodon.qydata.vo.common.ResponseVo;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureDsBody;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureDsVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 历史标准数据同步 -对外接口
 * @date 2021/11/19 9:09
 */
@Slf4j
@RestController
@RequestMapping("/basicInfo/openApi/historyDS")
@Tag(name = "历史标准数据同步 -对外接口", description = "历史标准数据同步 -对外接口")
public class HistoryDsOpenApiController extends BaseController {

    @Autowired
    private IProjectFeatureService projectFeatureService;
    @Autowired
    private IStandardsTradeService tradeService;
    @Autowired
    private IStandardsMainQuantityService mainQuantityService;


    /**
     * @description: 对外提供-获取工程特征内置数据
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/11/19 10:46
     */
    @Operation(summary = "对外提供-获取工程特征内置数据")
    @GetMapping("/getSystemFeature")
    public ResponseVo<List<ProjectFeatureDsVO>> getSystemFeature() {
        return ResponseVo.success(projectFeatureService.getSystemFeature());
    }

    /**
     * @description: 对外提供-历史标准数据同步-工程特征
     * 前提： 1.已处理工程分类、工程专业历史数据；
     *       2.提供一个所有内置数据的查询接口；
     *       3.调用方按约定传参（1.名称、类型、枚举值、是否必填、是否搜索条件；2.新的标准的专业id字段、排序字段由调用方处理）
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/11/19 10:46
     */
    @Operation(summary = "对外提供-历史标准数据同步-工程特征")
    @PostMapping("/projectFeature")
    public ResponseVo featureHistoryDs(@RequestBody ProjectFeatureDsBody body) {
        log.info("参数：{}", body);
        long startTime = System.currentTimeMillis();
        projectFeatureService.featureHistoryDs(body.getFeatureDsVOList(), body.getCustomerCode());
        long endTime = System.currentTimeMillis();
        log.info("controller打印总耗时...{}", endTime-startTime);
        return ResponseVo.success();
    }

    /**
     * @description: 对外提供-历史标准数据同步-工程专业
     * 数据保证： 1.内置专业编码在规定范围以内
     *           2、引用专业必须在7种专业内部引用，生成的专编码在20个指定编码内
     *           3、专业主键id应用到其他标准中，需对应起来
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/12/3 10:12
     */
    @Operation(summary = "对外提供-历史标准数据同步-工程专业")
    @PostMapping("/tradeData")
    public ResponseVo tradeHistoryDs(@RequestBody Map<String,List<StandardTradeHistoryDataDto>> map) {
        tradeService.insertHistoryData(map);
        return ResponseVo.success();
    }

    /**
    　　* @description: 对外提供-内置工程专业
    　　* @return ZbStandardsTrade
    　　* <AUTHOR>
    　　* @date 2021/12/6 10:35
    　　*/
    @Operation(summary = "对外提供-内置工程专业")
    @GetMapping("/getSystemTrade")
    public ResponseVo<List<ZbStandardsTrade>> getSystemTrade(){
          return new ResponseVo<>(ResponseCode.SUCCESS,tradeService.getSystemData());
    }

    /**
     　　* @description: 对外提供-查看指定企业下的工程专业列表
     　　* @return ZbStandardsTrade
     　　* <AUTHOR>
     　　* @date 2021/12/6 10:35
     　　*/
    @Operation(summary = "对外提供-查看指定企业下的工程专业列表")
    @GetMapping("/getTradeListByCustomerCode")
    public ResponseVo<List<ZbStandardsTrade>> getTradeListByCustomerCode(@RequestParam("customerCode") String customerCode) throws BusinessException {
        return new ResponseVo<>(ResponseCode.SUCCESS,tradeService.getTradeListByCustomerCode(customerCode));
    }


    /**
     　　* @description: 对外提供-内置主要量指标
     　　* @return ZbStandardsTrade
     　　* <AUTHOR>
     　　* @date 2021/12/6 10:35
     　　*/
    @Operation(summary = "对外提供-内置主要量指标")
    @GetMapping("/getSystemMainQuantity")
    public ResponseVo<List<ZbStandardsMainQuantity>> getSystemMainQuantity(){
        return new ResponseVo<>(ResponseCode.SUCCESS,mainQuantityService.getSystemData());
    }

    /**
     * @description: 对外提供-历史标准数据同步-主要量指标
     * 数据保证： 1、主要量指标的专业引用与专业的uuid保持一致
     *           2、必须在专业同步之后进行
     * @param
     * @return
     * <AUTHOR>
     * @date 2021/12/3 10:12
     */
    @Operation(summary = "对外提供-历史标准数据同步-主要量指标")
    @PostMapping("/mainQuantity")
    public ResponseVo mainQuantityHistoryDs(@RequestBody Map<String,List<ZbStandardsMainQuantity>> map) {
       mainQuantityService.insertHistoryData(map);
        return ResponseVo.success();
    }

}
