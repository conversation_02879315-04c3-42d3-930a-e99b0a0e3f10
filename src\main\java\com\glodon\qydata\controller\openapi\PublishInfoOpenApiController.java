package com.glodon.qydata.controller.openapi;

import com.glodon.qydata.common.BaseController;
import com.glodon.qydata.dto.PublishInfoDto;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.vo.common.ResponseVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


/**
 * 发布版本信息对外接口
 *
 * <AUTHOR>
 */

@Slf4j
@RestController
@RequestMapping("/basicInfo/openApi/publishInfo")
@Tag(name = "发布版本信息对外接口", description = "发布版本信息对外接口")
public class PublishInfoOpenApiController extends BaseController {

    @Resource
    private PublishInfoService publishInfoServiceImpl;

    @Operation(summary = "获取发布信息")
    @GetMapping("/publishStatusInfo")
    public ResponseVo<PublishInfoDto> publishStatusInfo() {
        String customerCode = getCustomerCode();
        return ResponseVo.success(publishInfoServiceImpl.getPublishInfo(customerCode));
    }

    @Operation(summary = "获取发布信息")
    @GetMapping("/test")
    public ResponseVo test(String type) {
        String customerCode = getCustomerCode();
        String globalId = getGlobalId();
        publishInfoServiceImpl.updateVersion(customerCode, globalId, type);
        return ResponseVo.success();
    }
}
