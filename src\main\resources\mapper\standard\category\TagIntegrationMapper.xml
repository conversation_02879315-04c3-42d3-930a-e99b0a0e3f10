<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.glodon.qydata.mapper.standard.tag.TagIntegrationMapper">

    <select id="selectByEnterpriseId" resultType="com.glodon.qydata.entity.standard.tag.TagIntegration">
        select * from tag_integration
        where enterprise_id = #{enterpriseId} and tag_type=#{tagType}
    </select>


    <insert id="save" useGeneratedKeys="false">
        insert into tag_integration (id,enterprise_id,create_time, tag_type) values
            (
                #{id},
                #{enterpriseId},
                #{createTime},
                #{tagType}
            )
    </insert>


</mapper>
